---
description:
globs:
alwaysApply: false
---
# 项目记忆

本文件存储AI助手在与用户交互过程中学习到的项目特定知识、约定和用户偏好。

## 用户偏好

- 用户希望使用Cursor的Memories功能来增强AI助手的记忆能力
- 用户使用中文进行交流

## 技术决策

- 项目基于RuoYi框架开发，采用前后端分离架构
- 前端使用Vue + Vite技术栈
- 后端使用Java开发，基于RuoYi框架
- 数据库使用SQL Server
- 缓存使用Redis

## 项目约定

- 使用Memory Bank结构管理项目记忆
- 采用Plan/Act模式进行工作
- 项目文档使用Markdown格式
- 批处理脚本用于项目启动和管理

## 项目结构

- ruoyi-java/：后端Java代码
- src/：前端源代码
- public/：前端静态资源
- sql/：数据库脚本
- redis/：Redis相关配置
- bin/：可执行脚本
- vite/：Vite配置文件
- .github/：GitHub相关配置
- .cursor/rules/：Cursor规则文件
- memory-bank/：项目记忆文件

## 工作流程

- 使用start-project.bat启动项目
- 使用setup-database.bat设置数据库
- 使用run-backend.bat运行后端服务

## 已知问题

- 项目信息主要基于文件结构推断，可能不完全准确
- Memories功能是否正常工作尚未验证
