linters:
  enable:
    - errcheck
    - gofmt
    - goimports
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused
    - revive
    - misspell
    - godot
    - gosec
    - nolintlint

linters-settings:
  gofmt:
    simplify: true
  goimports:
    local-prefixes: your-project
  revive:
    rules:
      - name: exported
        severity: warning
        disabled: false
        arguments:
          - checkPrivateR<PERSON>eivers
          - disableStutteringCheck
      - name: var-naming
        severity: warning
        disabled: false
      - name: package-comments
        severity: warning
        disabled: false

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gosec
        - errcheck

run:
  timeout: 5m
  tests: true
  skip-dirs:
    - vendor/
    - node_modules/
