# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache gcc musl-dev git

# 复制go.mod和go.sum
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -ldflags="-s -w" -o ruoyi-go main.go

# 多阶段构建，使用alpine作为基础镜像
FROM alpine:latest

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 从构建阶段复制二进制文件
COPY --from=builder /app/ruoyi-go .

# 创建必要的目录
RUN mkdir -p /app/configs /app/logs /app/upload /app/static

# 复制配置文件
COPY --from=builder /app/configs/config.yaml /app/configs/

# 暴露端口
EXPOSE 8080

# 运行
CMD ["./ruoyi-go"] 