.PHONY: all build clean run test vet fmt lint docker docker-compose help

# 全局变量
APP_NAME=ruoyi-go
VERSION=$(shell git describe --tags --always --dirty || echo "unknown")
LDFLAGS=-ldflags "-s -w -X main.version=$(VERSION)"
GOFILES=$(shell find . -name "*.go" -type f)
GOPATH:=$(shell go env GOPATH)
GOBIN:=$(GOPATH)/bin

# 默认目标
all: clean fmt vet lint test build

# 帮助信息
help:
	@echo "管理 $(APP_NAME) 的 Makefile"
	@echo ""
	@echo "用法:"
	@echo "    make build      编译应用"
	@echo "    make run        运行应用"
	@echo "    make clean      清理构建产物"
	@echo "    make test       运行测试"
	@echo "    make vet        代码检查"
	@echo "    make fmt        格式化代码"
	@echo "    make lint       静态代码分析"
	@echo "    make docker     构建Docker镜像"
	@echo "    make deploy     部署应用"
	@echo "    make all        执行清理、格式化、检查、测试和构建"
	@echo "    make help       显示此帮助信息"

# 编译应用
build:
	@echo "编译应用..."
	@go build $(LDFLAGS) -o dist/$(APP_NAME) main.go
	@echo "编译完成: ./dist/$(APP_NAME)"

# 清理构建产物
clean:
	@echo "清理构建产物..."
	@rm -rf dist/
	@echo "清理完成"

# 运行应用
run:
	@echo "运行应用..."
	@go run main.go

# 运行测试
test:
	@echo "运行测试..."
	@go test ./... -coverprofile=coverage.out
	@go tool cover -func=coverage.out

# 代码检查
vet:
	@echo "运行代码检查..."
	@go vet ./...

# 格式化代码
fmt:
	@echo "格式化代码..."
	@gofmt -s -w $(GOFILES)

# 静态代码分析
lint:
	@echo "运行静态代码分析..."
	@if [ ! -d $(GOBIN) ]; then mkdir -p $(GOBIN); fi
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "正在安装 golangci-lint..."; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(GOBIN) v1.53.3; \
	fi
	@golangci-lint run ./...

# 构建Docker镜像
docker:
	@echo "构建Docker镜像..."
	@docker build -t $(APP_NAME):$(VERSION) .
	@echo "构建完成: $(APP_NAME):$(VERSION)"

# 使用docker-compose部署
deploy:
	@echo "部署应用..."
	@docker-compose up -d
	@echo "部署完成"