# RuoYi-Go

RuoYi-Go 是一个基于 Go 语言的后台管理系统框架，使用 Gin + Zap + GORM 构建。本项目是从 Java 版本的 RuoYi 框架迁移而来，保留了原框架的核心功能和设计理念，同时充分利用 Go 语言的特性进行了优化。

## 技术栈

- **Web框架**: [Gin](https://github.com/gin-gonic/gin)
- **ORM**: [GORM](https://gorm.io/) + SQL Server
- **缓存**: [Redis](https://redis.io/)
- **日志**: [Zap](https://github.com/uber-go/zap)
- **配置**: [Viper](https://github.com/spf13/viper)

## 目录结构

```
├── cmd                   # 入口命令
├── configs               # 配置文件
├── internal              # 内部包
│   ├── config            # 配置结构
│   ├── controller        # 控制器
│   ├── database          # 数据库
│   ├── initialize        # 初始化
│   ├── middleware        # 中间件
│   ├── model             # 数据模型
│   ├── redis             # Redis服务
│   ├── repository        # 数据仓库
│   ├── router            # 路由
│   ├── service           # 业务逻辑
│   └── utils             # 工具包
├── pkg                   # 公共包
├── static                # 静态资源
└── upload                # 上传目录
```

## 快速开始

### 前置条件

- Go 1.18 或更高版本
- SQL Server 数据库
- Redis 服务

### 安装依赖

```bash
go mod tidy
```

### 配置

编辑 `configs/config.yaml` 文件，配置数据库、Redis 等信息。

### 运行

#### 开发环境

```bash
go run main.go
```

#### 生产环境

**Windows:**

```bash
.\build.bat
```

**Linux/Mac:**

```bash
chmod +x build.sh
./build.sh
```

或使用 Makefile:

```bash
make build
```

### Docker 部署

```bash
# 构建镜像
docker build -t ruoyi-go:latest .

# 或使用 docker-compose
docker-compose up -d
```

## 特性

- 基于 Gin 的高性能 Web 框架
- 使用 GORM 进行数据库操作，支持 SQL Server
- 使用 Zap 进行高性能日志记录
- Redis 缓存支持
- JWT 身份验证
- 统一的错误处理
- 参数验证
- 支持热重载的配置管理
- 优雅关闭
- Docker 支持

## 环境要求

- Go 1.18+
- SQL Server 2017+
- Redis 6.0+

## 许可证

[MIT](LICENSE)

## 项目介绍

本项目是将若依(RuoYi)管理系统从Java Spring Boot技术栈完整迁移到Go Gin技术栈的实现。目标是**100%复刻Java后端功能**，确保前端Vue应用无需任何修改即可对接Go后端。

### 迁移目标
- ✅ **功能完全一致**：所有Java后端功能在Go中完全实现
- ✅ **接口完全兼容**：API路径、参数、响应格式与Java版本完全一致
- ✅ **数据结构一致**：数据库表结构、字段类型、约束完全一致
- ✅ **业务逻辑一致**：权限控制、数据验证、业务流程保持一致

## 监控模块实现

监控模块主要实现了系统运行状态的监控和管理功能，包括操作日志、登录日志、在线用户、定时任务、服务器监控和缓存监控等功能。

### 操作日志管理

操作日志管理功能实现了对系统操作日志的查询、详情查看、删除和清空功能：

- 实现了`SysOperLog`领域模型
- 实现了`ISysOperLogService`接口及其实现类
- 使用真实数据库服务替代模拟数据

### 登录日志管理

登录日志管理功能实现了对系统登录日志的查询、删除和清空功能：

- 实现了`SysLogininfor`领域模型
- 实现了`ISysLogininforService`接口及其实现类
- 使用真实数据库服务替代模拟数据

### 在线用户管理

在线用户管理功能实现了对在线用户的查询和强制退出功能：

- 实现了`SysUserOnline`领域模型
- 实现了`ISysUserOnlineService`接口及其实现类
- 使用Redis存储在线用户信息
- 支持按IP和用户名过滤查询

### 定时任务管理

定时任务管理功能实现了对系统定时任务的增删改查、启停和立即执行功能：

- 实现了`SysJob`领域模型
- 实现了`ISysJobService`接口及其实现类
- 实现了任务日志记录功能

### 定时任务日志管理

定时任务日志管理功能实现了对定时任务执行日志的查询、详情查看、删除和清空功能：

- 实现了`SysJobLog`领域模型
- 实现了`ISysJobLogService`接口及其实现类
- 使用真实数据库服务替代模拟数据

### 服务器监控

服务器监控功能实现了对服务器基本信息、CPU、内存、磁盘等状态的监控：

- 实现了`ServerInfo`领域模型
- 实现了`IServerInfoService`接口及其实现类
- 使用`gopsutil`库获取系统实时信息

### 缓存监控

缓存监控功能实现了对Redis缓存的监控和管理：

- 实现了`IRedisCacheService`接口及其实现类
- 支持查看Redis基本信息、命令统计、数据库大小
- 支持查看缓存键值
- 支持清除缓存 