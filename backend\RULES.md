# Go 后端项目规范 (Go + Gin + Zap + GORM)

## 目录结构

```
backend/
├── cmd/                    # 应用程序入口
│   └── api/                # API 服务入口
│       └── main.go         # 主程序入口
├── configs/                # 配置文件目录
│   ├── config.yaml         # 主配置文件
│   └── config.dev.yaml     # 开发环境配置
├── internal/               # 私有应用和库代码
│   ├── api/                # API 层 (HTTP 处理程序)
│   │   └── v1/             # API 版本
│   ├── config/             # 配置结构和加载逻辑
│   ├── middleware/         # HTTP 中间件
│   ├── model/              # 数据模型定义
│   ├── repository/         # 数据访问层
│   ├── service/            # 业务逻辑层
│   └── utils/              # 内部工具包
├── pkg/                    # 可被外部应用使用的库代码
│   ├── auth/               # 认证相关
│   ├── cache/              # 缓存工具
│   ├── logger/             # 日志工具
│   └── validator/          # 验证工具
├── scripts/                # 构建、安装、分析等脚本
├── test/                   # 额外的外部测试应用和测试数据
├── docs/                   # 文档
│   └── swagger/            # Swagger API 文档
├── go.mod                  # Go 模块定义
├── go.sum                  # Go 模块校验和
├── Makefile                # 构建指令
└── README.md               # 项目说明
```

## 代码规范

### 命名规范

1. **包名**：
   - 使用小写单词，不使用下划线或混合大小写
   - 简短且有意义，通常为单个名词
   - 例如：`model`, `service`, `repository`

2. **文件名**：
   - 使用小写单词，用下划线分隔
   - 例如：`user_service.go`, `auth_middleware.go`

3. **变量名**：
   - 使用驼峰命名法
   - 局部变量使用小驼峰：`userName`, `pageSize`
   - 全局变量使用大驼峰：`DefaultClient`, `MaxRetryCount`

4. **常量名**：
   - 使用全大写，用下划线分隔
   - 例如：`MAX_CONNECTIONS`, `DEFAULT_TIMEOUT`

5. **接口名**：
   - 使用大驼峰命名法
   - 通常以 "er" 结尾
   - 例如：`UserService`, `Repository`, `Logger`

6. **结构体名**：
   - 使用大驼峰命名法
   - 例如：`User`, `OrderDetail`

7. **方法名**：
   - 使用大驼峰命名法
   - 例如：`GetUserByID`, `CreateOrder`

### 注释规范

1. **包注释**：
   - 每个包都应该有包注释
   - 放在 package 语句之前
   ```go
   // Package model 定义了应用的数据模型
   package model
   ```

2. **函数/方法注释**：
   - 每个导出的函数/方法必须有注释
   - 使用完整的句子描述功能
   ```go
   // GetUserByID 根据用户ID获取用户信息
   // 如果用户不存在，返回 nil 和 ErrUserNotFound
   func GetUserByID(id uint) (*User, error) {
       // ...
   }
   ```

3. **变量/常量注释**：
   - 对于重要的变量/常量，添加注释说明用途
   ```go
   // MaxConnectionRetries 定义了连接数据库的最大重试次数
   const MaxConnectionRetries = 5
   ```

### 错误处理规范

1. **错误值**：
   - 定义包级别的错误变量
   ```go
   var (
       ErrUserNotFound = errors.New("user not found")
       ErrInvalidInput = errors.New("invalid input")
   )
   ```

2. **错误处理**：
   - 不要使用 `_` 忽略错误，除非有明确理由
   - 使用 `errors.Is` 和 `errors.As` 进行错误比较和类型断言
   - 使用 `fmt.Errorf` 和 `%w` 包装错误
   ```go
   if err != nil {
       return fmt.Errorf("failed to fetch user: %w", err)
   }
   ```

3. **自定义错误类型**：
   - 对于需要携带额外信息的错误，定义自定义错误类型
   ```go
   type ValidationError struct {
       Field string
       Message string
   }

   func (e ValidationError) Error() string {
       return fmt.Sprintf("%s: %s", e.Field, e.Message)
   }
   ```

### 日志规范 (Zap)

1. **日志级别**：
   - DEBUG：调试信息，仅在开发环境使用
   - INFO：一般信息，记录正常操作
   - WARN：警告信息，不影响正常运行但需要注意
   - ERROR：错误信息，影响功能但不导致程序崩溃
   - FATAL：致命错误，导致程序无法继续运行

2. **日志格式**：
   - 结构化日志，使用字段而非字符串拼接
   ```go
   logger.Info("用户登录", 
       zap.String("username", user.Username),
       zap.String("ip", clientIP),
       zap.Duration("latency", latency),
   )
   ```

3. **日志内容**：
   - 记录关键操作的开始和结束
   - 记录错误详情，包括错误消息和堆栈
   - 不记录敏感信息（密码、令牌等）

### 数据库规范 (GORM + SQL Server)

1. **模型定义**：
   - 使用结构体标签定义表名、列名、索引等
   ```go
   type User struct {
       ID        uint      `gorm:"primaryKey"`
       Username  string    `gorm:"size:50;not null;unique"`
       Email     string    `gorm:"size:100;index"`
       Password  string    `gorm:"size:100;not null"`
       CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
       UpdatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
   }

   // TableName 指定表名
   func (User) TableName() string {
       return "sys_user"
   }
   ```

2. **查询优化**：
   - 使用索引字段进行查询
   - 使用 Preload 或 Joins 加载关联数据
   - 大数据集使用分页查询
   ```go
   var users []User
   result := db.Preload("Roles").
       Where("status = ?", "active").
       Offset(page * pageSize).
       Limit(pageSize).
       Find(&users)
   ```

3. **事务处理**：
   - 使用事务确保数据一致性
   ```go
   tx := db.Begin()
   defer func() {
       if r := recover(); r != nil {
           tx.Rollback()
       }
   }()

   if err := tx.Error; err != nil {
       return err
   }

   if err := tx.Create(&user).Error; err != nil {
       tx.Rollback()
       return err
   }

   if err := tx.Create(&userRole).Error; err != nil {
       tx.Rollback()
       return err
   }

   return tx.Commit().Error
   ```

4. **SQL Server 特定配置**：
   ```go
   dsn := "sqlserver://username:password@localhost:1433?database=dbname"
   db, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
       NamingStrategy: schema.NamingStrategy{
           TablePrefix: "sys_",   // 表前缀
           SingularTable: true,   // 使用单数表名
       },
       Logger: logger.Default.LogMode(logger.Info),
   })
   ```

### API 规范 (Gin)

1. **路由定义**：
   - 按资源和版本组织路由
   ```go
   v1 := router.Group("/api/v1")
   {
       userRoutes := v1.Group("/users")
       {
           userRoutes.GET("", userHandler.List)
           userRoutes.POST("", userHandler.Create)
           userRoutes.GET("/:id", userHandler.Get)
           userRoutes.PUT("/:id", userHandler.Update)
           userRoutes.DELETE("/:id", userHandler.Delete)
       }
   }
   ```

2. **请求验证**：
   - 使用结构体标签进行请求验证
   ```go
   type CreateUserRequest struct {
       Username string `json:"username" binding:"required,min=3,max=50"`
       Email    string `json:"email" binding:"required,email"`
       Password string `json:"password" binding:"required,min=8"`
   }

   func (h *UserHandler) Create(c *gin.Context) {
       var req CreateUserRequest
       if err := c.ShouldBindJSON(&req); err != nil {
           c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
           return
       }
       // ...
   }
   ```

3. **响应格式**：
   - 统一的响应格式
   ```go
   type Response struct {
       Code    int         `json:"code"`
       Message string      `json:"message"`
       Data    interface{} `json:"data,omitempty"`
   }

   func Success(c *gin.Context, data interface{}) {
       c.JSON(http.StatusOK, Response{
           Code:    200,
           Message: "success",
           Data:    data,
       })
   }

   func Error(c *gin.Context, code int, message string) {
       c.JSON(code, Response{
           Code:    code,
           Message: message,
       })
   }
   ```

4. **中间件使用**：
   - 全局中间件：日志、跨域、恢复等
   - 路由组中间件：认证、权限等
   ```go
   router := gin.New()
   router.Use(middleware.Logger(), middleware.Recovery())

   authorized := router.Group("/api")
   authorized.Use(middleware.Auth())
   {
       // 需要认证的路由
   }
   ```

### 缓存规范 (Redis)

1. **键命名**：
   - 使用冒号分隔不同部分
   - 格式：`应用名:对象类型:ID:字段`
   - 例如：`app:user:123:profile`

2. **过期时间**：
   - 为所有缓存设置合理的过期时间
   - 根据数据更新频率和重要性设置
   ```go
   rdb.Set(ctx, "app:user:123:profile", userJSON, time.Hour*24)
   ```

3. **缓存更新策略**：
   - 写操作时主动更新或删除缓存
   ```go
   // 更新用户信息后
   if err := updateUserInDB(user); err != nil {
       return err
   }
   // 删除缓存，下次读取时重新加载
   return rdb.Del(ctx, fmt.Sprintf("app:user:%d:profile", user.ID)).Err()
   ```

4. **缓存穿透防护**：
   - 对不存在的数据设置空值缓存
   ```go
   user, err := getUserFromDB(id)
   if err != nil {
       if errors.Is(err, sql.ErrNoRows) {
           // 设置空值缓存，防止缓存穿透
           rdb.Set(ctx, fmt.Sprintf("app:user:%d", id), "nil", time.Minute*10)
       }
       return nil, err
   }
   ```

## 测试规范

1. **单元测试**：
   - 每个包至少有一个 `*_test.go` 文件
   - 测试函数命名为 `Test<Function>`
   - 使用表驱动测试处理多个测试用例
   ```go
   func TestGetUserByID(t *testing.T) {
       tests := []struct {
           name     string
           id       uint
           wantUser *User
           wantErr  error
       }{
           {
               name:     "existing user",
               id:       1,
               wantUser: &User{ID: 1, Username: "admin"},
               wantErr:  nil,
           },
           {
               name:     "non-existing user",
               id:       999,
               wantUser: nil,
               wantErr:  ErrUserNotFound,
           },
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               gotUser, gotErr := GetUserByID(tt.id)
               if !errors.Is(gotErr, tt.wantErr) {
                   t.Errorf("GetUserByID() error = %v, wantErr %v", gotErr, tt.wantErr)
               }
               if !reflect.DeepEqual(gotUser, tt.wantUser) {
                   t.Errorf("GetUserByID() = %v, want %v", gotUser, tt.wantUser)
               }
           })
       }
   }
   ```

2. **模拟和存根**：
   - 使用接口进行依赖注入，便于测试时模拟
   - 使用 `testify/mock` 或 `gomock` 创建模拟对象
   ```go
   type UserRepository interface {
       GetByID(id uint) (*User, error)
       Create(user *User) error
   }

   type UserService struct {
       repo UserRepository
   }

   func NewUserService(repo UserRepository) *UserService {
       return &UserService{repo: repo}
   }
   ```

3. **集成测试**：
   - 使用 Docker 容器运行依赖服务
   - 使用环境变量区分测试环境
   - 测试前准备数据，测试后清理数据

## 部署规范

1. **容器化**：
   - 使用多阶段构建减小镜像大小
   ```dockerfile
   # 构建阶段
   FROM golang:1.21-alpine AS builder
   WORKDIR /app
   COPY . .
   RUN go mod download
   RUN go build -o main ./cmd/api

   # 运行阶段
   FROM alpine:latest
   RUN apk --no-cache add ca-certificates tzdata
   WORKDIR /app
   COPY --from=builder /app/main .
   COPY --from=builder /app/configs ./configs
   EXPOSE 8080
   CMD ["./main"]
   ```

2. **环境配置**：
   - 使用环境变量覆盖配置
   - 敏感信息使用密钥管理
   ```go
   type Config struct {
       Server struct {
           Port int `yaml:"port" env:"SERVER_PORT"`
       } `yaml:"server"`
       Database struct {
           Host     string `yaml:"host" env:"DB_HOST"`
           Port     int    `yaml:"port" env:"DB_PORT"`
           User     string `yaml:"user" env:"DB_USER"`
           Password string `yaml:"password" env:"DB_PASSWORD"`
           Name     string `yaml:"name" env:"DB_NAME"`
       } `yaml:"database"`
       Redis struct {
           Host     string `yaml:"host" env:"REDIS_HOST"`
           Port     int    `yaml:"port" env:"REDIS_PORT"`
           Password string `yaml:"password" env:"REDIS_PASSWORD"`
           DB       int    `yaml:"db" env:"REDIS_DB"`
       } `yaml:"redis"`
   }
   ```

3. **健康检查**：
   - 提供健康检查端点
   ```go
   router.GET("/health", func(c *gin.Context) {
       c.JSON(http.StatusOK, gin.H{
           "status": "ok",
           "time":   time.Now().Format(time.RFC3339),
       })
   })
   ```

4. **监控和日志**：
   - 集成 Prometheus 指标
   - 结构化日志输出到标准输出
   - 使用 ELK 或类似工具收集日志

## 安全规范

1. **认证**：
   - 使用 JWT 进行认证
   - 令牌存储在 HTTP-only Cookie 或 Authorization 头
   - 实现令牌刷新机制

2. **授权**：
   - 基于角色的访问控制 (RBAC)
   - 在中间件中检查权限

3. **密码处理**：
   - 使用 bcrypt 或 argon2 哈希密码
   - 不存储明文密码
   ```go
   func HashPassword(password string) (string, error) {
       bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
       return string(bytes), err
   }

   func CheckPassword(password, hash string) bool {
       err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
       return err == nil
   }
   ```

4. **输入验证**：
   - 验证所有用户输入
   - 防止 SQL 注入、XSS 和 CSRF 攻击

5. **敏感数据处理**：
   - 加密存储敏感数据
   - 传输中使用 TLS/SSL
   - 日志中不记录敏感信息

## 性能优化

1. **数据库优化**：
   - 合理使用索引
   - 避免 N+1 查询问题
   - 大数据集使用分页

2. **缓存策略**：
   - 缓存热点数据
   - 使用本地缓存减少网络调用

3. **并发处理**：
   - 使用 goroutine 处理并发任务
   - 使用 context 进行超时控制
   ```go
   ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
   defer cancel()

   ch := make(chan Result, 1)
   go func() {
       result, err := heavyOperation()
       ch <- Result{Value: result, Err: err}
   }()

   select {
   case result := <-ch:
       return result.Value, result.Err
   case <-ctx.Done():
       return nil, ctx.Err()
   }
   ```

4. **资源池化**：
   - 使用连接池管理数据库连接
   - 复用 HTTP 客户端

## 版本控制

1. **语义化版本**：
   - 遵循 `主版本.次版本.修订版本` 格式
   - 不兼容的 API 更改增加主版本号
   - 向后兼容的功能添加增加次版本号
   - 向后兼容的问题修复增加修订版本号

2. **分支管理**：
   - `main/master`: 稳定版本
   - `develop`: 开发版本
   - `feature/*`: 新功能开发
   - `release/*`: 版本发布准备
   - `hotfix/*`: 紧急修复

3. **提交消息**：
   - 使用约定式提交格式
   ```
   <type>(<scope>): <subject>

   <body>

   <footer>
   ```
   - 类型：feat, fix, docs, style, refactor, test, chore
   - 示例：`feat(user): add user registration endpoint`

## 文档规范

1. **API 文档**：
   - 使用 Swagger/OpenAPI 记录 API
   - 包含请求参数、响应格式和错误码
   ```go
   // @Summary 获取用户信息
   // @Description 根据用户ID获取用户详细信息
   // @Tags users
   // @Accept json
   // @Produce json
   // @Param id path int true "用户ID"
   // @Success 200 {object} Response{data=User}
   // @Failure 400 {object} Response
   // @Failure 404 {object} Response
   // @Router /users/{id} [get]
   func (h *UserHandler) Get(c *gin.Context) {
       // ...
   }
   ```

2. **代码文档**：
   - 使用 godoc 格式注释
   - 为包、导出函数和类型提供文档

3. **项目文档**：
   - README.md：项目概述、安装和使用说明
   - CONTRIBUTING.md：贡献指南
   - CHANGELOG.md：版本变更记录 