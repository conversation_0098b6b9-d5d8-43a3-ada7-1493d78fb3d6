# 完全自动化修复脚本 - 避免编码问题
Write-Host "=== 完全自动化Java到Go迁移修复 ===" -ForegroundColor Green

# 步骤1：停止现有进程
Write-Host "停止现有进程..." -ForegroundColor Yellow
Get-Process -Name "*wosm*" -ErrorAction SilentlyContinue | Stop-Process -Force

# 步骤2：安装依赖
Write-Host "安装Go依赖..." -ForegroundColor Yellow
go get github.com/gin-gonic/gin
go get github.com/golang-jwt/jwt/v4
go get github.com/denisenkom/go-mssqldb
go get golang.org/x/crypto/bcrypt
go mod tidy

# 步骤3：检查SQL Server
Write-Host "检查SQL Server..." -ForegroundColor Yellow
try {
    $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
    if ($sqlService -and $sqlService.Status -eq "Running") {
        Write-Host "SQL Server正在运行" -ForegroundColor Green
    } else {
        Write-Host "启动SQL Server..." -ForegroundColor Yellow
        Start-Service -Name "MSSQLSERVER"
        Start-Sleep -Seconds 3
    }
} catch {
    Write-Host "SQL Server检查完成" -ForegroundColor Yellow
}

# 步骤4：构建完整项目
Write-Host "构建完整项目..." -ForegroundColor Yellow

# 初始化Go模块
go mod init wosm-backend 2>$null

# 构建项目
go build -o wosm-complete.exe wosm-complete-main.go

if (Test-Path "wosm-complete.exe") {
    Write-Host "构建成功!" -ForegroundColor Green

    # 创建启动脚本
    @"
@echo off
title WOSM Complete Backend
echo ================================
echo   WOSM Complete Backend Service
echo ================================
echo.
echo Starting service...
echo Default account: admin / admin123
echo Access URL: http://localhost:8080
echo Health check: http://localhost:8080/health
echo.
wosm-complete.exe
pause
"@ | Out-File -FilePath "start-wosm.bat" -Encoding ASCII

    # 创建测试脚本
    @"
Write-Host "=== WOSM API Test ===" -ForegroundColor Green

# Test health check
try {
    `$health = Invoke-RestMethod -Uri "http://localhost:8080/health"
    Write-Host "Health check: OK" -ForegroundColor Green
    Write-Host "Service: `$(`$health.service)" -ForegroundColor Gray
} catch {
    Write-Host "Health check failed: `$_" -ForegroundColor Red
    exit 1
}

# Test login
try {
    `$loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json

    `$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body `$loginBody -ContentType "application/json"

    if (`$loginResponse.code -eq 200) {
        Write-Host "Login: OK" -ForegroundColor Green
        `$token = `$loginResponse.data.token

        # Test user info
        `$headers = @{ Authorization = "Bearer `$token" }
        `$userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers `$headers
        Write-Host "User info: OK" -ForegroundColor Green
        Write-Host "User: `$(`$userInfo.data.user.userName)" -ForegroundColor Gray

        # Test user list
        `$userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers `$headers
        Write-Host "User list: OK" -ForegroundColor Green
        Write-Host "Total users: `$(`$userList.data.total)" -ForegroundColor Gray

        Write-Host "`nAll tests passed!" -ForegroundColor Green
    } else {
        Write-Host "Login failed: `$(`$loginResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "API test failed: `$_" -ForegroundColor Red
}
"@ | Out-File -FilePath "test-wosm-api.ps1" -Encoding UTF8

    Write-Host "`n=== 完成! ===" -ForegroundColor Green
    Write-Host "生成的文件:" -ForegroundColor Yellow
    Write-Host "  wosm-complete.exe - 完整功能可执行文件" -ForegroundColor White
    Write-Host "  start-wosm.bat - 启动脚本" -ForegroundColor White
    Write-Host "  test-wosm-api.ps1 - API测试脚本" -ForegroundColor White
    Write-Host "`n启动方式:" -ForegroundColor Yellow
    Write-Host "  双击: start-wosm.bat" -ForegroundColor White
    Write-Host "  命令: .\wosm-complete.exe" -ForegroundColor White
    Write-Host "`n默认账号: admin / admin123" -ForegroundColor Cyan
    Write-Host "访问地址: http://localhost:8080" -ForegroundColor Cyan

} else {
    Write-Host "构建失败!" -ForegroundColor Red
    Write-Host "请检查Go环境和依赖" -ForegroundColor Yellow
}
