@echo off

REM 设置变量
set APP_NAME=ruoyi-go
set GO_FLAGS=-ldflags="-s -w"
set DIST_DIR=dist

REM 创建输出目录
echo 创建输出目录...
if not exist %DIST_DIR% mkdir %DIST_DIR%

REM 设置GOOS和GOARCH
echo 设置构建环境...
set GOOS=windows
set GOARCH=amd64

REM 显示当前版本
echo 当前Go版本:
go version

REM 整理和下载依赖
echo 整理和下载依赖...
go mod tidy
go mod download

REM 运行静态代码检查
echo 运行静态代码检查...
go vet ./...

REM 运行单元测试
echo 运行单元测试...
go test ./...

REM 构建应用
echo 构建应用...
go build %GO_FLAGS% -o %DIST_DIR%/%APP_NAME%.exe main.go

REM 复制配置文件和静态资源
echo 复制配置文件和静态资源...
xcopy /E /I /Y configs %DIST_DIR%\configs
if not exist %DIST_DIR%\logs mkdir %DIST_DIR%\logs
if not exist %DIST_DIR%\upload mkdir %DIST_DIR%\upload
if not exist %DIST_DIR%\static mkdir %DIST_DIR%\static

echo 构建完成! 可执行文件位于 %DIST_DIR%/%APP_NAME%.exe 