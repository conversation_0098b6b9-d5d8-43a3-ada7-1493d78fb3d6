#!/bin/bash

# 设置变量
APP_NAME="ruoyi-go"
GO_FLAGS="-ldflags=\"-s -w\""
DIST_DIR="dist"

# 创建输出目录
echo "创建输出目录..."
mkdir -p ${DIST_DIR}

# 设置GOOS和GOARCH
echo "设置构建环境..."
export GOOS=linux
export GOARCH=amd64

# 显示当前版本
echo "当前Go版本:"
go version

# 整理和下载依赖
echo "整理和下载依赖..."
go mod tidy
go mod download

# 运行静态代码检查
echo "运行静态代码检查..."
go vet ./...

# 运行单元测试
echo "运行单元测试..."
go test ./...

# 构建应用
echo "构建应用..."
go build ${GO_FLAGS} -o ${DIST_DIR}/${APP_NAME} main.go

# 添加执行权限
chmod +x ${DIST_DIR}/${APP_NAME}

# 复制配置文件和静态资源
echo "复制配置文件和静态资源..."
cp -r configs ${DIST_DIR}/
mkdir -p ${DIST_DIR}/logs
mkdir -p ${DIST_DIR}/upload
mkdir -p ${DIST_DIR}/static

echo "构建完成! 可执行文件位于 ${DIST_DIR}/${APP_NAME}"