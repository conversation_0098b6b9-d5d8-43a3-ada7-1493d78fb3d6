# 应用配置
app:
  name: "WOSM-Production"
  version: "1.0.0"
  description: "WOSM管理系统生产版本"
  env: "${APP_ENV:prod}"
  base_path: "${APP_BASE_PATH:./}"
  data_path: "${APP_DATA_PATH:./data}"
  log_path: "${APP_LOG_PATH:./logs}"
  temp_path: "${APP_TEMP_PATH:./temp}"
  date_format: "yyyy-MM-dd"
  time_format: "yyyy-MM-dd HH:mm:ss"
  json_mapping:
    time_in_seconds: false
    ignore_null: true
    property_naming_strategy: "snake_case"
    time_zone: "GMT+8"

# 服务器配置
server:
  host: "${SERVER_HOST:0.0.0.0}"
  port: ${SERVER_PORT:8080}
  context_path: ""
  read_timeout: ${SERVER_READ_TIMEOUT:60}
  write_timeout: ${SERVER_WRITE_TIMEOUT:60}
  idle_timeout: ${SERVER_IDLE_TIMEOUT:60}
  max_header_bytes: 1048576
  enable_http2: true

# 日志配置
log:
  level: "debug"
  format: "console"
  output: "both"
  file_path: "logs/ruoyi.log"
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: false
  caller: true

# 数据库配置
db:
  type: "${DB_TYPE:sqlserver}"
  host: "${DB_HOST}"
  port: ${DB_PORT:1433}
  database: "${DB_NAME}"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  charset: "${DB_CHARSET:utf8mb4}"
  max_idle_conns: ${DB_MAX_IDLE_CONNS:10}
  max_open_conns: ${DB_MAX_OPEN_CONNS:100}
  conn_max_lifetime: ${DB_CONN_MAX_LIFETIME:3600}
  log_mode: true
  slow_threshold: 200
  table_prefix: ""
  ssl_mode: "${DB_SSL_MODE:require}"

# Redis配置
redis:
  host: "${REDIS_HOST}"
  port: ${REDIS_PORT:6379}
  password: "${REDIS_PASSWORD}"
  db: ${REDIS_DB:0}
  pool_size: ${REDIS_POOL_SIZE:100}
  min_idle_conns: ${REDIS_MIN_IDLE_CONNS:10}
  idle_timeout: ${REDIS_IDLE_TIMEOUT:300}
  dial_timeout: 5
  read_timeout: 3
  write_timeout: 3
  enable_cluster: false
  cluster_nodes: []

# JWT配置
jwt:
  secret: "${JWT_SECRET}"
  expire: ${JWT_EXPIRE:86400}
  issuer: "${JWT_ISSUER:wosm-prod}"
  header: "${JWT_HEADER:Authorization}"
  token_prefix: "${JWT_TOKEN_PREFIX:Bearer }"
  refresh_expire: ${JWT_REFRESH_EXPIRE:604800}

# 验证码配置
captcha:
  type: "math"
  width: 160
  height: 60
  length: 4
  expire: 300
  digit_count: 1
  noise_count: 2
  show_line: true
  dpi: 80
  background_color: "rgba(255,255,255,1)"
  font_color: "rgba(0,0,0,1)"
  font_size: 36
  font_family: "Arial"
  cache_type: "memory"

# 过滤器配置
filter:
  xss_enabled: true
  xss_excludes: ["/system/notice/*"]
  csrf_enabled: true
  csrf_excludes: ["/login", "/captcha"]
  cors_enabled: true
  cors_allowed_origins: ["*"]
  cors_allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  cors_allowed_headers: ["*"]
  cors_allow_credentials: true
  cors_max_age: 1800

# 国际化配置
i18n:
  default_locale: "zh_CN"
  supported_locales: "zh_CN,en_US"
  locale_param: "lang"
  cookie_name: "locale"
  cookie_max_age: 30
  basename_list: "i18n/messages"
  use_cookie: true
  use_session: false
  session_name: "locale"
  use_accept_language_header: true

# GORM配置
gorm:
  log_level: "info"
  slow_threshold: 200
  colorful: false
  ignore_record_not_found_error: true
  disable_foreign_key_constraint: true
  singular_table: true
  skip_default_transaction: true
  prepare_stmt: true

# 资源配置
resources:
  static_locations: "classpath:/static/,classpath:/public/,file:./static/"
  static_path_pattern: "/**"
  add_default_resource_handlers: true
  favor_path_extension: true
  favor_parameter: true
  parameter_name: "format"
  ignore_accept_header: false
  default_content_type: "application/json"
  media_types: "json=application/json,xml=application/xml"
  cors_enabled: true
  cors_allowed_origins: "*"
  cors_allowed_methods: "GET,POST,PUT,DELETE,OPTIONS"
  cors_allowed_headers: "*"
  cors_allow_credentials: true
  cors_max_age: 1800

# 安全配置
security:
  authentication_type: "jwt"
  token_validity_seconds: 86400
  remember_me_seconds: 604800
  login_url: "/login"
  logout_url: "/logout"
  unauthorized_url: "/unauthorized"
  permit_all: ["/login", "/logout", "/captcha", "/unauthorized", "/static/**", "/favicon.ico"]
  anonymous: ["/api/v1/public/**"]
  authenticated: ["/api/v1/**"]
  admin_only: ["/api/v1/admin/**"]
  password_encoder: "bcrypt"
  password_strength: ${SECURITY_PASSWORD_STRENGTH:12}
  max_sessions: ${SECURITY_MAX_SESSIONS:1}
  prevent_login: ${SECURITY_PREVENT_LOGIN:true}

# 线程池配置
thread_pool:
  core_pool_size: 10
  max_pool_size: 100
  queue_capacity: 1000
  keep_alive_seconds: 60
  thread_name_prefix: "ruoyi-async-"
  wait_for_tasks_to_complete: true
  allow_core_thread_timeout: true
  rejection_policy: "caller_runs"

# TLS配置
tls:
  enable: false
  cert_file: "cert.pem"
  key_file: "key.pem"
  min_version: "TLS1.2"
  max_version: "TLS1.3"
  cipher_suites: []
  prefer_server_cipher_suites: true
  curve_preferences: []
  client_auth: "NoClientCert"
  client_cas: []
  enable_http2: true