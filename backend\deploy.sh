#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================${NC}"
echo -e "${BLUE}      RuoYi-Go 部署脚本            ${NC}"
echo -e "${BLUE}====================================${NC}"

# 检查参数
ENV=${1:-dev}
if [[ $ENV != "dev" && $ENV != "test" && $ENV != "prod" ]]; then
    echo -e "${RED}错误: 环境参数无效。请使用 dev, test 或 prod${NC}"
    exit 1
fi

echo -e "${YELLOW}部署环境: ${ENV}${NC}"

# 设置变量
OUTPUT_DIR="build"
BINARY_NAME="ruoyi-go"
CONFIG_DIR="configs"
GO_ENV="GO111MODULE=on CGO_ENABLED=0"

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go。请确保Go已安装并添加到PATH中。${NC}"
    exit 1
fi

# 显示Go版本
echo -e "${YELLOW}使用的Go版本:${NC}"
go version

# 清理旧的构建
echo -e "${YELLOW}清理旧的构建...${NC}"
if [ -d "$OUTPUT_DIR" ]; then
    rm -rf "$OUTPUT_DIR"
fi
mkdir -p "$OUTPUT_DIR"

# 下载依赖
echo -e "${YELLOW}下载依赖...${NC}"
go mod tidy

# 构建应用
echo -e "${YELLOW}构建应用...${NC}"
GOOS=linux GOARCH=amd64 $GO_ENV go build -o "$OUTPUT_DIR/$BINARY_NAME" ./cmd/main.go

# 检查构建结果
if [ $? -ne 0 ]; then
    echo -e "${RED}构建失败!${NC}"
    exit 1
fi

# 复制配置文件
echo -e "${YELLOW}复制配置文件...${NC}"
cp -r "$CONFIG_DIR" "$OUTPUT_DIR/"

# 根据环境修改配置
if [ -f "$OUTPUT_DIR/$CONFIG_DIR/config.$ENV.yaml" ]; then
    echo -e "${YELLOW}使用 $ENV 环境配置...${NC}"
    cp "$OUTPUT_DIR/$CONFIG_DIR/config.$ENV.yaml" "$OUTPUT_DIR/$CONFIG_DIR/config.yaml"
fi

# 创建日志和上传目录
mkdir -p "$OUTPUT_DIR/logs"
mkdir -p "$OUTPUT_DIR/uploads"

# 创建启动脚本
cat > "$OUTPUT_DIR/start.sh" << EOF
#!/bin/bash
nohup ./$BINARY_NAME > ./logs/startup.log 2>&1 &
echo \$! > ./ruoyi.pid
echo "RuoYi-Go 服务已启动! PID: \$(cat ./ruoyi.pid)"
EOF

# 创建停止脚本
cat > "$OUTPUT_DIR/stop.sh" << EOF
#!/bin/bash
if [ -f ./ruoyi.pid ]; then
    PID=\$(cat ./ruoyi.pid)
    if ps -p \$PID > /dev/null; then
        kill \$PID
        echo "RuoYi-Go 服务已停止! PID: \$PID"
    else
        echo "RuoYi-Go 服务未运行"
    fi
    rm ./ruoyi.pid
else
    echo "PID文件不存在"
fi
EOF

# 设置可执行权限
chmod +x "$OUTPUT_DIR/$BINARY_NAME"
chmod +x "$OUTPUT_DIR/start.sh"
chmod +x "$OUTPUT_DIR/stop.sh"

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}构建成功!${NC}"
echo -e "${GREEN}可执行文件位于: $OUTPUT_DIR/$BINARY_NAME${NC}"
echo -e "${GREEN}使用 $OUTPUT_DIR/start.sh 启动服务${NC}"
echo -e "${GREEN}使用 $OUTPUT_DIR/stop.sh 停止服务${NC}"
echo -e "${GREEN}====================================${NC}"