version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ruoyi-go
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
      - ./upload:/app/upload
      - ./static:/app/static
    depends_on:
      - redis
      - db
    networks:
      - ruoyi-net
    environment:
      - TZ=Asia/Shanghai

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - ruoyi-net

  # SQL Server服务
  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    container_name: ruoyi-sqlserver
    restart: always
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=F@2233
      - MSSQL_PID=Express
    networks:
      - ruoyi-net

# 网络配置
networks:
  ruoyi-net:
    driver: bridge

# 卷配置
volumes:
  redis-data:
    driver: local
  sqlserver-data:
    driver: local 