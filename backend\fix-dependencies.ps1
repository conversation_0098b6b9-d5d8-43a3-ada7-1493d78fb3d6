# Fix Dependencies Script
Write-Host "=== Fixing Project Dependencies ===" -ForegroundColor Green

# Install missing dependencies
Write-Host "Installing missing dependencies..." -ForegroundColor Yellow

$dependencies = @(
    "github.com/BurntSushi/toml",
    "github.com/gin-contrib/cors",
    "github.com/gin-contrib/gzip", 
    "github.com/gin-contrib/pprof",
    "github.com/nicksnyder/go-i18n/v2/i18n",
    "github.com/robfig/cron/v3",
    "github.com/golang-jwt/jwt/v4",
    "gorm.io/driver/mysql",
    "gorm.io/driver/postgres", 
    "gorm.io/driver/sqlite"
)

foreach ($dep in $dependencies) {
    Write-Host "Installing $dep..." -ForegroundColor Gray
    go get $dep
}

# Clean up and tidy
Write-Host "Cleaning up dependencies..." -ForegroundColor Yellow
go mod tidy

Write-Host "Dependencies fixed!" -ForegroundColor Green

# Try to build again
Write-Host "Attempting to build project..." -ForegroundColor Yellow
go build -o wosm-backend.exe cmd\main.go

if (Test-Path "wosm-backend.exe") {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "You can now run: .\wosm-backend.exe" -ForegroundColor Cyan
} else {
    Write-Host "Build still has issues. Let's check the errors..." -ForegroundColor Red
}

pause
