package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// BaseController Web层通用数据处理
type BaseController struct {
	Logger *zap.Logger
}

// StartPage 设置请求分页数据
func (c *BaseController) StartPage(ctx *gin.Context) {
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// 存储到上下文中，后续查询使用
	ctx.Set("pageNum", pageNum)
	ctx.Set("pageSize", pageSize)
	ctx.Set("orderByColumn", ctx.DefaultQuery("orderByColumn", ""))
	ctx.Set("isAsc", ctx.DefaultQuery("isAsc", "asc"))
}

// GetDataTable 响应请求分页数据
func (c *BaseController) GetDataTable(ctx *gin.Context, list interface{}, total int64) *model.TableDataInfo {
	rspData := &model.TableDataInfo{
		Code:  http.StatusOK,
		Msg:   "查询成功",
		Rows:  list,
		Total: total,
	}
	return rspData
}

// Success 返回成功
func (c *BaseController) Success(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusOK,
		Msg:  "操作成功",
	})
}

// SuccessWithMessage 返回成功消息
func (c *BaseController) SuccessWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusOK,
		Msg:  message,
	})
}

// SuccessWithData 返回成功数据
func (c *BaseController) SuccessWithData(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusOK,
		Msg:  "操作成功",
		Data: data,
	})
}

// Error 返回失败
func (c *BaseController) Error(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusInternalServerError,
		Msg:  "操作失败",
	})
}

// ErrorWithMessage 返回失败消息
func (c *BaseController) ErrorWithMessage(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusInternalServerError,
		Msg:  message,
	})
}

// Warn 返回警告消息
func (c *BaseController) Warn(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, model.AjaxResult{
		Code: http.StatusBadRequest,
		Msg:  message,
	})
}

// ToAjax 响应返回结果
func (c *BaseController) ToAjax(ctx *gin.Context, rows int64) {
	if rows > 0 {
		c.Success(ctx)
	} else {
		c.Error(ctx)
	}
}

// ToAjaxWithBool 响应返回结果
func (c *BaseController) ToAjaxWithBool(ctx *gin.Context, result bool) {
	if result {
		c.Success(ctx)
	} else {
		c.Error(ctx)
	}
}

// GetUserId 获取当前登录用户ID
func (c *BaseController) GetUserId(ctx *gin.Context) int64 {
	// 从上下文中获取用户ID，实际实现可能需要从JWT或Session中获取
	userId, exists := ctx.Get("userId")
	if !exists {
		return 0
	}

	if id, ok := userId.(int64); ok {
		return id
	}
	return 0
}

// GetUsername 获取当前登录用户名
func (c *BaseController) GetUsername(ctx *gin.Context) string {
	// 从上下文中获取用户名，实际实现可能需要从JWT或Session中获取
	username, exists := ctx.Get("username")
	if !exists {
		return ""
	}

	if name, ok := username.(string); ok {
		return name
	}
	return ""
}

// GetDeptId 获取当前登录用户部门ID
func (c *BaseController) GetDeptId(ctx *gin.Context) int64 {
	// 从上下文中获取部门ID，实际实现可能需要从JWT或Session中获取
	deptId, exists := ctx.Get("deptId")
	if !exists {
		return 0
	}

	if id, ok := deptId.(int64); ok {
		return id
	}
	return 0
}

// BindQuery 绑定查询参数到结构体
func (c *BaseController) BindQuery(ctx *gin.Context, obj interface{}) error {
	binder := NewParamBinder(ctx)
	return binder.BindQuery(obj)
}

// BindJSON 绑定JSON参数到结构体
func (c *BaseController) BindJSON(ctx *gin.Context, obj interface{}) error {
	binder := NewParamBinder(ctx)
	return binder.BindJSON(obj)
}

// BindForm 绑定表单参数到结构体
func (c *BaseController) BindForm(ctx *gin.Context, obj interface{}) error {
	binder := NewParamBinder(ctx)
	return binder.BindForm(obj)
}

// BindJSONParam 绑定JSON字符串参数到结构体
func (c *BaseController) BindJSONParam(ctx *gin.Context, paramName string, obj interface{}) error {
	binder := NewParamBinder(ctx)
	return binder.BindJSONParam(paramName, obj)
}

// GetDeptIdByName 根据部门名称获取部门ID
func (c *BaseController) GetDeptIdByName(ctx *gin.Context, deptName string, deptService interface{}) int64 {
	// 检查deptService是否实现了根据名称查询部门的方法
	if service, ok := deptService.(interface{ SelectDeptByName(string) interface{} }); ok {
		dept := service.SelectDeptByName(deptName)

		// 尝试从返回的部门对象中获取ID
		if deptObj, ok := dept.(interface{ GetDeptId() int64 }); ok {
			return deptObj.GetDeptId()
		}
	}

	return 0
}
