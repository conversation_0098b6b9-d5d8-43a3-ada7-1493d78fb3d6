package controller

import (
	"encoding/json"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ParamBinder 参数绑定工具
type ParamBinder struct {
	Ctx *gin.Context
}

// NewParamBinder 创建参数绑定工具
func NewParamBinder(ctx *gin.Context) *ParamBinder {
	return &ParamBinder{
		Ctx: ctx,
	}
}

// BindQuery 绑定查询参数到结构体
func (b *ParamBinder) BindQuery(obj interface{}) error {
	// 获取结构体类型和值
	objType := reflect.TypeOf(obj)
	objValue := reflect.ValueOf(obj)

	// 如果是指针，获取其指向的元素
	if objType.Kind() == reflect.Ptr {
		objType = objType.Elem()
		objValue = objValue.Elem()
	}

	// 遍历结构体字段
	for i := 0; i < objType.NumField(); i++ {
		field := objType.Field(i)
		fieldValue := objValue.Field(i)

		// 如果字段不可设置，跳过
		if !fieldValue.CanSet() {
			continue
		}

		// 获取字段标签
		tagValue := field.Tag.Get("json")
		if tagValue == "" || tagValue == "-" {
			tagValue = field.Name
		} else {
			// 处理json标签中的逗号
			tagValue = strings.Split(tagValue, ",")[0]
		}

		// 获取查询参数值
		paramValue := b.Ctx.Query(tagValue)
		if paramValue == "" {
			continue
		}

		// 根据字段类型设置值
		switch fieldValue.Kind() {
		case reflect.String:
			fieldValue.SetString(paramValue)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			intValue, err := strconv.ParseInt(paramValue, 10, 64)
			if err == nil {
				fieldValue.SetInt(intValue)
			}
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			uintValue, err := strconv.ParseUint(paramValue, 10, 64)
			if err == nil {
				fieldValue.SetUint(uintValue)
			}
		case reflect.Float32, reflect.Float64:
			floatValue, err := strconv.ParseFloat(paramValue, 64)
			if err == nil {
				fieldValue.SetFloat(floatValue)
			}
		case reflect.Bool:
			boolValue, err := strconv.ParseBool(paramValue)
			if err == nil {
				fieldValue.SetBool(boolValue)
			}
		case reflect.Struct:
			// 处理特殊结构体，如时间
			if fieldValue.Type().String() == "time.Time" {
				timeValue, err := time.Parse("2006-01-02 15:04:05", paramValue)
				if err == nil {
					fieldValue.Set(reflect.ValueOf(timeValue))
				}
			}
		case reflect.Map:
			// 处理Map类型
			if fieldValue.Type().Key().Kind() == reflect.String {
				// 如果是map[string]interface{}类型，检查是否已初始化
				if fieldValue.IsNil() {
					fieldValue.Set(reflect.MakeMap(fieldValue.Type()))
				}

				// 处理日期范围参数
				if tagValue == "params" {
					// 获取日期范围
					beginTime := b.Ctx.Query("beginTime")
					endTime := b.Ctx.Query("endTime")

					if beginTime != "" && endTime != "" {
						params := make(map[string]interface{})
						params["beginTime"] = beginTime
						params["endTime"] = endTime
						fieldValue.Set(reflect.ValueOf(params))
					}
				}
			}
		case reflect.Slice:
			// 处理切片类型
			if fieldValue.Type().Elem().Kind() == reflect.Int64 {
				// 处理int64切片
				values := strings.Split(paramValue, ",")
				slice := reflect.MakeSlice(fieldValue.Type(), 0, len(values))

				for _, v := range values {
					intValue, err := strconv.ParseInt(v, 10, 64)
					if err == nil {
						slice = reflect.Append(slice, reflect.ValueOf(intValue))
					}
				}

				fieldValue.Set(slice)
			} else if fieldValue.Type().Elem().Kind() == reflect.String {
				// 处理string切片
				values := strings.Split(paramValue, ",")
				slice := reflect.MakeSlice(fieldValue.Type(), 0, len(values))

				for _, v := range values {
					slice = reflect.Append(slice, reflect.ValueOf(v))
				}

				fieldValue.Set(slice)
			}
		}
	}

	return nil
}

// BindJSON 绑定JSON参数到结构体
func (b *ParamBinder) BindJSON(obj interface{}) error {
	return b.Ctx.ShouldBindJSON(obj)
}

// BindForm 绑定表单参数到结构体
func (b *ParamBinder) BindForm(obj interface{}) error {
	// 获取结构体类型和值
	objType := reflect.TypeOf(obj)
	objValue := reflect.ValueOf(obj)

	// 如果是指针，获取其指向的元素
	if objType.Kind() == reflect.Ptr {
		objType = objType.Elem()
		objValue = objValue.Elem()
	}

	// 遍历结构体字段
	for i := 0; i < objType.NumField(); i++ {
		field := objType.Field(i)
		fieldValue := objValue.Field(i)

		// 如果字段不可设置，跳过
		if !fieldValue.CanSet() {
			continue
		}

		// 获取字段标签
		tagValue := field.Tag.Get("form")
		if tagValue == "" {
			tagValue = field.Tag.Get("json")
		}
		if tagValue == "" || tagValue == "-" {
			tagValue = field.Name
		} else {
			// 处理标签中的逗号
			tagValue = strings.Split(tagValue, ",")[0]
		}

		// 获取表单参数值
		paramValue := b.Ctx.PostForm(tagValue)
		if paramValue == "" {
			continue
		}

		// 根据字段类型设置值
		switch fieldValue.Kind() {
		case reflect.String:
			fieldValue.SetString(paramValue)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			intValue, err := strconv.ParseInt(paramValue, 10, 64)
			if err == nil {
				fieldValue.SetInt(intValue)
			}
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			uintValue, err := strconv.ParseUint(paramValue, 10, 64)
			if err == nil {
				fieldValue.SetUint(uintValue)
			}
		case reflect.Float32, reflect.Float64:
			floatValue, err := strconv.ParseFloat(paramValue, 64)
			if err == nil {
				fieldValue.SetFloat(floatValue)
			}
		case reflect.Bool:
			boolValue, err := strconv.ParseBool(paramValue)
			if err == nil {
				fieldValue.SetBool(boolValue)
			}
		case reflect.Struct:
			// 处理特殊结构体，如时间
			if fieldValue.Type().String() == "time.Time" {
				timeValue, err := time.Parse("2006-01-02 15:04:05", paramValue)
				if err == nil {
					fieldValue.Set(reflect.ValueOf(timeValue))
				}
			}
		}
	}

	return nil
}

// BindJSONParam 绑定JSON字符串参数到结构体
func (b *ParamBinder) BindJSONParam(paramName string, obj interface{}) error {
	jsonStr := b.Ctx.Query(paramName)
	if jsonStr == "" {
		jsonStr = b.Ctx.PostForm(paramName)
	}

	if jsonStr == "" {
		return nil
	}

	return json.Unmarshal([]byte(jsonStr), obj)
}
