package auth

import (
	"sync"
)

// Authentication 认证信息接口
type Authentication interface {
	// GetPrincipal 获取主体
	GetPrincipal() interface{}

	// GetCredentials 获取凭证
	GetCredentials() interface{}

	// IsAuthenticated 是否已认证
	IsAuthenticated() bool

	// SetAuthenticated 设置认证状态
	SetAuthenticated(authenticated bool)
}

// 认证信息实现
type authenticationImpl struct {
	principal     interface{}
	credentials   interface{}
	authenticated bool
}

// GetPrincipal 获取主体
func (a *authenticationImpl) GetPrincipal() interface{} {
	return a.principal
}

// GetCredentials 获取凭证
func (a *authenticationImpl) GetCredentials() interface{} {
	return a.credentials
}

// IsAuthenticated 是否已认证
func (a *authenticationImpl) IsAuthenticated() bool {
	return a.authenticated
}

// SetAuthenticated 设置认证状态
func (a *authenticationImpl) SetAuthenticated(authenticated bool) {
	a.authenticated = authenticated
}

// NewAuthentication 创建认证信息
func NewAuthentication(principal, credentials interface{}, authenticated bool) Authentication {
	return &authenticationImpl{
		principal:     principal,
		credentials:   credentials,
		authenticated: authenticated,
	}
}

// AuthenticationContextHolder 认证上下文持有者
type authContextHolder struct {
	contextHolder sync.Map
}

var (
	authenticationContextHolder = &authContextHolder{}
)

// GetContext 获取认证上下文
func (h *authContextHolder) GetContext(key string) Authentication {
	if value, ok := h.contextHolder.Load(key); ok {
		if auth, ok := value.(Authentication); ok {
			return auth
		}
	}
	return nil
}

// SetContext 设置认证上下文
func (h *authContextHolder) SetContext(key string, auth Authentication) {
	if key != "" && auth != nil {
		h.contextHolder.Store(key, auth)
	}
}

// ClearContext 清除认证上下文
func (h *authContextHolder) ClearContext(key string) {
	if key != "" {
		h.contextHolder.Delete(key)
	}
}

// GetAuthenticationContext 获取认证上下文
func GetAuthenticationContext(key string) Authentication {
	return authenticationContextHolder.GetContext(key)
}

// SetAuthenticationContext 设置认证上下文
func SetAuthenticationContext(key string, auth Authentication) {
	authenticationContextHolder.SetContext(key, auth)
}

// ClearAuthenticationContext 清除认证上下文
func ClearAuthenticationContext(key string) {
	authenticationContextHolder.ClearContext(key)
}

// PermissionContextHolder 权限上下文持有者
type permContextHolder struct {
	contextHolder sync.Map
}

var (
	permissionContextHolder = &permContextHolder{}
)

// GetContext 获取权限上下文
func (h *permContextHolder) GetContext(key string) string {
	if value, ok := h.contextHolder.Load(key); ok {
		if permission, ok := value.(string); ok {
			return permission
		}
	}
	return ""
}

// SetContext 设置权限上下文
func (h *permContextHolder) SetContext(key string, permission string) {
	if key != "" && permission != "" {
		h.contextHolder.Store(key, permission)
	}
}

// ClearContext 清除权限上下文
func (h *permContextHolder) ClearContext(key string) {
	if key != "" {
		h.contextHolder.Delete(key)
	}
}

// GetPermissionContext 获取权限上下文
func GetPermissionContext(key string) string {
	return permissionContextHolder.GetContext(key)
}

// SetPermissionContext 设置权限上下文
func SetPermissionContext(key string, permission string) {
	permissionContextHolder.SetContext(key, permission)
}

// ClearPermissionContext 清除权限上下文
func ClearPermissionContext(key string) {
	permissionContextHolder.ClearContext(key)
}
