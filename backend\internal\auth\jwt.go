package auth

import (
	"errors"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
)

const (
	// TokenPrefix Token前缀
	TokenPrefix = "Bearer "

	// TokenHeader Token请求头
	TokenHeader = "Authorization"
)

// 全局JWT认证工具
var jwtAuth *JWTAuth

// InitJWT 初始化JWT认证工具
func InitJWT(jwtConfig *config.JWTConfig) error {
	if jwtConfig == nil {
		return errors.New("JWT配置为空")
	}
	jwtAuth = NewJWTAuth(jwtConfig)
	return nil
}

// GetJWTAuth 获取JWT认证工具
func GetJWTAuth() *JWTAuth {
	return jwtAuth
}

// JWTAuth JWT认证工具
type JWTAuth struct {
	jwtConfig *config.JWTConfig
}

// NewJWTAuth 创建JWT认证工具
func NewJWTAuth(jwtConfig *config.JWTConfig) *JWTAuth {
	return &JWTAuth{
		jwtConfig: jwtConfig,
	}
}

// CreateToken 创建Token
func (j *JWTAuth) CreateToken(claims *model.Claims) (string, error) {
	token := jwt.NewWithClaims(jwt.GetSigningMethod(j.jwtConfig.SigningMethod()), claims)
	return token.SignedString([]byte(j.jwtConfig.Secret))
}

// ParseToken 解析Token
func (j *JWTAuth) ParseToken(tokenString string) (*model.Claims, error) {
	// 去除Token前缀
	if len(tokenString) > len(TokenPrefix) && tokenString[:len(TokenPrefix)] == TokenPrefix {
		tokenString = tokenString[len(TokenPrefix):]
	}

	// 解析Token
	token, err := jwt.ParseWithClaims(tokenString, &model.Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(j.jwtConfig.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	// 验证Token
	if claims, ok := token.Claims.(*model.Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// RefreshToken 刷新Token
func (j *JWTAuth) RefreshToken(claims *model.Claims) (string, error) {
	// 更新过期时间
	now := time.Now()
	expireTime := now.Add(j.jwtConfig.GetExpireTime())

	claims.ExpirationTime = expireTime.Unix()
	claims.StandardClaims.ExpiresAt = expireTime.Unix()
	claims.StandardClaims.NotBefore = now.Unix()
	claims.StandardClaims.IssuedAt = now.Unix()

	return j.CreateToken(claims)
}

// ValidateToken 验证Token
func (j *JWTAuth) ValidateToken(tokenString string) bool {
	_, err := j.ParseToken(tokenString)
	return err == nil
}

// GetClaimsFromToken 从Token中获取Claims
func (j *JWTAuth) GetClaimsFromToken(tokenString string) (*model.Claims, error) {
	return j.ParseToken(tokenString)
}

// GetUserIdFromToken 从Token中获取用户ID
func (j *JWTAuth) GetUserIdFromToken(tokenString string) (int64, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserId, nil
}

// GetUsernameFromToken 从Token中获取用户名
func (j *JWTAuth) GetUsernameFromToken(tokenString string) (string, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Username, nil
}
