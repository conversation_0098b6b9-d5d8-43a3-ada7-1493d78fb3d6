package auth

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// PermissionChecker 权限检查器
type PermissionChecker struct {
	logger       *zap.Logger
	tokenManager *TokenManager
}

// NewPermissionChecker 创建权限检查器
func NewPermissionChecker(logger *zap.Logger, tokenManager *TokenManager) *PermissionChecker {
	return &PermissionChecker{
		logger:       logger,
		tokenManager: tokenManager,
	}
}

// HasPermi 判断用户是否具备某权限
func (p *PermissionChecker) HasPermi(ctx *gin.Context, permission string) bool {
	if permission == "" {
		return false
	}

	// 获取当前用户
	loginUser := p.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if p.IsAdmin(loginUser) {
		return true
	}

	// 判断是否具有权限
	permissions := loginUser.GetPermissions()
	for _, perm := range permissions {
		if perm == permission {
			return true
		}
	}

	return false
}

// HasAnyPermi 判断用户是否具有任意一个权限
func (p *PermissionChecker) HasAnyPermi(ctx *gin.Context, permissions ...string) bool {
	if len(permissions) == 0 {
		return false
	}

	// 获取当前用户
	loginUser := p.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if p.IsAdmin(loginUser) {
		return true
	}

	// 判断是否具有任意一个权限
	userPermissions := loginUser.GetPermissions()
	for _, reqPerm := range permissions {
		for _, userPerm := range userPermissions {
			if userPerm == reqPerm {
				return true
			}
		}
	}

	return false
}

// HasRole 判断用户是否具备某角色
func (p *PermissionChecker) HasRole(ctx *gin.Context, role string) bool {
	if role == "" {
		return false
	}

	// 获取当前用户
	loginUser := p.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if p.IsAdmin(loginUser) {
		return true
	}

	// 判断是否具有角色
	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return false
	}

	for _, userRole := range user.Roles {
		if userRole.RoleKey == role {
			return true
		}
	}

	return false
}

// HasAnyRole 判断用户是否具有任意一个角色
func (p *PermissionChecker) HasAnyRole(ctx *gin.Context, roles ...string) bool {
	if len(roles) == 0 {
		return false
	}

	// 获取当前用户
	loginUser := p.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if p.IsAdmin(loginUser) {
		return true
	}

	// 判断是否具有任意一个角色
	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return false
	}

	for _, reqRole := range roles {
		for _, userRole := range user.Roles {
			if userRole.RoleKey == reqRole {
				return true
			}
		}
	}

	return false
}

// IsAdmin 判断用户是否为管理员
func (p *PermissionChecker) IsAdmin(loginUser *model.LoginUser) bool {
	return loginUser != nil && p.IsAdminUser(loginUser.GetUserId())
}

// IsAdminUser 判断用户ID是否为管理员
func (p *PermissionChecker) IsAdminUser(userId int64) bool {
	return userId == 1
}

// SetPermissionContext 设置权限上下文
func (p *PermissionChecker) SetPermissionContext(ctx *gin.Context, permission string) {
	if ctx != nil && permission != "" {
		// 使用请求ID作为上下文键
		requestId := ctx.GetHeader("X-Request-Id")
		if requestId == "" {
			requestId = "default"
		}
		SetPermissionContext(requestId, permission)
	}
}

// GetPermissionContext 获取权限上下文
func (p *PermissionChecker) GetPermissionContext(ctx *gin.Context) string {
	if ctx != nil {
		// 使用请求ID作为上下文键
		requestId := ctx.GetHeader("X-Request-Id")
		if requestId == "" {
			requestId = "default"
		}
		return GetPermissionContext(requestId)
	}
	return ""
}

// ClearPermissionContext 清除权限上下文
func (p *PermissionChecker) ClearPermissionContext(ctx *gin.Context) {
	if ctx != nil {
		// 使用请求ID作为上下文键
		requestId := ctx.GetHeader("X-Request-Id")
		if requestId == "" {
			requestId = "default"
		}
		ClearPermissionContext(requestId)
	}
}
