package auth

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// RoleChecker 角色检查器
type RoleChecker struct {
	logger       *zap.Logger
	tokenManager *TokenManager
}

// NewRoleChecker 创建角色检查器
func NewRoleChecker(logger *zap.Logger, tokenManager *TokenManager) *RoleChecker {
	return &RoleChecker{
		logger:       logger,
		tokenManager: tokenManager,
	}
}

// IsRoleAllowed 判断角色是否允许访问
func (r *RoleChecker) IsRoleAllowed(ctx *gin.Context, requiredRoles ...string) bool {
	// 获取当前用户
	loginUser := r.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if r.<PERSON><PERSON>(loginUser) {
		return true
	}

	// 获取用户角色
	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return false
	}

	// 判断是否具有任意一个角色
	for _, reqRole := range requiredRoles {
		for _, userRole := range user.Roles {
			if userRole.RoleKey == reqRole {
				return true
			}
		}
	}

	return false
}

// IsAdmin 判断用户是否为管理员
func (r *RoleChecker) IsAdmin(loginUser *model.LoginUser) bool {
	return loginUser != nil && r.IsAdminUser(loginUser.GetUserId())
}

// IsAdminUser 判断用户ID是否为管理员
func (r *RoleChecker) IsAdminUser(userId int64) bool {
	return userId == 1
}

// HasDataScope 判断用户是否具有数据范围权限
func (r *RoleChecker) HasDataScope(ctx *gin.Context, deptId int64) bool {
	// 获取当前用户
	loginUser := r.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return false
	}

	// 判断是否为管理员
	if r.IsAdmin(loginUser) {
		return true
	}

	// 获取用户角色
	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return false
	}

	// 判断是否具有数据范围权限
	for _, role := range user.Roles {
		// 全部数据权限
		if role.DataScope == "1" {
			return true
		}
		// 自定义数据权限
		if role.DataScope == "2" {
			// TODO: 检查自定义数据权限
			return r.checkCustomDataScope(role, deptId)
		}
		// 部门数据权限
		if role.DataScope == "3" {
			return user.DeptId == deptId
		}
		// 部门及以下数据权限
		if role.DataScope == "4" {
			// TODO: 检查部门及以下数据权限
			return r.checkDeptAndChildDataScope(user.DeptId, deptId)
		}
		// 仅本人数据权限
		if role.DataScope == "5" {
			// 仅本人数据权限不适用于部门判断
			return false
		}
	}

	return false
}

// checkCustomDataScope 检查自定义数据权限
func (r *RoleChecker) checkCustomDataScope(role domain.SysRole, deptId int64) bool {
	// TODO: 实现自定义数据权限检查
	// 这里需要查询角色与部门的关联关系
	return false
}

// checkDeptAndChildDataScope 检查部门及以下数据权限
func (r *RoleChecker) checkDeptAndChildDataScope(userDeptId, targetDeptId int64) bool {
	// TODO: 实现部门及以下数据权限检查
	// 这里需要查询部门的层级关系
	return false
}

// GetRolePermission 获取角色权限标识
func (r *RoleChecker) GetRolePermission(ctx *gin.Context) []string {
	// 获取当前用户
	loginUser := r.tokenManager.GetLoginUser(ctx)
	if loginUser == nil {
		return nil
	}

	// 获取用户角色
	user := loginUser.GetUser()
	if user.Roles == nil || len(user.Roles) == 0 {
		return nil
	}

	// 获取角色权限标识
	var permissions []string
	for _, role := range user.Roles {
		permissions = append(permissions, "role:"+role.RoleKey)
	}

	return permissions
}
