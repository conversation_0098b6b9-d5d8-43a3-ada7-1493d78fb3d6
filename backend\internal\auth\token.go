package auth

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/utils"
	"go.uber.org/zap"
)

const (
	// TokenKey Token缓存key前缀
	TokenKey = "login_tokens:"
)

// TokenManager Token管理工具
type TokenManager struct {
	logger       *zap.Logger
	jwtConfig    *config.JWTConfig
	redisService redis.RedisService
	jwtAuth      *JWTAuth
}

// NewTokenManager 创建Token管理工具
func NewTokenManager(logger *zap.Logger, jwtConfig *config.JWTConfig, redisService redis.RedisService) *TokenManager {
	return &TokenManager{
		logger:       logger,
		jwtConfig:    jwtConfig,
		redisService: redisService,
		jwtAuth:      NewJWTAuth(jwtConfig),
	}
}

// GetLoginUser 获取登录用户
func (m *TokenManager) GetLoginUser(ctx *gin.Context) *model.LoginUser {
	// 获取请求携带的令牌
	token := m.GetToken(ctx)
	if token == "" {
		return nil
	}

	// 解析对应的权限信息
	claims, err := m.jwtAuth.ParseToken(token)
	if err != nil {
		m.logger.Error("解析Token失败", zap.Error(err))
		return nil
	}

	// 获取用户身份信息
	uuid := claims.UUID
	tokenKey := m.GetTokenKey(uuid)
	userJson, err := m.redisService.Get(tokenKey)
	if err != nil || userJson == "" {
		m.logger.Error("从Redis获取用户信息失败", zap.Error(err))
		return nil
	}

	// 反序列化用户信息
	var loginUser model.LoginUser
	err = json.Unmarshal([]byte(userJson), &loginUser)
	if err != nil {
		m.logger.Error("反序列化用户信息失败", zap.Error(err))
		return nil
	}

	return &loginUser
}

// SetLoginUser 设置登录用户
func (m *TokenManager) SetLoginUser(loginUser *model.LoginUser) {
	if loginUser != nil && loginUser.GetToken() != "" {
		tokenKey := m.GetTokenKey(loginUser.GetToken())
		expireTime := m.jwtConfig.GetExpireTime()

		// 序列化用户信息
		userJson, err := json.Marshal(loginUser)
		if err != nil {
			m.logger.Error("序列化用户信息失败", zap.Error(err))
			return
		}

		// 存入Redis
		m.redisService.Set(tokenKey, string(userJson), expireTime)
	}
}

// DelLoginUser 删除登录用户
func (m *TokenManager) DelLoginUser(token string) {
	if token == "" {
		return
	}

	claims, err := m.jwtAuth.ParseToken(token)
	if err != nil {
		m.logger.Error("解析Token失败", zap.Error(err))
		return
	}

	uuid := claims.UUID
	tokenKey := m.GetTokenKey(uuid)
	m.redisService.Del(tokenKey)
}

// CreateToken 创建Token
func (m *TokenManager) CreateToken(loginUser *model.LoginUser) (string, error) {
	// 生成UUID作为用户唯一标识
	uuid := utils.GenerateUUID()
	loginUser.SetToken(uuid)

	// 刷新令牌有效期
	m.RefreshToken(loginUser)

	// 创建JWT Claims
	now := time.Now()
	expireTime := now.Add(m.jwtConfig.GetExpireTime())
	claims := &model.Claims{
		UserId:         loginUser.GetUserId(),
		Username:       loginUser.GetUser().UserName,
		UUID:           uuid,
		LoginTime:      now.Unix(),
		ExpirationTime: expireTime.Unix(),
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			IssuedAt:  now.Unix(),
		},
	}

	// 创建Token
	return m.jwtAuth.CreateToken(claims)
}

// VerifyToken 验证Token有效期，相差不足20分钟自动刷新缓存
func (m *TokenManager) VerifyToken(loginUser *model.LoginUser) {
	expireTime := loginUser.GetExpireTime()
	currentTime := time.Now().Unix()
	// 相差不足20分钟，自动刷新缓存
	if expireTime-currentTime <= 20*60 {
		m.RefreshToken(loginUser)
	}
}

// RefreshToken 刷新Token有效期
func (m *TokenManager) RefreshToken(loginUser *model.LoginUser) {
	loginUser.SetLoginTime(time.Now().Unix())
	expireTime := time.Now().Add(m.jwtConfig.GetExpireTime()).Unix()
	loginUser.SetExpireTime(expireTime)

	// 缓存用户信息
	tokenKey := m.GetTokenKey(loginUser.GetToken())
	expiration := m.jwtConfig.GetExpireTime()

	// 序列化用户信息
	userJson, err := json.Marshal(loginUser)
	if err != nil {
		m.logger.Error("序列化用户信息失败", zap.Error(err))
		return
	}

	// 存入Redis
	m.redisService.Set(tokenKey, string(userJson), expiration)
}

// GetToken 获取请求Token
func (m *TokenManager) GetToken(ctx *gin.Context) string {
	token := ctx.GetHeader(TokenHeader)
	return token
}

// GetTokenKey 获取Token缓存key
func (m *TokenManager) GetTokenKey(uuid string) string {
	return fmt.Sprintf("%s%s", TokenKey, uuid)
}
