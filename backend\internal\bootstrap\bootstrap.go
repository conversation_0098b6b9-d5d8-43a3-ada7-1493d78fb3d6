package bootstrap

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/initialize"
	"go.uber.org/zap"
)

// App 应用程序
type App struct {
	config     *config.MainConfig
	httpServer *http.Server
	logger     *zap.Logger
}

// NewApp 创建应用程序
func NewApp() (*App, error) {
	// 初始化配置
	mainConfig, err := initialize.InitConfig()
	if err != nil {
		return nil, fmt.Errorf("初始化配置失败: %v", err)
	}

	// 初始化日志
	err = initialize.InitLogger(mainConfig.Log)
	if err != nil {
		return nil, fmt.Errorf("初始化日志失败: %v", err)
	}

	// 获取全局日志记录器
	logger := zap.L()

	return &App{
		config: mainConfig,
		logger: logger,
	}, nil
}

// Start 启动应用程序
func (a *App) Start() error {
	// 初始化数据库
	if err := initialize.InitDatabase(a.config.DB, a.logger); err != nil {
		return fmt.Errorf("初始化数据库失败: %v", err)
	}

	// 初始化Redis
	if err := initialize.InitRedis(a.config.Redis, a.logger); err != nil {
		return fmt.Errorf("初始化Redis失败: %v", err)
	}

	// 初始化验证器
	if err := initialize.InitValidator(a.logger); err != nil {
		return fmt.Errorf("初始化验证器失败: %v", err)
	}

	// 初始化国际化
	if err := initialize.InitI18n(a.config.I18n, a.logger); err != nil {
		return fmt.Errorf("初始化国际化失败: %v", err)
	}

	// 初始化任务
	if err := initialize.InitTasks(a.config.ThreadPool, a.logger); err != nil {
		return fmt.Errorf("初始化任务失败: %v", err)
	}

	// 初始化路由
	router := initialize.InitRouter(a.config, a.logger)

	// 创建HTTP服务器
	a.httpServer = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", a.config.Server.Host, a.config.Server.Port),
		Handler: router,
	}

	// 启动HTTP服务器
	go func() {
		a.logger.Info("启动HTTP服务器",
			zap.String("addr", a.httpServer.Addr),
		)

		var err error
		if a.config.TLS.Enable {
			err = a.httpServer.ListenAndServeTLS(a.config.TLS.CertFile, a.config.TLS.KeyFile)
		} else {
			err = a.httpServer.ListenAndServe()
		}

		if err != nil && err != http.ErrServerClosed {
			a.logger.Fatal("HTTP服务器启动失败", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止应用程序
func (a *App) Stop() {
	// 创建上下文，设置超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 优雅关闭HTTP服务器
	if a.httpServer != nil {
		a.logger.Info("正在关闭HTTP服务器...")
		if err := a.httpServer.Shutdown(ctx); err != nil {
			a.logger.Error("关闭HTTP服务器失败", zap.Error(err))
		}
	}

	// 关闭Redis连接
	if err := initialize.CloseRedis(); err != nil {
		a.logger.Error("关闭Redis连接失败", zap.Error(err))
	}

	// 停止任务管理器
	if taskManager := initialize.GetTaskManager(); taskManager != nil {
		taskManager.Stop()
		a.logger.Info("任务管理器已停止")
	}

	// 同步日志
	a.logger.Info("应用程序已停止")
	_ = a.logger.Sync()
}

// Run 运行应用程序
func Run() {
	// 创建应用程序
	app, err := NewApp()
	if err != nil {
		fmt.Printf("创建应用程序失败: %v\n", err)
		os.Exit(1)
	}

	// 启动应用程序
	if err := app.Start(); err != nil {
		app.logger.Fatal("启动应用程序失败", zap.Error(err))
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止应用程序
	app.Stop()
}

// Bootstrap 引导应用
func Bootstrap(logger *zap.Logger) (*gin.Engine, error) {
	// 初始化配置
	mainConfig, err := initialize.InitConfig(logger)
	if err != nil {
		return nil, fmt.Errorf("初始化配置失败: %v", err)
	}

	// 初始化日志
	if err := initialize.InitLogger(mainConfig.Log); err != nil {
		return nil, fmt.Errorf("初始化日志失败: %v", err)
	}

	// 初始化数据库
	if err := initialize.InitDatabase(mainConfig.Database, logger); err != nil {
		return nil, fmt.Errorf("初始化数据库失败: %v", err)
	}

	// 初始化Redis
	if err := initialize.InitRedis(mainConfig.Redis, logger); err != nil {
		return nil, fmt.Errorf("初始化Redis失败: %v", err)
	}

	// 初始化路由
	engine := initialize.InitRouter(mainConfig, logger)

	// 初始化验证器
	if err := initialize.InitValidator(logger); err != nil {
		return nil, fmt.Errorf("初始化验证器失败: %v", err)
	}

	// 初始化国际化
	if err := initialize.InitI18n(mainConfig.I18n, logger); err != nil {
		return nil, fmt.Errorf("初始化国际化失败: %v", err)
	}

	// 初始化任务
	if err := initialize.InitTasks(mainConfig.ThreadPool, logger); err != nil {
		return nil, fmt.Errorf("初始化任务失败: %v", err)
	}

	return engine, nil
}

// GetMainConfig 获取主配置
func GetMainConfig() (*config.MainConfig, error) {
	// 初始化日志
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("初始化日志失败: %v", err)
	}
	defer logger.Sync()

	// 初始化配置
	mainConfig, err := initialize.InitConfig(logger)
	if err != nil {
		return nil, fmt.Errorf("初始化配置失败: %v", err)
	}

	return mainConfig, nil
}
