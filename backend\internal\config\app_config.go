package config

import (
	"os"
	"path/filepath"
)

// AppConfig 应用配置
type AppConfig struct {
	// 应用名称
	Name string `json:"name" yaml:"name"`

	// 应用版本
	Version string `json:"version" yaml:"version"`

	// 应用环境
	Env string `json:"env" yaml:"env"`

	// 应用端口
	Port int `json:"port" yaml:"port"`

	// 应用路径
	BasePath string `json:"basePath" yaml:"basePath"`

	// 上传路径
	UploadPath string `json:"uploadPath" yaml:"uploadPath"`

	// 资源路径
	ResourcePath string `json:"resourcePath" yaml:"resourcePath"`

	// 头像路径
	AvatarPath string `json:"avatarPath" yaml:"avatarPath"`

	// 下载路径
	DownloadPath string `json:"downloadPath" yaml:"downloadPath"`

	// 日志路径
	LogPath string `json:"logPath" yaml:"logPath"`

	// 临时路径
	TempPath string `json:"tempPath" yaml:"tempPath"`
}

// NewAppConfig 创建应用配置
func NewAppConfig() *AppConfig {
	// 获取当前工作目录
	workDir, _ := os.Getwd()

	return &AppConfig{
		Name:         "RuoYi-Go",
		Version:      "1.0.0",
		Env:          "dev",
		Port:         8080,
		BasePath:     workDir,
		UploadPath:   filepath.Join(workDir, "upload"),
		ResourcePath: filepath.Join(workDir, "resource"),
		AvatarPath:   filepath.Join(workDir, "upload", "avatar"),
		DownloadPath: filepath.Join(workDir, "download"),
		LogPath:      filepath.Join(workDir, "logs"),
		TempPath:     filepath.Join(workDir, "temp"),
	}
}

// GetName 获取应用名称
func (c *AppConfig) GetName() string {
	return c.Name
}

// GetVersion 获取应用版本
func (c *AppConfig) GetVersion() string {
	return c.Version
}

// GetEnv 获取应用环境
func (c *AppConfig) GetEnv() string {
	return c.Env
}

// GetPort 获取应用端口
func (c *AppConfig) GetPort() int {
	return c.Port
}

// GetBasePath 获取应用基础路径
func (c *AppConfig) GetBasePath() string {
	return c.BasePath
}

// GetUploadPath 获取上传路径
func (c *AppConfig) GetUploadPath() string {
	return c.UploadPath
}

// GetResourcePath 获取资源路径
func (c *AppConfig) GetResourcePath() string {
	return c.ResourcePath
}

// GetAvatarPath 获取头像路径
func (c *AppConfig) GetAvatarPath() string {
	return c.AvatarPath
}

// GetDownloadPath 获取下载路径
func (c *AppConfig) GetDownloadPath() string {
	return c.DownloadPath
}

// GetLogPath 获取日志路径
func (c *AppConfig) GetLogPath() string {
	return c.LogPath
}

// GetTempPath 获取临时路径
func (c *AppConfig) GetTempPath() string {
	return c.TempPath
}

// GetProfile 获取配置文件路径
func (c *AppConfig) GetProfile() string {
	return c.BasePath
}

// EnsurePathExists 确保路径存在
func (c *AppConfig) EnsurePathExists() error {
	paths := []string{
		c.UploadPath,
		c.ResourcePath,
		c.AvatarPath,
		c.DownloadPath,
		c.LogPath,
		c.TempPath,
	}

	for _, p := range paths {
		if err := os.MkdirAll(p, 0755); err != nil {
			return err
		}
	}

	return nil
}

// IsDev 是否是开发环境
func (c *AppConfig) IsDev() bool {
	return c.Env == "dev"
}

// IsTest 是否是测试环境
func (c *AppConfig) IsTest() bool {
	return c.Env == "test"
}

// IsProd 是否是生产环境
func (c *AppConfig) IsProd() bool {
	return c.Env == "prod"
}

// RedisConfig Redis配置
type RedisConfig struct {
	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库
	DB int `mapstructure:"db" json:"db"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	// 类型
	Type string `mapstructure:"type" json:"type"`

	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 用户名
	Username string `mapstructure:"username" json:"username"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库名
	Database string `mapstructure:"database" json:"database"`

	// 参数
	Params string `mapstructure:"params" json:"params"`
}
