package config

import (
	"time"
)

// ApplicationConfig 应用配置类，对应Java版本的ApplicationConfig
type ApplicationConfig struct {
	// 日期时间格式
	DateTimeFormat string `mapstructure:"date_time_format" json:"date_time_format"`

	// 日期格式
	DateFormat string `mapstructure:"date_format" json:"date_format"`

	// 时间格式
	TimeFormat string `mapstructure:"time_format" json:"time_format"`

	// 是否输出美化JSON
	PrettyPrint bool `mapstructure:"pretty_print" json:"pretty_print"`

	// 是否忽略未知属性
	IgnoreUnknown bool `mapstructure:"ignore_unknown" json:"ignore_unknown"`

	// 是否允许空值
	AllowEmpty bool `mapstructure:"allow_empty" json:"allow_empty"`

	// 时区
	TimeZone string `mapstructure:"time_zone" json:"time_zone"`
}

// NewApplicationConfig 创建新的应用配置
func NewApplicationConfig() *ApplicationConfig {
	return &ApplicationConfig{
		DateTimeFormat: "2006-01-02 15:04:05",
		DateFormat:     "2006-01-02",
		TimeFormat:     "15:04:05",
		PrettyPrint:    false,
		IgnoreUnknown:  true,
		AllowEmpty:     true,
		TimeZone:       "Asia/Shanghai",
	}
}

// GetDateTimeFormat 获取日期时间格式
func (c *ApplicationConfig) GetDateTimeFormat() string {
	if c.DateTimeFormat == "" {
		return "2006-01-02 15:04:05"
	}
	return c.DateTimeFormat
}

// GetDateFormat 获取日期格式
func (c *ApplicationConfig) GetDateFormat() string {
	if c.DateFormat == "" {
		return "2006-01-02"
	}
	return c.DateFormat
}

// GetTimeFormat 获取时间格式
func (c *ApplicationConfig) GetTimeFormat() string {
	if c.TimeFormat == "" {
		return "15:04:05"
	}
	return c.TimeFormat
}

// GetTimeZone 获取时区
func (c *ApplicationConfig) GetTimeZone() *time.Location {
	loc, err := time.LoadLocation(c.TimeZone)
	if err != nil {
		return time.Local
	}
	return loc
}

// FormatDateTime 格式化日期时间
func (c *ApplicationConfig) FormatDateTime(t time.Time) string {
	return t.In(c.GetTimeZone()).Format(c.GetDateTimeFormat())
}

// FormatDate 格式化日期
func (c *ApplicationConfig) FormatDate(t time.Time) string {
	return t.In(c.GetTimeZone()).Format(c.GetDateFormat())
}

// FormatTime 格式化时间
func (c *ApplicationConfig) FormatTime(t time.Time) string {
	return t.In(c.GetTimeZone()).Format(c.GetTimeFormat())
}

// ParseDateTime 解析日期时间字符串
func (c *ApplicationConfig) ParseDateTime(s string) (time.Time, error) {
	return time.ParseInLocation(c.GetDateTimeFormat(), s, c.GetTimeZone())
}

// ParseDate 解析日期字符串
func (c *ApplicationConfig) ParseDate(s string) (time.Time, error) {
	return time.ParseInLocation(c.GetDateFormat(), s, c.GetTimeZone())
}

// ParseTime 解析时间字符串
func (c *ApplicationConfig) ParseTime(s string) (time.Time, error) {
	return time.ParseInLocation(c.GetTimeFormat(), s, c.GetTimeZone())
}
