package config

import (
	"image/color"
)

// CaptchaConfig 验证码配置类，对应Java版本的CaptchaConfig
type CaptchaConfig struct {
	// 是否开启验证码
	Enabled bool `mapstructure:"enabled" json:"enabled"`

	// 验证码类型（math/char）
	Type string `mapstructure:"type" json:"type"`

	// 验证码长度
	Length int `mapstructure:"length" json:"length"`

	// 验证码宽度
	Width int `mapstructure:"width" json:"width"`

	// 验证码高度
	Height int `mapstructure:"height" json:"height"`

	// 验证码过期时间（分钟）
	ExpireTime int `mapstructure:"expire_time" json:"expire_time"`

	// 验证码干扰线数量
	NoiseCount int `mapstructure:"noise_count" json:"noise_count"`

	// 验证码字体大小
	FontSize int `mapstructure:"font_size" json:"font_size"`

	// 字符验证码字符集
	CharSet string `mapstructure:"char_set" json:"char_set"`

	// 数学验证码运算符
	MathOperators string `mapstructure:"math_operators" json:"math_operators"`

	// 背景颜色
	BackgroundColor string `mapstructure:"background_color" json:"background_color"`

	// 文字颜色
	TextColor string `mapstructure:"text_color" json:"text_color"`
}

// NewCaptchaConfig 创建新的验证码配置
func NewCaptchaConfig() *CaptchaConfig {
	return &CaptchaConfig{
		Enabled:         true,
		Type:            "math",
		Length:          4,
		Width:           160,
		Height:          60,
		ExpireTime:      2,
		NoiseCount:      4,
		FontSize:        40,
		CharSet:         "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
		MathOperators:   "+-*",
		BackgroundColor: "#f5f5f5",
		TextColor:       "#000000",
	}
}

// IsEnabled 是否启用验证码
func (c *CaptchaConfig) IsEnabled() bool {
	return c.Enabled
}

// IsMathType 是否是数学运算类型验证码
func (c *CaptchaConfig) IsMathType() bool {
	return c.Type == "math"
}

// IsCharType 是否是字符类型验证码
func (c *CaptchaConfig) IsCharType() bool {
	return c.Type == "char"
}

// GetCharSet 获取字符集
func (c *CaptchaConfig) GetCharSet() string {
	if c.CharSet == "" {
		return "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	}
	return c.CharSet
}

// GetMathOperators 获取数学运算符
func (c *CaptchaConfig) GetMathOperators() string {
	if c.MathOperators == "" {
		return "+-*"
	}
	return c.MathOperators
}

// GetExpireTime 获取过期时间（分钟）
func (c *CaptchaConfig) GetExpireTime() int {
	if c.ExpireTime <= 0 {
		return 2
	}
	return c.ExpireTime
}

// GetBackgroundColor 获取背景颜色
func (c *CaptchaConfig) GetBackgroundColor() color.RGBA {
	// 默认浅灰色背景
	return color.RGBA{245, 245, 245, 255}
}

// GetTextColor 获取文字颜色
func (c *CaptchaConfig) GetTextColor() color.RGBA {
	// 默认黑色文字
	return color.RGBA{0, 0, 0, 255}
}

// GetCaptchaProperties 获取验证码属性
func (c *CaptchaConfig) GetCaptchaProperties() map[string]string {
	props := make(map[string]string)

	// 通用配置
	props["kaptcha.border"] = "yes"
	props["kaptcha.border.color"] = "105,179,90"
	props["kaptcha.textproducer.font.color"] = "blue"
	props["kaptcha.image.width"] = string(c.Width)
	props["kaptcha.image.height"] = string(c.Height)
	props["kaptcha.textproducer.font.size"] = string(c.FontSize)
	props["kaptcha.session.key"] = "kaptchaCode"
	props["kaptcha.textproducer.char.length"] = string(c.Length)
	props["kaptcha.textproducer.font.names"] = "Arial,Courier"
	props["kaptcha.obscurificator.impl"] = "com.google.code.kaptcha.impl.ShadowGimpy"
	props["kaptcha.noise.impl"] = "com.google.code.kaptcha.impl.NoNoise"

	// 根据验证码类型设置特定配置
	if c.IsMathType() {
		props["kaptcha.textproducer.impl"] = "com.ruoyi.framework.config.KaptchaTextCreator"
		props["kaptcha.textproducer.char.string"] = "0123456789"
		props["kaptcha.textproducer.char.space"] = "3"
	}

	return props
}

// GetCaptchaMathProperties 获取数学验证码属性
func (c *CaptchaConfig) GetCaptchaMathProperties() map[string]string {
	props := c.GetCaptchaProperties()
	props["kaptcha.textproducer.impl"] = "com.ruoyi.framework.config.KaptchaTextCreator"
	props["kaptcha.textproducer.char.string"] = "0123456789"
	props["kaptcha.textproducer.char.space"] = "3"
	return props
}
