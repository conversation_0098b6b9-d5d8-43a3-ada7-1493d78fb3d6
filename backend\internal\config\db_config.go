package config

import (
	"fmt"
	"time"
)

// DBConfig 数据库配置
type DBConfig struct {
	// Type 数据库类型
	Type string `mapstructure:"type" json:"type" yaml:"type"`

	// Host 主机地址
	Host string `mapstructure:"host" json:"host" yaml:"host"`

	// Port 端口号
	Port int `mapstructure:"port" json:"port" yaml:"port"`

	// Database 数据库名
	Database string `mapstructure:"database" json:"database" yaml:"database"`

	// Username 用户名
	Username string `mapstructure:"username" json:"username" yaml:"username"`

	// Password 密码
	Password string `mapstructure:"password" json:"password" yaml:"password"`

	// Charset 字符集
	Charset string `mapstructure:"charset" json:"charset" yaml:"charset"`

	// MaxIdleConns 最大空闲连接数
	MaxIdleConns int `mapstructure:"max_idle_conns" json:"max_idle_conns" yaml:"max_idle_conns"`

	// MaxOpenConns 最大打开连接数
	MaxOpenConns int `mapstructure:"max_open_conns" json:"max_open_conns" yaml:"max_open_conns"`

	// ConnMaxLifetime 连接最大生命周期（秒）
	ConnMaxLifetime int `mapstructure:"conn_max_lifetime" json:"conn_max_lifetime" yaml:"conn_max_lifetime"`

	// LogMode 日志模式
	LogMode bool `mapstructure:"log_mode" json:"log_mode" yaml:"log_mode"`

	// SlowThreshold 慢查询阈值（毫秒）
	SlowThreshold int `mapstructure:"slow_threshold" json:"slow_threshold" yaml:"slow_threshold"`

	// TablePrefix 表前缀
	TablePrefix string `mapstructure:"table_prefix" json:"table_prefix" yaml:"table_prefix"`

	// SSLMode SSL模式
	SSLMode string `mapstructure:"ssl_mode" json:"ssl_mode" yaml:"ssl_mode"`
}

// NewDBConfig 创建数据库配置
func NewDBConfig() *DBConfig {
	return &DBConfig{
		Type:            "mysql",
		Host:            "localhost",
		Port:            3306,
		Database:        "ruoyi",
		Username:        "root",
		Password:        "password",
		Charset:         "utf8mb4",
		MaxIdleConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: 3600,
		LogMode:         false,
		SlowThreshold:   200,
		TablePrefix:     "",
		SSLMode:         "disable",
	}
}

// GetDSN 获取数据源名称
func (c *DBConfig) GetDSN() string {
	switch c.Type {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.Database, c.Charset)
	case "postgres":
		sslmode := "disable"
		if c.SSLMode != "" {
			sslmode = c.SSLMode
		}
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.Username, c.Database, c.Password, sslmode)
	case "sqlserver":
		return fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s",
			c.Username, c.Password, c.Host, c.Port, c.Database)
	case "sqlite3":
		return c.Database
	default:
		return ""
	}
}

// GetConnMaxLifetime 获取连接最大生命周期
func (c *DBConfig) GetConnMaxLifetime() time.Duration {
	return time.Duration(c.ConnMaxLifetime) * time.Second
}

// GetSlowThreshold 获取慢查询阈值
func (c *DBConfig) GetSlowThreshold() time.Duration {
	return time.Duration(c.SlowThreshold) * time.Millisecond
}
