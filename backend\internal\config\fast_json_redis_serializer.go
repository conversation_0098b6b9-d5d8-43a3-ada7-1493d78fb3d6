package config

import (
	"encoding/json"
	"reflect"
)

// FastJson2JsonRedisSerializer Redis序列化器，对应Java版本的FastJson2JsonRedisSerializer
type FastJson2JsonRedisSerializer struct {
	// 序列化类型
	clazz reflect.Type
}

// NewFastJson2JsonRedisSerializer 创建新的Redis序列化器
func NewFastJson2JsonRedisSerializer(clazz reflect.Type) *FastJson2JsonRedisSerializer {
	return &FastJson2JsonRedisSerializer{
		clazz: clazz,
	}
}

// Serialize 序列化对象
func (s *FastJson2JsonRedisSerializer) Serialize(object interface{}) ([]byte, error) {
	if object == nil {
		return nil, nil
	}

	// 使用Go的标准JSON库进行序列化
	return json.Marshal(object)
}

// Deserialize 反序列化对象
func (s *FastJson2JsonRedisSerializer) Deserialize(bytes []byte) (interface{}, error) {
	if bytes == nil || len(bytes) == 0 {
		return nil, nil
	}

	// 根据类型创建新的实例
	value := reflect.New(s.clazz).Interface()

	// 使用Go的标准JSON库进行反序列化
	err := json.Unmarshal(bytes, value)
	if err != nil {
		return nil, err
	}

	return value, nil
}

// DeserializeToType 反序列化为指定类型
func (s *FastJson2JsonRedisSerializer) DeserializeToType(bytes []byte, t reflect.Type) (interface{}, error) {
	if bytes == nil || len(bytes) == 0 {
		return nil, nil
	}

	// 根据指定类型创建新的实例
	value := reflect.New(t).Interface()

	// 使用Go的标准JSON库进行反序列化
	err := json.Unmarshal(bytes, value)
	if err != nil {
		return nil, err
	}

	return value, nil
}

// SetType 设置序列化类型
func (s *FastJson2JsonRedisSerializer) SetType(t reflect.Type) {
	s.clazz = t
}

// GetType 获取序列化类型
func (s *FastJson2JsonRedisSerializer) GetType() reflect.Type {
	return s.clazz
}

// SerializeMap 序列化Map
func (s *FastJson2JsonRedisSerializer) SerializeMap(m map[string]interface{}) ([]byte, error) {
	if m == nil {
		return nil, nil
	}

	// 使用Go的标准JSON库进行序列化
	return json.Marshal(m)
}

// DeserializeMap 反序列化为Map
func (s *FastJson2JsonRedisSerializer) DeserializeMap(bytes []byte) (map[string]interface{}, error) {
	if bytes == nil || len(bytes) == 0 {
		return nil, nil
	}

	// 创建Map
	m := make(map[string]interface{})

	// 使用Go的标准JSON库进行反序列化
	err := json.Unmarshal(bytes, &m)
	if err != nil {
		return nil, err
	}

	return m, nil
}

// SerializeList 序列化List
func (s *FastJson2JsonRedisSerializer) SerializeList(list []interface{}) ([]byte, error) {
	if list == nil {
		return nil, nil
	}

	// 使用Go的标准JSON库进行序列化
	return json.Marshal(list)
}

// DeserializeList 反序列化为List
func (s *FastJson2JsonRedisSerializer) DeserializeList(bytes []byte) ([]interface{}, error) {
	if bytes == nil || len(bytes) == 0 {
		return nil, nil
	}

	// 创建List
	list := make([]interface{}, 0)

	// 使用Go的标准JSON库进行反序列化
	err := json.Unmarshal(bytes, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
