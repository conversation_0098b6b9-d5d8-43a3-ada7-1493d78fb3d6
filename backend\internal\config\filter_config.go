package config

import (
	"strings"
)

// FilterConfig 过滤器配置类，对应Java版本的FilterConfig
type FilterConfig struct {
	// 排除路径
	Excludes string `mapstructure:"excludes" json:"excludes"`

	// URL模式
	UrlPatterns string `mapstructure:"url_patterns" json:"url_patterns"`

	// 是否启用XSS过滤
	XssEnabled bool `mapstructure:"xss_enabled" json:"xss_enabled"`

	// 是否启用CSRF过滤
	CsrfEnabled bool `mapstructure:"csrf_enabled" json:"csrf_enabled"`

	// 是否启用SQL注入过滤
	SqlInjectionEnabled bool `mapstructure:"sql_injection_enabled" json:"sql_injection_enabled"`
}

// NewFilterConfig 创建新的过滤器配置
func NewFilterConfig() *FilterConfig {
	return &FilterConfig{
		Excludes:            "/system/notice/*,/system/config/*",
		UrlPatterns:         "/*",
		XssEnabled:          true,
		CsrfEnabled:         false,
		SqlInjectionEnabled: true,
	}
}

// GetExcludes 获取排除路径数组
func (c *FilterConfig) GetExcludes() []string {
	if c.Excludes == "" {
		return []string{}
	}
	return strings.Split(c.Excludes, ",")
}

// GetUrlPatterns 获取URL模式数组
func (c *FilterConfig) GetUrlPatterns() []string {
	if c.UrlPatterns == "" {
		return []string{"/*"}
	}
	return strings.Split(c.UrlPatterns, ",")
}

// IsExcluded 检查路径是否被排除
func (c *FilterConfig) IsExcluded(path string) bool {
	for _, exclude := range c.GetExcludes() {
		if matchPath(path, exclude) {
			return true
		}
	}
	return false
}

// IsXssEnabled 是否启用XSS过滤
func (c *FilterConfig) IsXssEnabled() bool {
	return c.XssEnabled
}

// IsCsrfEnabled 是否启用CSRF过滤
func (c *FilterConfig) IsCsrfEnabled() bool {
	return c.CsrfEnabled
}

// IsSqlInjectionEnabled 是否启用SQL注入过滤
func (c *FilterConfig) IsSqlInjectionEnabled() bool {
	return c.SqlInjectionEnabled
}

// matchPath 匹配路径
func matchPath(path string, pattern string) bool {
	// 简单的通配符匹配实现
	if pattern == "*" {
		return true
	}

	if strings.HasSuffix(pattern, "/*") {
		prefix := strings.TrimSuffix(pattern, "/*")
		return strings.HasPrefix(path, prefix)
	}

	return path == pattern
}

// RegisterXssFilter 注册XSS过滤器
func (c *FilterConfig) RegisterXssFilter() interface{} {
	// 在Go中，我们通常使用中间件而不是过滤器
	// 这里返回一个函数，可以在HTTP中间件中使用
	return func(next interface{}) interface{} {
		// 实现XSS过滤逻辑
		return next
	}
}

// RegisterCsrfFilter 注册CSRF过滤器
func (c *FilterConfig) RegisterCsrfFilter() interface{} {
	// 返回CSRF中间件函数
	return func(next interface{}) interface{} {
		// 实现CSRF过滤逻辑
		return next
	}
}

// RegisterSqlInjectionFilter 注册SQL注入过滤器
func (c *FilterConfig) RegisterSqlInjectionFilter() interface{} {
	// 返回SQL注入中间件函数
	return func(next interface{}) interface{} {
		// 实现SQL注入过滤逻辑
		return next
	}
}
