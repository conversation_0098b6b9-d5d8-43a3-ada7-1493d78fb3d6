package config

import (
	"strings"
)

// I18nConfig 国际化配置类，对应Java版本的I18nConfig
type I18nConfig struct {
	// 默认语言
	DefaultLocale string `mapstructure:"default_locale" json:"default_locale"`

	// 支持的语言列表
	SupportedLocales string `mapstructure:"supported_locales" json:"supported_locales"`

	// 语言参数名
	LocaleParam string `mapstructure:"locale_param" json:"locale_param"`

	// Cookie名称
	CookieName string `mapstructure:"cookie_name" json:"cookie_name"`

	// Cookie过期时间（天）
	CookieMaxAge int `mapstructure:"cookie_max_age" json:"cookie_max_age"`

	// 消息资源路径
	BasenameList string `mapstructure:"basename_list" json:"basename_list"`

	// 是否使用Cookie存储语言
	UseCookie bool `mapstructure:"use_cookie" json:"use_cookie"`

	// 是否使用Session存储语言
	UseSession bool `mapstructure:"use_session" json:"use_session"`

	// Session键名
	SessionName string `mapstructure:"session_name" json:"session_name"`

	// 是否使用Accept-Language头
	UseAcceptLanguageHeader bool `mapstructure:"use_accept_language_header" json:"use_accept_language_header"`
}

// NewI18nConfig 创建新的国际化配置
func NewI18nConfig() *I18nConfig {
	return &I18nConfig{
		DefaultLocale:           "zh_CN",
		SupportedLocales:        "zh_CN,en_US",
		LocaleParam:             "lang",
		CookieName:              "locale",
		CookieMaxAge:            30,
		BasenameList:            "i18n/messages",
		UseCookie:               true,
		UseSession:              false,
		SessionName:             "locale",
		UseAcceptLanguageHeader: true,
	}
}

// GetDefaultLocale 获取默认语言
func (c *I18nConfig) GetDefaultLocale() string {
	if c.DefaultLocale == "" {
		return "zh_CN"
	}
	return c.DefaultLocale
}

// GetSupportedLocales 获取支持的语言列表
func (c *I18nConfig) GetSupportedLocales() []string {
	if c.SupportedLocales == "" {
		return []string{"zh_CN", "en_US"}
	}
	return strings.Split(c.SupportedLocales, ",")
}

// GetLocaleParam 获取语言参数名
func (c *I18nConfig) GetLocaleParam() string {
	if c.LocaleParam == "" {
		return "lang"
	}
	return c.LocaleParam
}

// GetCookieName 获取Cookie名称
func (c *I18nConfig) GetCookieName() string {
	if c.CookieName == "" {
		return "locale"
	}
	return c.CookieName
}

// GetCookieMaxAge 获取Cookie过期时间（天）
func (c *I18nConfig) GetCookieMaxAge() int {
	if c.CookieMaxAge <= 0 {
		return 30
	}
	return c.CookieMaxAge
}

// GetBasenameList 获取消息资源路径列表
func (c *I18nConfig) GetBasenameList() []string {
	if c.BasenameList == "" {
		return []string{"i18n/messages"}
	}
	return strings.Split(c.BasenameList, ",")
}

// IsUseCookie 是否使用Cookie存储语言
func (c *I18nConfig) IsUseCookie() bool {
	return c.UseCookie
}

// IsUseSession 是否使用Session存储语言
func (c *I18nConfig) IsUseSession() bool {
	return c.UseSession
}

// GetSessionName 获取Session键名
func (c *I18nConfig) GetSessionName() string {
	if c.SessionName == "" {
		return "locale"
	}
	return c.SessionName
}

// IsUseAcceptLanguageHeader 是否使用Accept-Language头
func (c *I18nConfig) IsUseAcceptLanguageHeader() bool {
	return c.UseAcceptLanguageHeader
}

// IsSupportedLocale 检查语言是否受支持
func (c *I18nConfig) IsSupportedLocale(locale string) bool {
	for _, supported := range c.GetSupportedLocales() {
		if supported == locale {
			return true
		}
	}
	return false
}

// LocaleResolver 获取区域解析器
func (c *I18nConfig) LocaleResolver() interface{} {
	// 在Go中实现区域解析器
	// 这里返回一个函数，可以在HTTP中间件中使用
	return func(locale string) string {
		if c.IsSupportedLocale(locale) {
			return locale
		}
		return c.GetDefaultLocale()
	}
}

// LocaleChangeInterceptor 获取区域变更拦截器
func (c *I18nConfig) LocaleChangeInterceptor() interface{} {
	// 返回区域变更中间件函数
	return func(next interface{}) interface{} {
		// 实现区域变更逻辑
		return next
	}
}
