package config

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

// KaptchaTextCreator 验证码文本创建器，对应Java版本的KaptchaTextCreator
type KaptchaTextCreator struct {
	// 数字字符集
	CNUMBERS string
}

// NewKaptchaTextCreator 创建新的验证码文本创建器
func NewKaptchaTextCreator() *KaptchaTextCreator {
	return &KaptchaTextCreator{
		CNUMBERS: "0123456789",
	}
}

// GetText 获取验证码文本
func (c *KaptchaTextCreator) GetText() string {
	// 初始化随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 生成两个随机数
	num1 := r.Intn(10)
	num2 := r.Intn(10)

	// 生成运算符
	operators := []string{"+", "-", "*"}
	operator := operators[r.Intn(len(operators))]

	// 计算结果
	var result int
	switch operator {
	case "+":
		result = num1 + num2
	case "-":
		// 确保结果为非负数
		if num1 < num2 {
			num1, num2 = num2, num1
		}
		result = num1 - num2
	case "*":
		result = num1 * num2
	}

	// 构造验证码文本
	text := fmt.Sprintf("%d%s%d=?@%d", num1, operator, num2, result)

	return text
}

// GenerateRandomCode 生成随机验证码
func (c *KaptchaTextCreator) GenerateRandomCode(length int) string {
	if length <= 0 {
		length = 4
	}

	// 初始化随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 生成随机验证码
	code := ""
	for i := 0; i < length; i++ {
		idx := r.Intn(len(c.CNUMBERS))
		code += string(c.CNUMBERS[idx])
	}

	return code
}

// ValidateCode 验证验证码
func (c *KaptchaTextCreator) ValidateCode(text string, answer string) bool {
	// 解析验证码文本
	parts := []rune(text)
	if len(parts) < 5 {
		return false
	}

	// 查找@符号位置
	atIndex := -1
	for i, ch := range parts {
		if ch == '@' {
			atIndex = i
			break
		}
	}

	if atIndex == -1 || atIndex == len(parts)-1 {
		return false
	}

	// 获取正确答案
	correctAnswer := string(parts[atIndex+1:])

	// 比较用户答案
	return correctAnswer == answer
}

// ParseMathText 解析数学验证码文本
func (c *KaptchaTextCreator) ParseMathText(text string) (int, string, int, int, error) {
	// 查找运算符位置
	var operator string
	var operatorIndex int

	for i, ch := range text {
		if ch == '+' || ch == '-' || ch == '*' {
			operator = string(ch)
			operatorIndex = i
			break
		}
	}

	if operatorIndex == 0 || operatorIndex == len(text)-1 {
		return 0, "", 0, 0, fmt.Errorf("invalid math text format")
	}

	// 查找等号位置
	equalIndex := -1
	for i, ch := range text {
		if ch == '=' {
			equalIndex = i
			break
		}
	}

	if equalIndex == -1 || equalIndex <= operatorIndex || equalIndex == len(text)-1 {
		return 0, "", 0, 0, fmt.Errorf("invalid math text format")
	}

	// 解析数字
	num1Str := text[:operatorIndex]
	num2Str := text[operatorIndex+1 : equalIndex]

	num1, err := strconv.Atoi(num1Str)
	if err != nil {
		return 0, "", 0, 0, fmt.Errorf("invalid first number: %v", err)
	}

	num2, err := strconv.Atoi(num2Str)
	if err != nil {
		return 0, "", 0, 0, fmt.Errorf("invalid second number: %v", err)
	}

	// 计算结果
	var result int
	switch operator {
	case "+":
		result = num1 + num2
	case "-":
		result = num1 - num2
	case "*":
		result = num1 * num2
	default:
		return 0, "", 0, 0, fmt.Errorf("unsupported operator: %s", operator)
	}

	return num1, operator, num2, result, nil
}
