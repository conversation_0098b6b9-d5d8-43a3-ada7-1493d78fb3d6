package config

import (
	"os"
	"strconv"
)

// MainConfig 主配置
type MainConfig struct {
	// App 应用配置
	App *ApplicationConfig `mapstructure:"app" json:"app" yaml:"app"`

	// Server 服务器配置
	Server *ServerConfig `mapstructure:"server" json:"server" yaml:"server"`

	// Log 日志配置
	Log *LogConfig `mapstructure:"log" json:"log" yaml:"log"`

	// DB 数据库配置
	DB *DBConfig `mapstructure:"db" json:"db" yaml:"db"`

	// Redis Redis配置
	Redis *RedisCfg `mapstructure:"redis" json:"redis" yaml:"redis"`

	// JWT JWT配置
	JWT *JWTConfig `mapstructure:"jwt" json:"jwt" yaml:"jwt"`

	// Captcha 验证码配置
	Captcha *CaptchaConfig `mapstructure:"captcha" json:"captcha" yaml:"captcha"`

	// Filter 过滤器配置
	Filter *FilterConfig `mapstructure:"filter" json:"filter" yaml:"filter"`

	// I18n 国际化配置
	I18n *I18nConfig `mapstructure:"i18n" json:"i18n" yaml:"i18n"`

	// Gorm GORM配置
	Gorm *GormConfig `mapstructure:"gorm" json:"gorm" yaml:"gorm"`

	// Resources 资源配置
	Resources *ResourcesConfig `mapstructure:"resources" json:"resources" yaml:"resources"`

	// Security 安全配置
	Security *SecurityConfig `mapstructure:"security" json:"security" yaml:"security"`

	// ThreadPool 线程池配置
	ThreadPool *ThreadPoolConfig `mapstructure:"thread_pool" json:"thread_pool" yaml:"thread_pool"`

	// TLS TLS配置
	TLS *TLSConfig `mapstructure:"tls" json:"tls" yaml:"tls"`
}

// NewMainConfig 创建主配置
func NewMainConfig() *MainConfig {
	return &MainConfig{
		App:        NewApplicationConfig(),
		Server:     NewServerConfig(),
		Log:        NewLogConfig(),
		DB:         NewDBConfig(),
		Redis:      NewRedisCfg(),
		JWT:        NewJWTConfig(),
		Captcha:    NewCaptchaConfig(),
		Filter:     NewFilterConfig(),
		I18n:       NewI18nConfig(),
		Gorm:       NewGormConfig(),
		Resources:  NewResourcesConfig(),
		Security:   NewSecurityConfig(),
		ThreadPool: NewThreadPoolConfig(),
		TLS:        NewTLSConfig(),
	}
}

// GetJWTSecret 获取JWT密钥
func GetJWTSecret() string {
	secret := getEnvOrDefault("JWT_SECRET", "wosm-secret-key-2024-default-value")
	if len(secret) < 32 {
		secret = "wosm-secret-key-2024-default-value-extended"
	}
	return secret
}

// GetJWTExpire 获取JWT过期时间
func GetJWTExpire() int {
	return getEnvIntOrDefault("JWT_EXPIRE", 86400)
}

// 辅助函数
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// Validate 验证配置
func (c *MainConfig) Validate() error {
	// 验证必要的配置项
	if c.App == nil {
		c.App = NewApplicationConfig()
	}

	if c.Server == nil {
		c.Server = NewServerConfig()
	}

	if c.DB == nil {
		c.DB = NewDBConfig()
	}

	if c.Redis == nil {
		c.Redis = NewRedisCfg()
	}

	if c.JWT == nil {
		c.JWT = NewJWTConfig()
	}

	if c.Log == nil {
		c.Log = NewLogConfig()
	}

	if c.Captcha == nil {
		c.Captcha = NewCaptchaConfig()
	}

	if c.Filter == nil {
		c.Filter = NewFilterConfig()
	}

	if c.I18n == nil {
		c.I18n = NewI18nConfig()
	}

	if c.Gorm == nil {
		c.Gorm = NewGormConfig()
	}

	if c.Resources == nil {
		c.Resources = NewResourcesConfig()
	}

	if c.Security == nil {
		c.Security = NewSecurityConfig()
	}

	if c.ThreadPool == nil {
		c.ThreadPool = NewThreadPoolConfig()
	}

	if c.TLS == nil {
		c.TLS = NewTLSConfig()
	}

	return nil
}

// LogConfig 日志配置
type LogConfig struct {
	// 级别
	Level string `mapstructure:"level" json:"level"`

	// 格式
	Format string `mapstructure:"format" json:"format"`

	// 输出
	Output string `mapstructure:"output" json:"output"`

	// 文件路径
	FilePath string `mapstructure:"file_path" json:"file_path"`

	// 最大大小（MB）
	MaxSize int `mapstructure:"max_size" json:"max_size"`

	// 最大备份数
	MaxBackups int `mapstructure:"max_backups" json:"max_backups"`

	// 最大保存天数
	MaxAge int `mapstructure:"max_age" json:"max_age"`

	// 是否压缩
	Compress bool `mapstructure:"compress" json:"compress"`

	// 是否输出调用者信息
	Caller bool `mapstructure:"caller" json:"caller"`
}

// NewLogConfig 创建新的日志配置
func NewLogConfig() *LogConfig {
	return &LogConfig{
		Level:      "info",
		Format:     "json",
		Output:     "console",
		FilePath:   "logs/app.log",
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
		Caller:     true,
	}
}

// UploadConfig 上传配置
type UploadConfig struct {
	// 上传路径
	Path string `mapstructure:"path" json:"path"`

	// 允许的文件类型
	AllowedTypes string `mapstructure:"allowed_types" json:"allowed_types"`

	// 最大文件大小（MB）
	MaxSize int `mapstructure:"max_size" json:"max_size"`

	// 是否使用原始文件名
	UseOriginalName bool `mapstructure:"use_original_name" json:"use_original_name"`
}

// NewUploadConfig 创建新的上传配置
func NewUploadConfig() *UploadConfig {
	return &UploadConfig{
		Path:            "upload",
		AllowedTypes:    "jpg,jpeg,png,gif,doc,docx,xls,xlsx,ppt,pptx,pdf,txt,zip,rar",
		MaxSize:         50,
		UseOriginalName: false,
	}
}

// GenConfig 代码生成配置
type GenConfig struct {
	// 作者
	Author string `mapstructure:"author" json:"author"`

	// 默认生成包路径
	PackageName string `mapstructure:"package_name" json:"package_name"`

	// 自动去除表前缀
	AutoRemovePre bool `mapstructure:"auto_remove_pre" json:"auto_remove_pre"`

	// 表前缀
	TablePrefix string `mapstructure:"table_prefix" json:"table_prefix"`
}

// NewGenConfig 创建新的代码生成配置
func NewGenConfig() *GenConfig {
	return &GenConfig{
		Author:        "ruoyi",
		PackageName:   "com.ruoyi.system",
		AutoRemovePre: true,
		TablePrefix:   "sys_",
	}
}
