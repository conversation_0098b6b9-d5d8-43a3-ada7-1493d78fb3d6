package config

import (
	"strings"
	"time"
)

// GormConfig GORM配置类，对应Java版本的MyBatisConfig
type GormConfig struct {
	// 实体类包路径
	TypeAliasesPackage string `mapstructure:"type_aliases_package" json:"type_aliases_package"`

	// 映射文件路径
	MapperLocations string `mapstructure:"mapper_locations" json:"mapper_locations"`

	// 配置文件路径
	ConfigLocation string `mapstructure:"config_location" json:"config_location"`

	// 是否开启驼峰命名转换
	MapUnderscoreToCamelCase bool `mapstructure:"map_underscore_to_camel_case" json:"map_underscore_to_camel_case"`

	// 是否开启缓存
	CacheEnabled bool `mapstructure:"cache_enabled" json:"cache_enabled"`

	// 是否开启懒加载
	LazyLoadingEnabled bool `mapstructure:"lazy_loading_enabled" json:"lazy_loading_enabled"`

	// 是否开启多结果集支持
	MultipleResultSetsEnabled bool `mapstructure:"multiple_result_sets_enabled" json:"multiple_result_sets_enabled"`

	// 是否使用列标签
	UseColumnLabel bool `mapstructure:"use_column_label" json:"use_column_label"`

	// 是否使用生成的键
	UseGeneratedKeys bool `mapstructure:"use_generated_keys" json:"use_generated_keys"`

	// 默认执行器类型
	DefaultExecutorType string `mapstructure:"default_executor_type" json:"default_executor_type"`

	// 默认语句超时时间（秒）
	DefaultStatementTimeout int `mapstructure:"default_statement_timeout" json:"default_statement_timeout"`

	// 是否开启日志
	LogEnabled bool `mapstructure:"log_enabled" json:"log_enabled"`

	// 慢SQL阈值（毫秒）
	SlowSqlThreshold int64 `mapstructure:"slow_sql_threshold" json:"slow_sql_threshold"`
}

// NewGormConfig 创建新的GORM配置
func NewGormConfig() *GormConfig {
	return &GormConfig{
		TypeAliasesPackage:        "com.ruoyi.project.**.domain",
		MapperLocations:           "classpath:mybatis/**/*Mapper.xml",
		ConfigLocation:            "",
		MapUnderscoreToCamelCase:  true,
		CacheEnabled:              true,
		LazyLoadingEnabled:        false,
		MultipleResultSetsEnabled: true,
		UseColumnLabel:            true,
		UseGeneratedKeys:          true,
		DefaultExecutorType:       "simple",
		DefaultStatementTimeout:   25,
		LogEnabled:                true,
		SlowSqlThreshold:          300,
	}
}

// GetTypeAliasesPackage 获取实体类包路径数组
func (c *GormConfig) GetTypeAliasesPackage() []string {
	if c.TypeAliasesPackage == "" {
		return []string{}
	}
	return strings.Split(c.TypeAliasesPackage, ",")
}

// GetMapperLocations 获取映射文件路径数组
func (c *GormConfig) GetMapperLocations() []string {
	if c.MapperLocations == "" {
		return []string{}
	}
	return strings.Split(c.MapperLocations, ",")
}

// GetDefaultStatementTimeout 获取默认语句超时时间
func (c *GormConfig) GetDefaultStatementTimeout() time.Duration {
	return time.Duration(c.DefaultStatementTimeout) * time.Second
}

// GetSlowSqlThreshold 获取慢SQL阈值
func (c *GormConfig) GetSlowSqlThreshold() time.Duration {
	return time.Duration(c.SlowSqlThreshold) * time.Millisecond
}

// IsLogEnabled 是否开启日志
func (c *GormConfig) IsLogEnabled() bool {
	return c.LogEnabled
}

// IsCacheEnabled 是否开启缓存
func (c *GormConfig) IsCacheEnabled() bool {
	return c.CacheEnabled
}

// GetGormConfig 获取GORM配置
func (c *GormConfig) GetGormConfig() map[string]interface{} {
	config := make(map[string]interface{})

	// 设置GORM配置
	config["PrepareStmt"] = true
	config["SkipDefaultTransaction"] = true
	config["AllowGlobalUpdate"] = false
	config["QueryFields"] = true
	config["CreateBatchSize"] = 1000

	// 设置命名策略
	if c.MapUnderscoreToCamelCase {
		// 在GORM中，默认就是驼峰命名转下划线
		config["NamingStrategy"] = map[string]interface{}{
			"SingularTable": true, // 使用单数表名
		}
	}

	// 设置日志
	if c.IsLogEnabled() {
		config["Logger"] = map[string]interface{}{
			"SlowThreshold": c.GetSlowSqlThreshold(),
			"LogLevel":      4, // Info
		}
	}

	return config
}
