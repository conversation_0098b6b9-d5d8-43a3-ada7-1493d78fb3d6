package config

import (
	"fmt"
	"time"
)

// RedisCfg Redis配置
type RedisCfg struct {
	// Host 主机地址
	Host string `mapstructure:"host" json:"host" yaml:"host"`

	// Port 端口号
	Port int `mapstructure:"port" json:"port" yaml:"port"`

	// Password 密码
	Password string `mapstructure:"password" json:"password" yaml:"password"`

	// DB 数据库
	DB int `mapstructure:"db" json:"db" yaml:"db"`

	// PoolSize 连接池大小
	PoolSize int `mapstructure:"pool_size" json:"pool_size" yaml:"pool_size"`

	// MinIdleConns 最小空闲连接数
	MinIdleConns int `mapstructure:"min_idle_conns" json:"min_idle_conns" yaml:"min_idle_conns"`

	// IdleTimeout 空闲超时时间（秒）
	IdleTimeout int `mapstructure:"idle_timeout" json:"idle_timeout" yaml:"idle_timeout"`

	// DialTimeout 连接超时时间（秒）
	DialTimeout int `mapstructure:"dial_timeout" json:"dial_timeout" yaml:"dial_timeout"`

	// ReadTimeout 读取超时时间（秒）
	ReadTimeout int `mapstructure:"read_timeout" json:"read_timeout" yaml:"read_timeout"`

	// WriteTimeout 写入超时时间（秒）
	WriteTimeout int `mapstructure:"write_timeout" json:"write_timeout" yaml:"write_timeout"`

	// EnableCluster 是否启用集群
	EnableCluster bool `mapstructure:"enable_cluster" json:"enable_cluster" yaml:"enable_cluster"`

	// ClusterNodes 集群节点
	ClusterNodes []string `mapstructure:"cluster_nodes" json:"cluster_nodes" yaml:"cluster_nodes"`
}

// NewRedisCfg 创建Redis配置
func NewRedisCfg() *RedisCfg {
	return &RedisCfg{
		Host:          "localhost",
		Port:          6379,
		Password:      "",
		DB:            0,
		PoolSize:      100,
		MinIdleConns:  10,
		IdleTimeout:   300,
		DialTimeout:   5,
		ReadTimeout:   3,
		WriteTimeout:  3,
		EnableCluster: false,
		ClusterNodes:  []string{},
	}
}

// GetAddr 获取地址
func (c *RedisCfg) GetAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetIdleTimeout 获取空闲超时时间
func (c *RedisCfg) GetIdleTimeout() time.Duration {
	return time.Duration(c.IdleTimeout) * time.Second
}

// GetDialTimeout 获取连接超时时间
func (c *RedisCfg) GetDialTimeout() time.Duration {
	return time.Duration(c.DialTimeout) * time.Second
}

// GetReadTimeout 获取读取超时时间
func (c *RedisCfg) GetReadTimeout() time.Duration {
	return time.Duration(c.ReadTimeout) * time.Second
}

// GetWriteTimeout 获取写入超时时间
func (c *RedisCfg) GetWriteTimeout() time.Duration {
	return time.Duration(c.WriteTimeout) * time.Second
}

// IsClusterEnabled 是否启用集群
func (c *RedisCfg) IsClusterEnabled() bool {
	return c.EnableCluster && len(c.ClusterNodes) > 0
}
