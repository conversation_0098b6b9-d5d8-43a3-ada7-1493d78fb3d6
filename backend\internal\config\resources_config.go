package config

import (
	"strings"
)

// ResourcesConfig 资源配置类，对应Java版本的ResourcesConfig
type ResourcesConfig struct {
	// 静态资源路径
	StaticLocations string `mapstructure:"static_locations" json:"static_locations"`

	// 静态资源处理链
	StaticPathPattern string `mapstructure:"static_path_pattern" json:"static_path_pattern"`

	// 是否开启默认资源处理
	AddDefaultResourceHandlers bool `mapstructure:"add_default_resource_handlers" json:"add_default_resource_handlers"`

	// 是否使用内容协商
	FavorPathExtension bool `mapstructure:"favor_path_extension" json:"favor_path_extension"`

	// 是否使用请求参数
	FavorParameter bool `mapstructure:"favor_parameter" json:"favor_parameter"`

	// 请求参数名
	ParameterName string `mapstructure:"parameter_name" json:"parameter_name"`

	// 是否忽略Accept头
	IgnoreAcceptHeader bool `mapstructure:"ignore_accept_header" json:"ignore_accept_header"`

	// 默认内容类型
	DefaultContentType string `mapstructure:"default_content_type" json:"default_content_type"`

	// 媒体类型
	MediaTypes string `mapstructure:"media_types" json:"media_types"`

	// 是否开启CORS
	CorsEnabled bool `mapstructure:"cors_enabled" json:"cors_enabled"`

	// CORS允许的来源
	CorsAllowedOrigins string `mapstructure:"cors_allowed_origins" json:"cors_allowed_origins"`

	// CORS允许的方法
	CorsAllowedMethods string `mapstructure:"cors_allowed_methods" json:"cors_allowed_methods"`

	// CORS允许的头
	CorsAllowedHeaders string `mapstructure:"cors_allowed_headers" json:"cors_allowed_headers"`

	// CORS允许的凭证
	CorsAllowCredentials bool `mapstructure:"cors_allow_credentials" json:"cors_allow_credentials"`

	// CORS最大时间（秒）
	CorsMaxAge int `mapstructure:"cors_max_age" json:"cors_max_age"`
}

// NewResourcesConfig 创建新的资源配置
func NewResourcesConfig() *ResourcesConfig {
	return &ResourcesConfig{
		StaticLocations:            "classpath:/static/,classpath:/public/,file:./static/",
		StaticPathPattern:          "/**",
		AddDefaultResourceHandlers: true,
		FavorPathExtension:         true,
		FavorParameter:             true,
		ParameterName:              "format",
		IgnoreAcceptHeader:         false,
		DefaultContentType:         "application/json",
		MediaTypes:                 "json=application/json,xml=application/xml",
		CorsEnabled:                true,
		CorsAllowedOrigins:         "*",
		CorsAllowedMethods:         "GET,POST,PUT,DELETE,OPTIONS",
		CorsAllowedHeaders:         "*",
		CorsAllowCredentials:       true,
		CorsMaxAge:                 1800,
	}
}

// GetStaticLocations 获取静态资源路径数组
func (c *ResourcesConfig) GetStaticLocations() []string {
	if c.StaticLocations == "" {
		return []string{"classpath:/static/", "classpath:/public/", "file:./static/"}
	}
	return strings.Split(c.StaticLocations, ",")
}

// GetStaticPathPattern 获取静态资源处理链
func (c *ResourcesConfig) GetStaticPathPattern() string {
	if c.StaticPathPattern == "" {
		return "/**"
	}
	return c.StaticPathPattern
}

// IsAddDefaultResourceHandlers 是否开启默认资源处理
func (c *ResourcesConfig) IsAddDefaultResourceHandlers() bool {
	return c.AddDefaultResourceHandlers
}

// IsFavorPathExtension 是否使用内容协商
func (c *ResourcesConfig) IsFavorPathExtension() bool {
	return c.FavorPathExtension
}

// IsFavorParameter 是否使用请求参数
func (c *ResourcesConfig) IsFavorParameter() bool {
	return c.FavorParameter
}

// GetParameterName 获取请求参数名
func (c *ResourcesConfig) GetParameterName() string {
	if c.ParameterName == "" {
		return "format"
	}
	return c.ParameterName
}

// IsIgnoreAcceptHeader 是否忽略Accept头
func (c *ResourcesConfig) IsIgnoreAcceptHeader() bool {
	return c.IgnoreAcceptHeader
}

// GetDefaultContentType 获取默认内容类型
func (c *ResourcesConfig) GetDefaultContentType() string {
	if c.DefaultContentType == "" {
		return "application/json"
	}
	return c.DefaultContentType
}

// GetMediaTypes 获取媒体类型映射
func (c *ResourcesConfig) GetMediaTypes() map[string]string {
	mediaTypes := make(map[string]string)
	if c.MediaTypes == "" {
		mediaTypes["json"] = "application/json"
		mediaTypes["xml"] = "application/xml"
		return mediaTypes
	}

	pairs := strings.Split(c.MediaTypes, ",")
	for _, pair := range pairs {
		kv := strings.Split(pair, "=")
		if len(kv) == 2 {
			mediaTypes[kv[0]] = kv[1]
		}
	}

	return mediaTypes
}

// IsCorsEnabled 是否开启CORS
func (c *ResourcesConfig) IsCorsEnabled() bool {
	return c.CorsEnabled
}

// GetCorsAllowedOrigins 获取CORS允许的来源数组
func (c *ResourcesConfig) GetCorsAllowedOrigins() []string {
	if c.CorsAllowedOrigins == "" {
		return []string{"*"}
	}
	return strings.Split(c.CorsAllowedOrigins, ",")
}

// GetCorsAllowedMethods 获取CORS允许的方法数组
func (c *ResourcesConfig) GetCorsAllowedMethods() []string {
	if c.CorsAllowedMethods == "" {
		return []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	}
	return strings.Split(c.CorsAllowedMethods, ",")
}

// GetCorsAllowedHeaders 获取CORS允许的头数组
func (c *ResourcesConfig) GetCorsAllowedHeaders() []string {
	if c.CorsAllowedHeaders == "" {
		return []string{"*"}
	}
	return strings.Split(c.CorsAllowedHeaders, ",")
}

// IsCorsAllowCredentials 是否允许CORS凭证
func (c *ResourcesConfig) IsCorsAllowCredentials() bool {
	return c.CorsAllowCredentials
}

// GetCorsMaxAge 获取CORS最大时间（秒）
func (c *ResourcesConfig) GetCorsMaxAge() int {
	if c.CorsMaxAge <= 0 {
		return 1800
	}
	return c.CorsMaxAge
}

// CorsFilter 获取CORS过滤器
func (c *ResourcesConfig) CorsFilter() interface{} {
	// 返回CORS中间件函数
	return func(next interface{}) interface{} {
		// 实现CORS过滤逻辑
		return next
	}
}
