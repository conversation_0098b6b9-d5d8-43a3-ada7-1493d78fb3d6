package config

import (
	"strings"
	"time"
)

// SecurityConfig 安全配置类，对应Java版本的SecurityConfig
type SecurityConfig struct {
	// 是否开启XSS过滤
	XssEnabled bool `mapstructure:"xss_enabled" json:"xss_enabled"`

	// XSS排除路径
	XssExcludes string `mapstructure:"xss_excludes" json:"xss_excludes"`

	// 是否开启CSRF防护
	CsrfEnabled bool `mapstructure:"csrf_enabled" json:"csrf_enabled"`

	// 是否允许同一账号多处登录
	AllowMultiLogin bool `mapstructure:"allow_multi_login" json:"allow_multi_login"`

	// 密码最大错误次数
	MaxRetryCount int `mapstructure:"max_retry_count" json:"max_retry_count"`

	// 密码锁定时间（分钟）
	LockTime int `mapstructure:"lock_time" json:"lock_time"`

	// 是否允许注册
	AllowRegister bool `mapstructure:"allow_register" json:"allow_register"`

	// 密码强度正则表达式
	PasswordPattern string `mapstructure:"password_pattern" json:"password_pattern"`

	// 匿名访问URL
	AnonymousUrls string `mapstructure:"anonymous_urls" json:"anonymous_urls"`

	// 登录URL
	LoginUrl string `mapstructure:"login_url" json:"login_url"`

	// 登出URL
	LogoutUrl string `mapstructure:"logout_url" json:"logout_url"`

	// 登录成功URL
	LoginSuccessUrl string `mapstructure:"login_success_url" json:"login_success_url"`

	// 登出成功URL
	LogoutSuccessUrl string `mapstructure:"logout_success_url" json:"logout_success_url"`

	// 未授权URL
	UnauthorizedUrl string `mapstructure:"unauthorized_url" json:"unauthorized_url"`

	// 会话超时（分钟）
	SessionTimeout int `mapstructure:"session_timeout" json:"session_timeout"`

	// 记住我超时（天）
	RememberMeTimeout int `mapstructure:"remember_me_timeout" json:"remember_me_timeout"`

	// 是否开启记住我
	RememberMeEnabled bool `mapstructure:"remember_me_enabled" json:"remember_me_enabled"`
}

// NewSecurityConfig 创建新的安全配置
func NewSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		XssEnabled:        true,
		XssExcludes:       "/system/notice/*",
		CsrfEnabled:       false,
		AllowMultiLogin:   false,
		MaxRetryCount:     5,
		LockTime:          10,
		AllowRegister:     true,
		PasswordPattern:   "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$",
		AnonymousUrls:     "/login,/register,/captcha/**,/swagger-ui/**,/v3/api-docs/**",
		LoginUrl:          "/login",
		LogoutUrl:         "/logout",
		LoginSuccessUrl:   "/",
		LogoutSuccessUrl:  "/login",
		UnauthorizedUrl:   "/unauthorized",
		SessionTimeout:    30,
		RememberMeTimeout: 7,
		RememberMeEnabled: true,
	}
}

// IsXssEnabled 是否开启XSS过滤
func (c *SecurityConfig) IsXssEnabled() bool {
	return c.XssEnabled
}

// GetXssExcludes 获取XSS排除路径数组
func (c *SecurityConfig) GetXssExcludes() []string {
	if c.XssExcludes == "" {
		return []string{}
	}
	return strings.Split(c.XssExcludes, ",")
}

// IsCsrfEnabled 是否开启CSRF防护
func (c *SecurityConfig) IsCsrfEnabled() bool {
	return c.CsrfEnabled
}

// IsAllowMultiLogin 是否允许同一账号多处登录
func (c *SecurityConfig) IsAllowMultiLogin() bool {
	return c.AllowMultiLogin
}

// GetMaxRetryCount 获取密码最大错误次数
func (c *SecurityConfig) GetMaxRetryCount() int {
	if c.MaxRetryCount <= 0 {
		return 5
	}
	return c.MaxRetryCount
}

// GetLockTime 获取密码锁定时间
func (c *SecurityConfig) GetLockTime() time.Duration {
	return time.Duration(c.LockTime) * time.Minute
}

// IsAllowRegister 是否允许注册
func (c *SecurityConfig) IsAllowRegister() bool {
	return c.AllowRegister
}

// GetPasswordPattern 获取密码强度正则表达式
func (c *SecurityConfig) GetPasswordPattern() string {
	if c.PasswordPattern == "" {
		return "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$"
	}
	return c.PasswordPattern
}

// GetAnonymousUrls 获取匿名访问URL数组
func (c *SecurityConfig) GetAnonymousUrls() []string {
	if c.AnonymousUrls == "" {
		return []string{"/login", "/register", "/captcha/**"}
	}
	return strings.Split(c.AnonymousUrls, ",")
}

// GetLoginUrl 获取登录URL
func (c *SecurityConfig) GetLoginUrl() string {
	if c.LoginUrl == "" {
		return "/login"
	}
	return c.LoginUrl
}

// GetLogoutUrl 获取登出URL
func (c *SecurityConfig) GetLogoutUrl() string {
	if c.LogoutUrl == "" {
		return "/logout"
	}
	return c.LogoutUrl
}

// GetLoginSuccessUrl 获取登录成功URL
func (c *SecurityConfig) GetLoginSuccessUrl() string {
	if c.LoginSuccessUrl == "" {
		return "/"
	}
	return c.LoginSuccessUrl
}

// GetLogoutSuccessUrl 获取登出成功URL
func (c *SecurityConfig) GetLogoutSuccessUrl() string {
	if c.LogoutSuccessUrl == "" {
		return "/login"
	}
	return c.LogoutSuccessUrl
}

// GetUnauthorizedUrl 获取未授权URL
func (c *SecurityConfig) GetUnauthorizedUrl() string {
	if c.UnauthorizedUrl == "" {
		return "/unauthorized"
	}
	return c.UnauthorizedUrl
}

// GetSessionTimeout 获取会话超时时间
func (c *SecurityConfig) GetSessionTimeout() time.Duration {
	return time.Duration(c.SessionTimeout) * time.Minute
}

// GetRememberMeTimeout 获取记住我超时时间
func (c *SecurityConfig) GetRememberMeTimeout() time.Duration {
	return time.Duration(c.RememberMeTimeout) * 24 * time.Hour
}

// IsRememberMeEnabled 是否开启记住我
func (c *SecurityConfig) IsRememberMeEnabled() bool {
	return c.RememberMeEnabled
}

// BCryptPasswordEncoder 获取BCrypt密码编码器
func (c *SecurityConfig) BCryptPasswordEncoder() interface{} {
	// 返回BCrypt密码编码器
	return nil
}
