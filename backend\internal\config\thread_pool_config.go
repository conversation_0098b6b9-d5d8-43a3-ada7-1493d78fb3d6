package config

import (
	"time"
)

// ThreadPoolConfig 线程池配置类，对应Java版本的ThreadPoolConfig
type ThreadPoolConfig struct {
	// 核心线程数
	CorePoolSize int `mapstructure:"core_pool_size" json:"core_pool_size"`

	// 最大线程数
	MaxPoolSize int `mapstructure:"max_pool_size" json:"max_pool_size"`

	// 队列容量
	QueueCapacity int `mapstructure:"queue_capacity" json:"queue_capacity"`

	// 线程保持活动时间（秒）
	KeepAliveSeconds int `mapstructure:"keep_alive_seconds" json:"keep_alive_seconds"`

	// 线程名称前缀
	ThreadNamePrefix string `mapstructure:"thread_name_prefix" json:"thread_name_prefix"`

	// 是否允许核心线程超时
	AllowCoreThreadTimeOut bool `mapstructure:"allow_core_thread_time_out" json:"allow_core_thread_time_out"`

	// 是否等待任务完成
	WaitForTasksToComplete bool `mapstructure:"wait_for_tasks_to_complete" json:"wait_for_tasks_to_complete"`

	// 拒绝策略
	RejectionPolicy string `mapstructure:"rejection_policy" json:"rejection_policy"`
}

// NewThreadPoolConfig 创建新的线程池配置
func NewThreadPoolConfig() *ThreadPoolConfig {
	return &ThreadPoolConfig{
		CorePoolSize:           10,
		MaxPoolSize:            20,
		QueueCapacity:          100,
		KeepAliveSeconds:       60,
		ThreadNamePrefix:       "async-",
		AllowCoreThreadTimeOut: false,
		WaitForTasksToComplete: true,
		RejectionPolicy:        "CALLER_RUNS",
	}
}

// GetCorePoolSize 获取核心线程数
func (c *ThreadPoolConfig) GetCorePoolSize() int {
	if c.CorePoolSize <= 0 {
		// 默认为CPU核心数
		return 10
	}
	return c.CorePoolSize
}

// GetMaxPoolSize 获取最大线程数
func (c *ThreadPoolConfig) GetMaxPoolSize() int {
	if c.MaxPoolSize <= 0 {
		// 默认为CPU核心数的2倍
		return 20
	}
	return c.MaxPoolSize
}

// GetQueueCapacity 获取队列容量
func (c *ThreadPoolConfig) GetQueueCapacity() int {
	if c.QueueCapacity <= 0 {
		return 100
	}
	return c.QueueCapacity
}

// GetKeepAliveTime 获取线程保持活动时间
func (c *ThreadPoolConfig) GetKeepAliveTime() time.Duration {
	if c.KeepAliveSeconds <= 0 {
		return 60 * time.Second
	}
	return time.Duration(c.KeepAliveSeconds) * time.Second
}

// GetThreadNamePrefix 获取线程名称前缀
func (c *ThreadPoolConfig) GetThreadNamePrefix() string {
	if c.ThreadNamePrefix == "" {
		return "async-"
	}
	return c.ThreadNamePrefix
}

// IsAllowCoreThreadTimeOut 是否允许核心线程超时
func (c *ThreadPoolConfig) IsAllowCoreThreadTimeOut() bool {
	return c.AllowCoreThreadTimeOut
}

// IsWaitForTasksToComplete 是否等待任务完成
func (c *ThreadPoolConfig) IsWaitForTasksToComplete() bool {
	return c.WaitForTasksToComplete
}

// GetRejectionPolicy 获取拒绝策略
func (c *ThreadPoolConfig) GetRejectionPolicy() string {
	if c.RejectionPolicy == "" {
		return "CALLER_RUNS"
	}
	return c.RejectionPolicy
}

// ThreadPoolTaskExecutor 获取线程池任务执行器
func (c *ThreadPoolConfig) ThreadPoolTaskExecutor() interface{} {
	// 在Go中，我们通常使用goroutine池而不是线程池
	// 这里返回一个函数，可以创建goroutine池
	return nil
}

// ScheduledExecutorService 获取定时任务执行器
func (c *ThreadPoolConfig) ScheduledExecutorService() interface{} {
	// 在Go中，我们通常使用ticker或timer而不是定时任务执行器
	// 这里返回一个函数，可以创建定时任务执行器
	return nil
}
