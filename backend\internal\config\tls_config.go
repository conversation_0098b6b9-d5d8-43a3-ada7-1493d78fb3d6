package config

import (
	"crypto/tls"
)

// TLSConfig TLS配置
type TLSConfig struct {
	// Enable 是否启用TLS
	Enable bool `mapstructure:"enable" json:"enable" yaml:"enable"`

	// CertFile 证书文件
	CertFile string `mapstructure:"cert_file" json:"cert_file" yaml:"cert_file"`

	// KeyFile 密钥文件
	KeyFile string `mapstructure:"key_file" json:"key_file" yaml:"key_file"`

	// MinVersion 最低TLS版本
	MinVersion string `mapstructure:"min_version" json:"min_version" yaml:"min_version"`

	// MaxVersion 最高TLS版本
	MaxVersion string `mapstructure:"max_version" json:"max_version" yaml:"max_version"`

	// CipherSuites 加密套件
	CipherSuites []string `mapstructure:"cipher_suites" json:"cipher_suites" yaml:"cipher_suites"`

	// PreferServerCipherSuites 优先使用服务器加密套件
	PreferServerCipherSuites bool `mapstructure:"prefer_server_cipher_suites" json:"prefer_server_cipher_suites" yaml:"prefer_server_cipher_suites"`

	// CurvePreferences 曲线偏好
	CurvePreferences []string `mapstructure:"curve_preferences" json:"curve_preferences" yaml:"curve_preferences"`

	// ClientAuth 客户端认证
	ClientAuth string `mapstructure:"client_auth" json:"client_auth" yaml:"client_auth"`

	// ClientCAs 客户端CA证书
	ClientCAs []string `mapstructure:"client_cas" json:"client_cas" yaml:"client_cas"`

	// EnableHTTP2 是否启用HTTP/2
	EnableHTTP2 bool `mapstructure:"enable_http2" json:"enable_http2" yaml:"enable_http2"`
}

// NewTLSConfig 创建TLS配置
func NewTLSConfig() *TLSConfig {
	return &TLSConfig{
		Enable:                   false,
		CertFile:                 "cert.pem",
		KeyFile:                  "key.pem",
		MinVersion:               "TLS1.2",
		MaxVersion:               "TLS1.3",
		CipherSuites:             []string{},
		PreferServerCipherSuites: true,
		CurvePreferences:         []string{},
		ClientAuth:               "NoClientCert",
		ClientCAs:                []string{},
		EnableHTTP2:              true,
	}
}

// IsEnabled 是否启用TLS
func (c *TLSConfig) IsEnabled() bool {
	return c.Enable && c.CertFile != "" && c.KeyFile != ""
}

// GetMinVersionUint16 获取最低TLS版本（uint16）
func (c *TLSConfig) GetMinVersionUint16() uint16 {
	switch c.MinVersion {
	case "TLS1.0":
		return tls.VersionTLS10
	case "TLS1.1":
		return tls.VersionTLS11
	case "TLS1.2":
		return tls.VersionTLS12
	case "TLS1.3":
		return tls.VersionTLS13
	default:
		return tls.VersionTLS12
	}
}

// GetMaxVersionUint16 获取最高TLS版本（uint16）
func (c *TLSConfig) GetMaxVersionUint16() uint16 {
	switch c.MaxVersion {
	case "TLS1.0":
		return tls.VersionTLS10
	case "TLS1.1":
		return tls.VersionTLS11
	case "TLS1.2":
		return tls.VersionTLS12
	case "TLS1.3":
		return tls.VersionTLS13
	default:
		return tls.VersionTLS13
	}
}

// GetMinVersion 获取最低TLS版本
func (c *TLSConfig) GetMinVersion() string {
	if c.MinVersion == "" {
		return "TLS1.2"
	}
	return c.MinVersion
}

// GetMaxVersion 获取最高TLS版本
func (c *TLSConfig) GetMaxVersion() string {
	if c.MaxVersion == "" {
		return "TLS1.3"
	}
	return c.MaxVersion
}

// GetClientAuthType 获取客户端认证类型
func (c *TLSConfig) GetClientAuthType() tls.ClientAuthType {
	switch c.ClientAuth {
	case "NoClientCert":
		return tls.NoClientCert
	case "RequestClientCert":
		return tls.RequestClientCert
	case "RequireAnyClientCert":
		return tls.RequireAnyClientCert
	case "VerifyClientCertIfGiven":
		return tls.VerifyClientCertIfGiven
	case "RequireAndVerifyClientCert":
		return tls.RequireAndVerifyClientCert
	default:
		return tls.NoClientCert
	}
}

// IsHTTP2Enabled 是否启用HTTP/2
func (c *TLSConfig) IsHTTP2Enabled() bool {
	return c.EnableHTTP2
}

// GetHttpsProtocols 获取HTTPS协议数组
func (c *TLSConfig) GetHttpsProtocols() []string {
	return []string{"TLSv1.2", "TLSv1.3"}
}

// IsEnableHTTPS 是否开启HTTPS
func (c *TLSConfig) IsEnableHTTPS() bool {
	return c.Enable
}

// GetSSLCertPath 获取SSL证书路径
func (c *TLSConfig) GetSSLCertPath() string {
	return c.CertFile
}

// GetSSLKeyPath 获取SSL密钥路径
func (c *TLSConfig) GetSSLKeyPath() string {
	return c.KeyFile
}

// IsClientAuth 是否开启客户端认证
func (c *TLSConfig) IsClientAuth() bool {
	return c.ClientAuth != "NoClientCert"
}

// GetClientCAPath 获取客户端CA证书路径
func (c *TLSConfig) GetClientCAPath() string {
	if len(c.ClientCAs) > 0 {
		return c.ClientCAs[0]
	}
	return ""
}

// GetCipherSuites 获取密码套件数组
func (c *TLSConfig) GetCipherSuites() []uint16 {
	// 默认密码套件
	defaultCipherSuites := []uint16{
		tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
		tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
		tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
		tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
		tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
		tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
	}

	if len(c.CipherSuites) == 0 {
		return defaultCipherSuites
	}

	// 解析自定义密码套件
	// 实际应用中需要根据字符串映射到对应的密码套件
	return defaultCipherSuites
}

// GetTLSConfig 获取TLS配置
func (c *TLSConfig) GetTLSConfig() *tls.Config {
	return &tls.Config{
		MinVersion:   c.GetMinVersionUint16(),
		MaxVersion:   c.GetMaxVersionUint16(),
		CipherSuites: c.GetCipherSuites(),
	}
}

// Init 初始化TLS配置
func (c *TLSConfig) Init() {
	// 在Go中，我们通常在服务器启动时设置TLS配置
	// 这里可以进行一些初始化操作
}
