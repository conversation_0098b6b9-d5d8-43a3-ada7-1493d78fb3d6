package GenController

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// GenController Controller
type GenController struct {
	// TODO: Add service dependencies
}

// RegisterGenController Register routes
func RegisterGenController(r *gin.RouterGroup) {
	controller := &%!s(MISSING){}
	
	r.GET("/gen_list", controller.GenList)
	r.GET("/get_info", controller.GetInfo)
	r.GET("/data_list", controller.DataList)
	r.GET("/column_list", controller.ColumnList)
	r.POST("/import_table_save", controller.ImportTableSave)
	r.POST("/create_table_save", controller.CreateTableSave)
	r.PUT("/edit_save", controller.EditSave)
	r.DELETE("/remove", controller.Remove)
	r.GET("/preview", controller.Preview)
	r.GET("/download", controller.Download)
	r.GET("/gen_code", controller.GenCode)
	r.GET("/synch_db", controller.SynchDb)
	r.GET("/batch_gen_code", controller.BatchGenCode)
}

// GenList Handle request
func (c *GenController) GenList(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// GetInfo Handle request
func (c *GenController) GetInfo(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// DataList Handle request
func (c *GenController) DataList(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// ColumnList Handle request
func (c *GenController) ColumnList(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// ImportTableSave Handle request
func (c *GenController) ImportTableSave(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// CreateTableSave Handle request
func (c *GenController) CreateTableSave(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// EditSave Handle request
func (c *GenController) EditSave(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Remove Handle request
func (c *GenController) Remove(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Preview Handle request
func (c *GenController) Preview(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Download Handle request
func (c *GenController) Download(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// GenCode Handle request
func (c *GenController) GenCode(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// SynchDb Handle request
func (c *GenController) SynchDb(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// BatchGenCode Handle request
func (c *GenController) BatchGenCode(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

