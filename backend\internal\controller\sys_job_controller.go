package SysJobController

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// SysJobController Controller
type SysJobController struct {
	// TODO: Add service dependencies
}

// RegisterSysJobController Register routes
func RegisterSysJobController(r *gin.RouterGroup) {
	controller := &%!s(MISSING){}
	
	r.GET("/list", controller.List)
	r.POST("/export", controller.Export)
	r.GET("/get_info", controller.GetInfo)
	r.POST("/add", controller.Add)
	r.PUT("/edit", controller.Edit)
	r.PUT("/change_status", controller.ChangeStatus)
	r.PUT("/run", controller.Run)
	r.DELETE("/remove", controller.Remove)
}

// List Handle request
func (c *SysJobController) List(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Export Handle request
func (c *SysJobController) Export(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// GetInfo Handle request
func (c *SysJobController) GetInfo(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Add Handle request
func (c *SysJobController) Add(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Edit Handle request
func (c *SysJobController) Edit(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// ChangeStatus Handle request
func (c *SysJobController) ChangeStatus(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Run Handle request
func (c *SysJobController) Run(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Remove Handle request
func (c *SysJobController) Remove(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

