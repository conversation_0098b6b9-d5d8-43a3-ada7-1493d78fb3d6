package database

import (
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/ruoyi/backend/internal/config"
	pkglogger "github.com/ruoyi/backend/pkg/logger"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var (
	// DB 全局数据库连接
	DB *gorm.DB
	// 确保只初始化一次
	once sync.Once
)

// Options 数据库选项
type Options struct {
	// 配置
	Config *config.DBConfig
	// 是否启用日志
	EnableLog bool
	// 是否启用自动迁移
	EnableAutoMigrate bool
}

// Option 选项函数
type Option func(*gorm.Config)

// WithConfig 设置数据库配置
func WithConfig(cfg *config.DBConfig) Option {
	return func(config *gorm.Config) {
		// 配置数据库相关参数
		config.NamingStrategy = schema.NamingStrategy{
			TablePrefix:   cfg.TablePrefix,
			SingularTable: true,
		}
	}
}

// WithLog 设置是否启用日志
func WithLog(enable bool) Option {
	return func(config *gorm.Config) {
		if enable {
			config.Logger = gormlogger.Default.LogMode(gormlogger.Info)
		} else {
			config.Logger = gormlogger.Default.LogMode(gormlogger.Silent)
		}
	}
}

// WithAutoMigrate 设置是否启用自动迁移
func WithAutoMigrate(enable bool) Option {
	return func(config *gorm.Config) {
		// 在 GORM 配置中设置，后续可以根据此配置决定是否执行自动迁移
		// 这里仅仅设置一个标记，实际的迁移操作会在 Setup 后手动执行
	}
}

// Setup 设置数据库连接
func Setup(opts ...Option) error {
	var err error
	once.Do(func() {
		err = setup(opts...)
	})
	return err
}

// setup 设置数据库连接
func setup(opts ...Option) error {
	gormConfig := &gorm.Config{}

	// 应用配置选项
	for _, opt := range opts {
		opt(gormConfig)
	}

	var dbConfig *config.DBConfig
	for _, opt := range opts {
		// 提取配置参数
		withConfigOpt, ok := interface{}(opt).(func(*gorm.Config))
		if ok {
			// 尝试通过闭包获取 dbConfig
			// 这是一个技巧性的实现，在实际应用中可能需要调整
			withConfigOpt(nil)
		}
	}

	// 构建连接字符串
	dsn := fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
	)

	// 连接数据库
	var err error
	DB, err = gorm.Open(sqlserver.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的 SQL DB
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取 SQL DB 实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConns)
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Hour)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("测试数据库连接失败: %v", err)
	}

	pkglogger.Info("数据库连接成功", "type", dbConfig.Type, "host", dbConfig.Host, "database", dbConfig.Database)
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Ping 测试数据库连接
func Ping() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Ping()
}

// InitWithDefault 使用默认配置初始化数据库
func InitWithDefault(dbConfig *config.DBConfig) error {
	return Setup(WithConfig(dbConfig), WithLog(true), WithAutoMigrate(false))
}

// InitWithWosm 使用wosm数据库配置初始化数据库
func InitWithWosm() error {
	// 从环境变量或配置文件读取配置
	dbConfig := &config.DBConfig{
		Type:            getEnvOrDefault("DB_TYPE", "sqlserver"),
		Host:            getEnvOrDefault("DB_HOST", ""),
		Port:            getEnvIntOrDefault("DB_PORT", 1433),
		Username:        getEnvOrDefault("DB_USERNAME", ""),
		Password:        getEnvOrDefault("DB_PASSWORD", ""),
		Database:        getEnvOrDefault("DB_NAME", "wosm"),
		Charset:         getEnvOrDefault("DB_CHARSET", "utf8mb4"),
		MaxIdleConns:    getEnvIntOrDefault("DB_MAX_IDLE_CONNS", 10),
		MaxOpenConns:    getEnvIntOrDefault("DB_MAX_OPEN_CONNS", 100),
		ConnMaxLifetime: getEnvIntOrDefault("DB_CONN_MAX_LIFETIME", 1),
		TablePrefix:     getEnvOrDefault("DB_TABLE_PREFIX", ""),
		SSLMode:         getEnvOrDefault("DB_SSL_MODE", "disable"),
	}

	// 验证必要配置
	if dbConfig.Host == "" || dbConfig.Username == "" || dbConfig.Password == "" {
		return fmt.Errorf("数据库配置不完整，请检查环境变量 DB_HOST, DB_USERNAME, DB_PASSWORD")
	}

	return InitWithDefault(dbConfig)
}

// 辅助函数
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
