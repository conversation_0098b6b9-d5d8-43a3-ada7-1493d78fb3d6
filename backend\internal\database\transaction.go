package database

import (
	"context"
	"fmt"

	"github.com/ruoyi/backend/pkg/logger"
	"gorm.io/gorm"
)

// Tx 事务接口
type Tx interface {
	// Commit 提交事务
	Commit() error
	// Rollback 回滚事务
	Rollback() error
}

// Transaction 事务管理器
type Transaction struct {
	db *gorm.DB
	tx *gorm.DB
}

// NewTransaction 创建事务管理器
func NewTransaction(db *gorm.DB) *Transaction {
	if db == nil {
		db = GetDB()
	}
	return &Transaction{
		db: db,
	}
}

// Begin 开始事务
func (t *Transaction) Begin() error {
	t.tx = t.db.Begin()
	return t.tx.Error
}

// Commit 提交事务
func (t *Transaction) Commit() error {
	if t.tx == nil {
		return fmt.Errorf("事务未开始")
	}
	return t.tx.Commit().Error
}

// Rollback 回滚事务
func (t *Transaction) Rollback() error {
	if t.tx == nil {
		return fmt.Errorf("事务未开始")
	}
	return t.tx.Rollback().Error
}

// GetTx 获取事务
func (t *Transaction) GetTx() *gorm.DB {
	if t.tx == nil {
		return t.db
	}
	return t.tx
}

// ExecTx 执行事务
func ExecTx(ctx context.Context, db *gorm.DB, fn func(tx *gorm.DB) error) error {
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.Error("事务执行异常", "error", r)
			panic(r) // 重新抛出panic
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// WithTransaction 使用事务执行函数
func WithTransaction(fn func(tx *gorm.DB) error) error {
	return ExecTx(context.Background(), GetDB(), fn)
}

// TransactionFunc 事务函数类型
type TransactionFunc func(tx *gorm.DB) error

// RunInTransaction 在事务中运行函数
func RunInTransaction(fn TransactionFunc) error {
	return WithTransaction(fn)
}

// TxMiddleware 事务中间件
func TxMiddleware(db *gorm.DB) func(next TransactionFunc) TransactionFunc {
	return func(next TransactionFunc) TransactionFunc {
		return func(tx *gorm.DB) error {
			return ExecTx(context.Background(), db, next)
		}
	}
}
