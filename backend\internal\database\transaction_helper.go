package database

import (
	"gorm.io/gorm"
)

// TransactionHelper 事务帮助类接口
type TransactionHelper interface {
	// Transaction 执行事务
	Transaction(fn func(tx *gorm.DB) error) error
}

// transactionHelper 事务帮助类实现
type transactionHelper struct {
	db *gorm.DB
}

// NewTransactionHelper 创建事务帮助类
func NewTransactionHelper(db *gorm.DB) TransactionHelper {
	if db == nil {
		db = GetDB()
	}
	return &transactionHelper{
		db: db,
	}
}

// Transaction 执行事务
func (t *transactionHelper) Transaction(fn func(tx *gorm.DB) error) error {
	return ExecTx(nil, t.db, fn)
}
