package domain

import (
	"time"
)

// BaseEntity 实体基类，对应Java中的BaseEntity
type BaseEntity struct {
	// 搜索值
	SearchValue string `json:"-" gorm:"-"`

	// 创建者
	CreateBy string `json:"createBy" gorm:"column:create_by"`

	// 创建时间
	CreateTime *time.Time `json:"createTime" gorm:"column:create_time"`

	// 更新者
	UpdateBy string `json:"updateBy" gorm:"column:update_by"`

	// 更新时间
	UpdateTime *time.Time `json:"updateTime" gorm:"column:update_time"`

	// 备注
	Remark string `json:"remark" gorm:"column:remark"`

	// 请求参数
	Params map[string]interface{} `json:"params,omitempty" gorm:"-"`
}

// GetSearchValue 获取搜索值
func (e *BaseEntity) GetSearchValue() string {
	return e.SearchValue
}

// SetSearchValue 设置搜索值
func (e *BaseEntity) SetSearchValue(searchValue string) {
	e.SearchValue = searchValue
}

// GetCreateBy 获取创建者
func (e *BaseEntity) GetCreateBy() string {
	return e.CreateBy
}

// SetCreateBy 设置创建者
func (e *BaseEntity) SetCreateBy(createBy string) {
	e.CreateBy = createBy
}

// GetCreateTime 获取创建时间
func (e *BaseEntity) GetCreateTime() *time.Time {
	return e.CreateTime
}

// SetCreateTime 设置创建时间
func (e *BaseEntity) SetCreateTime(createTime *time.Time) {
	e.CreateTime = createTime
}

// GetUpdateBy 获取更新者
func (e *BaseEntity) GetUpdateBy() string {
	return e.UpdateBy
}

// SetUpdateBy 设置更新者
func (e *BaseEntity) SetUpdateBy(updateBy string) {
	e.UpdateBy = updateBy
}

// GetUpdateTime 获取更新时间
func (e *BaseEntity) GetUpdateTime() *time.Time {
	return e.UpdateTime
}

// SetUpdateTime 设置更新时间
func (e *BaseEntity) SetUpdateTime(updateTime *time.Time) {
	e.UpdateTime = updateTime
}

// GetRemark 获取备注
func (e *BaseEntity) GetRemark() string {
	return e.Remark
}

// SetRemark 设置备注
func (e *BaseEntity) SetRemark(remark string) {
	e.Remark = remark
}

// GetParams 获取请求参数
func (e *BaseEntity) GetParams() map[string]interface{} {
	if e.Params == nil {
		e.Params = make(map[string]interface{})
	}
	return e.Params
}

// SetParams 设置请求参数
func (e *BaseEntity) SetParams(params map[string]interface{}) {
	e.Params = params
}
