package domain

// ServerInfo 服务器信息
type ServerInfo struct {
	// CPU信息
	Cpu CpuInfo `json:"cpu"`

	// 内存信息
	Mem MemInfo `json:"mem"`

	// JVM信息（Go环境下模拟）
	Jvm JvmInfo `json:"jvm"`

	// 服务器信息
	Sys SysInfo `json:"sys"`

	// 磁盘信息
	SysFiles []SysFileInfo `json:"sysFiles"`
}

// CpuInfo CPU信息
type CpuInfo struct {
	// 核心数
	CpuNum int `json:"cpuNum"`

	// CPU总的使用率
	Total float64 `json:"total"`

	// CPU系统使用率
	Sys float64 `json:"sys"`

	// CPU用户使用率
	Used float64 `json:"used"`

	// CPU当前等待率
	Wait float64 `json:"wait"`

	// CPU当前空闲率
	Free float64 `json:"free"`
}

// MemInfo 内存信息
type MemInfo struct {
	// 内存总量
	Total uint64 `json:"total"`

	// 已用内存
	Used uint64 `json:"used"`

	// 剩余内存
	Free uint64 `json:"free"`

	// 使用率
	Usage float64 `json:"usage"`
}

// JvmInfo JVM信息（Go环境下模拟）
type JvmInfo struct {
	// 当前JVM占用的内存总数
	Total uint64 `json:"total"`

	// JVM最大可用内存总数
	Max uint64 `json:"max"`

	// JVM空闲内存
	Free uint64 `json:"free"`

	// JVM使用率
	Usage float64 `json:"usage"`

	// JDK版本
	Version string `json:"version"`

	// JDK路径
	Home string `json:"home"`
}

// SysInfo 服务器信息
type SysInfo struct {
	// 服务器名称
	ComputerName string `json:"computerName"`

	// 服务器IP
	ComputerIp string `json:"computerIp"`

	// 操作系统
	OsName string `json:"osName"`

	// 系统架构
	OsArch string `json:"osArch"`

	// 项目路径
	UserDir string `json:"userDir"`
}

// SysFileInfo 磁盘信息
type SysFileInfo struct {
	// 盘符路径
	DirName string `json:"dirName"`

	// 盘符类型
	SysTypeName string `json:"sysTypeName"`

	// 文件类型
	TypeName string `json:"typeName"`

	// 总大小
	Total string `json:"total"`

	// 剩余大小
	Free string `json:"free"`

	// 已经使用量
	Used string `json:"used"`

	// 资源的使用率
	Usage string `json:"usage"`
}
