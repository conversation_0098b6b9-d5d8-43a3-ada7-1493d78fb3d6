package domain

// SysConfig 参数配置表 sys_config
type SysConfig struct {
	BaseEntity

	// 参数主键
	ConfigId int64 `json:"configId" gorm:"column:config_id;primary_key"`

	// 参数名称
	ConfigName string `json:"configName" gorm:"column:config_name"`

	// 参数键名
	ConfigKey string `json:"configKey" gorm:"column:config_key"`

	// 参数键值
	ConfigValue string `json:"configValue" gorm:"column:config_value"`

	// 系统内置（Y是 N否）
	ConfigType string `json:"configType" gorm:"column:config_type"`
}

// TableName 设置表名
func (SysConfig) TableName() string {
	return "sys_config"
}

// GetConfigId 获取参数主键
func (c *SysConfig) GetConfigId() int64 {
	return c.ConfigId
}

// SetConfigId 设置参数主键
func (c *SysConfig) SetConfigId(configId int64) {
	c.ConfigId = configId
}

// GetConfigName 获取参数名称
func (c *SysConfig) GetConfigName() string {
	return c.ConfigName
}

// SetConfigName 设置参数名称
func (c *SysConfig) SetConfigName(configName string) {
	c.ConfigName = configName
}

// GetConfigKey 获取参数键名
func (c *SysConfig) GetConfigKey() string {
	return c.ConfigKey
}

// SetConfigKey 设置参数键名
func (c *SysConfig) SetConfigKey(configKey string) {
	c.ConfigKey = configKey
}

// GetConfigValue 获取参数键值
func (c *SysConfig) GetConfigValue() string {
	return c.ConfigValue
}

// SetConfigValue 设置参数键值
func (c *SysConfig) SetConfigValue(configValue string) {
	c.ConfigValue = configValue
}

// GetConfigType 获取系统内置
func (c *SysConfig) GetConfigType() string {
	return c.ConfigType
}

// SetConfigType 设置系统内置
func (c *SysConfig) SetConfigType(configType string) {
	c.ConfigType = configType
}
