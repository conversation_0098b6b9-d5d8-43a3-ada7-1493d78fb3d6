package domain

// SysDept 部门表 sys_dept
type SysDept struct {
	BaseEntity

	// 部门ID
	DeptId int64 `json:"deptId" gorm:"column:dept_id;primary_key"`

	// 父部门ID
	ParentId int64 `json:"parentId" gorm:"column:parent_id"`

	// 祖级列表
	Ancestors string `json:"ancestors" gorm:"column:ancestors"`

	// 部门名称
	DeptName string `json:"deptName" gorm:"column:dept_name"`

	// 显示顺序
	OrderNum int `json:"orderNum" gorm:"column:order_num"`

	// 负责人
	Leader string `json:"leader" gorm:"column:leader"`

	// 联系电话
	Phone string `json:"phone" gorm:"column:phone"`

	// 邮箱
	Email string `json:"email" gorm:"column:email"`

	// 部门状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag"`

	// 父部门名称
	ParentName string `json:"parentName" gorm:"-"`

	// 子部门
	Children []SysDept `json:"children" gorm:"-"`
}

// TableName 设置表名
func (SysDept) TableName() string {
	return "sys_dept"
}
