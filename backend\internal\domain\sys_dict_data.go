package domain

// SysDictData 字典数据表 sys_dict_data
type SysDictData struct {
	BaseEntity

	// 字典编码
	DictCode int64 `json:"dictCode" gorm:"column:dict_code;primary_key"`

	// 字典排序
	DictSort int64 `json:"dictSort" gorm:"column:dict_sort"`

	// 字典标签
	DictLabel string `json:"dictLabel" gorm:"column:dict_label"`

	// 字典键值
	DictValue string `json:"dictValue" gorm:"column:dict_value"`

	// 字典类型
	DictType string `json:"dictType" gorm:"column:dict_type"`

	// 样式属性（其他样式扩展）
	CssClass string `json:"cssClass" gorm:"column:css_class"`

	// 表格回显样式
	ListClass string `json:"listClass" gorm:"column:list_class"`

	// 是否默认（Y是 N否）
	IsDefault string `json:"isDefault" gorm:"column:is_default"`

	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`
}

// TableName 设置表名
func (SysDictData) TableName() string {
	return "sys_dict_data"
}

// GetDictCode 获取字典编码
func (d *SysDictData) GetDictCode() int64 {
	return d.DictCode
}

// SetDictCode 设置字典编码
func (d *SysDictData) SetDictCode(dictCode int64) {
	d.DictCode = dictCode
}

// GetDictSort 获取字典排序
func (d *SysDictData) GetDictSort() int64 {
	return d.DictSort
}

// SetDictSort 设置字典排序
func (d *SysDictData) SetDictSort(dictSort int64) {
	d.DictSort = dictSort
}

// GetDictLabel 获取字典标签
func (d *SysDictData) GetDictLabel() string {
	return d.DictLabel
}

// SetDictLabel 设置字典标签
func (d *SysDictData) SetDictLabel(dictLabel string) {
	d.DictLabel = dictLabel
}

// GetDictValue 获取字典键值
func (d *SysDictData) GetDictValue() string {
	return d.DictValue
}

// SetDictValue 设置字典键值
func (d *SysDictData) SetDictValue(dictValue string) {
	d.DictValue = dictValue
}

// GetDictType 获取字典类型
func (d *SysDictData) GetDictType() string {
	return d.DictType
}

// SetDictType 设置字典类型
func (d *SysDictData) SetDictType(dictType string) {
	d.DictType = dictType
}

// GetCssClass 获取样式属性
func (d *SysDictData) GetCssClass() string {
	return d.CssClass
}

// SetCssClass 设置样式属性
func (d *SysDictData) SetCssClass(cssClass string) {
	d.CssClass = cssClass
}

// GetListClass 获取表格回显样式
func (d *SysDictData) GetListClass() string {
	return d.ListClass
}

// SetListClass 设置表格回显样式
func (d *SysDictData) SetListClass(listClass string) {
	d.ListClass = listClass
}

// GetDefault 获取是否默认
func (d *SysDictData) GetDefault() bool {
	return d.IsDefault == "Y"
}

// GetIsDefault 获取是否默认
func (d *SysDictData) GetIsDefault() string {
	return d.IsDefault
}

// SetIsDefault 设置是否默认
func (d *SysDictData) SetIsDefault(isDefault string) {
	d.IsDefault = isDefault
}

// GetStatus 获取状态
func (d *SysDictData) GetStatus() string {
	return d.Status
}

// SetStatus 设置状态
func (d *SysDictData) SetStatus(status string) {
	d.Status = status
}
