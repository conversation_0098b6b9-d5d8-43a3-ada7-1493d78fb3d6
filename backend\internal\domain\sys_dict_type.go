package domain

// SysDictType 字典类型表 sys_dict_type
type SysDictType struct {
	BaseEntity

	// 字典主键
	DictId int64 `json:"dictId" gorm:"column:dict_id;primary_key"`

	// 字典名称
	DictName string `json:"dictName" gorm:"column:dict_name"`

	// 字典类型
	DictType string `json:"dictType" gorm:"column:dict_type"`

	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`
}

// TableName 设置表名
func (SysDictType) TableName() string {
	return "sys_dict_type"
}

// GetDictId 获取字典主键
func (d *SysDictType) GetDictId() int64 {
	return d.DictId
}

// SetDictId 设置字典主键
func (d *SysDictType) SetDictId(dictId int64) {
	d.DictId = dictId
}

// GetDictName 获取字典名称
func (d *SysDictType) GetDictName() string {
	return d.DictName
}

// SetDictName 设置字典名称
func (d *SysDictType) SetDictName(dictName string) {
	d.DictName = dictName
}

// GetDictType 获取字典类型
func (d *SysDictType) GetDictType() string {
	return d.DictType
}

// SetDictType 设置字典类型
func (d *SysDictType) SetDictType(dictType string) {
	d.DictType = dictType
}

// GetStatus 获取状态
func (d *SysDictType) GetStatus() string {
	return d.Status
}

// SetStatus 设置状态
func (d *SysDictType) SetStatus(status string) {
	d.Status = status
}
