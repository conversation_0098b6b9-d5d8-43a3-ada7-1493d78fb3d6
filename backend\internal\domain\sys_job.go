package domain

import (
	"time"
)

// SysJob 定时任务调度表 sys_job
type SysJob struct {
	BaseEntity

	// 任务ID
	JobId int64 `json:"jobId" gorm:"column:job_id;primary_key"`

	// 任务名称
	JobName string `json:"jobName" gorm:"column:job_name"`

	// 任务组名
	JobGroup string `json:"jobGroup" gorm:"column:job_group"`

	// 调用目标字符串
	InvokeTarget string `json:"invokeTarget" gorm:"column:invoke_target"`

	// cron执行表达式
	CronExpression string `json:"cronExpression" gorm:"column:cron_expression"`

	// 下次执行时间
	NextValidTime *time.Time `json:"nextValidTime" gorm:"-"`

	// 计划执行错误策略（1立即执行 2执行一次 3放弃执行）
	MisfirePolicy string `json:"misfirePolicy" gorm:"column:misfire_policy"`

	// 是否并发执行（0允许 1禁止）
	Concurrent string `json:"concurrent" gorm:"column:concurrent"`

	// 状态（0正常 1暂停）
	Status string `json:"status" gorm:"column:status"`
}

// TableName 设置表名
func (SysJob) TableName() string {
	return "sys_job"
}

// GetJobId 获取任务ID
func (j *SysJob) GetJobId() int64 {
	return j.JobId
}

// SetJobId 设置任务ID
func (j *SysJob) SetJobId(jobId int64) {
	j.JobId = jobId
}

// GetJobName 获取任务名称
func (j *SysJob) GetJobName() string {
	return j.JobName
}

// SetJobName 设置任务名称
func (j *SysJob) SetJobName(jobName string) {
	j.JobName = jobName
}

// GetJobGroup 获取任务组名
func (j *SysJob) GetJobGroup() string {
	return j.JobGroup
}

// SetJobGroup 设置任务组名
func (j *SysJob) SetJobGroup(jobGroup string) {
	j.JobGroup = jobGroup
}

// GetInvokeTarget 获取调用目标字符串
func (j *SysJob) GetInvokeTarget() string {
	return j.InvokeTarget
}

// SetInvokeTarget 设置调用目标字符串
func (j *SysJob) SetInvokeTarget(invokeTarget string) {
	j.InvokeTarget = invokeTarget
}

// GetCronExpression 获取cron执行表达式
func (j *SysJob) GetCronExpression() string {
	return j.CronExpression
}

// SetCronExpression 设置cron执行表达式
func (j *SysJob) SetCronExpression(cronExpression string) {
	j.CronExpression = cronExpression
}

// GetNextValidTime 获取下次执行时间
func (j *SysJob) GetNextValidTime() *time.Time {
	return j.NextValidTime
}

// SetNextValidTime 设置下次执行时间
func (j *SysJob) SetNextValidTime(nextValidTime *time.Time) {
	j.NextValidTime = nextValidTime
}

// GetMisfirePolicy 获取计划执行错误策略
func (j *SysJob) GetMisfirePolicy() string {
	return j.MisfirePolicy
}

// SetMisfirePolicy 设置计划执行错误策略
func (j *SysJob) SetMisfirePolicy(misfirePolicy string) {
	j.MisfirePolicy = misfirePolicy
}

// GetConcurrent 获取是否并发执行
func (j *SysJob) GetConcurrent() string {
	return j.Concurrent
}

// SetConcurrent 设置是否并发执行
func (j *SysJob) SetConcurrent(concurrent string) {
	j.Concurrent = concurrent
}

// GetStatus 获取状态
func (j *SysJob) GetStatus() string {
	return j.Status
}

// SetStatus 设置状态
func (j *SysJob) SetStatus(status string) {
	j.Status = status
}
