package domain

import (
	"time"
)

// SysJobLog 定时任务调度日志表 sys_job_log
type SysJobLog struct {
	BaseEntity

	// 任务日志ID
	JobLogId int64 `json:"jobLogId" gorm:"column:job_log_id;primary_key"`

	// 任务名称
	JobName string `json:"jobName" gorm:"column:job_name"`

	// 任务组名
	JobGroup string `json:"jobGroup" gorm:"column:job_group"`

	// 调用目标字符串
	InvokeTarget string `json:"invokeTarget" gorm:"column:invoke_target"`

	// 日志信息
	JobMessage string `json:"jobMessage" gorm:"column:job_message"`

	// 执行状态（0正常 1失败）
	Status string `json:"status" gorm:"column:status"`

	// 异常信息
	ExceptionInfo string `json:"exceptionInfo" gorm:"column:exception_info"`

	// 开始时间
	StartTime *time.Time `json:"startTime" gorm:"column:start_time"`

	// 停止时间
	StopTime *time.Time `json:"stopTime" gorm:"column:stop_time"`
}

// TableName 设置表名
func (SysJobLog) TableName() string {
	return "sys_job_log"
}

// GetJobLogId 获取任务日志ID
func (j *SysJobLog) GetJobLogId() int64 {
	return j.JobLogId
}

// SetJobLogId 设置任务日志ID
func (j *SysJobLog) SetJobLogId(jobLogId int64) {
	j.JobLogId = jobLogId
}

// GetJobName 获取任务名称
func (j *SysJobLog) GetJobName() string {
	return j.JobName
}

// SetJobName 设置任务名称
func (j *SysJobLog) SetJobName(jobName string) {
	j.JobName = jobName
}

// GetJobGroup 获取任务组名
func (j *SysJobLog) GetJobGroup() string {
	return j.JobGroup
}

// SetJobGroup 设置任务组名
func (j *SysJobLog) SetJobGroup(jobGroup string) {
	j.JobGroup = jobGroup
}

// GetInvokeTarget 获取调用目标字符串
func (j *SysJobLog) GetInvokeTarget() string {
	return j.InvokeTarget
}

// SetInvokeTarget 设置调用目标字符串
func (j *SysJobLog) SetInvokeTarget(invokeTarget string) {
	j.InvokeTarget = invokeTarget
}

// GetJobMessage 获取日志信息
func (j *SysJobLog) GetJobMessage() string {
	return j.JobMessage
}

// SetJobMessage 设置日志信息
func (j *SysJobLog) SetJobMessage(jobMessage string) {
	j.JobMessage = jobMessage
}

// GetStatus 获取执行状态
func (j *SysJobLog) GetStatus() string {
	return j.Status
}

// SetStatus 设置执行状态
func (j *SysJobLog) SetStatus(status string) {
	j.Status = status
}

// GetExceptionInfo 获取异常信息
func (j *SysJobLog) GetExceptionInfo() string {
	return j.ExceptionInfo
}

// SetExceptionInfo 设置异常信息
func (j *SysJobLog) SetExceptionInfo(exceptionInfo string) {
	j.ExceptionInfo = exceptionInfo
}

// GetStartTime 获取开始时间
func (j *SysJobLog) GetStartTime() *time.Time {
	return j.StartTime
}

// SetStartTime 设置开始时间
func (j *SysJobLog) SetStartTime(startTime *time.Time) {
	j.StartTime = startTime
}

// GetStopTime 获取停止时间
func (j *SysJobLog) GetStopTime() *time.Time {
	return j.StopTime
}

// SetStopTime 设置停止时间
func (j *SysJobLog) SetStopTime(stopTime *time.Time) {
	j.StopTime = stopTime
}
