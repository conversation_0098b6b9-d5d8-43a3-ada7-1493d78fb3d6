package domain

import (
	"time"
)

// SysLogininfor 系统访问记录表 sys_logininfor
type SysLogininfor struct {
	BaseEntity

	// 访问ID
	InfoId int64 `json:"infoId" gorm:"column:info_id;primary_key"`

	// 用户账号
	UserName string `json:"userName" gorm:"column:user_name"`

	// 登录状态 0成功 1失败
	Status string `json:"status" gorm:"column:status"`

	// 登录IP地址
	Ipaddr string `json:"ipaddr" gorm:"column:ipaddr"`

	// 登录地点
	LoginLocation string `json:"loginLocation" gorm:"column:login_location"`

	// 浏览器类型
	Browser string `json:"browser" gorm:"column:browser"`

	// 操作系统
	Os string `json:"os" gorm:"column:os"`

	// 提示消息
	Msg string `json:"msg" gorm:"column:msg"`

	// 访问时间
	LoginTime *time.Time `json:"loginTime" gorm:"column:login_time"`
}

// TableName 设置表名
func (SysLogininfor) TableName() string {
	return "sys_logininfor"
}

// GetInfoId 获取访问ID
func (l *SysLogininfor) GetInfoId() int64 {
	return l.InfoId
}

// SetInfoId 设置访问ID
func (l *SysLogininfor) SetInfoId(infoId int64) {
	l.InfoId = infoId
}

// GetUserName 获取用户账号
func (l *SysLogininfor) GetUserName() string {
	return l.UserName
}

// SetUserName 设置用户账号
func (l *SysLogininfor) SetUserName(userName string) {
	l.UserName = userName
}

// GetStatus 获取登录状态
func (l *SysLogininfor) GetStatus() string {
	return l.Status
}

// SetStatus 设置登录状态
func (l *SysLogininfor) SetStatus(status string) {
	l.Status = status
}

// GetIpaddr 获取登录IP地址
func (l *SysLogininfor) GetIpaddr() string {
	return l.Ipaddr
}

// SetIpaddr 设置登录IP地址
func (l *SysLogininfor) SetIpaddr(ipaddr string) {
	l.Ipaddr = ipaddr
}

// GetLoginLocation 获取登录地点
func (l *SysLogininfor) GetLoginLocation() string {
	return l.LoginLocation
}

// SetLoginLocation 设置登录地点
func (l *SysLogininfor) SetLoginLocation(loginLocation string) {
	l.LoginLocation = loginLocation
}

// GetBrowser 获取浏览器类型
func (l *SysLogininfor) GetBrowser() string {
	return l.Browser
}

// SetBrowser 设置浏览器类型
func (l *SysLogininfor) SetBrowser(browser string) {
	l.Browser = browser
}

// GetOs 获取操作系统
func (l *SysLogininfor) GetOs() string {
	return l.Os
}

// SetOs 设置操作系统
func (l *SysLogininfor) SetOs(os string) {
	l.Os = os
}

// GetMsg 获取提示消息
func (l *SysLogininfor) GetMsg() string {
	return l.Msg
}

// SetMsg 设置提示消息
func (l *SysLogininfor) SetMsg(msg string) {
	l.Msg = msg
}

// GetLoginTime 获取访问时间
func (l *SysLogininfor) GetLoginTime() *time.Time {
	return l.LoginTime
}

// SetLoginTime 设置访问时间
func (l *SysLogininfor) SetLoginTime(loginTime *time.Time) {
	l.LoginTime = loginTime
}
