package domain

// SysMenu 菜单权限表 sys_menu
type SysMenu struct {
	BaseEntity

	// 菜单ID
	MenuId int64 `json:"menuId" gorm:"column:menu_id;primary_key"`

	// 菜单名称
	MenuName string `json:"menuName" gorm:"column:menu_name"`

	// 父菜单名称
	ParentName string `json:"parentName" gorm:"-"`

	// 父菜单ID
	ParentId int64 `json:"parentId" gorm:"column:parent_id"`

	// 显示顺序
	OrderNum int `json:"orderNum" gorm:"column:order_num"`

	// 路由地址
	Path string `json:"path" gorm:"column:path"`

	// 组件路径
	Component string `json:"component" gorm:"column:component"`

	// 路由参数
	Query string `json:"query" gorm:"column:query"`

	// 路由名称
	RouteName string `json:"routeName" gorm:"column:route_name"`

	// 是否为外链（0是 1否）
	IsFrame string `json:"isFrame" gorm:"column:is_frame"`

	// 是否缓存（0缓存 1不缓存）
	IsCache string `json:"isCache" gorm:"column:is_cache"`

	// 菜单类型（M目录 C菜单 F按钮）
	MenuType string `json:"menuType" gorm:"column:menu_type"`

	// 菜单状态（0显示 1隐藏）
	Visible string `json:"visible" gorm:"column:visible"`

	// 菜单状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 权限标识
	Perms string `json:"perms" gorm:"column:perms"`

	// 菜单图标
	Icon string `json:"icon" gorm:"column:icon"`

	// 子菜单
	Children []SysMenu `json:"children" gorm:"-"`
}

// TableName 设置表名
func (SysMenu) TableName() string {
	return "sys_menu"
}

// GetMenuId 获取菜单ID
func (m *SysMenu) GetMenuId() int64 {
	return m.MenuId
}

// SetMenuId 设置菜单ID
func (m *SysMenu) SetMenuId(menuId int64) {
	m.MenuId = menuId
}

// GetMenuName 获取菜单名称
func (m *SysMenu) GetMenuName() string {
	return m.MenuName
}

// SetMenuName 设置菜单名称
func (m *SysMenu) SetMenuName(menuName string) {
	m.MenuName = menuName
}

// GetParentName 获取父菜单名称
func (m *SysMenu) GetParentName() string {
	return m.ParentName
}

// SetParentName 设置父菜单名称
func (m *SysMenu) SetParentName(parentName string) {
	m.ParentName = parentName
}

// GetParentId 获取父菜单ID
func (m *SysMenu) GetParentId() int64 {
	return m.ParentId
}

// SetParentId 设置父菜单ID
func (m *SysMenu) SetParentId(parentId int64) {
	m.ParentId = parentId
}

// GetOrderNum 获取显示顺序
func (m *SysMenu) GetOrderNum() int {
	return m.OrderNum
}

// SetOrderNum 设置显示顺序
func (m *SysMenu) SetOrderNum(orderNum int) {
	m.OrderNum = orderNum
}

// GetPath 获取路由地址
func (m *SysMenu) GetPath() string {
	return m.Path
}

// SetPath 设置路由地址
func (m *SysMenu) SetPath(path string) {
	m.Path = path
}

// GetComponent 获取组件路径
func (m *SysMenu) GetComponent() string {
	return m.Component
}

// SetComponent 设置组件路径
func (m *SysMenu) SetComponent(component string) {
	m.Component = component
}

// GetQuery 获取路由参数
func (m *SysMenu) GetQuery() string {
	return m.Query
}

// SetQuery 设置路由参数
func (m *SysMenu) SetQuery(query string) {
	m.Query = query
}

// GetRouteName 获取路由名称
func (m *SysMenu) GetRouteName() string {
	return m.RouteName
}

// SetRouteName 设置路由名称
func (m *SysMenu) SetRouteName(routeName string) {
	m.RouteName = routeName
}

// GetIsFrame 获取是否为外链
func (m *SysMenu) GetIsFrame() string {
	return m.IsFrame
}

// SetIsFrame 设置是否为外链
func (m *SysMenu) SetIsFrame(isFrame string) {
	m.IsFrame = isFrame
}

// GetIsCache 获取是否缓存
func (m *SysMenu) GetIsCache() string {
	return m.IsCache
}

// SetIsCache 设置是否缓存
func (m *SysMenu) SetIsCache(isCache string) {
	m.IsCache = isCache
}

// GetMenuType 获取菜单类型
func (m *SysMenu) GetMenuType() string {
	return m.MenuType
}

// SetMenuType 设置菜单类型
func (m *SysMenu) SetMenuType(menuType string) {
	m.MenuType = menuType
}

// GetVisible 获取菜单状态
func (m *SysMenu) GetVisible() string {
	return m.Visible
}

// SetVisible 设置菜单状态
func (m *SysMenu) SetVisible(visible string) {
	m.Visible = visible
}

// GetStatus 获取菜单状态
func (m *SysMenu) GetStatus() string {
	return m.Status
}

// SetStatus 设置菜单状态
func (m *SysMenu) SetStatus(status string) {
	m.Status = status
}

// GetPerms 获取权限标识
func (m *SysMenu) GetPerms() string {
	return m.Perms
}

// SetPerms 设置权限标识
func (m *SysMenu) SetPerms(perms string) {
	m.Perms = perms
}

// GetIcon 获取菜单图标
func (m *SysMenu) GetIcon() string {
	return m.Icon
}

// SetIcon 设置菜单图标
func (m *SysMenu) SetIcon(icon string) {
	m.Icon = icon
}

// GetChildren 获取子菜单
func (m *SysMenu) GetChildren() []SysMenu {
	return m.Children
}

// SetChildren 设置子菜单
func (m *SysMenu) SetChildren(children []SysMenu) {
	m.Children = children
}
