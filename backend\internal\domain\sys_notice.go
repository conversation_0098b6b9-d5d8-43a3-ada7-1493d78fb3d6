package domain

// SysNotice 通知公告表 sys_notice
type SysNotice struct {
	BaseEntity

	// 公告ID
	NoticeId int64 `json:"noticeId" gorm:"column:notice_id;primary_key"`

	// 公告标题
	NoticeTitle string `json:"noticeTitle" gorm:"column:notice_title"`

	// 公告类型（1通知 2公告）
	NoticeType string `json:"noticeType" gorm:"column:notice_type"`

	// 公告内容
	NoticeContent string `json:"noticeContent" gorm:"column:notice_content"`

	// 公告状态（0正常 1关闭）
	Status string `json:"status" gorm:"column:status"`
}

// TableName 设置表名
func (SysNotice) TableName() string {
	return "sys_notice"
}

// GetNoticeId 获取公告ID
func (n *SysNotice) GetNoticeId() int64 {
	return n.NoticeId
}

// SetNoticeId 设置公告ID
func (n *SysNotice) SetNoticeId(noticeId int64) {
	n.NoticeId = noticeId
}

// GetNoticeTitle 获取公告标题
func (n *SysNotice) GetNoticeTitle() string {
	return n.NoticeTitle
}

// SetNoticeTitle 设置公告标题
func (n *SysNotice) SetNoticeTitle(noticeTitle string) {
	n.NoticeTitle = noticeTitle
}

// GetNoticeType 获取公告类型
func (n *SysNotice) GetNoticeType() string {
	return n.NoticeType
}

// SetNoticeType 设置公告类型
func (n *SysNotice) SetNoticeType(noticeType string) {
	n.NoticeType = noticeType
}

// GetNoticeContent 获取公告内容
func (n *SysNotice) GetNoticeContent() string {
	return n.NoticeContent
}

// SetNoticeContent 设置公告内容
func (n *SysNotice) SetNoticeContent(noticeContent string) {
	n.NoticeContent = noticeContent
}

// GetStatus 获取公告状态
func (n *SysNotice) GetStatus() string {
	return n.Status
}

// SetStatus 设置公告状态
func (n *SysNotice) SetStatus(status string) {
	n.Status = status
}
