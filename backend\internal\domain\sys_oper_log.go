package domain

import (
	"time"
)

// SysOperLog 操作日志记录表 sys_oper_log
type SysOperLog struct {
	BaseEntity

	// 日志主键
	OperId int64 `json:"operId" gorm:"column:oper_id;primary_key"`

	// 模块标题
	Title string `json:"title" gorm:"column:title"`

	// 业务类型（0其它 1新增 2修改 3删除）
	BusinessType int `json:"businessType" gorm:"column:business_type"`

	// 业务类型数组
	BusinessTypes []int `json:"businessTypes" gorm:"-"`

	// 方法名称
	Method string `json:"method" gorm:"column:method"`

	// 请求方式
	RequestMethod string `json:"requestMethod" gorm:"column:request_method"`

	// 操作类别（0其它 1后台用户 2手机端用户）
	OperatorType int `json:"operatorType" gorm:"column:operator_type"`

	// 操作人员
	OperName string `json:"operName" gorm:"column:oper_name"`

	// 部门名称
	DeptName string `json:"deptName" gorm:"column:dept_name"`

	// 请求URL
	OperUrl string `json:"operUrl" gorm:"column:oper_url"`

	// 主机地址
	OperIp string `json:"operIp" gorm:"column:oper_ip"`

	// 操作地点
	OperLocation string `json:"operLocation" gorm:"column:oper_location"`

	// 请求参数
	OperParam string `json:"operParam" gorm:"column:oper_param"`

	// 返回参数
	JsonResult string `json:"jsonResult" gorm:"column:json_result"`

	// 操作状态（0正常 1异常）
	Status int `json:"status" gorm:"column:status"`

	// 错误消息
	ErrorMsg string `json:"errorMsg" gorm:"column:error_msg"`

	// 操作时间
	OperTime *time.Time `json:"operTime" gorm:"column:oper_time"`

	// 消耗时间
	CostTime int64 `json:"costTime" gorm:"column:cost_time"`
}

// TableName 设置表名
func (SysOperLog) TableName() string {
	return "sys_oper_log"
}

// GetOperId 获取日志主键
func (o *SysOperLog) GetOperId() int64 {
	return o.OperId
}

// SetOperId 设置日志主键
func (o *SysOperLog) SetOperId(operId int64) {
	o.OperId = operId
}

// GetTitle 获取模块标题
func (o *SysOperLog) GetTitle() string {
	return o.Title
}

// SetTitle 设置模块标题
func (o *SysOperLog) SetTitle(title string) {
	o.Title = title
}

// GetBusinessType 获取业务类型
func (o *SysOperLog) GetBusinessType() int {
	return o.BusinessType
}

// SetBusinessType 设置业务类型
func (o *SysOperLog) SetBusinessType(businessType int) {
	o.BusinessType = businessType
}

// GetBusinessTypes 获取业务类型数组
func (o *SysOperLog) GetBusinessTypes() []int {
	return o.BusinessTypes
}

// SetBusinessTypes 设置业务类型数组
func (o *SysOperLog) SetBusinessTypes(businessTypes []int) {
	o.BusinessTypes = businessTypes
}

// GetMethod 获取方法名称
func (o *SysOperLog) GetMethod() string {
	return o.Method
}

// SetMethod 设置方法名称
func (o *SysOperLog) SetMethod(method string) {
	o.Method = method
}

// GetRequestMethod 获取请求方式
func (o *SysOperLog) GetRequestMethod() string {
	return o.RequestMethod
}

// SetRequestMethod 设置请求方式
func (o *SysOperLog) SetRequestMethod(requestMethod string) {
	o.RequestMethod = requestMethod
}

// GetOperatorType 获取操作类别
func (o *SysOperLog) GetOperatorType() int {
	return o.OperatorType
}

// SetOperatorType 设置操作类别
func (o *SysOperLog) SetOperatorType(operatorType int) {
	o.OperatorType = operatorType
}

// GetOperName 获取操作人员
func (o *SysOperLog) GetOperName() string {
	return o.OperName
}

// SetOperName 设置操作人员
func (o *SysOperLog) SetOperName(operName string) {
	o.OperName = operName
}

// GetDeptName 获取部门名称
func (o *SysOperLog) GetDeptName() string {
	return o.DeptName
}

// SetDeptName 设置部门名称
func (o *SysOperLog) SetDeptName(deptName string) {
	o.DeptName = deptName
}

// GetOperUrl 获取请求URL
func (o *SysOperLog) GetOperUrl() string {
	return o.OperUrl
}

// SetOperUrl 设置请求URL
func (o *SysOperLog) SetOperUrl(operUrl string) {
	o.OperUrl = operUrl
}

// GetOperIp 获取主机地址
func (o *SysOperLog) GetOperIp() string {
	return o.OperIp
}

// SetOperIp 设置主机地址
func (o *SysOperLog) SetOperIp(operIp string) {
	o.OperIp = operIp
}

// GetOperLocation 获取操作地点
func (o *SysOperLog) GetOperLocation() string {
	return o.OperLocation
}

// SetOperLocation 设置操作地点
func (o *SysOperLog) SetOperLocation(operLocation string) {
	o.OperLocation = operLocation
}

// GetOperParam 获取请求参数
func (o *SysOperLog) GetOperParam() string {
	return o.OperParam
}

// SetOperParam 设置请求参数
func (o *SysOperLog) SetOperParam(operParam string) {
	o.OperParam = operParam
}

// GetJsonResult 获取返回参数
func (o *SysOperLog) GetJsonResult() string {
	return o.JsonResult
}

// SetJsonResult 设置返回参数
func (o *SysOperLog) SetJsonResult(jsonResult string) {
	o.JsonResult = jsonResult
}

// GetStatus 获取操作状态
func (o *SysOperLog) GetStatus() int {
	return o.Status
}

// SetStatus 设置操作状态
func (o *SysOperLog) SetStatus(status int) {
	o.Status = status
}

// GetErrorMsg 获取错误消息
func (o *SysOperLog) GetErrorMsg() string {
	return o.ErrorMsg
}

// SetErrorMsg 设置错误消息
func (o *SysOperLog) SetErrorMsg(errorMsg string) {
	o.ErrorMsg = errorMsg
}

// GetOperTime 获取操作时间
func (o *SysOperLog) GetOperTime() *time.Time {
	return o.OperTime
}

// SetOperTime 设置操作时间
func (o *SysOperLog) SetOperTime(operTime *time.Time) {
	o.OperTime = operTime
}

// GetCostTime 获取消耗时间
func (o *SysOperLog) GetCostTime() int64 {
	return o.CostTime
}

// SetCostTime 设置消耗时间
func (o *SysOperLog) SetCostTime(costTime int64) {
	o.CostTime = costTime
}
