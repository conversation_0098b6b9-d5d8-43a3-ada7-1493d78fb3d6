package domain

// SysPost 岗位表 sys_post
type SysPost struct {
	BaseEntity

	// 岗位ID
	PostId int64 `json:"postId" gorm:"column:post_id;primary_key"`

	// 岗位编码
	PostCode string `json:"postCode" gorm:"column:post_code"`

	// 岗位名称
	PostName string `json:"postName" gorm:"column:post_name"`

	// 显示顺序
	PostSort int `json:"postSort" gorm:"column:post_sort"`

	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 用户是否存在此岗位标识
	Flag bool `json:"flag" gorm:"-"`
}

// TableName 设置表名
func (SysPost) TableName() string {
	return "sys_post"
}

// GetPostId 获取岗位ID
func (p *SysPost) GetPostId() int64 {
	return p.PostId
}

// SetPostId 设置岗位ID
func (p *SysPost) SetPostId(postId int64) {
	p.PostId = postId
}

// GetPostCode 获取岗位编码
func (p *SysPost) GetPostCode() string {
	return p.PostCode
}

// SetPostCode 设置岗位编码
func (p *SysPost) SetPostCode(postCode string) {
	p.PostCode = postCode
}

// GetPostName 获取岗位名称
func (p *SysPost) GetPostName() string {
	return p.PostName
}

// SetPostName 设置岗位名称
func (p *SysPost) SetPostName(postName string) {
	p.PostName = postName
}

// GetPostSort 获取显示顺序
func (p *SysPost) GetPostSort() int {
	return p.PostSort
}

// SetPostSort 设置显示顺序
func (p *SysPost) SetPostSort(postSort int) {
	p.PostSort = postSort
}

// GetStatus 获取状态
func (p *SysPost) GetStatus() string {
	return p.Status
}

// SetStatus 设置状态
func (p *SysPost) SetStatus(status string) {
	p.Status = status
}

// IsFlag 获取用户是否存在此岗位标识
func (p *SysPost) IsFlag() bool {
	return p.Flag
}

// SetFlag 设置用户是否存在此岗位标识
func (p *SysPost) SetFlag(flag bool) {
	p.Flag = flag
}
