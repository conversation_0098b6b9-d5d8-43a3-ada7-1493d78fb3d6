package domain

// SysRole 角色表 sys_role
type SysRole struct {
	BaseEntity

	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key"`

	// 角色名称
	RoleName string `json:"roleName" gorm:"column:role_name"`

	// 角色权限字符串
	RoleKey string `json:"roleKey" gorm:"column:role_key"`

	// 显示顺序
	RoleSort int `json:"roleSort" gorm:"column:role_sort"`

	// 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
	DataScope string `json:"dataScope" gorm:"column:data_scope"`

	// 角色状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag"`

	// 菜单组
	MenuIds []int64 `json:"menuIds" gorm:"-"`

	// 部门组（数据权限）
	DeptIds []int64 `json:"deptIds" gorm:"-"`

	// 用户是否存在此角色标识
	Flag bool `json:"flag" gorm:"-"`
}

// TableName 设置表名
func (SysRole) TableName() string {
	return "sys_role"
}
