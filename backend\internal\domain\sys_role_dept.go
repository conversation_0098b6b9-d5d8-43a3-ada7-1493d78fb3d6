package domain

// SysRoleDept 角色和部门关联表 sys_role_dept
type SysRoleDept struct {
	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key"`

	// 部门ID
	DeptId int64 `json:"deptId" gorm:"column:dept_id;primary_key"`
}

// TableName 设置表名
func (SysRoleDept) TableName() string {
	return "sys_role_dept"
}

// GetRoleId 获取角色ID
func (rd *SysRoleDept) GetRoleId() int64 {
	return rd.RoleId
}

// SetRoleId 设置角色ID
func (rd *SysRoleDept) SetRoleId(roleId int64) {
	rd.RoleId = roleId
}

// GetDeptId 获取部门ID
func (rd *SysRoleDept) GetDeptId() int64 {
	return rd.DeptId
}

// SetDeptId 设置部门ID
func (rd *SysRoleDept) SetDeptId(deptId int64) {
	rd.DeptId = deptId
}
