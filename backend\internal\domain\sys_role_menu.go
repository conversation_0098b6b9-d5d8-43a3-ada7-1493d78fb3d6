package domain

// SysRoleMenu 角色和菜单关联表 sys_role_menu
type SysRoleMenu struct {
	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key"`

	// 菜单ID
	MenuId int64 `json:"menuId" gorm:"column:menu_id;primary_key"`
}

// TableName 设置表名
func (SysRoleMenu) TableName() string {
	return "sys_role_menu"
}

// GetRoleId 获取角色ID
func (rm *SysRoleMenu) GetRoleId() int64 {
	return rm.RoleId
}

// SetRoleId 设置角色ID
func (rm *SysRoleMenu) SetRoleId(roleId int64) {
	rm.RoleId = roleId
}

// GetMenuId 获取菜单ID
func (rm *SysRoleMenu) GetMenuId() int64 {
	return rm.MenuId
}

// SetMenuId 设置菜单ID
func (rm *SysRoleMenu) SetMenuId(menuId int64) {
	rm.MenuId = menuId
}
