package domain

import (
	"time"
)

// SysUserOnline 当前在线会话 sys_user_online
type SysUserOnline struct {
	BaseEntity

	// 用户会话id
	TokenId string `json:"tokenId" gorm:"column:token_id;primary_key"`

	// 部门名称
	DeptName string `json:"deptName" gorm:"column:dept_name"`

	// 用户名称
	UserName string `json:"userName" gorm:"column:user_name"`

	// 登录IP地址
	Ipaddr string `json:"ipaddr" gorm:"column:ipaddr"`

	// 登录地址
	LoginLocation string `json:"loginLocation" gorm:"column:login_location"`

	// 浏览器类型
	Browser string `json:"browser" gorm:"column:browser"`

	// 操作系统
	Os string `json:"os" gorm:"column:os"`

	// 登录时间
	LoginTime int64 `json:"loginTime" gorm:"column:login_time"`

	// 参数集合
	params map[string]interface{}
}

// TableName 设置表名
func (SysUserOnline) TableName() string {
	return "sys_user_online"
}

// GetTokenId 获取用户会话id
func (u *SysUserOnline) GetTokenId() string {
	return u.TokenId
}

// SetTokenId 设置用户会话id
func (u *SysUserOnline) SetTokenId(tokenId string) {
	u.TokenId = tokenId
}

// GetDeptName 获取部门名称
func (u *SysUserOnline) GetDeptName() string {
	return u.DeptName
}

// SetDeptName 设置部门名称
func (u *SysUserOnline) SetDeptName(deptName string) {
	u.DeptName = deptName
}

// GetUserName 获取用户名称
func (u *SysUserOnline) GetUserName() string {
	return u.UserName
}

// SetUserName 设置用户名称
func (u *SysUserOnline) SetUserName(userName string) {
	u.UserName = userName
}

// GetIpaddr 获取登录IP地址
func (u *SysUserOnline) GetIpaddr() string {
	return u.Ipaddr
}

// SetIpaddr 设置登录IP地址
func (u *SysUserOnline) SetIpaddr(ipaddr string) {
	u.Ipaddr = ipaddr
}

// GetLoginLocation 获取登录地址
func (u *SysUserOnline) GetLoginLocation() string {
	return u.LoginLocation
}

// SetLoginLocation 设置登录地址
func (u *SysUserOnline) SetLoginLocation(loginLocation string) {
	u.LoginLocation = loginLocation
}

// GetBrowser 获取浏览器类型
func (u *SysUserOnline) GetBrowser() string {
	return u.Browser
}

// SetBrowser 设置浏览器类型
func (u *SysUserOnline) SetBrowser(browser string) {
	u.Browser = browser
}

// GetOs 获取操作系统
func (u *SysUserOnline) GetOs() string {
	return u.Os
}

// SetOs 设置操作系统
func (u *SysUserOnline) SetOs(os string) {
	u.Os = os
}

// GetLoginTime 获取登录时间
func (u *SysUserOnline) GetLoginTime() int64 {
	return u.LoginTime
}

// SetLoginTime 设置登录时间
func (u *SysUserOnline) SetLoginTime(loginTime int64) {
	u.LoginTime = loginTime
}

// SetParams 设置请求参数
func (u *SysUserOnline) SetParams(params map[string]interface{}) {
	u.params = params
}

// GetParams 获取请求参数
func (u *SysUserOnline) GetParams() map[string]interface{} {
	return u.params
}

// GetLoginTimeFormatted 获取登录时间，返回格式化的字符串
func (u *SysUserOnline) GetLoginTimeFormatted() string {
	return time.Unix(u.LoginTime, 0).Format("2006-01-02 15:04:05")
}
