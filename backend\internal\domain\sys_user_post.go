package domain

// SysUserPost 用户与岗位关联表 sys_user_post
type SysUserPost struct {
	// 用户ID
	UserId int64 `json:"userId" gorm:"column:user_id;primary_key"`

	// 岗位ID
	PostId int64 `json:"postId" gorm:"column:post_id;primary_key"`
}

// TableName 设置表名
func (SysUserPost) TableName() string {
	return "sys_user_post"
}

// GetUserId 获取用户ID
func (up *SysUserPost) GetUserId() int64 {
	return up.UserId
}

// SetUserId 设置用户ID
func (up *SysUserPost) SetUserId(userId int64) {
	up.UserId = userId
}

// GetPostId 获取岗位ID
func (up *SysUserPost) GetPostId() int64 {
	return up.PostId
}

// SetPostId 设置岗位ID
func (up *SysUserPost) SetPostId(postId int64) {
	up.PostId = postId
}
