package domain

// SysUserRole 用户和角色关联表 sys_user_role
type SysUserRole struct {
	// 用户ID
	UserId int64 `json:"userId" gorm:"column:user_id;primary_key"`

	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key"`
}

// TableName 设置表名
func (SysUserRole) TableName() string {
	return "sys_user_role"
}

// GetUserId 获取用户ID
func (ur *SysUserRole) GetUserId() int64 {
	return ur.UserId
}

// SetUserId 设置用户ID
func (ur *SysUserRole) SetUserId(userId int64) {
	ur.UserId = userId
}

// GetRoleId 获取角色ID
func (ur *SysUserRole) GetRoleId() int64 {
	return ur.RoleId
}

// SetRoleId 设置角色ID
func (ur *SysUserRole) SetRoleId(roleId int64) {
	ur.RoleId = roleId
}
