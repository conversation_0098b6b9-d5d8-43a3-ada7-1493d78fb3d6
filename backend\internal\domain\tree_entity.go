package domain

// TreeEntity 树形实体基类，对应Java中的TreeEntity
type TreeEntity struct {
	BaseEntity

	// 父菜单名称
	ParentName string `json:"parentName" gorm:"-"`

	// 父菜单ID
	ParentId int64 `json:"parentId" gorm:"column:parent_id"`

	// 显示顺序
	OrderNum int `json:"orderNum" gorm:"column:order_num"`

	// 祖级列表
	Ancestors string `json:"ancestors" gorm:"column:ancestors"`

	// 子部门
	Children []interface{} `json:"children" gorm:"-"`
}

// GetParentName 获取父菜单名称
func (e *TreeEntity) GetParentName() string {
	return e.ParentName
}

// SetParentName 设置父菜单名称
func (e *TreeEntity) SetParentName(parentName string) {
	e.ParentName = parentName
}

// GetParentId 获取父菜单ID
func (e *TreeEntity) GetParentId() int64 {
	return e.ParentId
}

// SetParentId 设置父菜单ID
func (e *TreeEntity) SetParentId(parentId int64) {
	e.ParentId = parentId
}

// GetOrderNum 获取显示顺序
func (e *TreeEntity) GetOrderNum() int {
	return e.OrderNum
}

// SetOrderNum 设置显示顺序
func (e *TreeEntity) SetOrderNum(orderNum int) {
	e.OrderNum = orderNum
}

// GetAncestors 获取祖级列表
func (e *TreeEntity) GetAncestors() string {
	return e.Ancestors
}

// SetAncestors 设置祖级列表
func (e *TreeEntity) SetAncestors(ancestors string) {
	e.Ancestors = ancestors
}

// GetChildren 获取子部门
func (e *TreeEntity) GetChildren() []interface{} {
	return e.Children
}

// SetChildren 设置子部门
func (e *TreeEntity) SetChildren(children []interface{}) {
	e.Children = children
}
