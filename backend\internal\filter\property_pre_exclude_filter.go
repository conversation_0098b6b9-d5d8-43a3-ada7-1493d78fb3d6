package filter

import (
	"strings"
)

// PropertyPreExcludeFilter 属性预排除过滤器
// 用于在JSON序列化时排除指定的属性
type PropertyPreExcludeFilter struct {
	// 需要排除的属性集合
	excludes map[string]bool
}

// NewPropertyPreExcludeFilter 创建一个新的属性预排除过滤器
func NewPropertyPreExcludeFilter() *PropertyPreExcludeFilter {
	return &PropertyPreExcludeFilter{
		excludes: make(map[string]bool),
	}
}

// AddExcludes 添加需要排除的属性
// filters 格式为逗号分隔的属性名列表
func (f *PropertyPreExcludeFilter) AddExcludes(filters string) *PropertyPreExcludeFilter {
	if filters != "" {
		// 按逗号分隔
		props := strings.Split(filters, ",")
		for _, prop := range props {
			// 去除空格并添加到排除集合
			f.excludes[strings.TrimSpace(prop)] = true
		}
	}
	return f
}

// ShouldFilter 判断是否应该过滤指定属性
func (f *PropertyPreExcludeFilter) ShouldFilter(propertyName string) bool {
	return f.excludes[propertyName]
}

// GetExcludes 获取所有需要排除的属性
func (f *PropertyPreExcludeFilter) GetExcludes() []string {
	result := make([]string, 0, len(f.excludes))
	for key := range f.excludes {
		result = append(result, key)
	}
	return result
}
