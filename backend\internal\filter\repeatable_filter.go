package filter

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RepeatableFilter 可重复请求过滤器
// 用于包装HTTP请求，使其请求体可以被重复读取
type RepeatableFilter struct {
	logger *zap.Logger
}

// NewRepeatableFilter 创建一个新的可重复请求过滤器
func NewRepeatableFilter(logger *zap.Logger) *RepeatableFilter {
	return &RepeatableFilter{
		logger: logger,
	}
}

// Filter Gin中间件函数，用于处理请求
func (f *RepeatableFilter) Filter() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理有请求体的请求
		if shouldWrapRequest(c.Request) {
			// 包装请求
			wrapper, err := NewRepeatedlyRequestWrapper(c.Request)
			if err != nil {
				f.logger.Error("包装请求失败", zap.Error(err))
				c.Next()
				return
			}

			// 将包装器存储到上下文中，以便后续使用
			c.Set("requestWrapper", wrapper)
		}

		// 继续处理请求
		c.Next()
	}
}

// 判断是否应该包装请求
func shouldWrapRequest(request *http.Request) bool {
	// 只处理POST、PUT、DELETE请求
	if request.Method != http.MethodPost && request.Method != http.MethodPut && request.Method != http.MethodDelete {
		return false
	}

	// 检查Content-Type
	contentType := request.Header.Get("Content-Type")
	if contentType == "" {
		return false
	}

	// 只处理JSON和表单请求
	return strings.Contains(contentType, "application/json") ||
		strings.Contains(contentType, "application/x-www-form-urlencoded")
}

// GetRequestWrapper 从上下文中获取请求包装器
func GetRequestWrapper(c *gin.Context) *RepeatedlyRequestWrapper {
	if wrapper, exists := c.Get("requestWrapper"); exists {
		if w, ok := wrapper.(*RepeatedlyRequestWrapper); ok {
			return w
		}
	}
	return nil
}
