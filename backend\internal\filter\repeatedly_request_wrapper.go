package filter

import (
	"bytes"
	"io"
	"io/ioutil"
	"net/http"
)

// RepeatedlyRequestWrapper 可重复读取的请求包装器
// 用于解决HTTP请求体只能读取一次的问题
type RepeatedlyRequestWrapper struct {
	// 原始请求
	request *http.Request
	// 缓存的请求体
	body []byte
}

// NewRepeatedlyRequestWrapper 创建一个新的请求包装器
func NewRepeatedlyRequestWrapper(request *http.Request) (*RepeatedlyRequestWrapper, error) {
	// 读取原始请求体
	body, err := ioutil.ReadAll(request.Body)
	if err != nil {
		return nil, err
	}

	// 重置原始请求体，以便后续处理
	request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

	// 创建包装器
	return &RepeatedlyRequestWrapper{
		request: request,
		body:    body,
	}, nil
}

// GetBody 获取请求体字节数组
func (w *RepeatedlyRequestWrapper) GetBody() []byte {
	return w.body
}

// GetBodyString 获取请求体字符串
func (w *RepeatedlyRequestWrapper) GetBodyString() string {
	return string(w.body)
}

// GetReader 获取请求体的Reader
func (w *RepeatedlyRequestWrapper) GetReader() io.Reader {
	return bytes.NewReader(w.body)
}

// GetRequest 获取原始请求
func (w *RepeatedlyRequestWrapper) GetRequest() *http.Request {
	return w.request
}

// ResetBody 重置请求体
// 在每次读取请求体后调用，以便下一次读取
func (w *RepeatedlyRequestWrapper) ResetBody() {
	w.request.Body = ioutil.NopCloser(bytes.NewBuffer(w.body))
}
