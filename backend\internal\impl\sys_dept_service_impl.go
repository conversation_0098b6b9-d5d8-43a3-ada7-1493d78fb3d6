package impl

// SysDeptServiceImpl Service implementation
type SysDeptServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysDeptServiceImpl Create service instance
func NewSysDeptServiceImpl() *SysDeptServiceImpl {
	return &%!s(MISSING){}
}

// SelectDeptList Implement SysDeptService interface
func (s *SysDeptServiceImpl) SelectDeptList(dept SysDept) []SysDept {
	// TODO: Implement method logic
	return nil
}

// SelectDeptTreeList Implement SysDeptService interface
func (s *SysDeptServiceImpl) SelectDeptTreeList(dept SysDept) []TreeSelect {
	// TODO: Implement method logic
	return nil
}

// BuildDeptTree Implement SysDeptService interface
func (s *SysDeptServiceImpl) BuildDeptTree(depts []SysDept) []SysDept {
	// TODO: Implement method logic
	return nil
}

// BuildDeptTreeSelect Implement SysDeptService interface
func (s *SysDeptServiceImpl) BuildDeptTreeSelect(depts []SysDept) []TreeSelect {
	// TODO: Implement method logic
	return nil
}

// SelectDeptListByRoleId Implement SysDeptService interface
func (s *SysDeptServiceImpl) SelectDeptListByRoleId(roleId int64) []int64 {
	// TODO: Implement method logic
	return nil
}

// SelectDeptById Implement SysDeptService interface
func (s *SysDeptServiceImpl) SelectDeptById(deptId int64) SysDept {
	// TODO: Implement method logic
	return nil
}

// SelectNormalChildrenDeptById Implement SysDeptService interface
func (s *SysDeptServiceImpl) SelectNormalChildrenDeptById(deptId int64) int {
	// TODO: Implement method logic
	return 0
}

// HasChildByDeptId Implement SysDeptService interface
func (s *SysDeptServiceImpl) HasChildByDeptId(deptId int64) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckDeptExistUser Implement SysDeptService interface
func (s *SysDeptServiceImpl) CheckDeptExistUser(deptId int64) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckDeptNameUnique Implement SysDeptService interface
func (s *SysDeptServiceImpl) CheckDeptNameUnique(dept SysDept) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckDeptDataScope Implement SysDeptService interface
func (s *SysDeptServiceImpl) CheckDeptDataScope(deptId int64) {
	// TODO: Implement method logic
}

// InsertDept Implement SysDeptService interface
func (s *SysDeptServiceImpl) InsertDept(dept SysDept) int {
	// TODO: Implement method logic
	return 0
}

// UpdateDept Implement SysDeptService interface
func (s *SysDeptServiceImpl) UpdateDept(dept SysDept) int {
	// TODO: Implement method logic
	return 0
}

// UpdateParentDeptStatusNormal Implement SysDeptService interface
func (s *SysDeptServiceImpl) UpdateParentDeptStatusNormal(dept SysDept) {
	// TODO: Implement method logic
}

// UpdateDeptChildren Implement SysDeptService interface
func (s *SysDeptServiceImpl) UpdateDeptChildren(deptId int64, newAncestors string, oldAncestors string) {
	// TODO: Implement method logic
}

// DeleteDeptById Implement SysDeptService interface
func (s *SysDeptServiceImpl) DeleteDeptById(deptId int64) int {
	// TODO: Implement method logic
	return 0
}

// RecursionFn Implement SysDeptService interface
func (s *SysDeptServiceImpl) RecursionFn(list []SysDept, t SysDept) {
	// TODO: Implement method logic
}

// GetChildList Implement SysDeptService interface
func (s *SysDeptServiceImpl) GetChildList(list []SysDept, t SysDept) []SysDept {
	// TODO: Implement method logic
	return nil
}

// HasChild Implement SysDeptService interface
func (s *SysDeptServiceImpl) HasChild(list []SysDept, t SysDept) boolean {
	// TODO: Implement method logic
	return nil
}
