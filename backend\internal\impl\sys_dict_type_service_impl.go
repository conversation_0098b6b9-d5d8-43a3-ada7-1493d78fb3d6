package impl

// SysDictTypeServiceImpl Service implementation
type SysDictTypeServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysDictTypeServiceImpl Create service instance
func NewSysDictTypeServiceImpl() *SysDictTypeServiceImpl {
	return &%!s(MISSING){}
}

// Init Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) Init() {
	// TODO: Implement method logic
}

// SelectDictTypeList Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) SelectDictTypeList(dictType SysDictType) []SysDictType {
	// TODO: Implement method logic
	return nil
}

// SelectDictTypeAll Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) SelectDictTypeAll() []SysDictType {
	// TODO: Implement method logic
	return nil
}

// SelectDictDataByType Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) SelectDictDataByType(dictType string) []SysDictData {
	// TODO: Implement method logic
	return nil
}

// SelectDictTypeById Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) SelectDictTypeById(dictId int64) SysDictType {
	// TODO: Implement method logic
	return nil
}

// SelectDictTypeByType Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) SelectDictTypeByType(dictType string) SysDictType {
	// TODO: Implement method logic
	return nil
}

// DeleteDictTypeByIds Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) DeleteDictTypeByIds(dictIds []int64) {
	// TODO: Implement method logic
}

// LoadingDictCache Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) LoadingDictCache() {
	// TODO: Implement method logic
}

// ClearDictCache Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) ClearDictCache() {
	// TODO: Implement method logic
}

// ResetDictCache Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) ResetDictCache() {
	// TODO: Implement method logic
}

// InsertDictType Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) InsertDictType(dict SysDictType) int {
	// TODO: Implement method logic
	return 0
}

// UpdateDictType Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) UpdateDictType(dict SysDictType) int {
	// TODO: Implement method logic
	return 0
}

// CheckDictTypeUnique Implement SysDictTypeService interface
func (s *SysDictTypeServiceImpl) CheckDictTypeUnique(dict SysDictType) boolean {
	// TODO: Implement method logic
	return nil
}
