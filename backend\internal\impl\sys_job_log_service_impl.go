package impl

// SysJobLogServiceImpl Service implementation
type SysJobLogServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysJobLogServiceImpl Create service instance
func NewSysJobLogServiceImpl() *SysJobLogServiceImpl {
	return &%!s(MISSING){}
}

// SelectJobLogList Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) SelectJobLogList(jobLog SysJobLog) []SysJobLog {
	// TODO: Implement method logic
	return nil
}

// SelectJobLogById Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) SelectJobLogById(jobLogId int64) SysJobLog {
	// TODO: Implement method logic
	return nil
}

// AddJobLog Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) AddJobLog(jobLog SysJobLog) {
	// TODO: Implement method logic
}

// DeleteJobLogByIds Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) DeleteJobLogByIds(logIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// DeleteJobLogById Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) DeleteJobLogById(jobId int64) int {
	// TODO: Implement method logic
	return 0
}

// CleanJobLog Implement SysJobLogService interface
func (s *SysJobLogServiceImpl) CleanJobLog() {
	// TODO: Implement method logic
}
