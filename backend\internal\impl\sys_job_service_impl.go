package impl

// SysJobServiceImpl Service implementation
type SysJobServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysJobServiceImpl Create service instance
func NewSysJobServiceImpl() *SysJobServiceImpl {
	return &%!s(MISSING){}
}

// Init Implement SysJobService interface
func (s *SysJobServiceImpl) Init() {
	// TODO: Implement method logic
}

// SelectJobList Implement SysJobService interface
func (s *SysJobServiceImpl) SelectJobList(job SysJob) []SysJob {
	// TODO: Implement method logic
	return nil
}

// SelectJobById Implement SysJobService interface
func (s *SysJobServiceImpl) SelectJobById(jobId int64) SysJob {
	// TODO: Implement method logic
	return nil
}

// PauseJob Implement SysJobService interface
func (s *SysJobServiceImpl) PauseJob(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// ResumeJob Implement SysJobService interface
func (s *SysJobServiceImpl) ResumeJob(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// DeleteJob Implement SysJobService interface
func (s *SysJobServiceImpl) DeleteJob(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// DeleteJobByIds Implement SysJobService interface
func (s *SysJobServiceImpl) DeleteJobByIds(jobIds []int64) {
	// TODO: Implement method logic
}

// ChangeStatus Implement SysJobService interface
func (s *SysJobServiceImpl) ChangeStatus(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// Run Implement SysJobService interface
func (s *SysJobServiceImpl) Run(job SysJob) boolean {
	// TODO: Implement method logic
	return nil
}

// InsertJob Implement SysJobService interface
func (s *SysJobServiceImpl) InsertJob(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// UpdateJob Implement SysJobService interface
func (s *SysJobServiceImpl) UpdateJob(job SysJob) int {
	// TODO: Implement method logic
	return 0
}

// UpdateSchedulerJob Implement SysJobService interface
func (s *SysJobServiceImpl) UpdateSchedulerJob(job SysJob, jobGroup string) {
	// TODO: Implement method logic
}

// CheckCronExpressionIsValid Implement SysJobService interface
func (s *SysJobServiceImpl) CheckCronExpressionIsValid(cronExpression string) boolean {
	// TODO: Implement method logic
	return nil
}
