package impl

import (
	"errors"
	"fmt"
	"time"

	"github.com/mojocn/base64Captcha"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 定义常量
const (
	// 验证码缓存前缀
	CaptchaPrefix = "captcha:"
	// 验证码默认有效期（分钟）
	CaptchaExpire = 2
)

// SysLoginServiceImpl 登录服务实现
type SysLoginServiceImpl struct {
	logger       *zap.Logger
	db           *gorm.DB
	tokenService service.TokenService
	userService  service.ISysUserService
	captchaStore base64Captcha.Store
}

// NewSysLoginServiceImpl 创建登录服务实现
func NewSysLoginServiceImpl(
	logger *zap.Logger,
	db *gorm.DB,
	tokenService service.TokenService,
	userService service.ISysUserService,
) service.SysLoginService {
	// 初始化验证码存储
	captchaStore := base64Captcha.DefaultMemStore

	return &SysLoginServiceImpl{
		logger:       logger,
		db:           db,
		tokenService: tokenService,
		userService:  userService,
		captchaStore: captchaStore,
	}
}

// Login 登录验证
func (s *SysLoginServiceImpl) Login(username string, password string, code string, uuid string) (string, error) {
	// 登录前置校验
	if err := s.LoginPreCheck(username, password); err != nil {
		return "", err
	}

	// 验证码校验
	if err := s.ValidateCaptcha(username, code, uuid); err != nil {
		return "", err
	}

	// 用户验证
	user := s.userService.SelectUserByUserName(username)
	if user.UserId == 0 {
		s.RecordLoginInfo(0)
		return "", errors.New("用户不存在或密码错误")
	}

	if user.DelFlag == "2" {
		s.RecordLoginInfo(0)
		return "", errors.New("对不起，您的账号已被删除")
	}

	if user.Status == "1" {
		s.RecordLoginInfo(0)
		return "", errors.New("对不起，您的账号已被停用")
	}

	// 密码校验（暂时不加密直接比较）
	if user.Password != password {
		s.RecordLoginInfo(0)
		return "", errors.New("用户不存在或密码错误")
	}

	// 记录登录信息
	s.RecordLoginInfo(user.UserId)

	// 生成token
	loginUser := &model.LoginUser{
		UserId: user.UserId,
		DeptId: user.DeptId,
		User:   user,
	}

	// 生成token
	token := s.tokenService.CreateToken(loginUser)

	return token, nil
}

// ValidateCaptcha 验证验证码
func (s *SysLoginServiceImpl) ValidateCaptcha(username string, code string, uuid string) error {
	if len(code) == 0 || len(uuid) == 0 {
		return errors.New("验证码不能为空")
	}

	// 获取验证码
	key := fmt.Sprintf("%s%s", CaptchaPrefix, uuid)

	// 验证验证码
	if !s.captchaStore.Verify(key, code, true) {
		return errors.New("验证码错误")
	}

	return nil
}

// LoginPreCheck 登录前置校验
func (s *SysLoginServiceImpl) LoginPreCheck(username string, password string) error {
	// 用户名或密码为空
	if len(username) == 0 || len(password) == 0 {
		return errors.New("用户名或密码不能为空")
	}

	// 用户名不在指定范围内
	if len(username) < 2 || len(username) > 20 {
		return errors.New("用户名长度必须在2到20个字符之间")
	}

	// 密码不在指定范围内
	if len(password) < 5 || len(password) > 20 {
		return errors.New("密码长度必须在5到20个字符之间")
	}

	return nil
}

// RecordLoginInfo 记录登录信息
func (s *SysLoginServiceImpl) RecordLoginInfo(userId int64) error {
	// 获取客户端IP
	ip := utils.GetLocalIP()

	// 获取登录地点
	loginLocation := "内网IP"

	// 获取浏览器和操作系统
	browser := "Chrome"
	os := "Windows 10"

	// 当前时间
	loginTime := time.Now()

	// 封装对象
	logininfor := domain.SysLogininfor{
		UserName:      "", // 这里应该是通过userId查询到的用户名
		Ipaddr:        ip,
		LoginLocation: loginLocation,
		Browser:       browser,
		Os:            os,
		Status:        "0", // 0成功 1失败
		Msg:           "登录成功",
		LoginTime:     &loginTime,
	}

	if userId != 0 {
		// 查询用户信息
		user := s.userService.SelectUserById(userId)
		logininfor.UserName = user.UserName
	}

	// 插入数据
	result := s.db.Create(&logininfor)
	return result.Error
}

// Logout 退出登录
func (s *SysLoginServiceImpl) Logout(token string) error {
	// 删除用户缓存记录
	s.tokenService.DelLoginUser(token)
	return nil
}

// GetUserInfo 获取用户信息
func (s *SysLoginServiceImpl) GetUserInfo(userId int64) (map[string]interface{}, error) {
	user := s.userService.SelectUserById(userId)
	if user.UserId == 0 {
		return nil, errors.New("用户不存在")
	}

	// 角色集合
	roles := s.userService.SelectUserRoleGroup(user.UserName)

	// 权限集合
	permissions := []string{"*:*:*"}

	result := map[string]interface{}{
		"user":        user,
		"roles":       roles,
		"permissions": permissions,
	}

	return result, nil
}

// GetRouters 获取路由信息
func (s *SysLoginServiceImpl) GetRouters(userId int64) ([]model.Router, error) {
	// 模拟路由数据
	dashboard := model.RouterVo{
		Name:      "Dashboard",
		Path:      "/dashboard",
		Component: "dashboard/index",
		Meta: model.MetaVo{
			Title: "首页",
			Icon:  "dashboard",
		},
	}

	system := model.RouterVo{
		Name:       "System",
		Path:       "/system",
		Component:  "Layout",
		AlwaysShow: true,
		Meta: model.MetaVo{
			Title: "系统管理",
			Icon:  "system",
		},
		Children: []model.RouterVo{
			{
				Name:      "User",
				Path:      "user",
				Component: "system/user/index",
				Meta: model.MetaVo{
					Title: "用户管理",
					Icon:  "user",
				},
			},
			{
				Name:      "Role",
				Path:      "role",
				Component: "system/role/index",
				Meta: model.MetaVo{
					Title: "角色管理",
					Icon:  "role",
				},
			},
		},
	}

	// 构造路由
	routerVos := []model.RouterVo{dashboard, system}
	router := model.Router{
		Data: routerVos,
	}

	return []model.Router{router}, nil
}

// GenerateCaptcha 生成验证码
func (s *SysLoginServiceImpl) GenerateCaptcha() (string, string, error) {
	// 设置验证码参数
	driver := base64Captcha.NewDriverDigit(40, 120, 4, 0.7, 80)

	// 生成唯一ID
	uuid := utils.GenerateUUID()
	key := fmt.Sprintf("%s%s", CaptchaPrefix, uuid)

	// 创建验证码
	captcha := base64Captcha.NewCaptcha(driver, s.captchaStore)
	id, b64s, _, err := captcha.Generate()
	if err != nil {
		return "", "", err
	}

	// 存储验证码
	s.captchaStore.Set(key, id)

	return uuid, b64s, nil
}
