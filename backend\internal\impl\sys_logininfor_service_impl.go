package impl

// SysLogininforServiceImpl Service implementation
type SysLogininforServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysLogininforServiceImpl Create service instance
func NewSysLogininforServiceImpl() *SysLogininforServiceImpl {
	return &%!s(MISSING){}
}

// InsertLogininfor Implement SysLogininforService interface
func (s *SysLogininforServiceImpl) InsertLogininfor(logininfor SysLogininfor) {
	// TODO: Implement method logic
}

// SelectLogininforList Implement SysLogininforService interface
func (s *SysLogininforServiceImpl) SelectLogininforList(logininfor SysLogininfor) []SysLogininfor {
	// TODO: Implement method logic
	return nil
}

// DeleteLogininforByIds Implement SysLogininforService interface
func (s *SysLogininforServiceImpl) DeleteLogininforByIds(infoIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// CleanLogininfor Implement SysLogininforService interface
func (s *SysLogininforServiceImpl) CleanLogininfor() {
	// TODO: Implement method logic
}
