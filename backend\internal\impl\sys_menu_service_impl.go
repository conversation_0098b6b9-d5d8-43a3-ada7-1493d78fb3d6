package impl

// SysMenuServiceImpl Service implementation
type SysMenuServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysMenuServiceImpl Create service instance
func NewSysMenuServiceImpl() *SysMenuServiceImpl {
	return &%!s(MISSING){}
}

// SelectMenuList Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuList(userId int64) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// SelectMenuList Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuList(menu SysMenu, userId int64) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// SelectMenuPermsByUserId Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuPermsByUserId(userId int64) Set<String> {
	// TODO: Implement method logic
	return nil
}

// SelectMenuPermsByRoleId Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuPermsByRoleId(roleId int64) Set<String> {
	// TODO: Implement method logic
	return nil
}

// SelectMenuTreeByUserId Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuTreeByUserId(userId int64) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// SelectMenuListByRoleId Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuListByRoleId(roleId int64) []int64 {
	// TODO: Implement method logic
	return nil
}

// BuildMenus Implement SysMenuService interface
func (s *SysMenuServiceImpl) BuildMenus(menus []SysMenu) []RouterVo {
	// TODO: Implement method logic
	return nil
}

// BuildMenuTree Implement SysMenuService interface
func (s *SysMenuServiceImpl) BuildMenuTree(menus []SysMenu) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// BuildMenuTreeSelect Implement SysMenuService interface
func (s *SysMenuServiceImpl) BuildMenuTreeSelect(menus []SysMenu) []TreeSelect {
	// TODO: Implement method logic
	return nil
}

// SelectMenuById Implement SysMenuService interface
func (s *SysMenuServiceImpl) SelectMenuById(menuId int64) SysMenu {
	// TODO: Implement method logic
	return nil
}

// HasChildByMenuId Implement SysMenuService interface
func (s *SysMenuServiceImpl) HasChildByMenuId(menuId int64) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckMenuExistRole Implement SysMenuService interface
func (s *SysMenuServiceImpl) CheckMenuExistRole(menuId int64) boolean {
	// TODO: Implement method logic
	return nil
}

// InsertMenu Implement SysMenuService interface
func (s *SysMenuServiceImpl) InsertMenu(menu SysMenu) int {
	// TODO: Implement method logic
	return 0
}

// UpdateMenu Implement SysMenuService interface
func (s *SysMenuServiceImpl) UpdateMenu(menu SysMenu) int {
	// TODO: Implement method logic
	return 0
}

// DeleteMenuById Implement SysMenuService interface
func (s *SysMenuServiceImpl) DeleteMenuById(menuId int64) int {
	// TODO: Implement method logic
	return 0
}

// CheckMenuNameUnique Implement SysMenuService interface
func (s *SysMenuServiceImpl) CheckMenuNameUnique(menu SysMenu) boolean {
	// TODO: Implement method logic
	return nil
}

// GetRouteName Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetRouteName(menu SysMenu) string {
	// TODO: Implement method logic
	return ""
}

// GetRouteName Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetRouteName(name string, path string) string {
	// TODO: Implement method logic
	return ""
}

// GetRouterPath Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetRouterPath(menu SysMenu) string {
	// TODO: Implement method logic
	return ""
}

// GetComponent Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetComponent(menu SysMenu) string {
	// TODO: Implement method logic
	return ""
}

// IsMenuFrame Implement SysMenuService interface
func (s *SysMenuServiceImpl) IsMenuFrame(menu SysMenu) boolean {
	// TODO: Implement method logic
	return nil
}

// IsInnerLink Implement SysMenuService interface
func (s *SysMenuServiceImpl) IsInnerLink(menu SysMenu) boolean {
	// TODO: Implement method logic
	return nil
}

// IsParentView Implement SysMenuService interface
func (s *SysMenuServiceImpl) IsParentView(menu SysMenu) boolean {
	// TODO: Implement method logic
	return nil
}

// GetChildPerms Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetChildPerms(list []SysMenu, parentId int) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// RecursionFn Implement SysMenuService interface
func (s *SysMenuServiceImpl) RecursionFn(list []SysMenu, t SysMenu) {
	// TODO: Implement method logic
}

// GetChildList Implement SysMenuService interface
func (s *SysMenuServiceImpl) GetChildList(list []SysMenu, t SysMenu) []SysMenu {
	// TODO: Implement method logic
	return nil
}

// HasChild Implement SysMenuService interface
func (s *SysMenuServiceImpl) HasChild(list []SysMenu, t SysMenu) boolean {
	// TODO: Implement method logic
	return nil
}

// InnerLinkReplaceEach Implement SysMenuService interface
func (s *SysMenuServiceImpl) InnerLinkReplaceEach(path string) string {
	// TODO: Implement method logic
	return ""
}
