package impl

// SysNoticeServiceImpl Service implementation
type SysNoticeServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysNoticeServiceImpl Create service instance
func NewSysNoticeServiceImpl() *SysNoticeServiceImpl {
	return &%!s(MISSING){}
}

// SelectNoticeById Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) SelectNoticeById(noticeId int64) SysNotice {
	// TODO: Implement method logic
	return nil
}

// SelectNoticeList Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) SelectNoticeList(notice SysNotice) []SysNotice {
	// TODO: Implement method logic
	return nil
}

// InsertNotice Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) InsertNotice(notice SysNotice) int {
	// TODO: Implement method logic
	return 0
}

// UpdateNotice Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) UpdateNotice(notice SysNotice) int {
	// TODO: Implement method logic
	return 0
}

// DeleteNoticeById Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) DeleteNoticeById(noticeId int64) int {
	// TODO: Implement method logic
	return 0
}

// DeleteNoticeByIds Implement SysNoticeService interface
func (s *SysNoticeServiceImpl) DeleteNoticeByIds(noticeIds []int64) int {
	// TODO: Implement method logic
	return 0
}
