package impl

// SysOperLogServiceImpl Service implementation
type SysOperLogServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysOperLogServiceImpl Create service instance
func NewSysOperLogServiceImpl() *SysOperLogServiceImpl {
	return &%!s(MISSING){}
}

// InsertOperlog Implement SysOperLogService interface
func (s *SysOperLogServiceImpl) InsertOperlog(operLog SysOperLog) {
	// TODO: Implement method logic
}

// SelectOperLogList Implement SysOperLogService interface
func (s *SysOperLogServiceImpl) SelectOperLogList(operLog SysOperLog) []SysOperLog {
	// TODO: Implement method logic
	return nil
}

// DeleteOperLogByIds Implement SysOperLogService interface
func (s *SysOperLogServiceImpl) DeleteOperLogByIds(operIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// SelectOperLogById Implement SysOperLogService interface
func (s *SysOperLogServiceImpl) SelectOperLogById(operId int64) SysOperLog {
	// TODO: Implement method logic
	return nil
}

// CleanOperLog Implement SysOperLogService interface
func (s *SysOperLogServiceImpl) CleanOperLog() {
	// TODO: Implement method logic
}
