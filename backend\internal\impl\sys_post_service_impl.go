package impl

// SysPostServiceImpl Service implementation
type SysPostServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysPostServiceImpl Create service instance
func NewSysPostServiceImpl() *SysPostServiceImpl {
	return &%!s(MISSING){}
}

// SelectPostList Implement SysPostService interface
func (s *SysPostServiceImpl) SelectPostList(post SysPost) []SysPost {
	// TODO: Implement method logic
	return nil
}

// SelectPostAll Implement SysPostService interface
func (s *SysPostServiceImpl) SelectPostAll() []SysPost {
	// TODO: Implement method logic
	return nil
}

// SelectPostById Implement SysPostService interface
func (s *SysPostServiceImpl) SelectPostById(postId int64) SysPost {
	// TODO: Implement method logic
	return nil
}

// SelectPostListByUserId Implement SysPostService interface
func (s *SysPostServiceImpl) SelectPostListByUserId(userId int64) []int64 {
	// TODO: Implement method logic
	return nil
}

// CheckPostNameUnique Implement SysPostService interface
func (s *SysPostServiceImpl) CheckPostNameUnique(post SysPost) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckPostCodeUnique Implement SysPostService interface
func (s *SysPostServiceImpl) CheckPostCodeUnique(post SysPost) boolean {
	// TODO: Implement method logic
	return nil
}

// CountUserPostById Implement SysPostService interface
func (s *SysPostServiceImpl) CountUserPostById(postId int64) int {
	// TODO: Implement method logic
	return 0
}

// DeletePostById Implement SysPostService interface
func (s *SysPostServiceImpl) DeletePostById(postId int64) int {
	// TODO: Implement method logic
	return 0
}

// DeletePostByIds Implement SysPostService interface
func (s *SysPostServiceImpl) DeletePostByIds(postIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// InsertPost Implement SysPostService interface
func (s *SysPostServiceImpl) InsertPost(post SysPost) int {
	// TODO: Implement method logic
	return 0
}

// UpdatePost Implement SysPostService interface
func (s *SysPostServiceImpl) UpdatePost(post SysPost) int {
	// TODO: Implement method logic
	return 0
}
