package impl

// SysRoleServiceImpl Service implementation
type SysRoleServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysRoleServiceImpl Create service instance
func NewSysRoleServiceImpl() *SysRoleServiceImpl {
	return &%!s(MISSING){}
}

// SelectRoleList Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRoleList(role SysRole) []SysRole {
	// TODO: Implement method logic
	return nil
}

// SelectRolesByUserId Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRolesByUserId(userId int64) []SysRole {
	// TODO: Implement method logic
	return nil
}

// SelectRolePermissionByUserId Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRolePermissionByUserId(userId int64) Set<String> {
	// TODO: Implement method logic
	return nil
}

// SelectRoleAll Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRoleAll() []SysRole {
	// TODO: Implement method logic
	return nil
}

// SelectRoleListByUserId Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRoleListByUserId(userId int64) []int64 {
	// TODO: Implement method logic
	return nil
}

// SelectRoleById Implement SysRoleService interface
func (s *SysRoleServiceImpl) SelectRoleById(roleId int64) SysRole {
	// TODO: Implement method logic
	return nil
}

// CheckRoleNameUnique Implement SysRoleService interface
func (s *SysRoleServiceImpl) CheckRoleNameUnique(role SysRole) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckRoleKeyUnique Implement SysRoleService interface
func (s *SysRoleServiceImpl) CheckRoleKeyUnique(role SysRole) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckRoleAllowed Implement SysRoleService interface
func (s *SysRoleServiceImpl) CheckRoleAllowed(role SysRole) {
	// TODO: Implement method logic
}

// CheckRoleDataScope Implement SysRoleService interface
func (s *SysRoleServiceImpl) CheckRoleDataScope(roleIds int64) {
	// TODO: Implement method logic
}

// CountUserRoleByRoleId Implement SysRoleService interface
func (s *SysRoleServiceImpl) CountUserRoleByRoleId(roleId int64) int {
	// TODO: Implement method logic
	return 0
}

// InsertRole Implement SysRoleService interface
func (s *SysRoleServiceImpl) InsertRole(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// UpdateRole Implement SysRoleService interface
func (s *SysRoleServiceImpl) UpdateRole(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// UpdateRoleStatus Implement SysRoleService interface
func (s *SysRoleServiceImpl) UpdateRoleStatus(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// AuthDataScope Implement SysRoleService interface
func (s *SysRoleServiceImpl) AuthDataScope(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// InsertRoleMenu Implement SysRoleService interface
func (s *SysRoleServiceImpl) InsertRoleMenu(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// InsertRoleDept Implement SysRoleService interface
func (s *SysRoleServiceImpl) InsertRoleDept(role SysRole) int {
	// TODO: Implement method logic
	return 0
}

// DeleteRoleById Implement SysRoleService interface
func (s *SysRoleServiceImpl) DeleteRoleById(roleId int64) int {
	// TODO: Implement method logic
	return 0
}

// DeleteRoleByIds Implement SysRoleService interface
func (s *SysRoleServiceImpl) DeleteRoleByIds(roleIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// DeleteAuthUser Implement SysRoleService interface
func (s *SysRoleServiceImpl) DeleteAuthUser(userRole SysUserRole) int {
	// TODO: Implement method logic
	return 0
}

// DeleteAuthUsers Implement SysRoleService interface
func (s *SysRoleServiceImpl) DeleteAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// InsertAuthUsers Implement SysRoleService interface
func (s *SysRoleServiceImpl) InsertAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method logic
	return 0
}
