package impl

// SysUserOnlineServiceImpl Service implementation
type SysUserOnlineServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysUserOnlineServiceImpl Create service instance
func NewSysUserOnlineServiceImpl() *SysUserOnlineServiceImpl {
	return &%!s(MISSING){}
}

// SelectOnlineByIpaddr Implement SysUserOnlineService interface
func (s *SysUserOnlineServiceImpl) SelectOnlineByIpaddr(ipaddr string, user LoginUser) SysUserOnline {
	// TODO: Implement method logic
	return nil
}

// SelectOnlineByUserName Implement SysUserOnlineService interface
func (s *SysUserOnlineServiceImpl) SelectOnlineByUserName(userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method logic
	return nil
}

// SelectOnlineByInfo Implement SysUserOnlineService interface
func (s *SysUserOnlineServiceImpl) SelectOnlineByInfo(ipaddr string, userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method logic
	return nil
}

// LoginUserToUserOnline Implement SysUserOnlineService interface
func (s *SysUserOnlineServiceImpl) LoginUserToUserOnline(user LoginUser) SysUserOnline {
	// TODO: Implement method logic
	return nil
}
