package impl

// SysUserServiceImpl Service implementation
type SysUserServiceImpl struct {
	// TODO: Add dependencies
}

// NewSysUserServiceImpl Create service instance
func NewSysUserServiceImpl() *SysUserServiceImpl {
	return &%!s(MISSING){}
}

// SelectUserList Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUserList(user SysUser) []SysUser {
	// TODO: Implement method logic
	return nil
}

// SelectAllocatedList Implement SysUserService interface
func (s *SysUserServiceImpl) SelectAllocatedList(user SysUser) []SysUser {
	// TODO: Implement method logic
	return nil
}

// SelectUnallocatedList Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUnallocatedList(user SysUser) []SysUser {
	// TODO: Implement method logic
	return nil
}

// SelectUserByUserName Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUserByUserName(userName string) SysUser {
	// TODO: Implement method logic
	return nil
}

// SelectUserById Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUserById(userId int64) SysUser {
	// TODO: Implement method logic
	return nil
}

// SelectUserRoleGroup Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUserRoleGroup(userName string) string {
	// TODO: Implement method logic
	return ""
}

// SelectUserPostGroup Implement SysUserService interface
func (s *SysUserServiceImpl) SelectUserPostGroup(userName string) string {
	// TODO: Implement method logic
	return ""
}

// CheckUserNameUnique Implement SysUserService interface
func (s *SysUserServiceImpl) CheckUserNameUnique(user SysUser) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckPhoneUnique Implement SysUserService interface
func (s *SysUserServiceImpl) CheckPhoneUnique(user SysUser) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckEmailUnique Implement SysUserService interface
func (s *SysUserServiceImpl) CheckEmailUnique(user SysUser) boolean {
	// TODO: Implement method logic
	return nil
}

// CheckUserAllowed Implement SysUserService interface
func (s *SysUserServiceImpl) CheckUserAllowed(user SysUser) {
	// TODO: Implement method logic
}

// CheckUserDataScope Implement SysUserService interface
func (s *SysUserServiceImpl) CheckUserDataScope(userId int64) {
	// TODO: Implement method logic
}

// InsertUser Implement SysUserService interface
func (s *SysUserServiceImpl) InsertUser(user SysUser) int {
	// TODO: Implement method logic
	return 0
}

// RegisterUser Implement SysUserService interface
func (s *SysUserServiceImpl) RegisterUser(user SysUser) boolean {
	// TODO: Implement method logic
	return nil
}

// UpdateUser Implement SysUserService interface
func (s *SysUserServiceImpl) UpdateUser(user SysUser) int {
	// TODO: Implement method logic
	return 0
}

// InsertUserAuth Implement SysUserService interface
func (s *SysUserServiceImpl) InsertUserAuth(userId int64, roleIds []int64) {
	// TODO: Implement method logic
}

// UpdateUserStatus Implement SysUserService interface
func (s *SysUserServiceImpl) UpdateUserStatus(user SysUser) int {
	// TODO: Implement method logic
	return 0
}

// UpdateUserProfile Implement SysUserService interface
func (s *SysUserServiceImpl) UpdateUserProfile(user SysUser) int {
	// TODO: Implement method logic
	return 0
}

// UpdateUserAvatar Implement SysUserService interface
func (s *SysUserServiceImpl) UpdateUserAvatar(userId int64, avatar string) boolean {
	// TODO: Implement method logic
	return nil
}

// ResetPwd Implement SysUserService interface
func (s *SysUserServiceImpl) ResetPwd(user SysUser) int {
	// TODO: Implement method logic
	return 0
}

// ResetUserPwd Implement SysUserService interface
func (s *SysUserServiceImpl) ResetUserPwd(userId int64, password string) int {
	// TODO: Implement method logic
	return 0
}

// InsertUserRole Implement SysUserService interface
func (s *SysUserServiceImpl) InsertUserRole(user SysUser) {
	// TODO: Implement method logic
}

// InsertUserPost Implement SysUserService interface
func (s *SysUserServiceImpl) InsertUserPost(user SysUser) {
	// TODO: Implement method logic
}

// InsertUserRole Implement SysUserService interface
func (s *SysUserServiceImpl) InsertUserRole(userId int64, roleIds []int64) {
	// TODO: Implement method logic
}

// DeleteUserById Implement SysUserService interface
func (s *SysUserServiceImpl) DeleteUserById(userId int64) int {
	// TODO: Implement method logic
	return 0
}

// DeleteUserByIds Implement SysUserService interface
func (s *SysUserServiceImpl) DeleteUserByIds(userIds []int64) int {
	// TODO: Implement method logic
	return 0
}

// ImportUser Implement SysUserService interface
func (s *SysUserServiceImpl) ImportUser(userList []SysUser, isUpdateSupport bool, operName string) string {
	// TODO: Implement method logic
	return ""
}
