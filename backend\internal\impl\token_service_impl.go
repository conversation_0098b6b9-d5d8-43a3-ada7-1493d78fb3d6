package impl

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/utils"
	"go.uber.org/zap"
)

// TokenConstants 令牌常量
const (
	// TokenPrefix 令牌前缀
	TokenPrefix = "Bearer "
	// TokenHeaderName 令牌头名称
	TokenHeaderName = "Authorization"
	// UserIdKey 用户ID键
	UserIdKey = "userId"
	// UsernameKey 用户名键
	UsernameKey = "username"
	// LoginUserKey 登录用户键
	LoginUserKey = "login_user:"
	// TokenExpireTime 令牌有效期（默认30分钟）
	TokenExpireTime = 30 * time.Minute
)

// TokenServiceImpl 令牌服务实现
type TokenServiceImpl struct {
	logger    *zap.Logger
	redis     redis.RedisService
	appConfig *config.AppConfig
}

// NewTokenServiceImpl 创建令牌服务实现
func NewTokenServiceImpl(
	logger *zap.Logger,
	redis redis.RedisService,
	appConfig *config.AppConfig,
) service.TokenService {
	return &TokenServiceImpl{
		logger:    logger,
		redis:     redis,
		appConfig: appConfig,
	}
}

// GetLoginUser 获取登录用户
func (s *TokenServiceImpl) GetLoginUser(ctx *gin.Context) *model.LoginUser {
	// 获取请求携带的令牌
	token := s.GetToken(ctx)
	if token == "" {
		return nil
	}

	claims := s.ParseToken(token)
	if claims == nil {
		return nil
	}

	// 从redis中获取用户信息
	userKey := s.GetTokenKey(claims.GetUUID())
	loginUserJson, err := s.redis.Get(userKey)
	if err != nil || loginUserJson == "" {
		return nil
	}

	var loginUser model.LoginUser
	err = json.Unmarshal([]byte(loginUserJson), &loginUser)
	if err != nil {
		s.logger.Error("解析登录用户信息失败", zap.Error(err))
		return nil
	}

	return &loginUser
}

// SetLoginUser 设置登录用户
func (s *TokenServiceImpl) SetLoginUser(loginUser *model.LoginUser) {
	if loginUser != nil && loginUser.GetToken() != "" {
		refreshToken(loginUser)
		userKey := s.GetTokenKey(loginUser.GetToken())
		userJson, _ := json.Marshal(loginUser)
		s.redis.Set(userKey, string(userJson), TokenExpireTime)
	}
}

// DelLoginUser 删除登录用户
func (s *TokenServiceImpl) DelLoginUser(token string) {
	if token != "" {
		userKey := s.GetTokenKey(token)
		s.redis.Del(userKey)
	}
}

// CreateToken 创建令牌
func (s *TokenServiceImpl) CreateToken(loginUser *model.LoginUser) string {
	// 生成token
	uuid := utils.GenerateUUID()
	loginUser.SetToken(uuid)
	refreshToken(loginUser)

	// 将用户信息存入redis
	userKey := s.GetTokenKey(uuid)
	userJson, _ := json.Marshal(loginUser)
	s.redis.Set(userKey, string(userJson), TokenExpireTime)

	// 创建JWT token
	return s.CreateJWTToken(map[string]interface{}{
		UserIdKey:   loginUser.GetUserId(),
		UsernameKey: loginUser.GetUser().UserName,
		"uuid":      uuid,
	})
}

// VerifyToken 验证令牌有效期
func (s *TokenServiceImpl) VerifyToken(loginUser *model.LoginUser) {
	expireTime := loginUser.GetExpireTime()
	currentTime := time.Now().UnixNano() / int64(time.Millisecond)
	if expireTime > currentTime {
		refreshToken(loginUser)
	}
}

// RefreshToken 刷新令牌有效期
func (s *TokenServiceImpl) RefreshToken(loginUser *model.LoginUser) {
	refreshToken(loginUser)
	userKey := s.GetTokenKey(loginUser.GetToken())
	userJson, _ := json.Marshal(loginUser)
	s.redis.Set(userKey, string(userJson), TokenExpireTime)
}

// SetUserAgent 设置用户代理信息
func (s *TokenServiceImpl) SetUserAgent(loginUser *model.LoginUser) {
	if loginUser != nil && loginUser.GetToken() != "" {
		userKey := s.GetTokenKey(loginUser.GetToken())
		userJson, _ := json.Marshal(loginUser)
		s.redis.Set(userKey, string(userJson), TokenExpireTime)
	}
}

// CreateJWTToken 创建JWT令牌
func (s *TokenServiceImpl) CreateJWTToken(claims map[string]interface{}) string {
	// 创建JWT声明
	nowTime := time.Now()
	expireTime := nowTime.Add(TokenExpireTime)

	jwtClaims := model.Claims{
		UserId:         claims[UserIdKey].(int64),
		Username:       claims[UsernameKey].(string),
		UUID:           claims["uuid"].(string),
		LoginTime:      nowTime.UnixNano() / int64(time.Millisecond),
		ExpirationTime: expireTime.UnixNano() / int64(time.Millisecond),
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			Issuer:    s.appConfig.Name,
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwtClaims)
	// 签名token
	tokenString, err := token.SignedString([]byte(s.appConfig.JWTSecret))
	if err != nil {
		s.logger.Error("生成JWT令牌失败", zap.Error(err))
		return ""
	}

	return tokenString
}

// ParseToken 解析令牌
func (s *TokenServiceImpl) ParseToken(token string) *model.Claims {
	jwtToken, err := jwt.ParseWithClaims(token, &model.Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.appConfig.JWTSecret), nil
	})

	if err != nil {
		s.logger.Error("解析JWT令牌失败", zap.Error(err))
		return nil
	}

	if claims, ok := jwtToken.Claims.(*model.Claims); ok && jwtToken.Valid {
		return claims
	}

	return nil
}

// GetUsernameFromToken 从令牌中获取用户名
func (s *TokenServiceImpl) GetUsernameFromToken(token string) string {
	claims := s.ParseToken(token)
	if claims != nil {
		return claims.Username
	}
	return ""
}

// GetToken 获取请求token
func (s *TokenServiceImpl) GetToken(ctx *gin.Context) string {
	token := ctx.GetHeader(TokenHeaderName)
	if token != "" && strings.HasPrefix(token, TokenPrefix) {
		return token[len(TokenPrefix):]
	}
	return ""
}

// GetTokenKey 获取redis中保存用户信息的key
func (s *TokenServiceImpl) GetTokenKey(uuid string) string {
	return fmt.Sprintf("%s%s", LoginUserKey, uuid)
}

// refreshToken 刷新令牌有效期
func refreshToken(loginUser *model.LoginUser) {
	loginUser.SetLoginTime(time.Now().UnixNano() / int64(time.Millisecond))
	loginUser.SetExpireTime(loginUser.GetLoginTime() + TokenExpireTime.Milliseconds())
}
