package initialize

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/ruoyi/backend/internal/config"
	"github.com/spf13/viper"
)

// InitConfig 初始化配置
func InitConfig() (*config.MainConfig, error) {
	// 获取配置文件路径
	configFile := getConfigFile()

	// 创建Viper实例
	v := viper.New()
	v.SetConfigFile(configFile)
	v.SetConfigType("yaml")

	// 设置环境变量前缀
	v.SetEnvPrefix("RUOYI")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 创建主配置
	mainConfig := config.NewMainConfig()

	// 解析配置
	if err := v.Unmarshal(mainConfig); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	mainConfig.Validate()

	return mainConfig, nil
}

// getConfigFile 获取配置文件路径
func getConfigFile() string {
	// 默认配置文件路径
	configFile := "configs/config.yaml"

	// 检查环境变量
	if envConfig := os.Getenv("RUOYI_CONFIG_FILE"); envConfig != "" {
		configFile = envConfig
	}

	// 检查命令行参数
	for i, arg := range os.Args {
		if arg == "--config" && i+1 < len(os.Args) {
			configFile = os.Args[i+1]
			break
		}
		if strings.HasPrefix(arg, "--config=") {
			configFile = arg[9:]
			break
		}
	}

	// 确保配置文件存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		// 尝试在当前目录查找
		if _, err := os.Stat(filepath.Base(configFile)); err == nil {
			configFile = filepath.Base(configFile)
		}
	}

	return configFile
}

// createDirectories 创建必要的目录
func createDirectories() error {
	dirs := []string{
		"./data",
		"./logs",
		"./temp",
		"./static",
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %v", dir, err)
		}
	}

	return nil
}

// GetAppConfig 获取应用配置
func GetAppConfig(mainConfig *config.MainConfig) *config.ApplicationConfig {
	return mainConfig.App
}

// GetServerConfig 获取服务器配置
func GetServerConfig(mainConfig *config.MainConfig) *config.ServerConfig {
	return mainConfig.Server
}

// GetDatabaseConfig 获取数据库配置
func GetDatabaseConfig(mainConfig *config.MainConfig) *config.DBConfig {
	return mainConfig.DB
}

// GetRedisConfig 获取Redis配置
func GetRedisConfig(mainConfig *config.MainConfig) *config.RedisCfg {
	return mainConfig.Redis
}

// GetJWTConfig 获取JWT配置
func GetJWTConfig(mainConfig *config.MainConfig) *config.JWTConfig {
	return mainConfig.JWT
}

// GetLogConfig 获取日志配置
func GetLogConfig(mainConfig *config.MainConfig) *config.LogConfig {
	return mainConfig.Log
}

// GetCaptchaConfig 获取验证码配置
func GetCaptchaConfig(mainConfig *config.MainConfig) *config.CaptchaConfig {
	return mainConfig.Captcha
}

// GetSecurityConfig 获取安全配置
func GetSecurityConfig(mainConfig *config.MainConfig) *config.SecurityConfig {
	return mainConfig.Security
}

// GetThreadPoolConfig 获取线程池配置
func GetThreadPoolConfig(mainConfig *config.MainConfig) *config.ThreadPoolConfig {
	return mainConfig.ThreadPool
}

// GetFilterConfig 获取过滤器配置
func GetFilterConfig(mainConfig *config.MainConfig) *config.FilterConfig {
	return mainConfig.Filter
}

// GetI18nConfig 获取国际化配置
func GetI18nConfig(mainConfig *config.MainConfig) *config.I18nConfig {
	return mainConfig.I18n
}

// GetGormConfig 获取GORM配置
func GetGormConfig(mainConfig *config.MainConfig) *config.GormConfig {
	return mainConfig.Gorm
}

// GetResourcesConfig 获取资源配置
func GetResourcesConfig(mainConfig *config.MainConfig) *config.ResourcesConfig {
	return mainConfig.Resources
}

// GetTLSConfig 获取TLS配置
func GetTLSConfig(mainConfig *config.MainConfig) *config.TLSConfig {
	return mainConfig.TLS
}
