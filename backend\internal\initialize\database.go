package initialize

import (
	"fmt"
	"time"

	"github.com/ruoyi/backend/internal/config"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// DB 全局数据库实例
var DB *gorm.DB

// InitDatabase 初始化数据库
func InitDatabase(dbConfig *config.DBConfig, log *zap.Logger) error {
	var dialector gorm.Dialector

	// 根据数据库类型创建对应的方言
	switch dbConfig.Type {
	case "mysql":
		dialector = mysql.Open(dbConfig.GetDSN())
	case "postgres":
		dialector = postgres.Open(dbConfig.GetDSN())
	case "sqlserver":
		dialector = sqlserver.Open(dbConfig.GetDSN())
	case "sqlite3":
		dialector = sqlite.Open(dbConfig.GetDSN())
	default:
		return fmt.Errorf("不支持的数据库类型: %s", dbConfig.Type)
	}

	// 创建自定义日志记录器
	newLogger := logger.New(
		&zapGormWriter{log: log},
		logger.Config{
			SlowThreshold:             time.Second, // 慢SQL阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略记录未找到错误
			Colorful:                  false,       // 禁用彩色输出
		},
	)

	// 创建GORM配置
	gormConfig := &gorm.Config{
		Logger: newLogger,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用外键约束
	}

	// 连接数据库
	db, err := gorm.Open(dialector, gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 获取底层SQL数据库
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库失败: %v", err)
	}

	// 设置连接池
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConns)
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(dbConfig.GetConnMaxLifetime())

	// 设置全局数据库实例
	DB = db

	// 测试数据库连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("测试数据库连接失败: %v", err)
	}

	log.Info("数据库初始化成功",
		zap.String("type", dbConfig.Type),
		zap.String("host", dbConfig.Host),
		zap.Int("port", dbConfig.Port),
		zap.String("database", dbConfig.Database),
	)

	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// zapGormWriter Zap日志适配器
type zapGormWriter struct {
	log *zap.Logger
}

// Printf 实现logger.Writer接口
func (w *zapGormWriter) Printf(format string, args ...interface{}) {
	w.log.Info(fmt.Sprintf(format, args...))
}
