package initialize

import (
	"fmt"

	"github.com/BurntSushi/toml"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/ruoyi/backend/internal/config"
	"go.uber.org/zap"
	"golang.org/x/text/language"
)

// 嵌入国际化资源文件 - 暂时禁用embed，直接从文件系统加载
// //go:embed ../../i18n/*.toml
// var i18nFS embed.FS

// Bundle 全局国际化Bundle
var Bundle *i18n.Bundle

// InitI18n 初始化国际化
func InitI18n(i18nConfig *config.I18nConfig, log *zap.Logger) error {
	// 创建Bundle
	bundle := i18n.NewBundle(language.Chinese)
	bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)

	// 加载嵌入的国际化资源文件
	if err := loadEmbeddedI18nFiles(bundle, log); err != nil {
		return err
	}

	// 加载外部国际化资源文件
	if err := loadExternalI18nFiles(bundle, i18nConfig, log); err != nil {
		return err
	}

	// 设置全局Bundle
	Bundle = bundle

	log.Info("国际化初始化成功",
		zap.String("defaultLocale", i18nConfig.GetDefaultLocale()),
		zap.Strings("supportedLocales", i18nConfig.GetSupportedLocales()),
	)

	return nil
}

// loadEmbeddedI18nFiles 加载嵌入的国际化资源文件 - 暂时禁用
func loadEmbeddedI18nFiles(bundle *i18n.Bundle, log *zap.Logger) error {
	// 暂时跳过embed功能，直接从文件系统加载
	log.Debug("跳过嵌入式国际化文件加载")
	return nil
}

// loadExternalI18nFiles 加载外部国际化资源文件
func loadExternalI18nFiles(bundle *i18n.Bundle, i18nConfig *config.I18nConfig, log *zap.Logger) error {
	// 遍历配置的资源路径
	for _, baseName := range i18nConfig.GetBasenameList() {
		// 遍历支持的语言
		for _, locale := range i18nConfig.GetSupportedLocales() {
			// 构造文件路径
			path := fmt.Sprintf("%s_%s.toml", baseName, locale)

			// 加载国际化资源
			if _, err := bundle.LoadMessageFile(path); err != nil {
				// 文件不存在不报错，只记录日志
				log.Debug("加载外部国际化资源文件失败", zap.String("path", path), zap.Error(err))
				continue
			}

			log.Debug("加载外部国际化资源文件成功", zap.String("path", path), zap.String("locale", locale))
		}
	}

	return nil
}

// GetLocalizer 获取本地化器
func GetLocalizer(locale string) *i18n.Localizer {
	return i18n.NewLocalizer(Bundle, locale)
}

// GetMessage 获取国际化消息
func GetMessage(messageID string, locale string, templateData map[string]interface{}) string {
	localizer := i18n.NewLocalizer(Bundle, locale)
	message, err := localizer.Localize(&i18n.LocalizeConfig{
		MessageID:    messageID,
		TemplateData: templateData,
	})
	if err != nil {
		return messageID
	}
	return message
}
