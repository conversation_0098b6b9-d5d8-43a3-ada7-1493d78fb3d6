package initialize

import (
	"context"
	"fmt"

	"github.com/redis/go-redis/v9"
	"github.com/ruoyi/backend/internal/config"
	redisService "github.com/ruoyi/backend/internal/redis"
	"go.uber.org/zap"
)

// RedisClient Redis客户端
var RedisClient *redis.Client

// RedisFactory Redis工厂
var RedisFactory *redisService.RedisFactory

// InitRedis 初始化Redis
func InitRedis(redisConfig *config.RedisCfg, log *zap.Logger) (redisService.RedisService, redisService.RedisCache, error) {
	// 创建Redis选项
	options := &redis.Options{
		Addr:         redisConfig.GetAddr(),
		Password:     redisConfig.Password,
		DB:           redisConfig.DB,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: redisConfig.MinIdleConns,
		// 使用标准的时间字段
		DialTimeout:  redisConfig.GetDialTimeout(),
		ReadTimeout:  redisConfig.GetReadTimeout(),
		WriteTimeout: redisConfig.GetWriteTimeout(),
	}

	// 创建Redis客户端
	client := redis.NewClient(options)

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, nil, fmt.Errorf("连接Redis失败: %v", err)
	}

	// 设置全局Redis客户端
	RedisClient = client

	// 创建Redis工厂
	RedisFactory = redisService.NewRedisFactory(log, client)

	// 创建Redis服务和Redis缓存
	redisServiceImpl := RedisFactory.CreateRedisService()
	redisCacheImpl := RedisFactory.CreateRedisCache()

	log.Info("Redis初始化成功",
		zap.String("addr", redisConfig.GetAddr()),
		zap.Int("db", redisConfig.DB),
	)

	return redisServiceImpl, redisCacheImpl, nil
}

// GetRedisClient 获取Redis客户端
func GetRedisClient() *redis.Client {
	return RedisClient
}

// GetRedisFactory 获取Redis工厂
func GetRedisFactory() *redisService.RedisFactory {
	return RedisFactory
}

// CloseRedis 关闭Redis连接
func CloseRedis() error {
	if RedisClient != nil {
		return RedisClient.Close()
	}
	return nil
}
