package initialize

import (
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/filter"
	"github.com/ruoyi/backend/internal/middleware"
	"github.com/ruoyi/backend/internal/redis"
	"go.uber.org/zap"
)

// InitRouter 初始化路由
func InitRouter(mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) *gin.Engine {
	// 设置运行模式
	appConfig := config.NewAppConfig() // 创建AppConfig实例
	if appConfig.Env == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else if appConfig.Env == "test" {
		gin.SetMode(gin.TestMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 创建路由引擎
	engine := gin.New()

	// 注册中间件
	engine.Use(middleware.ZapLogger(log))
	engine.Use(middleware.RecoveryMiddleware(log)) // 使用自定义的Recovery中间件
	engine.Use(gzip.Gzip(gzip.DefaultCompression))

	// 注册可重复请求过滤器
	repeatableFilter := filter.NewRepeatableFilter(log)
	engine.Use(repeatableFilter.Filter())

	// 注册XSS过滤中间件
	engine.Use(middleware.XssMiddleware(log))

	// 配置CORS
	if mainConfig.Resources.IsCorsEnabled() {
		corsConfig := cors.Config{
			AllowOrigins:     mainConfig.Resources.GetCorsAllowedOrigins(),
			AllowMethods:     mainConfig.Resources.GetCorsAllowedMethods(),
			AllowHeaders:     mainConfig.Resources.GetCorsAllowedHeaders(),
			AllowCredentials: mainConfig.Resources.IsCorsAllowCredentials(),
			MaxAge:           time.Duration(mainConfig.Resources.GetCorsMaxAge()) * time.Second,
		}
		engine.Use(cors.New(corsConfig))
	}

	// 注册性能分析工具（仅在开发环境下）
	if appConfig.Env == "dev" {
		pprof.Register(engine)
	}

	// 注册静态资源
	engine.Static("/static", "./static")
	engine.StaticFile("/favicon.ico", "./static/favicon.ico")

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "up",
			"service": appConfig.Name,
			"version": appConfig.Version,
		})
	})

	// 注册API路由
	apiGroup := engine.Group("/api")
	{
		// 无需认证的路由
		publicGroup := apiGroup.Group("/public")
		registerPublicRoutes(publicGroup, mainConfig, log, redisService, redisCache)

		// 需要认证的路由
		privateGroup := apiGroup.Group("/")
		privateGroup.Use(middleware.JWTAuth())
		privateGroup.Use(middleware.RepeatSubmitMiddleware(log, redisService))
		registerPrivateRoutes(privateGroup, mainConfig, log, redisService, redisCache)
	}

	// 注册404处理
	engine.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "Not Found",
		})
	})

	return engine
}

// registerPublicRoutes 注册公开路由
func registerPublicRoutes(group *gin.RouterGroup, mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) {
	// 登录接口
	group.POST("/login", func(c *gin.Context) {
		// 生成真实的JWT令牌
		token, err := middleware.GenerateToken(1, "admin")
		if err != nil {
			log.Error("生成JWT令牌失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "生成令牌失败",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "登录成功",
			"data": gin.H{
				"token": token,
				"user": gin.H{
					"userId":    1,
					"userName":  "管理员",
					"loginName": "admin",
					"email":     "<EMAIL>",
				},
			},
		})
	})

	// 验证码接口
	group.GET("/captcha", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "验证码接口开发中",
			"data": gin.H{
				"uuid": "captcha-uuid-12345",
				"img":  "data:image/png;base64,demo-captcha-image",
			},
		})
	})
}

// registerPrivateRoutes 注册私有路由
func registerPrivateRoutes(group *gin.RouterGroup, mainConfig *config.MainConfig, log *zap.Logger, redisService redis.RedisService, redisCache redis.RedisCache) {
	// 获取用户信息
	group.GET("/getInfo", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": gin.H{
				"user": gin.H{
					"userId":    1,
					"userName":  "管理员",
					"loginName": "admin",
					"email":     "<EMAIL>",
					"status":    "0",
				},
				"roles":       []string{"admin"},
				"permissions": []string{"*:*:*"},
			},
		})
	})

	// 获取路由信息
	group.GET("/getRouters", func(c *gin.Context) {
		menus := []gin.H{
			{
				"name":      "System",
				"path":      "/system",
				"component": "Layout",
				"meta":      gin.H{"title": "系统管理", "icon": "system"},
				"children": []gin.H{
					{
						"name":      "User",
						"path":      "/system/user",
						"component": "system/user/index",
						"meta":      gin.H{"title": "用户管理", "icon": "user"},
					},
				},
			},
		}
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "操作成功",
			"data": menus,
		})
	})

	// 系统管理路由组
	systemGroup := group.Group("/system")
	{
		// 用户管理
		systemGroup.GET("/user/list", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"list": []gin.H{
						{
							"userId":     1,
							"userName":   "管理员",
							"loginName":  "admin",
							"email":      "<EMAIL>",
							"status":     "0",
							"createTime": "2024-01-01 00:00:00",
						},
					},
					"total": 1,
					"page":  1,
					"size":  10,
				},
			})
		})
	}
}
