package initialize

import (
	"context"
	"fmt"
	"sync"

	"github.com/robfig/cron/v3"
	"github.com/ruoyi/backend/internal/config"
	"go.uber.org/zap"
)

// TaskManager 任务管理器
type TaskManager struct {
	cron      *cron.Cron
	tasks     map[string]cron.EntryID
	taskFuncs map[string]func()
	mutex     sync.Mutex
	log       *zap.Logger
}

// GlobalTaskManager 全局任务管理器
var GlobalTaskManager *TaskManager

// InitTasks 初始化任务
func InitTasks(threadPoolConfig *config.ThreadPoolConfig, log *zap.Logger) error {
	// 创建Cron实例
	c := cron.New(
		cron.WithSeconds(), // 支持秒级调度
		cron.WithChain(cron.Recover(cron.DefaultLogger), cron.DelayIfStillRunning(cron.DefaultLogger)), // 错误恢复和防止并发执行
		cron.WithLogger(cronLogger{log: log}), // 自定义日志记录器
	)

	// 创建任务管理器
	GlobalTaskManager = &TaskManager{
		cron:      c,
		tasks:     make(map[string]cron.EntryID),
		taskFuncs: make(map[string]func()),
		log:       log,
	}

	// 注册系统任务
	if err := registerSystemTasks(GlobalTaskManager, log); err != nil {
		return err
	}

	// 启动Cron
	c.Start()

	log.Info("任务初始化成功")
	return nil
}

// registerSystemTasks 注册系统任务
func registerSystemTasks(tm *TaskManager, log *zap.Logger) error {
	// 注册系统任务示例
	if err := tm.AddTask("system_task", "0 0 * * * *", func() {
		log.Info("执行系统任务")
		// 执行系统任务逻辑
	}); err != nil {
		return err
	}

	// 注册清理临时文件任务
	if err := tm.AddTask("clean_temp_files", "0 0 0 * * *", func() {
		log.Info("执行清理临时文件任务")
		// 执行清理临时文件逻辑
	}); err != nil {
		return err
	}

	return nil
}

// AddTask 添加任务
func (tm *TaskManager) AddTask(name string, spec string, task func()) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 检查任务是否已存在
	if _, ok := tm.tasks[name]; ok {
		return fmt.Errorf("任务 %s 已存在", name)
	}

	// 添加任务
	entryID, err := tm.cron.AddFunc(spec, task)
	if err != nil {
		return err
	}

	// 记录任务
	tm.tasks[name] = entryID
	tm.taskFuncs[name] = task

	tm.log.Info("添加任务成功", zap.String("name", name), zap.String("spec", spec))
	return nil
}

// RemoveTask 移除任务
func (tm *TaskManager) RemoveTask(name string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 检查任务是否存在
	entryID, ok := tm.tasks[name]
	if !ok {
		return fmt.Errorf("任务 %s 不存在", name)
	}

	// 移除任务
	tm.cron.Remove(entryID)

	// 删除记录
	delete(tm.tasks, name)
	delete(tm.taskFuncs, name)

	tm.log.Info("移除任务成功", zap.String("name", name))
	return nil
}

// GetTask 获取任务
func (tm *TaskManager) GetTask(name string) (func(), bool) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, ok := tm.taskFuncs[name]
	return task, ok
}

// RunTask 立即执行任务
func (tm *TaskManager) RunTask(name string) error {
	task, ok := tm.GetTask(name)
	if !ok {
		return fmt.Errorf("任务 %s 不存在", name)
	}

	// 异步执行任务
	go task()

	tm.log.Info("立即执行任务", zap.String("name", name))
	return nil
}

// Stop 停止任务管理器
func (tm *TaskManager) Stop() context.Context {
	return tm.cron.Stop()
}

// cronLogger 实现cron.Logger接口
type cronLogger struct {
	log *zap.Logger
}

func (l cronLogger) Info(msg string, keysAndValues ...interface{}) {
	l.log.Info(msg, zap.Any("details", keysAndValues))
}

func (l cronLogger) Error(err error, msg string, keysAndValues ...interface{}) {
	l.log.Error(msg, zap.Error(err), zap.Any("details", keysAndValues))
}

// GetTaskManager 获取任务管理器
func GetTaskManager() *TaskManager {
	return GlobalTaskManager
}
