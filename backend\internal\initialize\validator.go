package initialize

import (
	"reflect"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	en_translations "github.com/go-playground/validator/v10/translations/en"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"go.uber.org/zap"
)

// Trans 全局翻译器
var Trans ut.Translator

// InitValidator 初始化验证器
func InitValidator(log *zap.Logger) error {
	// 获取验证器
	validate, ok := binding.Validator.Engine().(*validator.Validate)
	if !ok {
		log.Error("获取验证器失败")
		return nil
	}

	// 注册自定义标签
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	// 初始化翻译器
	zhT := zh.New()
	enT := en.New()
	uni := ut.New(enT, zhT, enT)

	// 默认使用中文
	Trans, _ = uni.GetTranslator("zh")

	// 注册翻译器
	err := zh_translations.RegisterDefaultTranslations(validate, Trans)
	if err != nil {
		log.Error("注册中文翻译器失败", zap.Error(err))
		return err
	}

	// 注册英文翻译器
	enTrans, _ := uni.GetTranslator("en")
	err = en_translations.RegisterDefaultTranslations(validate, enTrans)
	if err != nil {
		log.Error("注册英文翻译器失败", zap.Error(err))
		return err
	}

	// 注册自定义验证器
	if err := registerCustomValidators(validate); err != nil {
		log.Error("注册自定义验证器失败", zap.Error(err))
		return err
	}

	log.Info("验证器初始化成功")
	return nil
}

// registerCustomValidators 注册自定义验证器
func registerCustomValidators(validate *validator.Validate) error {
	// 示例：注册手机号验证器
	return validate.RegisterValidation("mobile", validateMobile)
}

// validateMobile 验证手机号
func validateMobile(fl validator.FieldLevel) bool {
	// 简单的手机号验证规则：1开头的11位数字
	value := fl.Field().String()
	if len(value) == 0 {
		return true
	}
	if len(value) != 11 || value[0] != '1' {
		return false
	}
	for _, v := range value {
		if v < '0' || v > '9' {
			return false
		}
	}
	return true
}
