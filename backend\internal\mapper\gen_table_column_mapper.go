package mapper

// GenTableColumnMapper Data access interface
type GenTableColumnMapper interface {
	SelectDbTableColumnsByName(tableName string) []GenTableColumn
	SelectGenTableColumnListByTableId(tableId int64) []GenTableColumn
	InsertGenTableColumn(genTableColumn GenTableColumn) int
	UpdateGenTableColumn(genTableColumn GenTableColumn) int
	DeleteGenTableColumns(genTableColumns []GenTableColumn) int
	DeleteGenTableColumnByIds(ids []int64) int
}
