package mapper

// GenTableMapper Data access interface
type GenTableMapper interface {
	SelectGenTableList(genTable GenTable) []GenTable
	SelectDbTableList(genTable GenTable) []GenTable
	SelectDbTableListByNames(tableNames []string) []GenTable
	SelectGenTableAll() []GenTable
	SelectGenTableById(id int64) GenTable
	SelectGenTableByName(tableName string) GenTable
	InsertGenTable(genTable GenTable) int
	UpdateGenTable(genTable GenTable) int
	DeleteGenTableByIds(ids []int64) int
	CreateTable(sql string) int
}
