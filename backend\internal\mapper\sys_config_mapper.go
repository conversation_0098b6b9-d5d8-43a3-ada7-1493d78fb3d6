package mapper

// SysConfigMapper Data access interface
type SysConfigMapper interface {
	SelectConfig(config SysConfig) SysConfig
	SelectConfigById(configId int64) SysConfig
	SelectConfigList(config SysConfig) []SysConfig
	CheckConfigKeyUnique(configKey string) SysConfig
	InsertConfig(config SysConfig) int
	UpdateConfig(config SysConfig) int
	DeleteConfigById(configId int64) int
	DeleteConfigByIds(configIds []int64) int
}
