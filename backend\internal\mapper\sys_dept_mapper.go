package mapper

// SysDeptMapper Data access interface
type SysDeptMapper interface {
	SelectDeptList(dept SysDept) []SysDept
	SelectDeptListByRoleId(roleId int64, deptCheckStrictly boolean) []int64
	SelectDeptById(deptId int64) SysDept
	SelectChildrenDeptById(deptId int64) []SysDept
	SelectNormalChildrenDeptById(deptId int64) int
	HasChildByDeptId(deptId int64) int
	CheckDeptExistUser(deptId int64) int
	CheckDeptNameUnique(deptName string, parentId int64) SysDept
	InsertDept(dept SysDept) int
	UpdateDept(dept SysDept) int
	UpdateDeptStatusNormal(deptIds []int64)
	UpdateDeptChildren(depts []SysDept) int
	DeleteDeptById(deptId int64) int
}
