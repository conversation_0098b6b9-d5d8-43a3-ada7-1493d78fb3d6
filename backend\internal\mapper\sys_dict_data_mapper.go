package mapper

// SysDictDataMapper Data access interface
type SysDictDataMapper interface {
	SelectDictDataList(dictData SysDictData) []SysDictData
	SelectDictDataByType(dictType string) []SysDictData
	SelectDictLabel(dictType string, dictValue string) string
	SelectDictDataById(dictCode int64) SysDictData
	CountDictDataByType(dictType string) int
	DeleteDictDataById(dictCode int64) int
	DeleteDictDataByIds(dictCodes []int64) int
	InsertDictData(dictData SysDictData) int
	UpdateDictData(dictData SysDictData) int
	UpdateDictDataType(oldDictType string, newDictType string) int
}
