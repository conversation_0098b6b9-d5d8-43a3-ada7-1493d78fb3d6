package mapper

// SysDictTypeMapper Data access interface
type SysDictTypeMapper interface {
	SelectDictTypeList(dictType SysDictType) []SysDictType
	SelectDictTypeAll() []SysDictType
	SelectDictTypeById(dictId int64) SysDictType
	SelectDictTypeByType(dictType string) SysDictType
	DeleteDictTypeById(dictId int64) int
	DeleteDictTypeByIds(dictIds []int64) int
	InsertDictType(dictType SysDictType) int
	UpdateDictType(dictType SysDictType) int
	CheckDictTypeUnique(dictType string) SysDictType
}
