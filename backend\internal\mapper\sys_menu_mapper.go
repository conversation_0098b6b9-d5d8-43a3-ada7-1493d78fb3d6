package mapper

// SysMenuMapper Data access interface
type SysMenuMapper interface {
	SelectMenuList(menu SysMenu) []SysMenu
	SelectMenuPerms() []string
	SelectMenuListByUserId(menu SysMenu) []SysMenu
	SelectMenuPermsByRoleId(roleId int64) []string
	SelectMenuPermsByUserId(userId int64) []string
	SelectMenuTreeAll() []SysMenu
	SelectMenuTreeByUserId(userId int64) []SysMenu
	SelectMenuListByRoleId(roleId int64, menuCheckStrictly boolean) []int64
	SelectMenuById(menuId int64) SysMenu
	HasChildByMenuId(menuId int64) int
	InsertMenu(menu SysMenu) int
	UpdateMenu(menu SysMenu) int
	DeleteMenuById(menuId int64) int
	CheckMenuNameUnique(menuName string, parentId int64) SysMenu
}
