package mapper

// SysPostMapper Data access interface
type SysPostMapper interface {
	SelectPostList(post SysPost) []SysPost
	SelectPostAll() []SysPost
	SelectPostById(postId int64) SysPost
	SelectPostListByUserId(userId int64) []int64
	SelectPostsByUserName(userName string) []SysPost
	DeletePostById(postId int64) int
	DeletePostByIds(postIds []int64) int
	UpdatePost(post SysPost) int
	InsertPost(post SysPost) int
	CheckPostNameUnique(postName string) SysPost
	CheckPostCodeUnique(postCode string) SysPost
}
