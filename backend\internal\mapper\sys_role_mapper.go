package mapper

// SysRoleMapper Data access interface
type SysRoleMapper interface {
	SelectRoleList(role SysRole) []SysRole
	SelectRolePermissionByUserId(userId int64) []SysRole
	SelectRoleAll() []SysRole
	SelectRoleListByUserId(userId int64) []int64
	SelectRoleById(roleId int64) SysRole
	SelectRolesByUserName(userName string) []SysRole
	CheckRoleNameUnique(roleName string) SysRole
	CheckRoleKeyUnique(roleKey string) SysRole
	UpdateRole(role SysRole) int
	InsertRole(role SysRole) int
	DeleteRoleById(roleId int64) int
	DeleteRoleByIds(roleIds []int64) int
}
