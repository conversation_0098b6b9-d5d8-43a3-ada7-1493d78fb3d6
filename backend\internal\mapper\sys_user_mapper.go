package mapper

// SysUserMapper Data access interface
type SysUserMapper interface {
	SelectUserList(sysUser SysUser) []SysUser
	SelectAllocatedList(user SysUser) []SysUser
	SelectUnallocatedList(user SysUser) []SysUser
	SelectUserByUserName(userName string) SysUser
	SelectUserById(userId int64) SysUser
	InsertUser(user SysUser) int
	UpdateUser(user SysUser) int
	UpdateUserAvatar(userId int64, avatar string) int
	ResetUserPwd(userId int64, password string) int
	DeleteUserById(userId int64) int
	DeleteUserByIds(userIds []int64) int
	CheckUserNameUnique(userName string) SysUser
	CheckPhoneUnique(phonenumber string) SysUser
	CheckEmailUnique(email string) SysUser
}
