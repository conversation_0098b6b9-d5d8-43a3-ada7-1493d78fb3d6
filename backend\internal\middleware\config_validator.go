package middleware

import (
	"fmt"
	"os"
	"strings"
)

// ValidateProductionConfig 验证生产环境配置
func ValidateProductionConfig() error {
	env := os.Getenv("APP_ENV")
	if env != "prod" {
		return nil // 非生产环境跳过验证
	}

	requiredEnvVars := []string{
		"DB_HOST", "DB_USERNAME", "DB_PASSWORD", "DB_NAME",
		"REDIS_HOST", "REDIS_PASSWORD",
		"JWT_SECRET",
	}

	var missingVars []string
	for _, envVar := range requiredEnvVars {
		if os.Getenv(envVar) == "" {
			missingVars = append(missingVars, envVar)
		}
	}

	if len(missingVars) > 0 {
		return fmt.Errorf("生产环境缺少必需的环境变量: %s", strings.Join(missingVars, ", "))
	}

	// JWT密钥长度验证
	jwtSecret := os.Getenv("JWT_SECRET")
	if len(jwtSecret) < 32 {
		return fmt.<PERSON><PERSON><PERSON>("JWT_SECRET长度必须至少32个字符，当前长度: %d", len(jwtSecret))
	}

	return nil
}
