package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CorsMiddleware 跨域请求中间件
func CorsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置允许的源
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		} else {
			c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		}

		// 设置允许的请求方法
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")

		// 设置允许的请求头
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Token")

		// 设置是否允许携带凭证信息
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")

		// 设置预检请求的缓存时间
		c.Writer.Header().Set("Access-Control-Max-Age", "86400") // 24小时

		// 如果是预检请求，直接返回200
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		// 继续处理请求
		c.Next()
	}
}

// 检查是否是允许的源
func isAllowedOrigin(origin string, allowedOrigins []string) bool {
	if len(allowedOrigins) == 0 || allowedOrigins[0] == "*" {
		return true
	}

	for _, allowed := range allowedOrigins {
		if allowed == origin {
			return true
		}

		// 支持通配符匹配，如 *.example.com
		if strings.HasPrefix(allowed, "*") && strings.HasSuffix(origin, allowed[1:]) {
			return true
		}
	}

	return false
}
