package middleware

import (
	"bytes"
	"io/ioutil"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 获取请求信息
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = ioutil.ReadAll(c.Request.Body)
			// 重新设置请求体，因为读取后会清空
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器，以获取响应状态码和响应体
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		// 处理请求
		c.Next()

		// 结束时间
		endTime := time.Now()
		// 执行时间
		latency := endTime.Sub(startTime)

		// 获取响应状态
		status := c.Writer.Status()

		// 记录日志
		logger.Info("HTTP请求日志",
			zap.String("path", path),
			zap.String("method", method),
			zap.String("clientIP", clientIP),
			zap.Int("status", status),
			zap.Duration("latency", latency),
			zap.String("requestBody", string(requestBody)),
			zap.String("responseBody", blw.body.String()),
		)
	}
}

// bodyLogWriter 用于捕获响应体
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 重写Write方法，用于捕获响应体
func (w bodyLogWriter) Write(b []byte) (int, error) {
	// 写入缓冲区
	w.body.Write(b)
	// 写入原始响应
	return w.ResponseWriter.Write(b)
}
