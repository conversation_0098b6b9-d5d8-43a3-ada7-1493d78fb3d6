package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// PermissionMiddleware 权限中间件
func PermissionMiddleware(logger *zap.Logger, permissionChecker *auth.PermissionChecker) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求路径和方法
		path := c.Request.URL.Path
		method := c.Request.Method

		// TODO: 根据路径和方法获取需要的权限
		// 这里需要从配置或数据库中获取路径对应的权限
		// 暂时使用简单的映射关系
		requiredPermission := getRequiredPermission(path, method)

		// 如果不需要权限，则直接通过
		if requiredPermission == "" {
			c.Next()
			return
		}

		// 检查用户是否有权限
		if !permissionChecker.HasPermi(c, requiredPermission) {
			result := model.AjaxResult{
				Code: http.StatusForbidden,
				Msg:  "没有访问权限，请联系管理员授权",
			}
			c.AbortWithStatusJSON(http.StatusForbidden, result)
			return
		}

		// 继续处理请求
		c.Next()
	}
}

// HasPermission 检查是否有指定权限
func HasPermission(c *gin.Context, permission string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasPermi(c, permission)
}

// HasAnyPermission 检查是否有任意一个权限
func HasAnyPermission(c *gin.Context, permissions []string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasAnyPermi(c, permissions...)
}

// HasRole 检查是否有指定角色
func HasRole(c *gin.Context, role string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasRole(c, role)
}

// HasAnyRole 检查是否有任意一个角色
func HasAnyRole(c *gin.Context, roles []string, permissionChecker *auth.PermissionChecker) bool {
	return permissionChecker.HasAnyRole(c, roles...)
}

// 根据路径和方法获取需要的权限
func getRequiredPermission(path string, method string) string {
	// TODO: 实现权限映射
	// 这里需要从配置或数据库中获取路径对应的权限
	// 暂时返回空字符串，表示不需要权限
	return ""
}
