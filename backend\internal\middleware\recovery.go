package middleware

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 检查连接是否已断开
				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					if se, ok := ne.Err.(*os.SyscallError); ok {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") ||
							strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}

				// 获取堆栈信息
				stack := string(debug.Stack())
				httpRequest := fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
				headers := getRequestHeaders(c)

				if brokenPipe {
					// 如果是连接断开，只记录日志，不返回错误响应
					logger.Error("[Recovery] Broken pipe",
						zap.Any("error", err),
						zap.String("request", httpRequest),
						zap.Any("headers", headers),
					)
					c.Abort()
					return
				}

				// 记录panic日志
				logger.Error("[Recovery] Panic recovered",
					zap.Any("error", err),
					zap.String("request", httpRequest),
					zap.Any("headers", headers),
					zap.String("stack", stack),
				)

				// 返回500错误
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"code": 500,
					"msg":  "Internal Server Error",
				})
			}
		}()
		c.Next()
	}
}

// getRequestHeaders 获取请求头信息
func getRequestHeaders(c *gin.Context) map[string]string {
	headers := make(map[string]string)
	for k, v := range c.Request.Header {
		if len(v) > 0 {
			headers[k] = v[0]
		}
	}
	return headers
}
