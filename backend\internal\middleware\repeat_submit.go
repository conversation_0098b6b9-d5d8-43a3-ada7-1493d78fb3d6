package middleware

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/filter"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"go.uber.org/zap"
)

const (
	// 防重提交Redis前缀
	RepeatSubmitKey = "repeat_submit:"

	// 默认防重提交间隔时间（秒）
	DefaultInterval = 5
)

// RepeatSubmitMiddleware 防止重复提交中间件
func RepeatSubmitMiddleware(logger *zap.Logger, redisService redis.RedisService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理POST、PUT、DELETE请求
		if c.Request.Method != http.MethodPost && c.Request.Method != http.MethodPut && c.Request.Method != http.MethodDelete {
			c.Next()
			return
		}

		var requestId string

		// 获取请求包装器
		wrapper := filter.GetRequestWrapper(c)
		if wrapper != nil {
			// 使用包装器获取请求体
			body := wrapper.GetBody()
			requestId = generateRequestId(c.Request.URL.Path, body)
		} else {
			// 传统方式获取请求体
			body, err := ioutil.ReadAll(c.Request.Body)
			if err != nil {
				logger.Error("读取请求体失败", zap.Error(err))
				c.Next()
				return
			}

			// 重置请求体
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

			// 生成请求唯一标识
			requestId = generateRequestId(c.Request.URL.Path, body)
		}

		// 获取用户ID
		userId := getUserId(c)

		// 生成Redis键
		key := fmt.Sprintf("%s%d:%s", RepeatSubmitKey, userId, requestId)

		// 检查是否存在重复提交
		if exists, _ := redisService.Exists(key); exists {
			result := model.AjaxResult{
				Code: http.StatusBadRequest,
				Msg:  "请求过于频繁，请稍后再试",
			}
			c.AbortWithStatusJSON(http.StatusBadRequest, result)
			return
		}

		// 设置防重提交标记
		redisService.Set(key, "1", time.Duration(DefaultInterval)*time.Second)

		// 继续处理请求
		c.Next()
	}
}

// 生成请求唯一标识
func generateRequestId(path string, body []byte) string {
	// 使用路径和请求体的MD5作为唯一标识
	h := md5.New()
	h.Write([]byte(path))
	h.Write(body)
	return hex.EncodeToString(h.Sum(nil))
}

// 获取用户ID
func getUserId(c *gin.Context) int64 {
	// 从上下文中获取登录用户
	loginUser := GetLoginUser(c)
	if loginUser != nil {
		return loginUser.GetUserId()
	}

	// 如果没有登录用户，则使用IP地址的哈希值作为用户ID
	ip := c.ClientIP()
	h := md5.New()
	h.Write([]byte(ip))
	ipHash := hex.EncodeToString(h.Sum(nil))

	// 将哈希值的前8位转换为整数
	var userId int64
	fmt.Sscanf(ipHash[:8], "%x", &userId)
	return userId
}
