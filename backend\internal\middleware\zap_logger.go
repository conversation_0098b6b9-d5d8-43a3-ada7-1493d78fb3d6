package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ZapLogger 使用zap.Logger记录日志的中间件
func ZapLogger(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery
		c.Next()

		end := time.Now()
		latency := end.Sub(start)

		if len(c.Errors) > 0 {
			// 记录错误日志
			for _, e := range c.Errors.Errors() {
				logger.Error(e)
			}
		} else {
			fields := []zap.Field{
				zap.Int("status", c.Writer.Status()),
				zap.String("method", c.Request.Method),
				zap.String("path", path),
				zap.String("query", query),
				zap.String("ip", c.ClientIP()),
				zap.String("user-agent", c.Request.UserAgent()),
				zap.Duration("latency", latency),
			}

			// 添加请求ID
			if requestID, exists := c.Get("X-Request-Id"); exists {
				fields = append(fields, zap.Any("request_id", requestID))
			}

			// 添加用户ID
			if userID, exists := c.Get("X-User-Id"); exists {
				fields = append(fields, zap.Any("user_id", userID))
			}

			// 根据状态码记录不同级别的日志
			switch {
			case c.Writer.Status() >= 500:
				logger.Error(fmt.Sprintf("[HTTP] %s %s", c.Request.Method, path), fields...)
			case c.Writer.Status() >= 400:
				logger.Warn(fmt.Sprintf("[HTTP] %s %s", c.Request.Method, path), fields...)
			default:
				logger.Info(fmt.Sprintf("[HTTP] %s %s", c.Request.Method, path), fields...)
			}
		}
	}
}
