package model

// AjaxResult 操作消息提醒
type AjaxResult struct {
	// 状态码
	Code int `json:"code"`

	// 返回内容
	Msg string `json:"msg"`

	// 数据对象
	Data interface{} `json:"data,omitempty"`
}

// Success 返回成功消息
func Success() AjaxResult {
	return AjaxResult{
		Code: 200,
		Msg:  "操作成功",
	}
}

// SuccessWithMessage 返回成功消息
func SuccessWithMessage(msg string) AjaxResult {
	return AjaxResult{
		Code: 200,
		Msg:  msg,
	}
}

// SuccessWithData 返回成功消息
func SuccessWithData(data interface{}) AjaxResult {
	return AjaxResult{
		Code: 200,
		Msg:  "操作成功",
		Data: data,
	}
}

// Error 返回错误消息
func Error() AjaxResult {
	return AjaxResult{
		Code: 500,
		Msg:  "操作失败",
	}
}

// ErrorWithMessage 返回错误消息
func ErrorWithMessage(msg string) AjaxResult {
	return AjaxResult{
		Code: 500,
		Msg:  msg,
	}
}

// Warn 返回警告消息
func Warn(msg string) AjaxResult {
	return AjaxResult{
		Code: 400,
		Msg:  msg,
	}
}
