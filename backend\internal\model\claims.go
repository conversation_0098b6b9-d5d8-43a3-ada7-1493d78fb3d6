package model

import (
	"github.com/dgrijalva/jwt-go"
)

// Claims 自定义声明
type Claims struct {
	// 用户ID
	UserId int64 `json:"userId"`

	// 用户名
	Username string `json:"username"`

	// 用户唯一标识
	UUID string `json:"uuid"`

	// 登录时间
	LoginTime int64 `json:"loginTime"`

	// 过期时间
	ExpirationTime int64 `json:"expirationTime"`

	// 基础声明
	jwt.StandardClaims
}

// GetUsername 获取用户名
func (c *Claims) GetUsername() string {
	return c.Username
}

// GetUserId 获取用户ID
func (c *Claims) GetUserId() int64 {
	return c.UserId
}

// GetUUID 获取UUID
func (c *Claims) GetUUID() string {
	return c.UUID
}

// GetLoginTime 获取登录时间
func (c *Claims) GetLoginTime() int64 {
	return c.LoginTime
}

// GetExpirationTime 获取过期时间
func (c *Claims) GetExpirationTime() int64 {
	return c.ExpirationTime
}
