package model

import (
	"github.com/ruoyi/backend/internal/domain"
)

// LoginUser 登录用户身份权限
type LoginUser struct {
	// 用户ID
	UserId int64 `json:"userId"`

	// 部门ID
	DeptId int64 `json:"deptId"`

	// 用户唯一标识
	Token string `json:"token"`

	// 登录时间
	LoginTime int64 `json:"loginTime"`

	// 过期时间
	ExpireTime int64 `json:"expireTime"`

	// 登录IP地址
	Ipaddr string `json:"ipaddr"`

	// 登录地点
	LoginLocation string `json:"loginLocation"`

	// 浏览器类型
	Browser string `json:"browser"`

	// 操作系统
	Os string `json:"os"`

	// 权限列表
	Permissions []string `json:"permissions"`

	// 用户信息
	User domain.SysUser `json:"user"`
}

// GetUserId 获取用户ID
func (u *LoginUser) GetUserId() int64 {
	return u.UserId
}

// SetUserId 设置用户ID
func (u *LoginUser) SetUserId(userId int64) {
	u.UserId = userId
}

// GetDeptId 获取部门ID
func (u *LoginUser) GetDeptId() int64 {
	return u.DeptId
}

// SetDeptId 设置部门ID
func (u *LoginUser) SetDeptId(deptId int64) {
	u.DeptId = deptId
}

// GetToken 获取用户唯一标识
func (u *LoginUser) GetToken() string {
	return u.Token
}

// SetToken 设置用户唯一标识
func (u *LoginUser) SetToken(token string) {
	u.Token = token
}

// GetLoginTime 获取登录时间
func (u *LoginUser) GetLoginTime() int64 {
	return u.LoginTime
}

// SetLoginTime 设置登录时间
func (u *LoginUser) SetLoginTime(loginTime int64) {
	u.LoginTime = loginTime
}

// GetIpaddr 获取登录IP地址
func (u *LoginUser) GetIpaddr() string {
	return u.Ipaddr
}

// SetIpaddr 设置登录IP地址
func (u *LoginUser) SetIpaddr(ipaddr string) {
	u.Ipaddr = ipaddr
}

// GetLoginLocation 获取登录地点
func (u *LoginUser) GetLoginLocation() string {
	return u.LoginLocation
}

// SetLoginLocation 设置登录地点
func (u *LoginUser) SetLoginLocation(loginLocation string) {
	u.LoginLocation = loginLocation
}

// GetBrowser 获取浏览器类型
func (u *LoginUser) GetBrowser() string {
	return u.Browser
}

// SetBrowser 设置浏览器类型
func (u *LoginUser) SetBrowser(browser string) {
	u.Browser = browser
}

// GetOs 获取操作系统
func (u *LoginUser) GetOs() string {
	return u.Os
}

// SetOs 设置操作系统
func (u *LoginUser) SetOs(os string) {
	u.Os = os
}

// GetExpireTime 获取过期时间
func (u *LoginUser) GetExpireTime() int64 {
	return u.ExpireTime
}

// SetExpireTime 设置过期时间
func (u *LoginUser) SetExpireTime(expireTime int64) {
	u.ExpireTime = expireTime
}

// GetPermissions 获取权限列表
func (u *LoginUser) GetPermissions() []string {
	return u.Permissions
}

// SetPermissions 设置权限列表
func (u *LoginUser) SetPermissions(permissions []string) {
	u.Permissions = permissions
}

// GetUser 获取用户信息
func (u *LoginUser) GetUser() domain.SysUser {
	return u.User
}

// SetUser 设置用户信息
func (u *LoginUser) SetUser(user domain.SysUser) {
	u.User = user
}
