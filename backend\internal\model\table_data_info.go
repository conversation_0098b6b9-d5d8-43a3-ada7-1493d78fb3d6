package model

// TableDataInfo 表格分页数据对象
type TableDataInfo struct {
	// 总记录数
	Total int64 `json:"total"`

	// 列表数据
	Rows interface{} `json:"rows"`

	// 消息状态码
	Code int `json:"code"`

	// 消息内容
	Msg string `json:"msg"`
}

// TableDataInfoBuilder 表格数据构建器
func TableDataInfoBuilder() *TableDataInfo {
	return &TableDataInfo{}
}

// SetTotal 设置总记录数
func (t *TableDataInfo) SetTotal(total int64) *TableDataInfo {
	t.Total = total
	return t
}

// SetRows 设置列表数据
func (t *TableDataInfo) SetRows(rows interface{}) *TableDataInfo {
	t.Rows = rows
	return t
}

// SetCode 设置消息状态码
func (t *TableDataInfo) SetCode(code int) *TableDataInfo {
	t.Code = code
	return t
}

// SetMsg 设置消息内容
func (t *TableDataInfo) SetMsg(msg string) *TableDataInfo {
	t.Msg = msg
	return t
}

// Build 构建表格数据
func (t *TableDataInfo) Build() *TableDataInfo {
	return t
}
