package ServerController

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// ServerController Controller
type ServerController struct {
	// TODO: Add service dependencies
}

// RegisterServerController Register routes
func RegisterServerController(r *gin.RouterGroup) {
	controller := &%!s(MISSING){}
	
	r.GET("/get_info", controller.GetInfo)
}

// GetInfo Handle request
func (c *ServerController) GetInfo(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

