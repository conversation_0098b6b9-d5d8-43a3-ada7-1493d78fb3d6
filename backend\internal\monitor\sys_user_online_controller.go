package SysUserOnlineController

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// SysUserOnlineController Controller
type SysUserOnlineController struct {
	// TODO: Add service dependencies
}

// RegisterSysUserOnlineController Register routes
func RegisterSysUserOnlineController(r *gin.RouterGroup) {
	controller := &%!s(MISSING){}
	
	r.GET("/list", controller.List)
	r.DELETE("/force_logout", controller.ForceLogout)
}

// List Handle request
func (c *SysUserOnlineController) List(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// ForceLogout Handle request
func (c *SysUserOnlineController) ForceLogout(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

