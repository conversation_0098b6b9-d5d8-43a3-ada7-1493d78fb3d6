package redis

import (
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisFactory Redis工厂
type RedisFactory struct {
	logger *zap.Logger
	client *redis.Client
}

// NewRedisFactory 创建Redis工厂
func NewRedisFactory(logger *zap.Logger, client *redis.Client) *RedisFactory {
	return &RedisFactory{
		logger: logger,
		client: client,
	}
}

// CreateRedisService 创建Redis服务
func (f *RedisFactory) CreateRedisService() RedisService {
	return NewRedisServiceImpl(f.logger, f.client)
}

// CreateRedisCache 创建Redis缓存
func (f *RedisFactory) CreateRedisCache() RedisCache {
	return NewRedisCache(f.logger, f.client)
}

// CreateMemoryRedisCache 创建内存版Redis缓存（用于开发和测试）
func (f *RedisFactory) CreateMemoryRedisCache() RedisCache {
	return NewMemoryRedisCache(f.logger)
}
