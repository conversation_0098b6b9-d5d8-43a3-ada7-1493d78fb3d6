package redis

import (
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 缓存项结构
type cacheItem struct {
	value      interface{}
	expireTime *time.Time // 过期时间，nil表示永不过期
}

// MemoryRedisCache 内存实现的Redis缓存（用于开发和测试）
type MemoryRedisCache struct {
	logger       *zap.Logger
	cache        map[string]cacheItem
	listCache    map[string][]interface{}
	setCache     map[string]map[interface{}]bool
	mapCache     map[string]map[string]interface{}
	mutex        sync.RWMutex
	startTime    time.Time
	commandStats map[string]int64
}

// NewMemoryRedisCache 创建内存Redis缓存实例
func NewMemoryRedisCache(logger *zap.Logger) RedisCache {
	cache := &MemoryRedisCache{
		logger:       logger,
		cache:        make(map[string]cacheItem),
		listCache:    make(map[string][]interface{}),
		setCache:     make(map[string]map[interface{}]bool),
		mapCache:     make(map[string]map[string]interface{}),
		startTime:    time.Now(),
		commandStats: make(map[string]int64),
	}

	// 启动一个goroutine定期清理过期的缓存项
	go cache.cleanExpiredItems()

	return cache
}

// cleanExpiredItems 定期清理过期的缓存项
func (c *MemoryRedisCache) cleanExpiredItems() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()

		// 清理普通对象缓存
		for key, item := range c.cache {
			if item.expireTime != nil && now.After(*item.expireTime) {
				delete(c.cache, key)
			}
		}

		c.mutex.Unlock()
	}
}

// SetCacheObject 缓存基本对象
func (c *MemoryRedisCache) SetCacheObject(key string, value interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[key] = cacheItem{value: value, expireTime: nil}
	c.commandStats["set"]++
	return nil
}

// SetCacheObjectWithTimeout 缓存基本对象并设置过期时间
func (c *MemoryRedisCache) SetCacheObjectWithTimeout(key string, value interface{}, timeout time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	expireTime := time.Now().Add(timeout)
	c.cache[key] = cacheItem{value: value, expireTime: &expireTime}
	c.commandStats["set"]++
	return nil
}

// GetCacheObject 获取缓存对象
func (c *MemoryRedisCache) GetCacheObject(key string) (interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.cache[key]
	if !exists {
		c.commandStats["miss"]++
		return nil, nil
	}

	// 检查是否过期
	if item.expireTime != nil && time.Now().After(*item.expireTime) {
		// 异步删除过期项
		go func() {
			c.mutex.Lock()
			delete(c.cache, key)
			c.mutex.Unlock()
		}()
		c.commandStats["miss"]++
		return nil, nil
	}

	c.commandStats["get"]++
	return item.value, nil
}

// DeleteObject 删除单个对象
func (c *MemoryRedisCache) DeleteObject(key string) bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	_, exists := c.cache[key]
	if exists {
		delete(c.cache, key)
		c.commandStats["del"]++
		return true
	}

	// 检查列表缓存
	_, exists = c.listCache[key]
	if exists {
		delete(c.listCache, key)
		c.commandStats["del"]++
		return true
	}

	// 检查集合缓存
	_, exists = c.setCache[key]
	if exists {
		delete(c.setCache, key)
		c.commandStats["del"]++
		return true
	}

	// 检查映射缓存
	_, exists = c.mapCache[key]
	if exists {
		delete(c.mapCache, key)
		c.commandStats["del"]++
		return true
	}

	return false
}

// DeleteObjects 删除集合对象
func (c *MemoryRedisCache) DeleteObjects(keys []string) int64 {
	if len(keys) == 0 {
		return 0
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	var count int64
	for _, key := range keys {
		// 检查普通对象缓存
		_, exists := c.cache[key]
		if exists {
			delete(c.cache, key)
			count++
			continue
		}

		// 检查列表缓存
		_, exists = c.listCache[key]
		if exists {
			delete(c.listCache, key)
			count++
			continue
		}

		// 检查集合缓存
		_, exists = c.setCache[key]
		if exists {
			delete(c.setCache, key)
			count++
			continue
		}

		// 检查映射缓存
		_, exists = c.mapCache[key]
		if exists {
			delete(c.mapCache, key)
			count++
			continue
		}
	}

	if count > 0 {
		c.commandStats["del"] += count
	}
	return count
}

// Expire 设置有效时间（秒）
func (c *MemoryRedisCache) Expire(key string, timeout int64) bool {
	return c.ExpireWithTimeUnit(key, timeout, time.Second)
}

// ExpireWithTimeUnit 设置有效时间（指定时间单位）
func (c *MemoryRedisCache) ExpireWithTimeUnit(key string, timeout int64, timeUnit time.Duration) bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查普通对象缓存
	item, exists := c.cache[key]
	if exists {
		expireTime := time.Now().Add(time.Duration(timeout) * timeUnit)
		c.cache[key] = cacheItem{value: item.value, expireTime: &expireTime}
		c.commandStats["expire"]++
		return true
	}

	// 对于其他类型的缓存（列表、集合、映射），我们需要将它们转换为普通对象
	// 检查列表缓存
	if list, exists := c.listCache[key]; exists {
		expireTime := time.Now().Add(time.Duration(timeout) * timeUnit)
		c.cache[key] = cacheItem{value: list, expireTime: &expireTime}
		delete(c.listCache, key)
		c.commandStats["expire"]++
		return true
	}

	// 检查集合缓存
	if set, exists := c.setCache[key]; exists {
		expireTime := time.Now().Add(time.Duration(timeout) * timeUnit)
		c.cache[key] = cacheItem{value: set, expireTime: &expireTime}
		delete(c.setCache, key)
		c.commandStats["expire"]++
		return true
	}

	// 检查映射缓存
	if m, exists := c.mapCache[key]; exists {
		expireTime := time.Now().Add(time.Duration(timeout) * timeUnit)
		c.cache[key] = cacheItem{value: m, expireTime: &expireTime}
		delete(c.mapCache, key)
		c.commandStats["expire"]++
		return true
	}

	return false
}

// GetExpire 获取有效时间
func (c *MemoryRedisCache) GetExpire(key string) int64 {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查普通对象缓存
	item, exists := c.cache[key]
	if exists {
		if item.expireTime == nil {
			// 永不过期
			return -1
		}
		ttl := item.expireTime.Sub(time.Now()).Seconds()
		if ttl < 0 {
			// 已过期
			return -2
		}
		return int64(ttl)
	}

	// 检查是否存在于其他缓存中（列表、集合、映射）
	if _, exists := c.listCache[key]; exists {
		// 没有设置过期时间
		return -1
	}
	if _, exists := c.setCache[key]; exists {
		// 没有设置过期时间
		return -1
	}
	if _, exists := c.mapCache[key]; exists {
		// 没有设置过期时间
		return -1
	}

	// 键不存在
	return -2
}

// HasKey 判断key是否存在
func (c *MemoryRedisCache) HasKey(key string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查普通对象缓存
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return false
		}
		return true
	}

	// 检查列表缓存
	if _, exists := c.listCache[key]; exists {
		return true
	}

	// 检查集合缓存
	if _, exists := c.setCache[key]; exists {
		return true
	}

	// 检查映射缓存
	if _, exists := c.mapCache[key]; exists {
		return true
	}

	return false
}

// Keys 获取匹配的键集合
func (c *MemoryRedisCache) Keys(pattern string) []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	var keys []string
	now := time.Now()

	// 将模式转换为正则表达式
	regexPattern := strings.ReplaceAll(pattern, "*", ".*")
	regex, err := regexp.Compile("^" + regexPattern + "$")
	if err != nil {
		c.logger.Error("无效的模式", zap.String("pattern", pattern), zap.Error(err))
		return []string{}
	}

	// 检查普通对象缓存
	for key, item := range c.cache {
		// 检查是否过期
		if item.expireTime != nil && now.After(*item.expireTime) {
			continue
		}
		if regex.MatchString(key) {
			keys = append(keys, key)
		}
	}

	// 检查列表缓存
	for key := range c.listCache {
		if regex.MatchString(key) {
			keys = append(keys, key)
		}
	}

	// 检查集合缓存
	for key := range c.setCache {
		if regex.MatchString(key) {
			keys = append(keys, key)
		}
	}

	// 检查映射缓存
	for key := range c.mapCache {
		if regex.MatchString(key) {
			keys = append(keys, key)
		}
	}

	// 排序以保持一致的顺序
	sort.Strings(keys)

	c.commandStats["keys"]++
	return keys
}

// SetCacheList 缓存List数据
func (c *MemoryRedisCache) SetCacheList(key string, dataList []interface{}) int64 {
	if len(dataList) == 0 {
		return 0
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 删除可能存在的其他类型缓存
	delete(c.cache, key)
	delete(c.setCache, key)
	delete(c.mapCache, key)

	// 复制数据到列表缓存
	c.listCache[key] = make([]interface{}, len(dataList))
	copy(c.listCache[key], dataList)

	c.commandStats["rpush"]++
	return int64(len(dataList))
}

// GetCacheList 获取缓存的list
func (c *MemoryRedisCache) GetCacheList(key string) []interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查列表缓存
	list, exists := c.listCache[key]
	if exists {
		c.commandStats["lrange"]++
		return list
	}

	// 检查普通对象缓存，如果是列表类型则返回
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return []interface{}{}
		}

		// 尝试转换为列表
		if list, ok := item.value.([]interface{}); ok {
			c.commandStats["lrange"]++
			return list
		}
	}

	return []interface{}{}
}

// SetCacheSet 缓存Set数据
func (c *MemoryRedisCache) SetCacheSet(key string, dataSet []interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 删除可能存在的其他类型缓存
	delete(c.cache, key)
	delete(c.listCache, key)
	delete(c.mapCache, key)

	// 创建新的集合
	set := make(map[interface{}]bool)
	for _, item := range dataSet {
		set[item] = true
	}

	c.setCache[key] = set
	c.commandStats["sadd"]++
	return nil
}

// GetCacheSet 获取缓存的set
func (c *MemoryRedisCache) GetCacheSet(key string) []interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查集合缓存
	set, exists := c.setCache[key]
	if exists {
		result := make([]interface{}, 0, len(set))
		for item := range set {
			result = append(result, item)
		}
		c.commandStats["smembers"]++
		return result
	}

	// 检查普通对象缓存，如果是集合类型则返回
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return []interface{}{}
		}

		// 尝试转换为集合
		if set, ok := item.value.(map[interface{}]bool); ok {
			result := make([]interface{}, 0, len(set))
			for item := range set {
				result = append(result, item)
			}
			c.commandStats["smembers"]++
			return result
		}
	}

	return []interface{}{}
}

// SetCacheMap 缓存Map数据
func (c *MemoryRedisCache) SetCacheMap(key string, dataMap map[string]interface{}) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 删除可能存在的其他类型缓存
	delete(c.cache, key)
	delete(c.listCache, key)
	delete(c.setCache, key)

	// 创建新的映射
	m := make(map[string]interface{})
	for k, v := range dataMap {
		m[k] = v
	}

	c.mapCache[key] = m
	c.commandStats["hset"]++
}

// GetCacheMap 获取缓存的Map
func (c *MemoryRedisCache) GetCacheMap(key string) map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查映射缓存
	m, exists := c.mapCache[key]
	if exists {
		result := make(map[string]interface{})
		for k, v := range m {
			result[k] = v
		}
		c.commandStats["hgetall"]++
		return result
	}

	// 检查普通对象缓存，如果是映射类型则返回
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return map[string]interface{}{}
		}

		// 尝试转换为映射
		if m, ok := item.value.(map[string]interface{}); ok {
			result := make(map[string]interface{})
			for k, v := range m {
				result[k] = v
			}
			c.commandStats["hgetall"]++
			return result
		}
	}

	return map[string]interface{}{}
}

// SetCacheMapValue 往Hash中存入数据
func (c *MemoryRedisCache) SetCacheMapValue(key string, hKey string, value interface{}) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查映射缓存是否存在
	m, exists := c.mapCache[key]
	if !exists {
		// 创建新的映射
		m = make(map[string]interface{})
		c.mapCache[key] = m
	}

	// 设置字段值
	m[hKey] = value
	c.commandStats["hset"]++
}

// GetCacheMapValue 获取Hash中的数据
func (c *MemoryRedisCache) GetCacheMapValue(key string, hKey string) interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查映射缓存
	m, exists := c.mapCache[key]
	if exists {
		value, exists := m[hKey]
		if exists {
			c.commandStats["hget"]++
			return value
		}
	}

	// 检查普通对象缓存，如果是映射类型则返回字段值
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return nil
		}

		// 尝试转换为映射
		if m, ok := item.value.(map[string]interface{}); ok {
			value, exists := m[hKey]
			if exists {
				c.commandStats["hget"]++
				return value
			}
		}
	}

	return nil
}

// GetMultiCacheMapValue 获取多个Hash中的数据
func (c *MemoryRedisCache) GetMultiCacheMapValue(key string, hKeys []string) []interface{} {
	if len(hKeys) == 0 {
		return []interface{}{}
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make([]interface{}, len(hKeys))

	// 检查映射缓存
	m, exists := c.mapCache[key]
	if exists {
		for i, hKey := range hKeys {
			result[i] = m[hKey]
		}
		c.commandStats["hmget"]++
		return result
	}

	// 检查普通对象缓存，如果是映射类型则返回字段值
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return result
		}

		// 尝试转换为映射
		if m, ok := item.value.(map[string]interface{}); ok {
			for i, hKey := range hKeys {
				result[i] = m[hKey]
			}
			c.commandStats["hmget"]++
			return result
		}
	}

	return result
}

// DeleteCacheMapValue 删除Hash中的数据
func (c *MemoryRedisCache) DeleteCacheMapValue(key string, hKey string) bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查映射缓存
	m, exists := c.mapCache[key]
	if exists {
		_, exists := m[hKey]
		if exists {
			delete(m, hKey)
			c.commandStats["hdel"]++
			return true
		}
	}

	// 检查普通对象缓存，如果是映射类型则删除字段
	item, exists := c.cache[key]
	if exists {
		// 检查是否过期
		if item.expireTime != nil && time.Now().After(*item.expireTime) {
			return false
		}

		// 尝试转换为映射
		if m, ok := item.value.(map[string]interface{}); ok {
			_, exists := m[hKey]
			if exists {
				delete(m, hKey)
				c.commandStats["hdel"]++
				return true
			}
		}
	}

	return false
}
