package redis

import (
	"time"
)

// RedisCache Redis缓存接口
// 参考Java版本的RedisCache实现，提供完整的缓存操作功能
type RedisCache interface {
	// 对象操作
	// SetCacheObject 缓存基本对象
	SetCacheObject(key string, value interface{}) error

	// SetCacheObjectWithTimeout 缓存基本对象并设置过期时间
	SetCacheObjectWithTimeout(key string, value interface{}, timeout time.Duration) error

	// GetCacheObject 获取缓存对象
	GetCacheObject(key string) (interface{}, error)

	// DeleteObject 删除单个对象
	DeleteObject(key string) bool

	// DeleteObjects 删除集合对象
	DeleteObjects(keys []string) int64

	// 过期时间操作
	// Expire 设置有效时间（秒）
	Expire(key string, timeout int64) bool

	// ExpireWithTimeUnit 设置有效时间（指定时间单位）
	ExpireWithTimeUnit(key string, timeout int64, timeUnit time.Duration) bool

	// GetExpire 获取有效时间
	GetExpire(key string) int64

	// 键操作
	// HasKey 判断key是否存在
	HasKey(key string) bool

	// Keys 获取匹配的键集合
	Keys(pattern string) []string

	// 列表操作
	// SetCacheList 缓存List数据
	SetCacheList(key string, dataList []interface{}) int64

	// GetCacheList 获取缓存的list
	GetCacheList(key string) []interface{}

	// 集合操作
	// SetCacheSet 缓存Set数据
	SetCacheSet(key string, dataSet []interface{}) error

	// GetCacheSet 获取缓存的set
	GetCacheSet(key string) []interface{}

	// 映射操作
	// SetCacheMap 缓存Map数据
	SetCacheMap(key string, dataMap map[string]interface{})

	// GetCacheMap 获取缓存的Map
	GetCacheMap(key string) map[string]interface{}

	// SetCacheMapValue 往Hash中存入数据
	SetCacheMapValue(key string, hKey string, value interface{})

	// GetCacheMapValue 获取Hash中的数据
	GetCacheMapValue(key string, hKey string) interface{}

	// GetMultiCacheMapValue 获取多个Hash中的数据
	GetMultiCacheMapValue(key string, hKeys []string) []interface{}

	// DeleteCacheMapValue 删除Hash中的数据
	DeleteCacheMapValue(key string, hKey string) bool
}
