package redis

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisCacheImpl Redis缓存实现
type RedisCacheImpl struct {
	logger *zap.Logger
	client *redis.Client
}

// NewRedisCacheImpl 创建Redis缓存实例
func NewRedisCacheImpl(logger *zap.Logger, client *redis.Client) RedisCache {
	return &RedisCacheImpl{
		logger: logger,
		client: client,
	}
}

// SetCacheObject 缓存基本对象
func (c *RedisCacheImpl) SetCacheObject(key string, value interface{}) error {
	ctx := context.Background()
	err := c.client.Set(ctx, key, value, 0).Err()
	if err != nil {
		c.logger.Error("Redis设置缓存对象失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// SetCacheObjectWithTimeout 缓存基本对象并设置过期时间
func (c *RedisCacheImpl) SetCacheObjectWithTimeout(key string, value interface{}, timeout time.Duration) error {
	ctx := context.Background()
	err := c.client.Set(ctx, key, value, timeout).Err()
	if err != nil {
		c.logger.Error("Redis设置缓存对象失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// GetCacheObject 获取缓存对象
func (c *RedisCacheImpl) GetCacheObject(key string) (interface{}, error) {
	ctx := context.Background()
	val, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			// 键不存在
			return nil, nil
		}
		c.logger.Error("Redis获取缓存对象失败", zap.String("key", key), zap.Error(err))
		return nil, err
	}
	return val, nil
}

// DeleteObject 删除单个对象
func (c *RedisCacheImpl) DeleteObject(key string) bool {
	ctx := context.Background()
	result, err := c.client.Del(ctx, key).Result()
	if err != nil {
		c.logger.Error("Redis删除缓存对象失败", zap.String("key", key), zap.Error(err))
		return false
	}
	return result > 0
}

// DeleteObjects 删除集合对象
func (c *RedisCacheImpl) DeleteObjects(keys []string) int64 {
	if len(keys) == 0 {
		return 0
	}
	ctx := context.Background()
	result, err := c.client.Del(ctx, keys...).Result()
	if err != nil {
		c.logger.Error("Redis删除多个缓存对象失败", zap.Strings("keys", keys), zap.Error(err))
		return 0
	}
	return result
}

// Expire 设置有效时间（秒）
func (c *RedisCacheImpl) Expire(key string, timeout int64) bool {
	ctx := context.Background()
	result, err := c.client.Expire(ctx, key, time.Duration(timeout)*time.Second).Result()
	if err != nil {
		c.logger.Error("Redis设置过期时间失败", zap.String("key", key), zap.Error(err))
		return false
	}
	return result
}

// ExpireWithTimeUnit 设置有效时间（指定时间单位）
func (c *RedisCacheImpl) ExpireWithTimeUnit(key string, timeout int64, timeUnit time.Duration) bool {
	ctx := context.Background()
	result, err := c.client.Expire(ctx, key, time.Duration(timeout)*timeUnit).Result()
	if err != nil {
		c.logger.Error("Redis设置过期时间失败", zap.String("key", key), zap.Error(err))
		return false
	}
	return result
}

// GetExpire 获取有效时间
func (c *RedisCacheImpl) GetExpire(key string) int64 {
	ctx := context.Background()
	result, err := c.client.TTL(ctx, key).Result()
	if err != nil {
		c.logger.Error("Redis获取过期时间失败", zap.String("key", key), zap.Error(err))
		return -2 // -2表示键不存在
	}
	return int64(result.Seconds())
}

// HasKey 判断key是否存在
func (c *RedisCacheImpl) HasKey(key string) bool {
	ctx := context.Background()
	result, err := c.client.Exists(ctx, key).Result()
	if err != nil {
		c.logger.Error("Redis判断键是否存在失败", zap.String("key", key), zap.Error(err))
		return false
	}
	return result > 0
}

// Keys 获取匹配的键集合
func (c *RedisCacheImpl) Keys(pattern string) []string {
	ctx := context.Background()
	result, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		c.logger.Error("Redis获取匹配的键集合失败", zap.String("pattern", pattern), zap.Error(err))
		return []string{}
	}
	return result
}

// SetCacheList 缓存List数据
func (c *RedisCacheImpl) SetCacheList(key string, dataList []interface{}) int64 {
	if len(dataList) == 0 {
		return 0
	}
	ctx := context.Background()

	// 先删除已存在的key
	c.client.Del(ctx, key)

	// 转换为[]interface{}
	values := make([]interface{}, len(dataList))
	for i, v := range dataList {
		values[i] = v
	}

	// 使用RPush添加多个值
	result, err := c.client.RPush(ctx, key, values...).Result()
	if err != nil {
		c.logger.Error("Redis缓存List数据失败", zap.String("key", key), zap.Error(err))
		return 0
	}
	return result
}

// GetCacheList 获取缓存的list
func (c *RedisCacheImpl) GetCacheList(key string) []interface{} {
	ctx := context.Background()
	result, err := c.client.LRange(ctx, key, 0, -1).Result()
	if err != nil {
		c.logger.Error("Redis获取缓存List失败", zap.String("key", key), zap.Error(err))
		return []interface{}{}
	}

	// 转换为[]interface{}
	values := make([]interface{}, len(result))
	for i, v := range result {
		values[i] = v
	}

	return values
}

// SetCacheSet 缓存Set数据
func (c *RedisCacheImpl) SetCacheSet(key string, dataSet []interface{}) error {
	if len(dataSet) == 0 {
		return nil
	}
	ctx := context.Background()

	// 先删除已存在的key
	c.client.Del(ctx, key)

	// 使用SAdd添加多个值
	_, err := c.client.SAdd(ctx, key, dataSet...).Result()
	if err != nil {
		c.logger.Error("Redis缓存Set数据失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// GetCacheSet 获取缓存的set
func (c *RedisCacheImpl) GetCacheSet(key string) []interface{} {
	ctx := context.Background()
	result, err := c.client.SMembers(ctx, key).Result()
	if err != nil {
		c.logger.Error("Redis获取缓存Set失败", zap.String("key", key), zap.Error(err))
		return []interface{}{}
	}

	// 转换为[]interface{}
	values := make([]interface{}, len(result))
	for i, v := range result {
		values[i] = v
	}

	return values
}

// SetCacheMap 缓存Map数据
func (c *RedisCacheImpl) SetCacheMap(key string, dataMap map[string]interface{}) {
	if len(dataMap) == 0 {
		return
	}
	ctx := context.Background()

	// 使用HSet设置多个字段值
	for field, value := range dataMap {
		err := c.client.HSet(ctx, key, field, value).Err()
		if err != nil {
			c.logger.Error("Redis缓存Map数据失败",
				zap.String("key", key),
				zap.String("field", field),
				zap.Error(err))
		}
	}
}

// GetCacheMap 获取缓存的Map
func (c *RedisCacheImpl) GetCacheMap(key string) map[string]interface{} {
	ctx := context.Background()
	result, err := c.client.HGetAll(ctx, key).Result()
	if err != nil {
		c.logger.Error("Redis获取缓存Map失败", zap.String("key", key), zap.Error(err))
		return map[string]interface{}{}
	}

	// 转换为map[string]interface{}
	values := make(map[string]interface{}, len(result))
	for k, v := range result {
		values[k] = v
	}

	return values
}

// SetCacheMapValue 往Hash中存入数据
func (c *RedisCacheImpl) SetCacheMapValue(key string, hKey string, value interface{}) {
	ctx := context.Background()
	err := c.client.HSet(ctx, key, hKey, value).Err()
	if err != nil {
		c.logger.Error("Redis设置Hash字段值失败",
			zap.String("key", key),
			zap.String("hKey", hKey),
			zap.Error(err))
	}
}

// GetCacheMapValue 获取Hash中的数据
func (c *RedisCacheImpl) GetCacheMapValue(key string, hKey string) interface{} {
	ctx := context.Background()
	result, err := c.client.HGet(ctx, key, hKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 字段不存在
			return nil
		}
		c.logger.Error("Redis获取Hash字段值失败",
			zap.String("key", key),
			zap.String("hKey", hKey),
			zap.Error(err))
		return nil
	}
	return result
}

// GetMultiCacheMapValue 获取多个Hash中的数据
func (c *RedisCacheImpl) GetMultiCacheMapValue(key string, hKeys []string) []interface{} {
	if len(hKeys) == 0 {
		return []interface{}{}
	}
	ctx := context.Background()

	// 转换为[]string
	fields := make([]string, len(hKeys))
	for i, v := range hKeys {
		fields[i] = v
	}

	result, err := c.client.HMGet(ctx, key, fields...).Result()
	if err != nil {
		c.logger.Error("Redis获取多个Hash字段值失败",
			zap.String("key", key),
			zap.Strings("hKeys", fields),
			zap.Error(err))
		return []interface{}{}
	}
	return result
}

// DeleteCacheMapValue 删除Hash中的数据
func (c *RedisCacheImpl) DeleteCacheMapValue(key string, hKey string) bool {
	ctx := context.Background()
	result, err := c.client.HDel(ctx, key, hKey).Result()
	if err != nil {
		c.logger.Error("Redis删除Hash字段值失败",
			zap.String("key", key),
			zap.String("hKey", hKey),
			zap.Error(err))
		return false
	}
	return result > 0
}
