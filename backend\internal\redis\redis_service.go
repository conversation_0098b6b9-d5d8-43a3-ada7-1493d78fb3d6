package redis

import (
	"context"
	"time"
)

// RedisService Redis服务接口
type RedisService interface {
	// Set 设置缓存
	Set(key string, value string, timeout time.Duration) error

	// Get 获取缓存
	Get(key string) (string, error)

	// Delete 删除缓存
	Del(key string) error

	// Exists 判断key是否存在
	Exists(key string) (bool, error)

	// Expire 设置过期时间
	Expire(key string, timeout time.Duration) error

	// FlushDB 清空当前数据库
	FlushDB(ctx context.Context) error

	// Close 关闭连接
	Close() error
}
