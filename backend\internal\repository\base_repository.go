package repository

import (
	"context"
	"reflect"

	"github.com/ruoyi/backend/internal/database"
	"gorm.io/gorm"
)

// Repository 基础仓储接口
type Repository interface {
	// GetDB 获取数据库连接
	GetDB() *gorm.DB

	// GetDBWithContext 获取带上下文的数据库连接
	GetDBWithContext(ctx context.Context) *gorm.DB

	// WithTx 使用事务
	WithTx(tx *gorm.DB) Repository
}

// BaseRepository 基础仓储实现
type BaseRepository struct {
	// 数据库连接
	db *gorm.DB
}

// NewBaseRepository 创建基础仓储
func NewBaseRepository() *BaseRepository {
	return &BaseRepository{
		db: database.GetDB(),
	}
}

// GetDB 获取数据库连接
func (r *BaseRepository) GetDB() *gorm.DB {
	if r.db == nil {
		return database.GetDB()
	}
	return r.db
}

// GetDBWithContext 获取带上下文的数据库连接
func (r *BaseRepository) GetDBWithContext(ctx context.Context) *gorm.DB {
	return r.GetDB().WithContext(ctx)
}

// WithTx 使用事务
func (r *BaseRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &BaseRepository{
		db: tx,
	}
}

// CRUD 通用操作

// Create 创建记录
func Create[T any](r Repository, entity *T) error {
	return r.GetDB().Create(entity).Error
}

// Update 更新记录
func Update[T any](r Repository, entity *T) error {
	return r.GetDB().Save(entity).Error
}

// Delete 删除记录
func Delete[T any](r Repository, entity *T) error {
	return r.GetDB().Delete(entity).Error
}

// DeleteByID 根据ID删除记录
func DeleteByID[T any](r Repository, id interface{}) error {
	var entity T
	return r.GetDB().Delete(&entity, id).Error
}

// DeleteByIDs 根据ID列表删除记录
func DeleteByIDs[T any](r Repository, ids interface{}) error {
	var entity T
	return r.GetDB().Delete(&entity, ids).Error
}

// FindByID 根据ID查询记录
func FindByID[T any](r Repository, id interface{}) (*T, error) {
	var entity T
	err := r.GetDB().First(&entity, id).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

// FindOne 查询单条记录
func FindOne[T any](r Repository, query interface{}, args ...interface{}) (*T, error) {
	var entity T
	err := r.GetDB().Where(query, args...).First(&entity).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

// FindList 查询记录列表
func FindList[T any](r Repository, query interface{}, args ...interface{}) ([]T, error) {
	var entities []T
	err := r.GetDB().Where(query, args...).Find(&entities).Error
	if err != nil {
		return nil, err
	}
	return entities, nil
}

// Count 统计记录数
func Count[T any](r Repository, query interface{}, args ...interface{}) (int64, error) {
	var entity T
	var count int64
	err := r.GetDB().Model(&entity).Where(query, args...).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// Exists 判断记录是否存在
func Exists[T any](r Repository, query interface{}, args ...interface{}) (bool, error) {
	count, err := Count[T](r, query, args...)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// IsUnique 判断字段值是否唯一
func IsUnique[T any](r Repository, field string, value interface{}, excludeID ...interface{}) (bool, error) {
	var entity T
	query := r.GetDB().Model(&entity).Where(field+" = ?", value)

	// 排除当前记录
	if len(excludeID) > 0 && excludeID[0] != nil {
		entityType := reflect.TypeOf(entity)
		if entityType.Kind() == reflect.Ptr {
			entityType = entityType.Elem()
		}

		// 尝试获取主键字段
		var primaryKey string
		for i := 0; i < entityType.NumField(); i++ {
			field := entityType.Field(i)
			if tag := field.Tag.Get("gorm"); tag != "" {
				if hasTag(tag, "primary_key") {
					primaryKey = field.Name
					break
				}
			}
		}

		if primaryKey != "" {
			query = query.Where(primaryKey+" != ?", excludeID[0])
		}
	}

	var count int64
	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

// hasTag 判断标签是否包含指定值
func hasTag(tag, value string) bool {
	for tag != "" {
		var next string
		i := 0
		for i < len(tag) && tag[i] != ';' {
			i++
		}
		if i < len(tag) {
			next = tag[i+1:]
			tag = tag[:i]
		}
		if tag == value {
			return true
		}
		tag = next
	}
	return false
}
