package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// ConfigRepository 配置仓储接口
type ConfigRepository interface {
	Repository
	// SelectConfig 查询配置信息
	SelectConfig(config *domain.SysConfig) (*domain.SysConfig, error)

	// SelectConfigById 根据配置ID查询配置
	SelectConfigById(configId int64) (*domain.SysConfig, error)

	// SelectConfigList 查询配置列表
	SelectConfigList(config *domain.SysConfig) ([]domain.SysConfig, error)

	// CheckConfigKeyUnique 校验配置键名是否唯一
	CheckConfigKeyUnique(configKey string) (*domain.SysConfig, error)

	// InsertConfig 新增配置
	InsertConfig(config *domain.SysConfig) error

	// UpdateConfig 修改配置
	UpdateConfig(config *domain.SysConfig) error

	// DeleteConfigById 删除配置
	DeleteConfigById(configId int64) error

	// DeleteConfigByIds 批量删除配置
	DeleteConfigByIds(configIds []int64) error
}

// configRepository 配置仓储实现
type configRepository struct {
	*BaseRepository
}

// NewConfigRepository 创建配置仓储
func NewConfigRepository() ConfigRepository {
	return &configRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *configRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &configRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectConfig 查询配置信息
func (r *configRepository) SelectConfig(config *domain.SysConfig) (*domain.SysConfig, error) {
	var result domain.SysConfig
	db := r.GetDB().Model(&domain.SysConfig{})

	// 构建查询条件
	if config != nil {
		if config.ConfigId > 0 {
			db = db.Where("config_id = ?", config.ConfigId)
		}
		if config.ConfigKey != "" {
			db = db.Where("config_key = ?", config.ConfigKey)
		}
	}

	// 执行查询
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// SelectConfigById 根据配置ID查询配置
func (r *configRepository) SelectConfigById(configId int64) (*domain.SysConfig, error) {
	var config domain.SysConfig
	err := r.GetDB().Where("config_id = ?", configId).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// SelectConfigList 查询配置列表
func (r *configRepository) SelectConfigList(config *domain.SysConfig) ([]domain.SysConfig, error) {
	var configs []domain.SysConfig
	db := r.GetDB().Model(&domain.SysConfig{})

	// 构建查询条件
	if config != nil {
		if config.ConfigName != "" {
			db = db.Where("config_name like ?", "%"+config.ConfigName+"%")
		}
		if config.ConfigType != "" {
			db = db.Where("config_type = ?", config.ConfigType)
		}
		if config.ConfigKey != "" {
			db = db.Where("config_key like ?", "%"+config.ConfigKey+"%")
		}
		// 开始时间和结束时间过滤
		if config.GetParams() != nil {
			if config.GetParams()["beginTime"] != nil && config.GetParams()["endTime"] != nil {
				db = db.Where("create_time between ? and ?", config.GetParams()["beginTime"], config.GetParams()["endTime"])
			}
		}
	}

	// 执行查询
	if err := db.Find(&configs).Error; err != nil {
		return nil, err
	}

	return configs, nil
}

// CheckConfigKeyUnique 校验配置键名是否唯一
func (r *configRepository) CheckConfigKeyUnique(configKey string) (*domain.SysConfig, error) {
	var config domain.SysConfig
	err := r.GetDB().Where("config_key = ?", configKey).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// InsertConfig 新增配置
func (r *configRepository) InsertConfig(config *domain.SysConfig) error {
	return r.GetDB().Create(config).Error
}

// UpdateConfig 修改配置
func (r *configRepository) UpdateConfig(config *domain.SysConfig) error {
	return r.GetDB().Save(config).Error
}

// DeleteConfigById 删除配置
func (r *configRepository) DeleteConfigById(configId int64) error {
	return r.GetDB().Where("config_id = ?", configId).Delete(&domain.SysConfig{}).Error
}

// DeleteConfigByIds 批量删除配置
func (r *configRepository) DeleteConfigByIds(configIds []int64) error {
	return r.GetDB().Where("config_id in ?", configIds).Delete(&domain.SysConfig{}).Error
}
