package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// DeptRepository 部门仓储接口
type DeptRepository interface {
	Repository
	// SelectDeptList 查询部门列表
	SelectDeptList(dept *domain.SysDept) ([]domain.SysDept, error)

	// SelectDeptListByRoleId 根据角色ID查询部门树信息
	SelectDeptListByRoleId(roleId int64, deptCheckStrictly bool) ([]int64, error)

	// SelectDeptById 根据部门ID查询信息
	SelectDeptById(deptId int64) (*domain.SysDept, error)

	// SelectChildrenDeptById 根据部门ID查询子部门
	SelectChildrenDeptById(deptId int64) ([]domain.SysDept, error)

	// SelectNormalChildrenDeptById 根据部门ID查询正常状态的子部门
	SelectNormalChildrenDeptById(deptId int64) (int, error)

	// HasChildByDeptId 是否存在子部门
	HasChildByDeptId(deptId int64) (bool, error)

	// CheckDeptExistUser 查询部门是否存在用户
	CheckDeptExistUser(deptId int64) (bool, error)

	// CheckDeptNameUnique 校验部门名称是否唯一
	CheckDeptNameUnique(deptName string, parentId int64) (*domain.SysDept, error)

	// InsertDept 新增部门信息
	InsertDept(dept *domain.SysDept) error

	// UpdateDept 修改部门信息
	UpdateDept(dept *domain.SysDept) error

	// UpdateDeptStatusNormal 修改子元素关系
	UpdateDeptStatusNormal(deptIds []int64) error

	// UpdateDeptChildren 修改子部门数据
	UpdateDeptChildren(depts []domain.SysDept) error

	// DeleteDeptById 删除部门管理信息
	DeleteDeptById(deptId int64) error
}

// deptRepository 部门仓储实现
type deptRepository struct {
	*BaseRepository
}

// NewDeptRepository 创建部门仓储
func NewDeptRepository() DeptRepository {
	return &deptRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *deptRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &deptRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectDeptList 查询部门列表
func (r *deptRepository) SelectDeptList(dept *domain.SysDept) ([]domain.SysDept, error) {
	var depts []domain.SysDept
	db := r.GetDB().Model(&domain.SysDept{})

	// 构建查询条件
	if dept != nil {
		if dept.DeptId > 0 {
			db = db.Where("dept_id = ?", dept.DeptId)
		}
		if dept.ParentId > 0 {
			db = db.Where("parent_id = ?", dept.ParentId)
		}
		if dept.DeptName != "" {
			db = db.Where("dept_name like ?", "%"+dept.DeptName+"%")
		}
		if dept.Status != "" {
			db = db.Where("status = ?", dept.Status)
		}
		// 数据范围过滤
		if dept.GetParams() != nil && dept.GetParams()["dataScope"] != nil {
			db = db.Where(dept.GetParams()["dataScope"].(string))
		}
	}

	// 执行查询
	if err := db.Order("parent_id, order_num").Find(&depts).Error; err != nil {
		return nil, err
	}

	return depts, nil
}

// SelectDeptListByRoleId 根据角色ID查询部门树信息
func (r *deptRepository) SelectDeptListByRoleId(roleId int64, deptCheckStrictly bool) ([]int64, error) {
	var deptIds []int64

	// 构建查询条件
	db := r.GetDB().Table("sys_role_dept").
		Select("dept_id").
		Where("role_id = ?", roleId)

	// 执行查询
	if err := db.Pluck("dept_id", &deptIds).Error; err != nil {
		return nil, err
	}

	return deptIds, nil
}

// SelectDeptById 根据部门ID查询信息
func (r *deptRepository) SelectDeptById(deptId int64) (*domain.SysDept, error) {
	var dept domain.SysDept
	err := r.GetDB().Where("dept_id = ?", deptId).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// SelectChildrenDeptById 根据部门ID查询子部门
func (r *deptRepository) SelectChildrenDeptById(deptId int64) ([]domain.SysDept, error) {
	var depts []domain.SysDept

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDept{}).
		Where("find_in_set(?, ancestors)", deptId)

	// 执行查询
	if err := db.Find(&depts).Error; err != nil {
		return nil, err
	}

	return depts, nil
}

// SelectNormalChildrenDeptById 根据部门ID查询正常状态的子部门
func (r *deptRepository) SelectNormalChildrenDeptById(deptId int64) (int, error) {
	var count int64

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDept{}).
		Where("status = '0' and del_flag = '0'").
		Where("find_in_set(?, ancestors)", deptId)

	// 执行查询
	if err := db.Count(&count).Error; err != nil {
		return 0, err
	}

	return int(count), nil
}

// HasChildByDeptId 是否存在子部门
func (r *deptRepository) HasChildByDeptId(deptId int64) (bool, error) {
	var count int64

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDept{}).
		Where("parent_id = ?", deptId).
		Where("del_flag = '0'")

	// 执行查询
	if err := db.Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckDeptExistUser 查询部门是否存在用户
func (r *deptRepository) CheckDeptExistUser(deptId int64) (bool, error) {
	var count int64

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysUser{}).
		Where("dept_id = ?", deptId).
		Where("del_flag = '0'")

	// 执行查询
	if err := db.Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckDeptNameUnique 校验部门名称是否唯一
func (r *deptRepository) CheckDeptNameUnique(deptName string, parentId int64) (*domain.SysDept, error) {
	var dept domain.SysDept

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDept{}).
		Where("dept_name = ?", deptName).
		Where("parent_id = ?", parentId)

	// 执行查询
	err := db.First(&dept).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &dept, nil
}

// InsertDept 新增部门信息
func (r *deptRepository) InsertDept(dept *domain.SysDept) error {
	return r.GetDB().Create(dept).Error
}

// UpdateDept 修改部门信息
func (r *deptRepository) UpdateDept(dept *domain.SysDept) error {
	return r.GetDB().Save(dept).Error
}

// UpdateDeptStatusNormal 修改子元素关系
func (r *deptRepository) UpdateDeptStatusNormal(deptIds []int64) error {
	return r.GetDB().Model(&domain.SysDept{}).
		Where("dept_id in ?", deptIds).
		Update("status", "0").
		Error
}

// UpdateDeptChildren 修改子部门数据
func (r *deptRepository) UpdateDeptChildren(depts []domain.SysDept) error {
	tx := r.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	for _, dept := range depts {
		err := tx.Model(&domain.SysDept{}).
			Where("dept_id = ?", dept.DeptId).
			Update("ancestors", dept.Ancestors).
			Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// DeleteDeptById 删除部门管理信息
func (r *deptRepository) DeleteDeptById(deptId int64) error {
	return r.GetDB().Where("dept_id = ?", deptId).Delete(&domain.SysDept{}).Error
}
