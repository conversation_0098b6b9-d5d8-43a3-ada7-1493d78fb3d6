package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// DictDataRepository 字典数据仓储接口
type DictDataRepository interface {
	Repository
	// SelectDictDataList 查询字典数据列表
	SelectDictDataList(dictData *domain.SysDictData) ([]domain.SysDictData, error)

	// SelectDictDataByType 根据字典类型查询字典数据
	SelectDictDataByType(dictType string) ([]domain.SysDictData, error)

	// SelectDictLabel 根据字典类型和字典值查询字典标签
	SelectDictLabel(dictType string, dictValue string) (string, error)

	// SelectDictDataById 根据字典编码查询字典数据
	SelectDictDataById(dictCode int64) (*domain.SysDictData, error)

	// CountDictDataByType 统计字典类型数量
	CountDictDataByType(dictType string) (int, error)

	// DeleteDictDataById 删除字典数据
	DeleteDictDataById(dictCode int64) error

	// DeleteDictDataByIds 批量删除字典数据
	DeleteDictDataByIds(dictCodes []int64) error

	// InsertDictData 新增字典数据
	InsertDictData(dictData *domain.SysDictData) error

	// UpdateDictData 修改字典数据
	UpdateDictData(dictData *domain.SysDictData) error

	// UpdateDictDataType 修改字典类型
	UpdateDictDataType(oldDictType string, newDictType string) error
}

// dictDataRepository 字典数据仓储实现
type dictDataRepository struct {
	*BaseRepository
}

// NewDictDataRepository 创建字典数据仓储
func NewDictDataRepository() DictDataRepository {
	return &dictDataRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *dictDataRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &dictDataRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectDictDataList 查询字典数据列表
func (r *dictDataRepository) SelectDictDataList(dictData *domain.SysDictData) ([]domain.SysDictData, error) {
	var dictDatas []domain.SysDictData
	db := r.GetDB().Model(&domain.SysDictData{})

	// 构建查询条件
	if dictData != nil {
		if dictData.DictType != "" {
			db = db.Where("dict_type = ?", dictData.DictType)
		}
		if dictData.DictLabel != "" {
			db = db.Where("dict_label like ?", "%"+dictData.DictLabel+"%")
		}
		if dictData.Status != "" {
			db = db.Where("status = ?", dictData.Status)
		}
	}

	// 执行查询
	if err := db.Order("dict_sort").Find(&dictDatas).Error; err != nil {
		return nil, err
	}

	return dictDatas, nil
}

// SelectDictDataByType 根据字典类型查询字典数据
func (r *dictDataRepository) SelectDictDataByType(dictType string) ([]domain.SysDictData, error) {
	var dictDatas []domain.SysDictData

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDictData{}).
		Where("dict_type = ?", dictType).
		Where("status = '0'").
		Order("dict_sort")

	// 执行查询
	if err := db.Find(&dictDatas).Error; err != nil {
		return nil, err
	}

	return dictDatas, nil
}

// SelectDictLabel 根据字典类型和字典值查询字典标签
func (r *dictDataRepository) SelectDictLabel(dictType string, dictValue string) (string, error) {
	var dictLabel string

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDictData{}).
		Select("dict_label").
		Where("dict_type = ? and dict_value = ? and status = '0'", dictType, dictValue)

	// 执行查询
	if err := db.Pluck("dict_label", &dictLabel).Error; err != nil {
		return "", err
	}

	return dictLabel, nil
}

// SelectDictDataById 根据字典编码查询字典数据
func (r *dictDataRepository) SelectDictDataById(dictCode int64) (*domain.SysDictData, error) {
	var dictData domain.SysDictData
	err := r.GetDB().Where("dict_code = ?", dictCode).First(&dictData).Error
	if err != nil {
		return nil, err
	}
	return &dictData, nil
}

// CountDictDataByType 统计字典类型数量
func (r *dictDataRepository) CountDictDataByType(dictType string) (int, error) {
	var count int64

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDictData{}).
		Where("dict_type = ?", dictType)

	// 执行查询
	if err := db.Count(&count).Error; err != nil {
		return 0, err
	}

	return int(count), nil
}

// DeleteDictDataById 删除字典数据
func (r *dictDataRepository) DeleteDictDataById(dictCode int64) error {
	return r.GetDB().Where("dict_code = ?", dictCode).Delete(&domain.SysDictData{}).Error
}

// DeleteDictDataByIds 批量删除字典数据
func (r *dictDataRepository) DeleteDictDataByIds(dictCodes []int64) error {
	return r.GetDB().Where("dict_code in ?", dictCodes).Delete(&domain.SysDictData{}).Error
}

// InsertDictData 新增字典数据
func (r *dictDataRepository) InsertDictData(dictData *domain.SysDictData) error {
	return r.GetDB().Create(dictData).Error
}

// UpdateDictData 修改字典数据
func (r *dictDataRepository) UpdateDictData(dictData *domain.SysDictData) error {
	return r.GetDB().Save(dictData).Error
}

// UpdateDictDataType 修改字典类型
func (r *dictDataRepository) UpdateDictDataType(oldDictType string, newDictType string) error {
	return r.GetDB().Model(&domain.SysDictData{}).
		Where("dict_type = ?", oldDictType).
		Update("dict_type", newDictType).
		Error
}
