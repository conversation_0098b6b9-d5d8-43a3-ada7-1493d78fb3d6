package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// DictTypeRepository 字典类型仓储接口
type DictTypeRepository interface {
	Repository
	// SelectDictTypeList 查询字典类型列表
	SelectDictTypeList(dictType *domain.SysDictType) ([]domain.SysDictType, error)

	// SelectDictTypeAll 查询所有字典类型
	SelectDictTypeAll() ([]domain.SysDictType, error)

	// SelectDictTypeById 根据字典类型ID查询信息
	SelectDictTypeById(dictId int64) (*domain.SysDictType, error)

	// SelectDictTypeByType 根据字典类型查询信息
	SelectDictTypeByType(dictType string) (*domain.SysDictType, error)

	// DeleteDictTypeById 删除字典类型
	DeleteDictTypeById(dictId int64) error

	// DeleteDictTypeByIds 批量删除字典类型
	DeleteDictTypeByIds(dictIds []int64) error

	// InsertDictType 新增字典类型
	InsertDictType(dictType *domain.SysDictType) error

	// UpdateDictType 修改字典类型
	UpdateDictType(dictType *domain.SysDictType) error

	// CheckDictTypeUnique 校验字典类型是否唯一
	CheckDictTypeUnique(dictType string) (*domain.SysDictType, error)
}

// dictTypeRepository 字典类型仓储实现
type dictTypeRepository struct {
	*BaseRepository
}

// NewDictTypeRepository 创建字典类型仓储
func NewDictTypeRepository() DictTypeRepository {
	return &dictTypeRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *dictTypeRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &dictTypeRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectDictTypeList 查询字典类型列表
func (r *dictTypeRepository) SelectDictTypeList(dictType *domain.SysDictType) ([]domain.SysDictType, error) {
	var dictTypes []domain.SysDictType
	db := r.GetDB().Model(&domain.SysDictType{})

	// 构建查询条件
	if dictType != nil {
		if dictType.DictName != "" {
			db = db.Where("dict_name like ?", "%"+dictType.DictName+"%")
		}
		if dictType.Status != "" {
			db = db.Where("status = ?", dictType.Status)
		}
		if dictType.DictType != "" {
			db = db.Where("dict_type like ?", "%"+dictType.DictType+"%")
		}
		// 开始时间和结束时间过滤
		if dictType.GetParams() != nil {
			if dictType.GetParams()["beginTime"] != nil && dictType.GetParams()["endTime"] != nil {
				db = db.Where("create_time between ? and ?", dictType.GetParams()["beginTime"], dictType.GetParams()["endTime"])
			}
		}
	}

	// 执行查询
	if err := db.Find(&dictTypes).Error; err != nil {
		return nil, err
	}

	return dictTypes, nil
}

// SelectDictTypeAll 查询所有字典类型
func (r *dictTypeRepository) SelectDictTypeAll() ([]domain.SysDictType, error) {
	var dictTypes []domain.SysDictType

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysDictType{}).
		Where("status = '0'").
		Order("dict_sort")

	// 执行查询
	if err := db.Find(&dictTypes).Error; err != nil {
		return nil, err
	}

	return dictTypes, nil
}

// SelectDictTypeById 根据字典类型ID查询信息
func (r *dictTypeRepository) SelectDictTypeById(dictId int64) (*domain.SysDictType, error) {
	var dictType domain.SysDictType
	err := r.GetDB().Where("dict_id = ?", dictId).First(&dictType).Error
	if err != nil {
		return nil, err
	}
	return &dictType, nil
}

// SelectDictTypeByType 根据字典类型查询信息
func (r *dictTypeRepository) SelectDictTypeByType(dictType string) (*domain.SysDictType, error) {
	var sysDictType domain.SysDictType
	err := r.GetDB().Where("dict_type = ?", dictType).First(&sysDictType).Error
	if err != nil {
		return nil, err
	}
	return &sysDictType, nil
}

// DeleteDictTypeById 删除字典类型
func (r *dictTypeRepository) DeleteDictTypeById(dictId int64) error {
	return r.GetDB().Where("dict_id = ?", dictId).Delete(&domain.SysDictType{}).Error
}

// DeleteDictTypeByIds 批量删除字典类型
func (r *dictTypeRepository) DeleteDictTypeByIds(dictIds []int64) error {
	return r.GetDB().Where("dict_id in ?", dictIds).Delete(&domain.SysDictType{}).Error
}

// InsertDictType 新增字典类型
func (r *dictTypeRepository) InsertDictType(dictType *domain.SysDictType) error {
	return r.GetDB().Create(dictType).Error
}

// UpdateDictType 修改字典类型
func (r *dictTypeRepository) UpdateDictType(dictType *domain.SysDictType) error {
	return r.GetDB().Save(dictType).Error
}

// CheckDictTypeUnique 校验字典类型是否唯一
func (r *dictTypeRepository) CheckDictTypeUnique(dictType string) (*domain.SysDictType, error) {
	var sysDictType domain.SysDictType
	err := r.GetDB().Where("dict_type = ?", dictType).First(&sysDictType).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &sysDictType, nil
}
