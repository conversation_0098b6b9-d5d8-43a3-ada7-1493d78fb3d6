package repository

import (
	"fmt"
	"os"
	"time"

	"golang.org/x/crypto/bcrypt"

	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// InitDataRepository 初始化数据仓库
type InitDataRepository struct {
	db *gorm.DB
}

// NewInitDataRepository 创建初始化数据仓库
func NewInitDataRepository(db *gorm.DB) *InitDataRepository {
	return &InitDataRepository{
		db: db,
	}
}

// InitData 初始化数据
func (r *InitDataRepository) InitData() error {
	// 初始化管理员用户
	if err := r.InitAdminUser(); err != nil {
		return err
	}

	// 其他数据初始化...

	return nil
}

// InitAdminUser 初始化管理员用户
func (r *InitDataRepository) InitAdminUser() error {
	// 检查是否已存在管理员用户
	var count int64
	if err := r.db.Model(&domain.SysUser{}).Where("user_name = ?", "admin").Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return nil // 已存在，跳过初始化
	}

	// 从环境变量获取初始管理员密码
	adminPassword := getEnvOrDefault("ADMIN_INITIAL_PASSWORD", "")
	if adminPassword == "" {
		// 生产环境必须设置初始密码
		if getEnvOrDefault("APP_ENV", "dev") == "prod" {
			return fmt.Errorf("生产环境必须设置 ADMIN_INITIAL_PASSWORD 环境变量")
		}
		adminPassword = "Admin@123456" // 开发环境默认密码
	}

	// 密码强度验证
	if len(adminPassword) < 8 {
		return fmt.Errorf("管理员密码长度必须至少8个字符")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(adminPassword), 12)
	if err != nil {
		return err
	}

	// 创建当前时间
	now := time.Now()

	adminUser := &domain.SysUser{
		UserName:    "admin",
		NickName:    "超级管理员",
		Email:       getEnvOrDefault("ADMIN_EMAIL", "<EMAIL>"),
		Phonenumber: getEnvOrDefault("ADMIN_PHONE", ""),
		Sex:         "1",
		Password:    string(hashedPassword),
		Status:      "0",
		DeptId:      1,
		BaseEntity: domain.BaseEntity{
			CreateBy:   "system",
			CreateTime: &now,
			UpdateBy:   "system",
			UpdateTime: &now,
		},
	}

	return r.db.Create(adminUser).Error
}

// 辅助函数
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
