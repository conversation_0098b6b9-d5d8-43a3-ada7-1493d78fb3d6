package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// JobLogRepository 定时任务日志仓储接口
type JobLogRepository interface {
	Repository
	// SelectJobLogList 查询定时任务日志列表
	SelectJobLogList(jobLog *domain.SysJobLog) ([]domain.SysJobLog, error)

	// SelectJobLogById 通过调度日志ID查询调度信息
	SelectJobLogById(jobLogId int64) (*domain.SysJobLog, error)

	// DeleteJobLogById 通过调度日志ID删除调度日志信息
	DeleteJobLogById(jobLogId int64) error

	// DeleteJobLogByIds 批量删除调度日志信息
	DeleteJobLogByIds(jobLogIds []int64) error

	// InsertJobLog 新增任务日志
	InsertJobLog(jobLog *domain.SysJobLog) error

	// CleanJobLog 清空任务日志
	CleanJobLog() error
}

// jobLogRepository 定时任务日志仓储实现
type jobLogRepository struct {
	*BaseRepository
}

// NewJobLogRepository 创建定时任务日志仓储
func NewJobLogRepository() JobLogRepository {
	return &jobLogRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *jobLogRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &jobLogRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectJobLogList 查询定时任务日志列表
func (r *jobLogRepository) SelectJobLogList(jobLog *domain.SysJobLog) ([]domain.SysJobLog, error) {
	var jobLogs []domain.SysJobLog
	db := r.GetDB().Model(&domain.SysJobLog{})

	// 构建查询条件
	if jobLog != nil {
		if jobLog.JobName != "" {
			db = db.Where("job_name like ?", "%"+jobLog.JobName+"%")
		}
		if jobLog.JobGroup != "" {
			db = db.Where("job_group = ?", jobLog.JobGroup)
		}
		if jobLog.Status != "" {
			db = db.Where("status = ?", jobLog.Status)
		}
		if jobLog.InvokeTarget != "" {
			db = db.Where("invoke_target like ?", "%"+jobLog.InvokeTarget+"%")
		}
		if jobLog.JobLogId > 0 {
			db = db.Where("job_log_id = ?", jobLog.JobLogId)
		}
		// 开始时间和结束时间过滤
		params := jobLog.GetParams()
		if params != nil {
			beginTime, hasBeginTime := params["beginTime"]
			endTime, hasEndTime := params["endTime"]
			if hasBeginTime && hasEndTime {
				db = db.Where("create_time between ? and ?", beginTime, endTime)
			}
		}
	}

	// 执行查询
	if err := db.Order("job_log_id DESC").Find(&jobLogs).Error; err != nil {
		return nil, err
	}

	return jobLogs, nil
}

// SelectJobLogById 通过调度日志ID查询调度信息
func (r *jobLogRepository) SelectJobLogById(jobLogId int64) (*domain.SysJobLog, error) {
	var jobLog domain.SysJobLog
	err := r.GetDB().Where("job_log_id = ?", jobLogId).First(&jobLog).Error
	if err != nil {
		return nil, err
	}
	return &jobLog, nil
}

// DeleteJobLogById 通过调度日志ID删除调度日志信息
func (r *jobLogRepository) DeleteJobLogById(jobLogId int64) error {
	return r.GetDB().Where("job_log_id = ?", jobLogId).Delete(&domain.SysJobLog{}).Error
}

// DeleteJobLogByIds 批量删除调度日志信息
func (r *jobLogRepository) DeleteJobLogByIds(jobLogIds []int64) error {
	return r.GetDB().Where("job_log_id in ?", jobLogIds).Delete(&domain.SysJobLog{}).Error
}

// InsertJobLog 新增任务日志
func (r *jobLogRepository) InsertJobLog(jobLog *domain.SysJobLog) error {
	return r.GetDB().Create(jobLog).Error
}

// CleanJobLog 清空任务日志
func (r *jobLogRepository) CleanJobLog() error {
	return r.GetDB().Exec("DELETE FROM sys_job_log").Error
}
