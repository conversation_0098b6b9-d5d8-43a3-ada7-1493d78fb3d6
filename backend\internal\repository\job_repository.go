package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// JobRepository 定时任务仓储接口
type JobRepository interface {
	Repository
	// SelectJobList 查询定时任务列表
	SelectJobList(job *domain.SysJob) ([]domain.SysJob, error)

	// SelectJobAll 查询所有调度任务
	SelectJobAll() ([]domain.SysJob, error)

	// SelectJobById 通过调度ID查询调度任务信息
	SelectJobById(jobId int64) (*domain.SysJob, error)

	// DeleteJobById 通过调度ID删除调度任务信息
	DeleteJobById(jobId int64) error

	// DeleteJobByIds 批量删除调度任务信息
	DeleteJobByIds(jobIds []int64) error

	// UpdateJob 修改调度任务信息
	UpdateJob(job *domain.SysJob) error

	// InsertJob 新增调度任务信息
	InsertJob(job *domain.SysJob) error

	// CheckJobNameUnique 校验任务名称是否唯一
	CheckJobNameUnique(jobName string, jobGroup string) (*domain.SysJob, error)
}

// jobRepository 定时任务仓储实现
type jobRepository struct {
	*BaseRepository
}

// NewJobRepository 创建定时任务仓储
func NewJobRepository() JobRepository {
	return &jobRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *jobRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &jobRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectJobList 查询定时任务列表
func (r *jobRepository) SelectJobList(job *domain.SysJob) ([]domain.SysJob, error) {
	var jobs []domain.SysJob
	db := r.GetDB().Model(&domain.SysJob{})

	// 构建查询条件
	if job != nil {
		if job.JobName != "" {
			db = db.Where("job_name like ?", "%"+job.JobName+"%")
		}
		if job.JobGroup != "" {
			db = db.Where("job_group = ?", job.JobGroup)
		}
		if job.Status != "" {
			db = db.Where("status = ?", job.Status)
		}
		if job.InvokeTarget != "" {
			db = db.Where("invoke_target like ?", "%"+job.InvokeTarget+"%")
		}
	}

	// 执行查询
	if err := db.Find(&jobs).Error; err != nil {
		return nil, err
	}

	return jobs, nil
}

// SelectJobAll 查询所有调度任务
func (r *jobRepository) SelectJobAll() ([]domain.SysJob, error) {
	var jobs []domain.SysJob

	// 执行查询
	if err := r.GetDB().Find(&jobs).Error; err != nil {
		return nil, err
	}

	return jobs, nil
}

// SelectJobById 通过调度ID查询调度任务信息
func (r *jobRepository) SelectJobById(jobId int64) (*domain.SysJob, error) {
	var job domain.SysJob
	err := r.GetDB().Where("job_id = ?", jobId).First(&job).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

// DeleteJobById 通过调度ID删除调度任务信息
func (r *jobRepository) DeleteJobById(jobId int64) error {
	return r.GetDB().Where("job_id = ?", jobId).Delete(&domain.SysJob{}).Error
}

// DeleteJobByIds 批量删除调度任务信息
func (r *jobRepository) DeleteJobByIds(jobIds []int64) error {
	return r.GetDB().Where("job_id in ?", jobIds).Delete(&domain.SysJob{}).Error
}

// UpdateJob 修改调度任务信息
func (r *jobRepository) UpdateJob(job *domain.SysJob) error {
	return r.GetDB().Save(job).Error
}

// InsertJob 新增调度任务信息
func (r *jobRepository) InsertJob(job *domain.SysJob) error {
	return r.GetDB().Create(job).Error
}

// CheckJobNameUnique 校验任务名称是否唯一
func (r *jobRepository) CheckJobNameUnique(jobName string, jobGroup string) (*domain.SysJob, error) {
	var job domain.SysJob
	err := r.GetDB().Where("job_name = ? AND job_group = ?", jobName, jobGroup).First(&job).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &job, nil
}
