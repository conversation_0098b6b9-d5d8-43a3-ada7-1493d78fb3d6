package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// LogininforRepository 系统访问记录仓储接口
type LogininforRepository interface {
	Repository
	// SelectLogininforList 查询系统登录日志集合
	SelectLogininforList(logininfor *domain.SysLogininfor) ([]domain.SysLogininfor, error)

	// SelectLogininforById 查询系统登录日志
	SelectLogininforById(infoId int64) (*domain.SysLogininfor, error)

	// InsertLogininfor 新增系统登录日志
	InsertLogininfor(logininfor *domain.SysLogininfor) error

	// DeleteLogininforById 删除系统登录日志
	DeleteLogininforById(infoId int64) error

	// DeleteLogininforByIds 批量删除系统登录日志
	DeleteLogininforByIds(infoIds []int64) error

	// CleanLogininfor 清空系统登录日志
	CleanLogininfor() error
}

// logininforRepository 系统访问记录仓储实现
type logininforRepository struct {
	*BaseRepository
}

// NewLogininforRepository 创建系统访问记录仓储
func NewLogininforRepository() LogininforRepository {
	return &logininforRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *logininforRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &logininforRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectLogininforList 查询系统登录日志集合
func (r *logininforRepository) SelectLogininforList(logininfor *domain.SysLogininfor) ([]domain.SysLogininfor, error) {
	var logininforList []domain.SysLogininfor
	db := r.GetDB().Model(&domain.SysLogininfor{})

	// 构建查询条件
	if logininfor != nil {
		if logininfor.Ipaddr != "" {
			db = db.Where("ipaddr like ?", "%"+logininfor.Ipaddr+"%")
		}
		if logininfor.Status != "" {
			db = db.Where("status = ?", logininfor.Status)
		}
		if logininfor.UserName != "" {
			db = db.Where("user_name like ?", "%"+logininfor.UserName+"%")
		}
		// 开始时间和结束时间过滤
		params := logininfor.GetParams()
		if params != nil {
			beginTime, hasBeginTime := params["beginTime"]
			endTime, hasEndTime := params["endTime"]
			if hasBeginTime && hasEndTime {
				db = db.Where("login_time between ? and ?", beginTime, endTime)
			}
		}
	}

	// 执行查询
	if err := db.Order("info_id DESC").Find(&logininforList).Error; err != nil {
		return nil, err
	}

	return logininforList, nil
}

// SelectLogininforById 查询系统登录日志
func (r *logininforRepository) SelectLogininforById(infoId int64) (*domain.SysLogininfor, error) {
	var logininfor domain.SysLogininfor
	err := r.GetDB().Where("info_id = ?", infoId).First(&logininfor).Error
	if err != nil {
		return nil, err
	}
	return &logininfor, nil
}

// InsertLogininfor 新增系统登录日志
func (r *logininforRepository) InsertLogininfor(logininfor *domain.SysLogininfor) error {
	return r.GetDB().Create(logininfor).Error
}

// DeleteLogininforById 删除系统登录日志
func (r *logininforRepository) DeleteLogininforById(infoId int64) error {
	return r.GetDB().Where("info_id = ?", infoId).Delete(&domain.SysLogininfor{}).Error
}

// DeleteLogininforByIds 批量删除系统登录日志
func (r *logininforRepository) DeleteLogininforByIds(infoIds []int64) error {
	return r.GetDB().Where("info_id in ?", infoIds).Delete(&domain.SysLogininfor{}).Error
}

// CleanLogininfor 清空系统登录日志
func (r *logininforRepository) CleanLogininfor() error {
	return r.GetDB().Exec("DELETE FROM sys_logininfor").Error
}
