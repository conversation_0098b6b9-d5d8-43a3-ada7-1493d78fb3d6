package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// MenuRepository 菜单仓储接口
type MenuRepository interface {
	Repository
	// SelectMenuList 查询菜单列表
	SelectMenuList(menu *domain.SysMenu) ([]domain.SysMenu, error)

	// SelectMenuPerms 查询菜单权限列表
	SelectMenuPerms() ([]string, error)

	// SelectMenuListByUserId 根据用户ID查询菜单列表
	SelectMenuListByUserId(menu *domain.SysMenu, userId int64) ([]domain.SysMenu, error)

	// SelectMenuPermsByRoleId 根据角色ID查询菜单权限
	SelectMenuPermsByRoleId(roleId int64) ([]string, error)

	// SelectMenuPermsByUserId 根据用户ID查询菜单权限
	SelectMenuPermsByUserId(userId int64) ([]string, error)

	// SelectMenuTreeAll 查询所有菜单树
	SelectMenuTreeAll() ([]domain.SysMenu, error)

	// SelectMenuTreeByUserId 根据用户ID查询菜单树
	SelectMenuTreeByUserId(userId int64) ([]domain.SysMenu, error)

	// SelectMenuListByRoleId 根据角色ID查询菜单ID列表
	SelectMenuListByRoleId(roleId int64, menuCheckStrictly bool) ([]int64, error)

	// SelectMenuById 根据菜单ID查询菜单
	SelectMenuById(menuId int64) (*domain.SysMenu, error)

	// HasChildByMenuId 是否存在菜单子节点
	HasChildByMenuId(menuId int64) (bool, error)

	// InsertMenu 新增菜单信息
	InsertMenu(menu *domain.SysMenu) error

	// UpdateMenu 修改菜单信息
	UpdateMenu(menu *domain.SysMenu) error

	// DeleteMenuById 删除菜单信息
	DeleteMenuById(menuId int64) error

	// CheckMenuNameUnique 校验菜单名称是否唯一
	CheckMenuNameUnique(menuName string, parentId int64) (*domain.SysMenu, error)
}

// menuRepository 菜单仓储实现
type menuRepository struct {
	*BaseRepository
}

// NewMenuRepository 创建菜单仓储
func NewMenuRepository() MenuRepository {
	return &menuRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *menuRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &menuRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectMenuList 查询菜单列表
func (r *menuRepository) SelectMenuList(menu *domain.SysMenu) ([]domain.SysMenu, error) {
	var menus []domain.SysMenu
	db := r.GetDB().Model(&domain.SysMenu{})

	// 构建查询条件
	if menu != nil {
		if menu.MenuName != "" {
			db = db.Where("menu_name like ?", "%"+menu.MenuName+"%")
		}
		if menu.Visible != "" {
			db = db.Where("visible = ?", menu.Visible)
		}
		if menu.Status != "" {
			db = db.Where("status = ?", menu.Status)
		}
	}

	// 执行查询
	if err := db.Order("parent_id, order_num").Find(&menus).Error; err != nil {
		return nil, err
	}

	return menus, nil
}

// SelectMenuPerms 查询菜单权限列表
func (r *menuRepository) SelectMenuPerms() ([]string, error) {
	var perms []string

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Select("distinct perms").
		Where("perms is not null and perms != ''")

	// 执行查询
	if err := db.Pluck("perms", &perms).Error; err != nil {
		return nil, err
	}

	return perms, nil
}

// SelectMenuListByUserId 根据用户ID查询菜单列表
func (r *menuRepository) SelectMenuListByUserId(menu *domain.SysMenu, userId int64) ([]domain.SysMenu, error) {
	var menus []domain.SysMenu

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Select("distinct m.*").
		Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Joins("left join sys_user_role ur on rm.role_id = ur.role_id").
		Joins("left join sys_role ro on ur.role_id = ro.role_id").
		Where("ur.user_id = ? and m.status = '0' and ro.status = '0'", userId)

	if menu != nil {
		if menu.MenuName != "" {
			db = db.Where("m.menu_name like ?", "%"+menu.MenuName+"%")
		}
		if menu.Visible != "" {
			db = db.Where("m.visible = ?", menu.Visible)
		}
		if menu.Status != "" {
			db = db.Where("m.status = ?", menu.Status)
		}
	}

	// 执行查询
	if err := db.Order("m.parent_id, m.order_num").Find(&menus).Error; err != nil {
		return nil, err
	}

	return menus, nil
}

// SelectMenuPermsByRoleId 根据角色ID查询菜单权限
func (r *menuRepository) SelectMenuPermsByRoleId(roleId int64) ([]string, error) {
	var perms []string

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Select("distinct m.perms").
		Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Where("rm.role_id = ? and m.status = '0' and m.perms is not null and m.perms != ''", roleId)

	// 执行查询
	if err := db.Pluck("m.perms", &perms).Error; err != nil {
		return nil, err
	}

	return perms, nil
}

// SelectMenuPermsByUserId 根据用户ID查询菜单权限
func (r *menuRepository) SelectMenuPermsByUserId(userId int64) ([]string, error) {
	var perms []string

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Select("distinct m.perms").
		Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Joins("left join sys_user_role ur on rm.role_id = ur.role_id").
		Joins("left join sys_role ro on ur.role_id = ro.role_id").
		Where("ur.user_id = ? and m.status = '0' and ro.status = '0' and m.perms is not null and m.perms != ''", userId)

	// 执行查询
	if err := db.Pluck("m.perms", &perms).Error; err != nil {
		return nil, err
	}

	return perms, nil
}

// SelectMenuTreeAll 查询所有菜单树
func (r *menuRepository) SelectMenuTreeAll() ([]domain.SysMenu, error) {
	var menus []domain.SysMenu

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Where("status = '0'").
		Order("parent_id, order_num")

	// 执行查询
	if err := db.Find(&menus).Error; err != nil {
		return nil, err
	}

	return menus, nil
}

// SelectMenuTreeByUserId 根据用户ID查询菜单树
func (r *menuRepository) SelectMenuTreeByUserId(userId int64) ([]domain.SysMenu, error) {
	var menus []domain.SysMenu

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysMenu{}).
		Select("distinct m.*").
		Table("sys_menu m").
		Joins("left join sys_role_menu rm on m.menu_id = rm.menu_id").
		Joins("left join sys_user_role ur on rm.role_id = ur.role_id").
		Joins("left join sys_role ro on ur.role_id = ro.role_id").
		Where("ur.user_id = ? and m.status = '0' and ro.status = '0'", userId).
		Order("m.parent_id, m.order_num")

	// 执行查询
	if err := db.Find(&menus).Error; err != nil {
		return nil, err
	}

	return menus, nil
}

// SelectMenuListByRoleId 根据角色ID查询菜单ID列表
func (r *menuRepository) SelectMenuListByRoleId(roleId int64, menuCheckStrictly bool) ([]int64, error) {
	var menuIds []int64

	// 构建查询条件
	db := r.GetDB().Table("sys_role_menu").
		Select("menu_id").
		Where("role_id = ?", roleId)

	// 执行查询
	if err := db.Pluck("menu_id", &menuIds).Error; err != nil {
		return nil, err
	}

	return menuIds, nil
}

// SelectMenuById 根据菜单ID查询菜单
func (r *menuRepository) SelectMenuById(menuId int64) (*domain.SysMenu, error) {
	var menu domain.SysMenu
	err := r.GetDB().Where("menu_id = ?", menuId).First(&menu).Error
	if err != nil {
		return nil, err
	}
	return &menu, nil
}

// HasChildByMenuId 是否存在菜单子节点
func (r *menuRepository) HasChildByMenuId(menuId int64) (bool, error) {
	var count int64
	err := r.GetDB().Model(&domain.SysMenu{}).Where("parent_id = ?", menuId).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// InsertMenu 新增菜单信息
func (r *menuRepository) InsertMenu(menu *domain.SysMenu) error {
	return r.GetDB().Create(menu).Error
}

// UpdateMenu 修改菜单信息
func (r *menuRepository) UpdateMenu(menu *domain.SysMenu) error {
	return r.GetDB().Save(menu).Error
}

// DeleteMenuById 删除菜单信息
func (r *menuRepository) DeleteMenuById(menuId int64) error {
	return r.GetDB().Where("menu_id = ?", menuId).Delete(&domain.SysMenu{}).Error
}

// CheckMenuNameUnique 校验菜单名称是否唯一
func (r *menuRepository) CheckMenuNameUnique(menuName string, parentId int64) (*domain.SysMenu, error) {
	var menu domain.SysMenu
	err := r.GetDB().Where("menu_name = ? AND parent_id = ?", menuName, parentId).First(&menu).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &menu, nil
}
