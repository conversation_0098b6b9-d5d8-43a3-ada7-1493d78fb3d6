package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// NoticeRepository 通知公告仓储接口
type NoticeRepository interface {
	Repository
	// SelectNoticeById 查询公告信息
	SelectNoticeById(noticeId int64) (*domain.SysNotice, error)

	// SelectNoticeList 查询公告列表
	SelectNoticeList(notice *domain.SysNotice) ([]domain.SysNotice, error)

	// InsertNotice 新增公告
	InsertNotice(notice *domain.SysNotice) error

	// UpdateNotice 修改公告
	UpdateNotice(notice *domain.SysNotice) error

	// DeleteNoticeById 删除公告
	DeleteNoticeById(noticeId int64) error

	// DeleteNoticeByIds 批量删除公告
	DeleteNoticeByIds(noticeIds []int64) error
}

// noticeRepository 通知公告仓储实现
type noticeRepository struct {
	*BaseRepository
}

// NewNoticeRepository 创建通知公告仓储
func NewNoticeRepository() NoticeRepository {
	return &noticeRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *noticeRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &noticeRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectNoticeById 查询公告信息
func (r *noticeRepository) SelectNoticeById(noticeId int64) (*domain.SysNotice, error) {
	var notice domain.SysNotice
	err := r.GetDB().Where("notice_id = ?", noticeId).First(&notice).Error
	if err != nil {
		return nil, err
	}
	return &notice, nil
}

// SelectNoticeList 查询公告列表
func (r *noticeRepository) SelectNoticeList(notice *domain.SysNotice) ([]domain.SysNotice, error) {
	var notices []domain.SysNotice
	db := r.GetDB().Model(&domain.SysNotice{})

	// 构建查询条件
	if notice != nil {
		if notice.NoticeTitle != "" {
			db = db.Where("notice_title like ?", "%"+notice.NoticeTitle+"%")
		}
		if notice.CreateBy != "" {
			db = db.Where("create_by like ?", "%"+notice.CreateBy+"%")
		}
		if notice.NoticeType != "" {
			db = db.Where("notice_type = ?", notice.NoticeType)
		}
	}

	// 执行查询
	if err := db.Find(&notices).Error; err != nil {
		return nil, err
	}

	return notices, nil
}

// InsertNotice 新增公告
func (r *noticeRepository) InsertNotice(notice *domain.SysNotice) error {
	return r.GetDB().Create(notice).Error
}

// UpdateNotice 修改公告
func (r *noticeRepository) UpdateNotice(notice *domain.SysNotice) error {
	return r.GetDB().Save(notice).Error
}

// DeleteNoticeById 删除公告
func (r *noticeRepository) DeleteNoticeById(noticeId int64) error {
	return r.GetDB().Where("notice_id = ?", noticeId).Delete(&domain.SysNotice{}).Error
}

// DeleteNoticeByIds 批量删除公告
func (r *noticeRepository) DeleteNoticeByIds(noticeIds []int64) error {
	return r.GetDB().Where("notice_id in ?", noticeIds).Delete(&domain.SysNotice{}).Error
}
