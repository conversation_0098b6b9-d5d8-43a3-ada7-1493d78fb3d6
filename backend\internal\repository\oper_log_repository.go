package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// OperLogRepository 操作日志记录仓储接口
type OperLogRepository interface {
	Repository
	// SelectOperLogList 查询系统操作日志集合
	SelectOperLogList(operLog *domain.SysOperLog) ([]domain.SysOperLog, error)

	// SelectOperLogById 查询操作日志详细
	SelectOperLogById(operId int64) (*domain.SysOperLog, error)

	// InsertOperLog 新增操作日志
	InsertOperLog(operLog *domain.SysOperLog) error

	// DeleteOperLogById 删除操作日志
	DeleteOperLogById(operId int64) error

	// DeleteOperLogByIds 批量删除操作日志
	DeleteOperLogByIds(operIds []int64) error

	// CleanOperLog 清空操作日志
	CleanOperLog() error
}

// operLogRepository 操作日志记录仓储实现
type operLogRepository struct {
	*BaseRepository
}

// NewOperLogRepository 创建操作日志记录仓储
func NewOperLogRepository() OperLogRepository {
	return &operLogRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *operLogRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &operLogRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectOperLogList 查询系统操作日志集合
func (r *operLogRepository) SelectOperLogList(operLog *domain.SysOperLog) ([]domain.SysOperLog, error) {
	var operLogs []domain.SysOperLog
	db := r.GetDB().Model(&domain.SysOperLog{})

	// 构建查询条件
	if operLog != nil {
		if operLog.Title != "" {
			db = db.Where("title like ?", "%"+operLog.Title+"%")
		}
		if operLog.OperName != "" {
			db = db.Where("oper_name like ?", "%"+operLog.OperName+"%")
		}
		if operLog.BusinessType != 0 {
			db = db.Where("business_type = ?", operLog.BusinessType)
		}
		if operLog.Status != 0 {
			db = db.Where("status = ?", operLog.Status)
		}
		// 开始时间和结束时间过滤
		params := operLog.GetParams()
		if params != nil {
			beginTime, hasBeginTime := params["beginTime"]
			endTime, hasEndTime := params["endTime"]
			if hasBeginTime && hasEndTime {
				db = db.Where("oper_time between ? and ?", beginTime, endTime)
			}
		}
	}

	// 执行查询
	if err := db.Order("oper_id DESC").Find(&operLogs).Error; err != nil {
		return nil, err
	}

	return operLogs, nil
}

// SelectOperLogById 查询操作日志详细
func (r *operLogRepository) SelectOperLogById(operId int64) (*domain.SysOperLog, error) {
	var operLog domain.SysOperLog
	err := r.GetDB().Where("oper_id = ?", operId).First(&operLog).Error
	if err != nil {
		return nil, err
	}
	return &operLog, nil
}

// InsertOperLog 新增操作日志
func (r *operLogRepository) InsertOperLog(operLog *domain.SysOperLog) error {
	return r.GetDB().Create(operLog).Error
}

// DeleteOperLogById 删除操作日志
func (r *operLogRepository) DeleteOperLogById(operId int64) error {
	return r.GetDB().Where("oper_id = ?", operId).Delete(&domain.SysOperLog{}).Error
}

// DeleteOperLogByIds 批量删除操作日志
func (r *operLogRepository) DeleteOperLogByIds(operIds []int64) error {
	return r.GetDB().Where("oper_id in ?", operIds).Delete(&domain.SysOperLog{}).Error
}

// CleanOperLog 清空操作日志
func (r *operLogRepository) CleanOperLog() error {
	return r.GetDB().Exec("DELETE FROM sys_oper_log").Error
}
