package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// PostRepository 岗位仓储接口
type PostRepository interface {
	Repository
	// SelectPostList 查询岗位列表
	SelectPostList(post *domain.SysPost) ([]domain.SysPost, error)

	// SelectPostAll 查询所有岗位
	SelectPostAll() ([]domain.SysPost, error)

	// SelectPostById 根据岗位ID查询岗位
	SelectPostById(postId int64) (*domain.SysPost, error)

	// SelectPostListByUserId 根据用户ID查询岗位ID列表
	SelectPostListByUserId(userId int64) ([]int64, error)

	// SelectPostsByUserName 根据用户名查询岗位
	SelectPostsByUserName(userName string) ([]domain.SysPost, error)

	// DeletePostById 删除岗位
	DeletePostById(postId int64) error

	// DeletePostByIds 批量删除岗位
	DeletePostByIds(postIds []int64) error

	// UpdatePost 修改岗位
	UpdatePost(post *domain.SysPost) error

	// InsertPost 新增岗位
	InsertPost(post *domain.SysPost) error

	// CheckPostNameUnique 校验岗位名称是否唯一
	CheckPostNameUnique(postName string) (*domain.SysPost, error)

	// CheckPostCodeUnique 校验岗位编码是否唯一
	CheckPostCodeUnique(postCode string) (*domain.SysPost, error)
}

// postRepository 岗位仓储实现
type postRepository struct {
	*BaseRepository
}

// NewPostRepository 创建岗位仓储
func NewPostRepository() PostRepository {
	return &postRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *postRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &postRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectPostList 查询岗位列表
func (r *postRepository) SelectPostList(post *domain.SysPost) ([]domain.SysPost, error) {
	var posts []domain.SysPost
	db := r.GetDB().Model(&domain.SysPost{})

	// 构建查询条件
	if post != nil {
		if post.PostCode != "" {
			db = db.Where("post_code like ?", "%"+post.PostCode+"%")
		}
		if post.Status != "" {
			db = db.Where("status = ?", post.Status)
		}
		if post.PostName != "" {
			db = db.Where("post_name like ?", "%"+post.PostName+"%")
		}
	}

	// 执行查询
	if err := db.Order("post_sort").Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

// SelectPostAll 查询所有岗位
func (r *postRepository) SelectPostAll() ([]domain.SysPost, error) {
	var posts []domain.SysPost

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysPost{}).
		Where("status = '0'").
		Order("post_sort")

	// 执行查询
	if err := db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

// SelectPostById 根据岗位ID查询岗位
func (r *postRepository) SelectPostById(postId int64) (*domain.SysPost, error) {
	var post domain.SysPost
	err := r.GetDB().Where("post_id = ?", postId).First(&post).Error
	if err != nil {
		return nil, err
	}
	return &post, nil
}

// SelectPostListByUserId 根据用户ID查询岗位ID列表
func (r *postRepository) SelectPostListByUserId(userId int64) ([]int64, error) {
	var postIds []int64

	// 构建查询条件
	db := r.GetDB().Table("sys_user_post").
		Select("post_id").
		Where("user_id = ?", userId)

	// 执行查询
	if err := db.Pluck("post_id", &postIds).Error; err != nil {
		return nil, err
	}

	return postIds, nil
}

// SelectPostsByUserName 根据用户名查询岗位
func (r *postRepository) SelectPostsByUserName(userName string) ([]domain.SysPost, error) {
	var posts []domain.SysPost

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysPost{}).
		Select("p.*").
		Table("sys_post p").
		Joins("left join sys_user_post up on up.post_id = p.post_id").
		Joins("left join sys_user u on u.user_id = up.user_id").
		Where("u.user_name = ?", userName).
		Where("p.status = '0'")

	// 执行查询
	if err := db.Find(&posts).Error; err != nil {
		return nil, err
	}

	return posts, nil
}

// DeletePostById 删除岗位
func (r *postRepository) DeletePostById(postId int64) error {
	return r.GetDB().Where("post_id = ?", postId).Delete(&domain.SysPost{}).Error
}

// DeletePostByIds 批量删除岗位
func (r *postRepository) DeletePostByIds(postIds []int64) error {
	return r.GetDB().Where("post_id in ?", postIds).Delete(&domain.SysPost{}).Error
}

// UpdatePost 修改岗位
func (r *postRepository) UpdatePost(post *domain.SysPost) error {
	return r.GetDB().Save(post).Error
}

// InsertPost 新增岗位
func (r *postRepository) InsertPost(post *domain.SysPost) error {
	return r.GetDB().Create(post).Error
}

// CheckPostNameUnique 校验岗位名称是否唯一
func (r *postRepository) CheckPostNameUnique(postName string) (*domain.SysPost, error) {
	var post domain.SysPost
	err := r.GetDB().Where("post_name = ?", postName).First(&post).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &post, nil
}

// CheckPostCodeUnique 校验岗位编码是否唯一
func (r *postRepository) CheckPostCodeUnique(postCode string) (*domain.SysPost, error) {
	var post domain.SysPost
	err := r.GetDB().Where("post_code = ?", postCode).First(&post).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &post, nil
}
