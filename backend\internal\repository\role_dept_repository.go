package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// RoleDeptRepository 角色部门关联仓储接口
type RoleDeptRepository interface {
	Repository
	// DeleteRoleDeptByRoleId 通过角色ID删除角色和部门关联
	DeleteRoleDeptByRoleId(roleId int64) error

	// BatchRoleDept 批量新增角色部门信息
	BatchRoleDept(roleDepts []domain.SysRoleDept) error

	// SelectRoleDeptByRoleId 查询角色关联的部门ID列表
	SelectRoleDeptByRoleId(roleId int64) ([]int64, error)
}

// roleDeptRepository 角色部门关联仓储实现
type roleDeptRepository struct {
	*BaseRepository
}

// NewRoleDeptRepository 创建角色部门关联仓储
func NewRoleDeptRepository() RoleDeptRepository {
	return &roleDeptRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *roleDeptRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &roleDeptRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// DeleteRoleDeptByRoleId 通过角色ID删除角色和部门关联
func (r *roleDeptRepository) DeleteRoleDeptByRoleId(roleId int64) error {
	return r.GetDB().Where("role_id = ?", roleId).Delete(&domain.SysRoleDept{}).Error
}

// BatchRoleDept 批量新增角色部门信息
func (r *roleDeptRepository) BatchRoleDept(roleDepts []domain.SysRoleDept) error {
	return r.GetDB().Create(&roleDepts).Error
}

// SelectRoleDeptByRoleId 查询角色关联的部门ID列表
func (r *roleDeptRepository) SelectRoleDeptByRoleId(roleId int64) ([]int64, error) {
	var deptIds []int64
	err := r.GetDB().Model(&domain.SysRoleDept{}).
		Where("role_id = ?", roleId).
		Pluck("dept_id", &deptIds).Error
	return deptIds, err
}
