package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// RoleMenuRepository 角色菜单关联仓储接口
type RoleMenuRepository interface {
	Repository
	// DeleteRoleMenuByRoleId 通过角色ID删除角色和菜单关联
	DeleteRoleMenuByRoleId(roleId int64) error

	// BatchRoleMenu 批量新增角色菜单信息
	BatchRoleMenu(roleMenus []domain.SysRoleMenu) error

	// SelectMenuListByRoleId 查询菜单使用数量
	SelectMenuListByRoleId(roleId int64) ([]int64, error)

	// CheckMenuExistRole 检查菜单是否有角色在使用
	CheckMenuExistRole(menuId int64) (bool, error)
}

// roleMenuRepository 角色菜单关联仓储实现
type roleMenuRepository struct {
	*BaseRepository
}

// NewRoleMenuRepository 创建角色菜单关联仓储
func NewRoleMenuRepository() RoleMenuRepository {
	return &roleMenuRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *roleMenuRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &roleMenuRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// DeleteRoleMenuByRoleId 通过角色ID删除角色和菜单关联
func (r *roleMenuRepository) DeleteRoleMenuByRoleId(roleId int64) error {
	return r.GetDB().Where("role_id = ?", roleId).Delete(&domain.SysRoleMenu{}).Error
}

// BatchRoleMenu 批量新增角色菜单信息
func (r *roleMenuRepository) BatchRoleMenu(roleMenus []domain.SysRoleMenu) error {
	return r.GetDB().Create(&roleMenus).Error
}

// SelectMenuListByRoleId 查询菜单使用数量
func (r *roleMenuRepository) SelectMenuListByRoleId(roleId int64) ([]int64, error) {
	var menuIds []int64
	err := r.GetDB().Model(&domain.SysRoleMenu{}).
		Where("role_id = ?", roleId).
		Pluck("menu_id", &menuIds).Error
	return menuIds, err
}

// CheckMenuExistRole 检查菜单是否有角色在使用
func (r *roleMenuRepository) CheckMenuExistRole(menuId int64) (bool, error) {
	var count int64
	err := r.GetDB().Model(&domain.SysRoleMenu{}).
		Where("menu_id = ?", menuId).
		Count(&count).Error
	return count > 0, err
}
