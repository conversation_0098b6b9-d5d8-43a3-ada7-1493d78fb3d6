package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// RoleRepository 角色仓储接口
type RoleRepository interface {
	Repository
	// SelectRoleList 查询角色列表
	SelectRoleList(role *domain.SysRole) ([]domain.SysRole, error)

	// SelectRolePermissionByUserId 根据用户ID查询角色权限
	SelectRolePermissionByUserId(userId int64) ([]domain.SysRole, error)

	// SelectRoleAll 查询所有角色
	SelectRoleAll() ([]domain.SysRole, error)

	// SelectRoleListByUserId 根据用户ID查询角色ID列表
	SelectRoleListByUserId(userId int64) ([]int64, error)

	// SelectRoleById 根据角色ID查询角色
	SelectRoleById(roleId int64) (*domain.SysRole, error)

	// SelectRolesByUserName 根据用户名查询角色
	SelectRolesByUserName(userName string) ([]domain.SysRole, error)

	// CheckRoleNameUnique 校验角色名称是否唯一
	CheckRoleNameUnique(roleName string) (*domain.SysRole, error)

	// CheckRoleKeyUnique 校验角色权限是否唯一
	CheckRoleKeyUnique(roleKey string) (*domain.SysRole, error)

	// UpdateRole 修改角色信息
	UpdateRole(role *domain.SysRole) error

	// InsertRole 新增角色信息
	InsertRole(role *domain.SysRole) error

	// DeleteRoleById 删除角色信息
	DeleteRoleById(roleId int64) error

	// DeleteRoleByIds 批量删除角色信息
	DeleteRoleByIds(roleIds []int64) error
}

// roleRepository 角色仓储实现
type roleRepository struct {
	*BaseRepository
}

// NewRoleRepository 创建角色仓储
func NewRoleRepository() RoleRepository {
	return &roleRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *roleRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &roleRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectRoleList 查询角色列表
func (r *roleRepository) SelectRoleList(role *domain.SysRole) ([]domain.SysRole, error) {
	var roles []domain.SysRole
	db := r.GetDB().Model(&domain.SysRole{})

	// 构建查询条件
	if role != nil {
		if role.RoleId > 0 {
			db = db.Where("role_id = ?", role.RoleId)
		}
		if role.RoleName != "" {
			db = db.Where("role_name like ?", "%"+role.RoleName+"%")
		}
		if role.Status != "" {
			db = db.Where("status = ?", role.Status)
		}
		if role.RoleKey != "" {
			db = db.Where("role_key like ?", "%"+role.RoleKey+"%")
		}
		// 数据范围过滤
		if role.GetParams() != nil && role.GetParams()["dataScope"] != nil {
			db = db.Where(role.GetParams()["dataScope"].(string))
		}
		// 开始时间和结束时间过滤
		if role.GetParams() != nil {
			if role.GetParams()["beginTime"] != nil && role.GetParams()["endTime"] != nil {
				db = db.Where("create_time between ? and ?", role.GetParams()["beginTime"], role.GetParams()["endTime"])
			}
		}
	}

	// 排除已删除的角色
	db = db.Where("del_flag = '0'")

	// 执行查询
	if err := db.Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// SelectRolePermissionByUserId 根据用户ID查询角色权限
func (r *roleRepository) SelectRolePermissionByUserId(userId int64) ([]domain.SysRole, error) {
	var roles []domain.SysRole

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysRole{}).
		Select("distinct r.*").
		Table("sys_role r").
		Joins("left join sys_user_role ur on ur.role_id = r.role_id").
		Joins("left join sys_user u on u.user_id = ur.user_id").
		Where("u.user_id = ? and r.status = '0' and r.del_flag = '0'", userId)

	// 执行查询
	if err := db.Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// SelectRoleAll 查询所有角色
func (r *roleRepository) SelectRoleAll() ([]domain.SysRole, error) {
	var roles []domain.SysRole

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysRole{}).Where("del_flag = '0'")

	// 执行查询
	if err := db.Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// SelectRoleListByUserId 根据用户ID查询角色ID列表
func (r *roleRepository) SelectRoleListByUserId(userId int64) ([]int64, error) {
	var roleIds []int64

	// 构建查询条件
	db := r.GetDB().Table("sys_user_role").
		Select("role_id").
		Where("user_id = ?", userId)

	// 执行查询
	if err := db.Pluck("role_id", &roleIds).Error; err != nil {
		return nil, err
	}

	return roleIds, nil
}

// SelectRoleById 根据角色ID查询角色
func (r *roleRepository) SelectRoleById(roleId int64) (*domain.SysRole, error) {
	var role domain.SysRole
	err := r.GetDB().Where("role_id = ? AND del_flag = '0'", roleId).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// SelectRolesByUserName 根据用户名查询角色
func (r *roleRepository) SelectRolesByUserName(userName string) ([]domain.SysRole, error) {
	var roles []domain.SysRole

	// 构建查询条件
	db := r.GetDB().Model(&domain.SysRole{}).
		Select("distinct r.*").
		Table("sys_role r").
		Joins("left join sys_user_role ur on ur.role_id = r.role_id").
		Joins("left join sys_user u on u.user_id = ur.user_id").
		Where("u.user_name = ? and r.status = '0' and r.del_flag = '0'", userName)

	// 执行查询
	if err := db.Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (r *roleRepository) CheckRoleNameUnique(roleName string) (*domain.SysRole, error) {
	var role domain.SysRole
	err := r.GetDB().Where("role_name = ? AND del_flag = '0'", roleName).First(&role).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (r *roleRepository) CheckRoleKeyUnique(roleKey string) (*domain.SysRole, error) {
	var role domain.SysRole
	err := r.GetDB().Where("role_key = ? AND del_flag = '0'", roleKey).First(&role).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// UpdateRole 修改角色信息
func (r *roleRepository) UpdateRole(role *domain.SysRole) error {
	return r.GetDB().Save(role).Error
}

// InsertRole 新增角色信息
func (r *roleRepository) InsertRole(role *domain.SysRole) error {
	return r.GetDB().Create(role).Error
}

// DeleteRoleById 删除角色信息
func (r *roleRepository) DeleteRoleById(roleId int64) error {
	return r.GetDB().Model(&domain.SysRole{}).Where("role_id = ?", roleId).Update("del_flag", "2").Error
}

// DeleteRoleByIds 批量删除角色信息
func (r *roleRepository) DeleteRoleByIds(roleIds []int64) error {
	return r.GetDB().Model(&domain.SysRole{}).Where("role_id in ?", roleIds).Update("del_flag", "2").Error
}
