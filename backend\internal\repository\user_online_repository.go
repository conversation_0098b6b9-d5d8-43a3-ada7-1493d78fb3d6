package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// UserOnlineRepository 在线用户仓储接口
type UserOnlineRepository interface {
	Repository
	// SelectOnlineById 通过会话编号查询信息
	SelectOnlineById(sessionId string) (*domain.SysUserOnline, error)

	// DeleteOnlineById 通过会话编号删除信息
	DeleteOnlineById(sessionId string) error

	// DeleteOnlineByIds 批量删除会话信息
	DeleteOnlineByIds(sessionIds []string) error

	// SaveOnline 保存会话信息
	SaveOnline(online *domain.SysUserOnline) error

	// SelectOnlineByIpaddr 查询会话集合
	SelectOnlineByIpaddr(ipaddr string) ([]domain.SysUserOnline, error)

	// SelectOnlineByUserName 通过用户名查询会话
	SelectOnlineByUserName(userName string) ([]domain.SysUserOnline, error)

	// SelectOnlineList 查询会话集合
	SelectOnlineList(userOnline *domain.SysUserOnline) ([]domain.SysUserOnline, error)
}

// userOnlineRepository 在线用户仓储实现
type userOnlineRepository struct {
	*BaseRepository
}

// NewUserOnlineRepository 创建在线用户仓储
func NewUserOnlineRepository() UserOnlineRepository {
	return &userOnlineRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *userOnlineRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &userOnlineRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectOnlineById 通过会话编号查询信息
func (r *userOnlineRepository) SelectOnlineById(sessionId string) (*domain.SysUserOnline, error) {
	var online domain.SysUserOnline
	err := r.GetDB().Where("sessionId = ?", sessionId).First(&online).Error
	if err != nil {
		return nil, err
	}
	return &online, nil
}

// DeleteOnlineById 通过会话编号删除信息
func (r *userOnlineRepository) DeleteOnlineById(sessionId string) error {
	return r.GetDB().Where("sessionId = ?", sessionId).Delete(&domain.SysUserOnline{}).Error
}

// DeleteOnlineByIds 批量删除会话信息
func (r *userOnlineRepository) DeleteOnlineByIds(sessionIds []string) error {
	return r.GetDB().Where("sessionId in ?", sessionIds).Delete(&domain.SysUserOnline{}).Error
}

// SaveOnline 保存会话信息
func (r *userOnlineRepository) SaveOnline(online *domain.SysUserOnline) error {
	return r.GetDB().Save(online).Error
}

// SelectOnlineByIpaddr 查询会话集合
func (r *userOnlineRepository) SelectOnlineByIpaddr(ipaddr string) ([]domain.SysUserOnline, error) {
	var onlineList []domain.SysUserOnline
	err := r.GetDB().Where("ipaddr = ?", ipaddr).Find(&onlineList).Error
	if err != nil {
		return nil, err
	}
	return onlineList, nil
}

// SelectOnlineByUserName 通过用户名查询会话
func (r *userOnlineRepository) SelectOnlineByUserName(userName string) ([]domain.SysUserOnline, error) {
	var onlineList []domain.SysUserOnline
	err := r.GetDB().Where("user_name = ?", userName).Find(&onlineList).Error
	if err != nil {
		return nil, err
	}
	return onlineList, nil
}

// SelectOnlineList 查询会话集合
func (r *userOnlineRepository) SelectOnlineList(userOnline *domain.SysUserOnline) ([]domain.SysUserOnline, error) {
	var onlineList []domain.SysUserOnline
	db := r.GetDB().Model(&domain.SysUserOnline{})

	// 构建查询条件
	if userOnline != nil {
		if userOnline.Ipaddr != "" {
			db = db.Where("ipaddr like ?", "%"+userOnline.Ipaddr+"%")
		}
		if userOnline.UserName != "" {
			db = db.Where("user_name like ?", "%"+userOnline.UserName+"%")
		}
		if userOnline.LoginLocation != "" {
			db = db.Where("login_location like ?", "%"+userOnline.LoginLocation+"%")
		}
	}

	// 执行查询
	if err := db.Find(&onlineList).Error; err != nil {
		return nil, err
	}

	return onlineList, nil
}
