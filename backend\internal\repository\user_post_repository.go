package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// UserPostRepository 用户岗位关联仓储接口
type UserPostRepository interface {
	Repository
	// DeleteUserPostByUserId 通过用户ID删除用户和岗位关联
	DeleteUserPostByUserId(userId int64) error

	// DeleteUserPostByPostId 通过岗位ID删除用户和岗位关联
	DeleteUserPostByPostId(postId int64) error

	// CountUserPostByPostId 通过岗位ID查询岗位使用数量
	CountUserPostByPostId(postId int64) (int64, error)

	// BatchUserPost 批量新增用户岗位信息
	BatchUserPost(userPosts []domain.SysUserPost) error
}

// userPostRepository 用户岗位关联仓储实现
type userPostRepository struct {
	*BaseRepository
}

// NewUserPostRepository 创建用户岗位关联仓储
func NewUserPostRepository() UserPostRepository {
	return &userPostRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *userPostRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &userPostRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// DeleteUserPostByUserId 通过用户ID删除用户和岗位关联
func (r *userPostRepository) DeleteUserPostByUserId(userId int64) error {
	return r.GetDB().Where("user_id = ?", userId).Delete(&domain.SysUserPost{}).Error
}

// DeleteUserPostByPostId 通过岗位ID删除用户和岗位关联
func (r *userPostRepository) DeleteUserPostByPostId(postId int64) error {
	return r.GetDB().Where("post_id = ?", postId).Delete(&domain.SysUserPost{}).Error
}

// CountUserPostByPostId 通过岗位ID查询岗位使用数量
func (r *userPostRepository) CountUserPostByPostId(postId int64) (int64, error) {
	var count int64
	err := r.GetDB().Model(&domain.SysUserPost{}).Where("post_id = ?", postId).Count(&count).Error
	return count, err
}

// BatchUserPost 批量新增用户岗位信息
func (r *userPostRepository) BatchUserPost(userPosts []domain.SysUserPost) error {
	return r.GetDB().Create(&userPosts).Error
}
