package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Repository
	// SelectUserList 查询用户列表
	SelectUserList(user *domain.SysUser) ([]domain.SysUser, error)

	// SelectAllocatedList 查询已分配用户角色列表
	SelectAllocatedList(user *domain.SysUser) ([]domain.SysUser, error)

	// SelectUnallocatedList 查询未分配用户角色列表
	SelectUnallocatedList(user *domain.SysUser) ([]domain.SysUser, error)

	// SelectUserByUserName 根据用户名查询用户
	SelectUserByUserName(userName string) (*domain.SysUser, error)

	// SelectUserById 根据用户ID查询用户
	SelectUserById(userId int64) (*domain.SysUser, error)

	// InsertUser 新增用户信息
	InsertUser(user *domain.SysUser) error

	// UpdateUser 修改用户信息
	UpdateUser(user *domain.SysUser) error

	// UpdateUserAvatar 修改用户头像
	UpdateUserAvatar(userId int64, avatar string) error

	// ResetUserPwd 重置用户密码
	ResetUserPwd(userId int64, password string) error

	// DeleteUserById 删除用户信息
	DeleteUserById(userId int64) error

	// DeleteUserByIds 批量删除用户信息
	DeleteUserByIds(userIds []int64) error

	// CheckUserNameUnique 校验用户名称是否唯一
	CheckUserNameUnique(userName string) (*domain.SysUser, error)

	// CheckPhoneUnique 校验手机号码是否唯一
	CheckPhoneUnique(phonenumber string) (*domain.SysUser, error)

	// CheckEmailUnique 校验email是否唯一
	CheckEmailUnique(email string) (*domain.SysUser, error)
}

// userRepository 用户仓储实现
type userRepository struct {
	*BaseRepository
}

// NewUserRepository 创建用户仓储
func NewUserRepository() UserRepository {
	return &userRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *userRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &userRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// SelectUserList 查询用户列表
func (r *userRepository) SelectUserList(user *domain.SysUser) ([]domain.SysUser, error) {
	var users []domain.SysUser
	db := r.GetDB().Model(&domain.SysUser{})

	// 构建查询条件
	if user != nil {
		if user.UserId > 0 {
			db = db.Where("user_id = ?", user.UserId)
		}
		if user.UserName != "" {
			db = db.Where("user_name like ?", "%"+user.UserName+"%")
		}
		if user.Status != "" {
			db = db.Where("status = ?", user.Status)
		}
		if user.Phonenumber != "" {
			db = db.Where("phonenumber like ?", "%"+user.Phonenumber+"%")
		}
		if user.DeptId > 0 {
			db = db.Where("dept_id = ?", user.DeptId)
		}
		// 数据范围过滤
		if user.GetParams() != nil && user.GetParams()["dataScope"] != nil {
			db = db.Where(user.GetParams()["dataScope"].(string))
		}
	}

	// 排除已删除的用户
	db = db.Where("del_flag = '0'")

	// 执行查询
	if err := db.Find(&users).Error; err != nil {
		return nil, err
	}

	return users, nil
}

// SelectAllocatedList 查询已分配用户角色列表
func (r *userRepository) SelectAllocatedList(user *domain.SysUser) ([]domain.SysUser, error) {
	var users []domain.SysUser
	db := r.GetDB().Model(&domain.SysUser{})

	// 构建查询条件
	db = db.Select("distinct u.*").
		Table("sys_user u").
		Joins("left join sys_user_role ur on u.user_id = ur.user_id").
		Joins("left join sys_role r on r.role_id = ur.role_id")

	if user != nil {
		if user.UserName != "" {
			db = db.Where("u.user_name like ?", "%"+user.UserName+"%")
		}
		if user.Phonenumber != "" {
			db = db.Where("u.phonenumber like ?", "%"+user.Phonenumber+"%")
		}
		if user.RoleId > 0 {
			db = db.Where("r.role_id = ?", user.RoleId)
		}
	}

	// 排除已删除的用户
	db = db.Where("u.del_flag = '0'")

	// 执行查询
	if err := db.Find(&users).Error; err != nil {
		return nil, err
	}

	return users, nil
}

// SelectUnallocatedList 查询未分配用户角色列表
func (r *userRepository) SelectUnallocatedList(user *domain.SysUser) ([]domain.SysUser, error) {
	var users []domain.SysUser
	db := r.GetDB().Model(&domain.SysUser{})

	// 构建查询条件
	db = db.Select("distinct u.*").
		Table("sys_user u").
		Joins("left join sys_user_role ur on u.user_id = ur.user_id").
		Where("ur.role_id is null")

	if user != nil {
		if user.UserName != "" {
			db = db.Where("u.user_name like ?", "%"+user.UserName+"%")
		}
		if user.Phonenumber != "" {
			db = db.Where("u.phonenumber like ?", "%"+user.Phonenumber+"%")
		}
		if user.RoleId > 0 {
			db = db.Where("ur.role_id != ? or ur.role_id is null", user.RoleId)
		}
	}

	// 排除已删除的用户
	db = db.Where("u.del_flag = '0'")

	// 执行查询
	if err := db.Find(&users).Error; err != nil {
		return nil, err
	}

	return users, nil
}

// SelectUserByUserName 根据用户名查询用户
func (r *userRepository) SelectUserByUserName(userName string) (*domain.SysUser, error) {
	var user domain.SysUser
	err := r.GetDB().Where("user_name = ? AND del_flag = '0'", userName).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// SelectUserById 根据用户ID查询用户
func (r *userRepository) SelectUserById(userId int64) (*domain.SysUser, error) {
	var user domain.SysUser
	err := r.GetDB().Where("user_id = ? AND del_flag = '0'", userId).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// InsertUser 新增用户信息
func (r *userRepository) InsertUser(user *domain.SysUser) error {
	return r.GetDB().Create(user).Error
}

// UpdateUser 修改用户信息
func (r *userRepository) UpdateUser(user *domain.SysUser) error {
	return r.GetDB().Save(user).Error
}

// UpdateUserAvatar 修改用户头像
func (r *userRepository) UpdateUserAvatar(userId int64, avatar string) error {
	return r.GetDB().Model(&domain.SysUser{}).Where("user_id = ?", userId).Update("avatar", avatar).Error
}

// ResetUserPwd 重置用户密码
func (r *userRepository) ResetUserPwd(userId int64, password string) error {
	return r.GetDB().Model(&domain.SysUser{}).Where("user_id = ?", userId).Update("password", password).Error
}

// DeleteUserById 删除用户信息
func (r *userRepository) DeleteUserById(userId int64) error {
	return r.GetDB().Model(&domain.SysUser{}).Where("user_id = ?", userId).Update("del_flag", "2").Error
}

// DeleteUserByIds 批量删除用户信息
func (r *userRepository) DeleteUserByIds(userIds []int64) error {
	return r.GetDB().Model(&domain.SysUser{}).Where("user_id in ?", userIds).Update("del_flag", "2").Error
}

// CheckUserNameUnique 校验用户名称是否唯一
func (r *userRepository) CheckUserNameUnique(userName string) (*domain.SysUser, error) {
	var user domain.SysUser
	err := r.GetDB().Where("user_name = ?", userName).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// CheckPhoneUnique 校验手机号码是否唯一
func (r *userRepository) CheckPhoneUnique(phonenumber string) (*domain.SysUser, error) {
	var user domain.SysUser
	err := r.GetDB().Where("phonenumber = ?", phonenumber).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// CheckEmailUnique 校验email是否唯一
func (r *userRepository) CheckEmailUnique(email string) (*domain.SysUser, error) {
	var user domain.SysUser
	err := r.GetDB().Where("email = ?", email).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}
