package repository

import (
	"github.com/ruoyi/backend/internal/domain"
	"gorm.io/gorm"
)

// UserRoleRepository 用户角色关联仓储接口
type UserRoleRepository interface {
	Repository
	// DeleteUserRoleByUserId 通过用户ID删除用户和角色关联
	DeleteUserRoleByUserId(userId int64) error

	// DeleteUserRoleByRoleId 通过角色ID删除用户和角色关联
	DeleteUserRoleByRoleId(roleId int64) error

	// CountUserRoleByRoleId 通过角色ID查询角色使用数量
	CountUserRoleByRoleId(roleId int64) (int64, error)

	// DeleteUserRole 删除用户和角色关联信息
	DeleteUserRole(userRole domain.SysUserRole) error

	// BatchUserRole 批量新增用户角色信息
	BatchUserRole(userRoles []domain.SysUserRole) error

	// DeleteAuthUsers 批量取消用户授权
	DeleteAuthUsers(roleId int64, userIds []int64) error

	// InsertAuthUsers 批量选择用户授权
	InsertAuthUsers(roleId int64, userIds []int64) error
}

// userRoleRepository 用户角色关联仓储实现
type userRoleRepository struct {
	*BaseRepository
}

// NewUserRoleRepository 创建用户角色关联仓储
func NewUserRoleRepository() UserRoleRepository {
	return &userRoleRepository{
		BaseRepository: NewBaseRepository(),
	}
}

// WithTx 使用事务
func (r *userRoleRepository) WithTx(tx *gorm.DB) Repository {
	if tx == nil {
		return r
	}
	return &userRoleRepository{
		BaseRepository: &BaseRepository{
			db: tx,
		},
	}
}

// DeleteUserRoleByUserId 通过用户ID删除用户和角色关联
func (r *userRoleRepository) DeleteUserRoleByUserId(userId int64) error {
	return r.GetDB().Where("user_id = ?", userId).Delete(&domain.SysUserRole{}).Error
}

// DeleteUserRoleByRoleId 通过角色ID删除用户和角色关联
func (r *userRoleRepository) DeleteUserRoleByRoleId(roleId int64) error {
	return r.GetDB().Where("role_id = ?", roleId).Delete(&domain.SysUserRole{}).Error
}

// CountUserRoleByRoleId 通过角色ID查询角色使用数量
func (r *userRoleRepository) CountUserRoleByRoleId(roleId int64) (int64, error) {
	var count int64
	err := r.GetDB().Model(&domain.SysUserRole{}).Where("role_id = ?", roleId).Count(&count).Error
	return count, err
}

// DeleteUserRole 删除用户和角色关联信息
func (r *userRoleRepository) DeleteUserRole(userRole domain.SysUserRole) error {
	return r.GetDB().Where("user_id = ? AND role_id = ?", userRole.UserId, userRole.RoleId).Delete(&domain.SysUserRole{}).Error
}

// BatchUserRole 批量新增用户角色信息
func (r *userRoleRepository) BatchUserRole(userRoles []domain.SysUserRole) error {
	return r.GetDB().Create(&userRoles).Error
}

// DeleteAuthUsers 批量取消用户授权
func (r *userRoleRepository) DeleteAuthUsers(roleId int64, userIds []int64) error {
	return r.GetDB().Where("role_id = ? AND user_id IN ?", roleId, userIds).Delete(&domain.SysUserRole{}).Error
}

// InsertAuthUsers 批量选择用户授权
func (r *userRoleRepository) InsertAuthUsers(roleId int64, userIds []int64) error {
	// 构建批量插入数据
	userRoles := make([]domain.SysUserRole, 0, len(userIds))
	for _, userId := range userIds {
		userRoles = append(userRoles, domain.SysUserRole{
			UserId: userId,
			RoleId: roleId,
		})
	}
	return r.BatchUserRole(userRoles)
}
