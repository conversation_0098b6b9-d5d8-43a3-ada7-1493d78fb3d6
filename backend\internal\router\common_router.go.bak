package router

import (
	"fmt"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/middleware"
	"github.com/ruoyi/backend/internal/redis"
	"go.uber.org/zap"
)

// 定义验证码存储
var captchaStore = base64Captcha.DefaultMemStore

// RegisterCommonRoutes 注册通用路由
func RegisterCommonRoutes(r *gin.RouterGroup, logger *zap.Logger, appConfig *config.AppConfig) {
	// 创建服务实例
	jwtConfig := &config.JWTConfig{}          // 应该从配置中获取
	redisService := &redis.RedisServiceImpl{} // 应该从依赖注入获取
	tokenManager := auth.NewTokenManager(logger, jwtConfig, redisService)

	// 无需认证的路由组
	publicGroup := r.Group("")
	{
		// 通用下载
		publicGroup.GET("/common/download", commonDownload)

		// 通用预览
		publicGroup.GET("/common/preview", func(c *gin.Context) {
			commonPreview(c, appConfig)
		})

		// 获取验证码
		publicGroup.GET("/common/captcha", getCaptcha)
	}

	// 需要认证的路由组
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuthMiddleware(logger, tokenManager))
	{
		// 通用上传
		authGroup.POST("/common/upload", func(c *gin.Context) {
			commonUpload(c, logger, appConfig)
		})

		// 获取当前系统时间
		authGroup.GET("/common/currentTime", getCurrentTime)
	}
}

// 通用下载处理函数
func commonDownload(c *gin.Context) {
	// 获取文件名
	fileName := c.Query("fileName")
	if fileName == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "文件名不能为空",
		})
		return
	}

	// 获取删除标志
	delete := c.DefaultQuery("delete", "false")

	// 获取文件路径
	filePath := c.Query("filePath")
	if filePath == "" {
		filePath = "download"
	}

	// 构建完整的文件路径
	fullPath := path.Join(filepath.FromSlash(filePath), fileName)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "文件不存在",
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Transfer-Encoding", "binary")

	// 下载文件
	c.File(fullPath)

	// 如果需要删除文件
	if delete == "true" {
		os.Remove(fullPath)
	}
}

// 通用预览处理函数
func commonPreview(c *gin.Context, appConfig *config.AppConfig) {
	// 获取文件名
	fileName := c.Query("fileName")
	if fileName == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "文件名不能为空",
		})
		return
	}

	// 获取文件路径
	filePath := c.DefaultQuery("filePath", "")

	// 构建完整的文件路径
	var fullPath string
	if filePath == "" {
		// 默认使用资源路径
		fullPath = path.Join(appConfig.GetResourcePath(), fileName)
	} else {
		fullPath = path.Join(filepath.FromSlash(filePath), fileName)
	}

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "文件不存在",
		})
		return
	}

	// 获取文件扩展名
	fileExt := strings.ToLower(path.Ext(fileName))

	// 根据文件类型设置Content-Type
	contentType := "application/octet-stream"
	switch fileExt {
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp":
		contentType = "image/" + fileExt[1:]
	case ".pdf":
		contentType = "application/pdf"
	case ".txt":
		contentType = "text/plain"
	case ".html", ".htm":
		contentType = "text/html"
	case ".mp4":
		contentType = "video/mp4"
	case ".mp3":
		contentType = "audio/mp3"
	}

	// 设置响应头
	c.Header("Content-Type", contentType)

	// 预览文件
	c.File(fullPath)
}

// 通用上传处理函数
func commonUpload(c *gin.Context, logger *zap.Logger, appConfig *config.AppConfig) {
	// 获取文件
	file, err := c.FormFile("file")
	if err != nil {
		logger.Error("获取上传文件失败", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "获取上传文件失败",
		})
		return
	}

	// 获取上传目录
	uploadDir := c.DefaultPostForm("uploadDir", "common")
	baseDir := appConfig.GetUploadPath()
	uploadPath := path.Join(baseDir, uploadDir)

	// 确保上传目录存在
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		logger.Error("创建上传目录失败", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  fmt.Sprintf("创建上传目录失败: %s", err.Error()),
		})
		return
	}

	// 生成新的文件名
	fileExt := strings.ToLower(path.Ext(file.Filename))
	fileName := fmt.Sprintf("%s_%d%s", time.Now().Format("20060102150405"), time.Now().UnixNano()%1000, fileExt)

	// 保存文件
	filePath := path.Join(uploadPath, fileName)
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		logger.Error("上传文件失败", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  fmt.Sprintf("上传文件失败: %s", err.Error()),
		})
		return
	}

	// 构建URL
	url := path.Join(uploadDir, fileName)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "上传成功",
		"data": map[string]interface{}{
			"fileName":         fileName,
			"url":              url,
			"newFileName":      fileName,
			"originalFileName": file.Filename,
		},
	})
}

// 获取验证码处理函数
func getCaptcha(c *gin.Context) {
	// 配置验证码参数
	driverString := base64Captcha.DriverString{
		Height:          40,
		Width:           120,
		NoiseCount:      0,
		ShowLineOptions: 2 | 4,
		Length:          4,
		Source:          "1234567890abcdefghijklmnopqrstuvwxyz",
		BgColor:         &[]int{240, 240, 246},
		Fonts:           []string{"wqy-microhei.ttc"},
	}
	driver := driverString.ConvertFonts()

	// 生成验证码
	captcha := base64Captcha.NewCaptcha(driver, captchaStore)
	id, b64s, err := captcha.Generate()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "生成验证码失败",
		})
		return
	}

	// 返回验证码
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取验证码成功",
		"data": map[string]string{
			"uuid": id,
			"img":  b64s,
		},
	})
}

// 获取当前系统时间处理函数
func getCurrentTime(c *gin.Context) {
	now := time.Now()
	c.JSON(200, gin.H{
		"code": 200,
		"msg":  "获取当前系统时间成功",
		"data": map[string]interface{}{
			"currentTime": now.Format("2006-01-02 15:04:05"),
			"date":        now.Format("2006-01-02"),
			"time":        now.Format("15:04:05"),
			"year":        now.Year(),
			"month":       int(now.Month()),
			"day":         now.Day(),
			"hour":        now.Hour(),
			"minute":      now.Minute(),
			"second":      now.Second(),
			"timestamp":   now.Unix(),
		},
	})
}
