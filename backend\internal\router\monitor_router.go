package router

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/middleware"
	myredis "github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service/impl"
	"go.uber.org/zap"
)

// 创建Redis客户端的通用函数
func createRedisClient() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr: fmt.Sprintf("%s:%s",
			getEnvOrDefault("REDIS_HOST", "localhost"),
			getEnvOrDefault("REDIS_PORT", "6379")),
		Password: getEnvOrDefault("REDIS_PASSWORD", ""),
		DB:       getEnvIntOrDefault("REDIS_DB", 0),
	})
}

// 辅助函数
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// RegisterMonitorRoutes 注册监控路由
func RegisterMonitorRoutes(r *gin.RouterGroup, logger *zap.Logger) {
	// 创建服务实例
	jwtConfig := &config.JWTConfig{}            // 应该从配置中获取
	redisService := &myredis.RedisServiceImpl{} // 应该从依赖注入获取
	tokenManager := auth.NewTokenManager(logger, jwtConfig, redisService)
	permissionChecker := auth.NewPermissionChecker(logger, tokenManager)

	// 需要认证的路由组
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuthMiddleware(logger, tokenManager))
	{
		// 定时任务管理
		jobGroup := authGroup.Group("/monitor/job")
		jobGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			jobGroup.GET("/list", getJobList)
			jobGroup.GET("/:jobId", getJobInfo)
			jobGroup.POST("", addJob)
			jobGroup.PUT("", updateJob)
			jobGroup.DELETE("/:jobIds", deleteJob)
			jobGroup.PUT("/changeStatus", changeJobStatus)
			jobGroup.PUT("/run", runJob)
		}

		// 定时任务日志管理
		jobLogGroup := authGroup.Group("/monitor/jobLog")
		jobLogGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			jobLogGroup.GET("/list", getJobLogList)
			jobLogGroup.GET("/:jobLogId", getJobLogInfo)
			jobLogGroup.DELETE("/:jobLogIds", deleteJobLog)
			jobLogGroup.DELETE("/clean", cleanJobLog)
		}

		// 登录日志管理
		loginInfoGroup := authGroup.Group("/monitor/logininfor")
		loginInfoGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			loginInfoGroup.GET("/list", getLoginLogList)
			loginInfoGroup.DELETE("/:infoIds", deleteLoginLog)
			loginInfoGroup.DELETE("/clean", cleanLoginLog)
		}

		// 操作日志管理
		operLogGroup := authGroup.Group("/monitor/operlog")
		operLogGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			operLogGroup.GET("/list", getOperLogList)
			operLogGroup.GET("/:operId", getOperLogInfo)
			operLogGroup.DELETE("/:operIds", deleteOperLog)
			operLogGroup.DELETE("/clean", cleanOperLog)
		}

		// 在线用户管理
		onlineGroup := authGroup.Group("/monitor/online")
		onlineGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			onlineGroup.GET("/list", getOnlineUserList)
			onlineGroup.DELETE("/:tokenId", forceLogout)
		}

		// 服务器监控
		serverGroup := authGroup.Group("/monitor/server")
		serverGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			serverGroup.GET("", getServerInfo)
		}

		// 缓存监控
		cacheGroup := authGroup.Group("/monitor/cache")
		cacheGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		{
			cacheGroup.GET("", getCacheInfo)
			cacheGroup.GET("/getNames", getCacheNames)
			cacheGroup.GET("/getKeys/:cacheName", getCacheKeys)
			cacheGroup.GET("/getValue/:cacheName/:cacheKey", getCacheValue)
			cacheGroup.DELETE("/clearCacheName/:cacheName", clearCacheName)
			cacheGroup.DELETE("/clearCacheKey/:cacheKey", clearCacheKey)
			cacheGroup.DELETE("/clearCacheAll", clearCacheAll)
		}
	}
}

// 任务相关处理函数
func getJobList(c *gin.Context) {
	// 从请求中获取查询参数
	jobName := c.Query("jobName")
	jobGroup := c.Query("jobGroup")
	status := c.Query("status")

	// 获取分页参数
	pageNum, _ := strconv.Atoi(c.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 构建查询参数
	job := domain.SysJob{}
	job.JobName = jobName
	job.JobGroup = jobGroup
	job.Status = status

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 查询任务列表
	list := jobService.SelectJobList(job)

	// 分页处理
	startIndex := (pageNum - 1) * pageSize
	endIndex := startIndex + pageSize

	var pagedList []domain.SysJob
	if startIndex < len(list) {
		if endIndex > len(list) {
			endIndex = len(list)
		}
		pagedList = list[startIndex:endIndex]
	} else {
		pagedList = []domain.SysJob{}
	}

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"total": len(list),
		"rows":  pagedList,
	})
}

func getJobInfo(c *gin.Context) {
	// 获取任务ID
	jobIdStr := c.Param("jobId")
	jobId, err := strconv.ParseInt(jobIdStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "任务ID格式错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 查询任务详情
	job := jobService.SelectJobById(jobId)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "查询成功",
		"data": job,
	})
}

func addJob(c *gin.Context) {
	// 解析请求体
	var job domain.SysJob
	if err := c.ShouldBindJSON(&job); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "请求参数错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 新增任务
	result := jobService.InsertJob(job)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "新增成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "新增失败",
		})
	}
}

func updateJob(c *gin.Context) {
	// 解析请求体
	var job domain.SysJob
	if err := c.ShouldBindJSON(&job); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "请求参数错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 修改任务
	result := jobService.UpdateJob(job)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "修改成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "修改失败",
		})
	}
}

func deleteJob(c *gin.Context) {
	// 获取任务ID
	jobIdsStr := c.Param("jobIds")
	if jobIdsStr == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}

	// 解析任务ID
	jobIds := make([]int64, 0)
	for _, idStr := range strings.Split(jobIdsStr, ",") {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		jobIds = append(jobIds, id)
	}

	if len(jobIds) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 删除任务
	jobService.DeleteJobByIds(jobIds)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

func changeJobStatus(c *gin.Context) {
	// 解析请求体
	var job domain.SysJob
	if err := c.ShouldBindJSON(&job); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "请求参数错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 修改任务状态
	result := jobService.ChangeStatus(job)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "状态修改成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "状态修改失败",
		})
	}
}

func runJob(c *gin.Context) {
	// 解析请求体
	var job domain.SysJob
	if err := c.ShouldBindJSON(&job); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "请求参数错误",
		})
		return
	}

	// 创建任务服务
	jobRepository := repository.NewJobRepository()
	jobService := impl.NewSysJobService(jobRepository)

	// 立即执行任务
	result := jobService.Run(job)

	// 返回结果
	if result {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "执行成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "执行失败",
		})
	}
}

// 任务日志相关处理函数
func getJobLogList(c *gin.Context) {
	// 从请求中获取查询参数
	jobName := c.Query("jobName")
	jobGroup := c.Query("jobGroup")
	status := c.Query("status")
	invokeTarget := c.Query("invokeTarget")
	startTime := c.Query("startTime")
	endTime := c.Query("endTime")

	// 获取分页参数
	pageNum, _ := strconv.Atoi(c.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 构建查询参数
	jobLog := domain.SysJobLog{}
	jobLog.JobName = jobName
	jobLog.JobGroup = jobGroup
	jobLog.Status = status
	jobLog.InvokeTarget = invokeTarget

	// 设置日期范围
	if startTime != "" && endTime != "" {
		params := make(map[string]interface{})
		params["beginTime"] = startTime
		params["endTime"] = endTime
		jobLog.SetParams(params)
	}

	// 创建任务日志服务
	jobLogRepository := repository.NewJobLogRepository()
	jobLogService := impl.NewSysJobLogService(jobLogRepository)

	// 查询任务日志列表
	list := jobLogService.SelectJobLogList(jobLog)

	// 分页处理
	startIndex := (pageNum - 1) * pageSize
	endIndex := startIndex + pageSize

	var pagedList []domain.SysJobLog
	if startIndex < len(list) {
		if endIndex > len(list) {
			endIndex = len(list)
		}
		pagedList = list[startIndex:endIndex]
	} else {
		pagedList = []domain.SysJobLog{}
	}

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"total": len(list),
		"rows":  pagedList,
	})
}

func getJobLogInfo(c *gin.Context) {
	// 获取日志ID
	jobLogIdStr := c.Param("jobLogId")
	jobLogId, err := strconv.ParseInt(jobLogIdStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "日志ID格式错误",
		})
		return
	}

	// 创建任务日志服务
	jobLogRepository := repository.NewJobLogRepository()
	jobLogService := impl.NewSysJobLogService(jobLogRepository)

	// 查询任务日志详情
	jobLog := jobLogService.SelectJobLogById(jobLogId)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "查询成功",
		"data": jobLog,
	})
}

func deleteJobLog(c *gin.Context) {
	// 获取日志ID
	jobLogIdsStr := c.Param("jobLogIds")
	if jobLogIdsStr == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}

	// 解析日志ID
	jobLogIds := make([]int64, 0)
	for _, idStr := range strings.Split(jobLogIdsStr, ",") {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		jobLogIds = append(jobLogIds, id)
	}

	if len(jobLogIds) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	// 创建任务日志服务
	jobLogRepository := repository.NewJobLogRepository()
	jobLogService := impl.NewSysJobLogService(jobLogRepository)

	// 删除任务日志
	result := jobLogService.DeleteJobLogByIds(jobLogIds)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "删除成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "删除失败",
		})
	}
}

func cleanJobLog(c *gin.Context) {
	// 创建任务日志服务
	jobLogRepository := repository.NewJobLogRepository()
	jobLogService := impl.NewSysJobLogService(jobLogRepository)

	// 清空任务日志
	jobLogService.CleanJobLog()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "清空成功",
	})
}

// 登录日志相关处理函数
func getLoginLogList(c *gin.Context) {
	// 从请求中获取查询参数
	ipaddr := c.Query("ipaddr")
	userName := c.Query("userName")
	status := c.Query("status")
	startTime := c.Query("startTime")
	endTime := c.Query("endTime")

	// 获取分页参数
	pageNum, _ := strconv.Atoi(c.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 构建查询参数
	logininfor := domain.SysLogininfor{}
	logininfor.Ipaddr = ipaddr
	logininfor.UserName = userName
	logininfor.Status = status

	// 设置日期范围
	if startTime != "" && endTime != "" {
		params := make(map[string]interface{})
		params["beginTime"] = startTime
		params["endTime"] = endTime
		logininfor.SetParams(params)
	}

	// 创建登录日志服务
	logininforRepository := repository.NewLogininforRepository()
	logininforService := impl.NewSysLogininforService(logininforRepository)

	// 查询登录日志列表
	list := logininforService.SelectLogininforList(logininfor)

	// 分页处理
	startIndex := (pageNum - 1) * pageSize
	endIndex := startIndex + pageSize

	var pagedList []domain.SysLogininfor
	if startIndex < len(list) {
		if endIndex > len(list) {
			endIndex = len(list)
		}
		pagedList = list[startIndex:endIndex]
	} else {
		pagedList = []domain.SysLogininfor{}
	}

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"total": len(list),
		"rows":  pagedList,
	})
}

func deleteLoginLog(c *gin.Context) {
	// 获取日志ID
	infoIdsStr := c.Param("infoIds")
	if infoIdsStr == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}

	// 解析日志ID
	infoIds := make([]int64, 0)
	for _, idStr := range strings.Split(infoIdsStr, ",") {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		infoIds = append(infoIds, id)
	}

	if len(infoIds) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	// 创建登录日志服务
	logininforRepository := repository.NewLogininforRepository()
	logininforService := impl.NewSysLogininforService(logininforRepository)

	// 删除登录日志
	result := logininforService.DeleteLogininforByIds(infoIds)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "删除成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "删除失败",
		})
	}
}

func cleanLoginLog(c *gin.Context) {
	// 创建登录日志服务
	logininforRepository := repository.NewLogininforRepository()
	logininforService := impl.NewSysLogininforService(logininforRepository)

	// 清空登录日志
	logininforService.CleanLogininfor()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "清空成功",
	})
}

// 操作日志相关处理函数
func getOperLogList(c *gin.Context) {
	// 从请求中获取查询参数
	title := c.Query("title")
	operName := c.Query("operName")
	businessType := c.Query("businessType")
	status := c.Query("status")
	startTime := c.Query("startTime")
	endTime := c.Query("endTime")

	// 获取分页参数
	pageNum, _ := strconv.Atoi(c.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 构建查询参数
	operLog := domain.SysOperLog{}
	operLog.Title = title
	operLog.OperName = operName

	if businessType != "" {
		bType, _ := strconv.Atoi(businessType)
		operLog.BusinessType = bType
	}

	if status != "" {
		s, _ := strconv.Atoi(status)
		operLog.Status = s
	}

	// 设置日期范围
	if startTime != "" && endTime != "" {
		params := make(map[string]interface{})
		params["beginTime"] = startTime
		params["endTime"] = endTime
		operLog.SetParams(params)
	}

	// 创建操作日志服务
	operLogRepository := repository.NewOperLogRepository()
	operLogService := impl.NewSysOperLogService(operLogRepository)

	// 查询操作日志列表
	list := operLogService.SelectOperLogList(operLog)

	// 分页处理
	startIndex := (pageNum - 1) * pageSize
	endIndex := startIndex + pageSize

	var pagedList []domain.SysOperLog
	if startIndex < len(list) {
		if endIndex > len(list) {
			endIndex = len(list)
		}
		pagedList = list[startIndex:endIndex]
	} else {
		pagedList = []domain.SysOperLog{}
	}

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"total": len(list),
		"rows":  pagedList,
	})
}

func getOperLogInfo(c *gin.Context) {
	// 获取日志ID
	operIdStr := c.Param("operId")
	operId, err := strconv.ParseInt(operIdStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "日志ID格式错误",
		})
		return
	}

	// 创建操作日志服务
	operLogRepository := repository.NewOperLogRepository()
	operLogService := impl.NewSysOperLogService(operLogRepository)

	// 查询操作日志详情
	operLog := operLogService.SelectOperLogById(operId)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "查询成功",
		"data": operLog,
	})
}

func deleteOperLog(c *gin.Context) {
	// 获取日志ID
	operIdsStr := c.Param("operIds")
	if operIdsStr == "" {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}

	// 解析日志ID
	operIds := make([]int64, 0)
	for _, idStr := range strings.Split(operIdsStr, ",") {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		operIds = append(operIds, id)
	}

	if len(operIds) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	// 创建操作日志服务
	operLogRepository := repository.NewOperLogRepository()
	operLogService := impl.NewSysOperLogService(operLogRepository)

	// 删除操作日志
	result := operLogService.DeleteOperLogByIds(operIds)

	// 返回结果
	if result > 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "删除成功",
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "删除失败",
		})
	}
}

func cleanOperLog(c *gin.Context) {
	// 创建操作日志服务
	operLogRepository := repository.NewOperLogRepository()
	operLogService := impl.NewSysOperLogService(operLogRepository)

	// 清空操作日志
	operLogService.CleanOperLog()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "清空成功",
	})
}

// 在线用户相关处理函数
func getOnlineUserList(c *gin.Context) {
	// 获取查询参数
	ipaddr := c.Query("ipaddr")
	userName := c.Query("userName")

	// 创建查询条件
	userOnline := &domain.SysUserOnline{}
	userOnline.Ipaddr = ipaddr
	userOnline.UserName = userName

	// 创建在线用户服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	userOnlineService := impl.NewSysUserOnlineService(logger, redisClient, redisService)

	// 查询在线用户列表
	list := userOnlineService.SelectOnlineList(userOnline)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"total": len(list),
		"rows":  list,
	})
}

func forceLogout(c *gin.Context) {
	// 获取令牌ID
	tokenId := c.Param("tokenId")

	// 创建在线用户服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	userOnlineService := impl.NewSysUserOnlineService(logger, redisClient, redisService)

	// 强制用户下线
	userOnlineService.ForceLogout(tokenId)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
	})
}

// 服务器监控相关处理函数
func getServerInfo(c *gin.Context) {
	// 创建服务器信息服务
	serverInfoService := impl.NewServerInfoService()

	// 获取服务器信息
	serverInfo := serverInfoService.GetServerInfo()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": serverInfo,
	})
}

// 缓存监控相关处理函数
func getCacheInfo(c *gin.Context) {
	// 创建Redis缓存服务
	// 注意：这里应该从依赖注入容器中获取服务实例，但为了简化，我们直接创建
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	// 创建Redis客户端
	redisClient := createRedisClient()
	defer redisClient.Close()

	// 创建Redis服务
	redisService := myredis.NewRedisServiceImpl(logger, redisClient)

	// 创建Redis缓存服务
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 获取缓存信息
	cacheInfo := redisCacheService.GetCacheInfo()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": cacheInfo,
	})
}

func getCacheNames(c *gin.Context) {
	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 获取缓存名称
	cacheNames := redisCacheService.GetCacheNames()

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": cacheNames,
	})
}

func getCacheKeys(c *gin.Context) {
	// 获取缓存名称
	cacheName := c.Param("cacheName")

	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 获取缓存键
	cacheKeys := redisCacheService.GetCacheKeys(cacheName)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": cacheKeys,
	})
}

func getCacheValue(c *gin.Context) {
	// 获取缓存名称和键
	cacheName := c.Param("cacheName")
	cacheKey := c.Param("cacheKey")

	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 获取缓存值
	cacheValue := redisCacheService.GetCacheValue(cacheName, cacheKey)

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
		"data": cacheValue,
	})
}

func clearCacheName(c *gin.Context) {
	// 获取缓存名称
	cacheName := c.Param("cacheName")

	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 清空缓存名称下的所有键
	err := redisCacheService.ClearCacheName(cacheName)

	// 返回结果
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "操作失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
	})
}

func clearCacheKey(c *gin.Context) {
	// 获取缓存键
	cacheKey := c.Param("cacheKey")

	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 清除指定缓存键
	err := redisCacheService.ClearCacheKey(cacheKey)

	// 返回结果
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "操作失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
	})
}

func clearCacheAll(c *gin.Context) {
	// 创建Redis缓存服务
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	redisClient := createRedisClient()
	defer redisClient.Close()

	redisService := myredis.NewRedisServiceImpl(logger, redisClient)
	redisCacheService := impl.NewRedisCacheService(logger, redisClient, redisService)

	// 清空所有缓存
	err := redisCacheService.ClearCacheAll()

	// 返回结果
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code": 500,
			"msg":  "操作失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "操作成功",
	})
}
