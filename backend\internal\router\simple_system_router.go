package router

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/middleware"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RegisterSimpleSystemRoutes 注册简化的系统路由
func RegisterSimpleSystemRoutes(r *gin.RouterGroup, logger *zap.Logger, db *gorm.DB) {
	// 无需认证的路由组
	publicGroup := r.Group("")
	{
		// 登录接口
		publicGroup.POST("/login", func(c *gin.Context) {
			var loginRequest struct {
				Username string `json:"username" binding:"required"`
				Password string `json:"password" binding:"required"`
				Code     string `json:"code"`
				UUID     string `json:"uuid"`
			}

			if err := c.ShouldBindJSON(&loginRequest); err != nil {
				c.<PERSON>(http.StatusBadRequest, gin.H{
					"code": 400,
					"msg":  "参数错误",
				})
				return
			}

			// 简单的用户名密码验证
			if loginRequest.Username == "admin" && loginRequest.Password == "admin123" {
				// 生成JWT令牌
				token, err := middleware.GenerateToken(1, "admin")
				if err != nil {
					logger.Error("生成JWT令牌失败", zap.Error(err))
					c.JSON(http.StatusInternalServerError, gin.H{
						"code": 500,
						"msg":  "生成令牌失败",
					})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"code": 200,
					"msg":  "登录成功",
					"data": gin.H{
						"token": token,
					},
				})
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{
					"code": 401,
					"msg":  "用户名或密码错误",
				})
			}
		})

		// 验证码接口
		publicGroup.GET("/captchaImage", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"uuid": "captcha-uuid-12345",
					"img":  "data:image/png;base64,demo-captcha-image",
				},
			})
		})
	}

	// 需要认证的路由组
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuth())
	{
		// 获取用户信息
		authGroup.GET("/getInfo", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"user": gin.H{
						"userId":    1,
						"userName":  "管理员",
						"loginName": "admin",
						"email":     "<EMAIL>",
						"status":    "0",
					},
					"roles":       []string{"admin"},
					"permissions": []string{"*:*:*"},
				},
			})
		})

		// 获取路由信息
		authGroup.GET("/getRouters", func(c *gin.Context) {
			menus := []gin.H{
				{
					"name":      "System",
					"path":      "/system",
					"component": "Layout",
					"meta":      gin.H{"title": "系统管理", "icon": "system"},
					"children": []gin.H{
						{
							"name":      "User",
							"path":      "/system/user",
							"component": "system/user/index",
							"meta":      gin.H{"title": "用户管理", "icon": "user"},
						},
						{
							"name":      "Role",
							"path":      "/system/role",
							"component": "system/role/index",
							"meta":      gin.H{"title": "角色管理", "icon": "peoples"},
						},
						{
							"name":      "Menu",
							"path":      "/system/menu",
							"component": "system/menu/index",
							"meta":      gin.H{"title": "菜单管理", "icon": "tree-table"},
						},
					},
				},
			}
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": menus,
			})
		})

		// 系统管理路由组
		systemGroup := authGroup.Group("/system")
		{
			// 用户管理
			userGroup := systemGroup.Group("/user")
			{
				userGroup.GET("/list", func(c *gin.Context) {
					// 检查数据库连接
					if db == nil {
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "数据库连接失败",
						})
						return
					}

					// 查询用户列表
					var users []domain.SysUser
					var total int64

					// 构建查询条件
					query := db.Model(&domain.SysUser{}).Where("del_flag = ?", "0")

					// 获取总数
					if err := query.Count(&total).Error; err != nil {
						logger.Error("查询用户总数失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "查询用户总数失败",
						})
						return
					}

					// 分页查询
					pageNum := 1
					pageSize := 10
					if page := c.Query("pageNum"); page != "" {
						if p, err := strconv.Atoi(page); err == nil && p > 0 {
							pageNum = p
						}
					}
					if size := c.Query("pageSize"); size != "" {
						if s, err := strconv.Atoi(size); err == nil && s > 0 {
							pageSize = s
						}
					}

					offset := (pageNum - 1) * pageSize
					if err := query.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
						logger.Error("查询用户列表失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "查询用户列表失败",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"rows":  users,
							"total": total,
						},
					})
				})

				userGroup.GET("/:userId", func(c *gin.Context) {
					userId := c.Param("userId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"data": gin.H{
								"userId":    userId,
								"userName":  "管理员",
								"loginName": "admin",
								"email":     "<EMAIL>",
								"status":    "0",
							},
							"roles": []gin.H{
								{"roleId": 1, "roleName": "超级管理员", "roleKey": "admin"},
							},
							"posts": []gin.H{
								{"postId": 1, "postName": "董事长", "postCode": "ceo"},
							},
						},
					})
				})

				userGroup.POST("", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "新增成功",
					})
				})

				userGroup.PUT("", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "修改成功",
					})
				})

				userGroup.DELETE("/:userIds", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "删除成功",
					})
				})
			}

			// 角色管理
			roleGroup := systemGroup.Group("/role")
			{
				roleGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"rows": []gin.H{
								{
									"roleId":   1,
									"roleName": "超级管理员",
									"roleKey":  "admin",
									"status":   "0",
								},
								{
									"roleId":   2,
									"roleName": "普通角色",
									"roleKey":  "common",
									"status":   "0",
								},
							},
							"total": 2,
						},
					})
				})
			}

			// 菜单管理
			menuGroup := systemGroup.Group("/menu")
			{
				menuGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": []gin.H{
							{
								"menuId":   1,
								"menuName": "系统管理",
								"parentId": 0,
								"orderNum": 1,
								"path":     "/system",
								"menuType": "M",
								"visible":  "0",
								"status":   "0",
							},
							{
								"menuId":   2,
								"menuName": "用户管理",
								"parentId": 1,
								"orderNum": 1,
								"path":     "/system/user",
								"menuType": "C",
								"visible":  "0",
								"status":   "0",
							},
						},
					})
				})
			}
		}
	}
}
