package router

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/middleware"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service/impl"
	"github.com/ruoyi/backend/internal/system"
	"go.uber.org/zap"
)

// RegisterSystemRoutes 注册系统路由
func RegisterSystemRoutes(r *gin.RouterGroup, logger *zap.Logger) {
	// 创建服务实例
	jwtConfig := &config.JWTConfig{}          // 应该从配置中获取
	redisService := &redis.RedisServiceImpl{} // 应该从依赖注入获取
	tokenService := impl.NewTokenServiceImpl(logger, jwtConfig, redisService)
	tokenManager := auth.NewTokenManager(logger, jwtConfig, redisService)
	permissionChecker := auth.NewPermissionChecker(logger, tokenManager)

	loginService := impl.NewSysLoginServiceImpl(logger)
	userService := impl.NewSysUserServiceImpl(logger)
	roleService := impl.NewSysRoleServiceImpl(logger)
	menuService := impl.NewSysMenuServiceImpl(logger)
	deptService := impl.NewSysDeptServiceImpl(logger)
	postService := impl.NewSysPostServiceImpl(logger)
	dictTypeService := impl.NewSysDictTypeServiceImpl(logger)
	dictDataService := impl.NewSysDictDataServiceImpl(logger)
	configService := impl.NewSysConfigServiceImpl(logger)
	noticeService := impl.NewSysNoticeServiceImpl(logger)

	// 创建控制器实例
	loginController := system.NewSysLoginController(logger, loginService, tokenService)
	registerController := system.NewSysRegisterController(logger)
	userController := system.NewSysUserController(logger, userService)
	roleController := system.NewSysRoleController(logger, roleService)
	menuController := system.NewSysMenuController(logger, menuService)
	deptController := system.NewSysDeptController(logger, deptService)
	postController := system.NewSysPostController(logger, postService)
	dictTypeController := system.NewSysDictTypeController(logger, dictTypeService)
	dictDataController := system.NewSysDictDataController(logger, dictDataService)
	configController := system.NewSysConfigController(logger, configService)
	noticeController := system.NewSysNoticeController(logger, noticeService)
	profileController := system.NewSysProfileController(logger, userService)
	indexController := system.NewSysIndexController(logger)

	// 无需认证的路由组
	publicGroup := r.Group("")
	{
		// 登录相关
		loginController.RegisterRoutes(publicGroup)
		// 注册相关
		registerController.RegisterRoutes(publicGroup)
		// 获取验证码
		publicGroup.GET("/captchaImage", loginController.GetCaptchaImage)
	}

	// 需要认证的路由组
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuthMiddleware(logger, tokenManager))
	{
		// 用户管理
		userGroup := authGroup.Group("/system/user")
		userGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		userController.RegisterRoutes(userGroup)

		// 角色管理
		roleGroup := authGroup.Group("/system/role")
		roleGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		roleController.RegisterRoutes(roleGroup)

		// 菜单管理
		menuGroup := authGroup.Group("/system/menu")
		menuGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		menuController.RegisterRoutes(menuGroup)

		// 部门管理
		deptGroup := authGroup.Group("/system/dept")
		deptGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		deptController.RegisterRoutes(deptGroup)

		// 岗位管理
		postGroup := authGroup.Group("/system/post")
		postGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		postController.RegisterRoutes(postGroup)

		// 字典类型管理
		dictTypeGroup := authGroup.Group("/system/dict/type")
		dictTypeGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		dictTypeController.RegisterRoutes(dictTypeGroup)

		// 字典数据管理
		dictDataGroup := authGroup.Group("/system/dict/data")
		dictDataGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		dictDataController.RegisterRoutes(dictDataGroup)

		// 参数配置管理
		configGroup := authGroup.Group("/system/config")
		configGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		configController.RegisterRoutes(configGroup)

		// 通知公告管理
		noticeGroup := authGroup.Group("/system/notice")
		noticeGroup.Use(middleware.PermissionMiddleware(logger, permissionChecker))
		noticeController.RegisterRoutes(noticeGroup)

		// 个人中心
		profileGroup := authGroup.Group("/system/user/profile")
		profileController.RegisterRoutes(profileGroup)

		// 首页
		indexGroup := authGroup.Group("/")
		indexController.RegisterRoutes(indexGroup)
	}
}
