package service

// GenTableColumnServiceImpl Service implementation
type GenTableColumnServiceImpl struct {
	// TODO: Add dependencies
}

// NewGenTableColumnServiceImpl Create service instance
func NewGenTableColumnServiceImpl() *GenTableColumnServiceImpl {
	return &%!s(MISSING){}
}

// SelectGenTableColumnListByTableId Implement GenTableColumnService interface
func (s *GenTableColumnServiceImpl) SelectGenTableColumnListByTableId(tableId int64) []GenTableColumn {
	// TODO: Implement method logic
	return nil
}

// InsertGenTableColumn Implement GenTableColumnService interface
func (s *GenTableColumnServiceImpl) InsertGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method logic
	return 0
}

// UpdateGenTableColumn Implement GenTableColumnService interface
func (s *GenTableColumnServiceImpl) UpdateGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method logic
	return 0
}

// DeleteGenTableColumnByIds Implement GenTableColumnService interface
func (s *GenTableColumnServiceImpl) DeleteGenTableColumnByIds(ids string) int {
	// TODO: Implement method logic
	return 0
}
