package service

// GenTableServiceImpl Service implementation
type GenTableServiceImpl struct {
	// TODO: Add dependencies
}

// NewGenTableServiceImpl Create service instance
func NewGenTableServiceImpl() *GenTableServiceImpl {
	return &%!s(MISSING){}
}

// SelectGenTableById Implement GenTableService interface
func (s *GenTableServiceImpl) SelectGenTableById(id int64) GenTable {
	// TODO: Implement method logic
	return nil
}

// SelectGenTableList Implement GenTableService interface
func (s *GenTableServiceImpl) SelectGenTableList(genTable GenTable) []GenTable {
	// TODO: Implement method logic
	return nil
}

// SelectDbTableList Implement GenTableService interface
func (s *GenTableServiceImpl) SelectDbTableList(genTable GenTable) []GenTable {
	// TODO: Implement method logic
	return nil
}

// SelectDbTableListByNames Implement GenTableService interface
func (s *GenTableServiceImpl) SelectDbTableListByNames(tableNames []string) []GenTable {
	// TODO: Implement method logic
	return nil
}

// SelectGenTableAll Implement GenTableService interface
func (s *GenTableServiceImpl) SelectGenTableAll() []GenTable {
	// TODO: Implement method logic
	return nil
}

// UpdateGenTable Implement GenTableService interface
func (s *GenTableServiceImpl) UpdateGenTable(genTable GenTable) {
	// TODO: Implement method logic
}

// DeleteGenTableByIds Implement GenTableService interface
func (s *GenTableServiceImpl) DeleteGenTableByIds(tableIds []int64) {
	// TODO: Implement method logic
}

// CreateTable Implement GenTableService interface
func (s *GenTableServiceImpl) CreateTable(sql string) boolean {
	// TODO: Implement method logic
	return nil
}

// ImportGenTable Implement GenTableService interface
func (s *GenTableServiceImpl) ImportGenTable(tableList []GenTable, operName string) {
	// TODO: Implement method logic
}

// String> previewCode Implement GenTableService interface
func (s *GenTableServiceImpl) String> previewCode(tableId int64) map[string]interface{} {
	// TODO: Implement method logic
	return nil
}

// DownloadCode Implement GenTableService interface
func (s *GenTableServiceImpl) DownloadCode(tableName string) []byte {
	// TODO: Implement method logic
	return nil
}

// GeneratorCode Implement GenTableService interface
func (s *GenTableServiceImpl) GeneratorCode(tableName string) {
	// TODO: Implement method logic
}

// SynchDb Implement GenTableService interface
func (s *GenTableServiceImpl) SynchDb(tableName string) {
	// TODO: Implement method logic
}

// DownloadCode Implement GenTableService interface
func (s *GenTableServiceImpl) DownloadCode(tableNames []string) []byte {
	// TODO: Implement method logic
	return nil
}

// GeneratorCode Implement GenTableService interface
func (s *GenTableServiceImpl) GeneratorCode(tableName string, zip ZipOutputStream) {
	// TODO: Implement method logic
}

// ValidateEdit Implement GenTableService interface
func (s *GenTableServiceImpl) ValidateEdit(genTable GenTable) {
	// TODO: Implement method logic
}

// SetPkColumn Implement GenTableService interface
func (s *GenTableServiceImpl) SetPkColumn(table GenTable) {
	// TODO: Implement method logic
}

// SetSubTable Implement GenTableService interface
func (s *GenTableServiceImpl) SetSubTable(table GenTable) {
	// TODO: Implement method logic
}

// SetTableFromOptions Implement GenTableService interface
func (s *GenTableServiceImpl) SetTableFromOptions(genTable GenTable) {
	// TODO: Implement method logic
}

// GetGenPath Implement GenTableService interface
func (s *GenTableServiceImpl) GetGenPath(table GenTable, template string) string {
	// TODO: Implement method logic
	return ""
}
