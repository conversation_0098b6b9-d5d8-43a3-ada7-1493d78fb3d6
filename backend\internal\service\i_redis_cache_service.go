package service

// IRedisCacheService Redis缓存服务接口
type IRedisCacheService interface {
	// GetCacheInfo 获取缓存信息
	GetCacheInfo() map[string]interface{}

	// GetCacheNames 获取缓存名称列表
	GetCacheNames() []map[string]string

	// GetCacheKeys 获取缓存键列表
	GetCacheKeys(cacheName string) []string

	// GetCacheValue 获取缓存值
	GetCacheValue(cacheName string, cacheKey string) map[string]string

	// ClearCacheName 清空缓存名称下的所有键
	ClearCacheName(cacheName string) error

	// ClearCacheKey 清除指定缓存键
	ClearCacheKey(cacheKey string) error

	// ClearCacheAll 清空所有缓存
	ClearCacheAll() error
}
