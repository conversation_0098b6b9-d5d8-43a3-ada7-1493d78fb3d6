package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysConfigService 参数配置服务接口
type ISysConfigService interface {
	// SelectConfigById 查询参数配置信息
	SelectConfigById(configId int64) domain.SysConfig

	// SelectConfigByKey 根据键名查询参数配置信息
	SelectConfigByKey(configKey string) domain.SysConfig

	// SelectCaptchaEnabled 获取验证码开关
	SelectCaptchaEnabled() bool

	// SelectConfigList 查询参数配置列表
	SelectConfigList(config domain.SysConfig) []domain.SysConfig

	// InsertConfig 新增参数配置
	InsertConfig(config domain.SysConfig) int

	// UpdateConfig 修改参数配置
	UpdateConfig(config domain.SysConfig) int

	// DeleteConfigByIds 批量删除参数信息
	DeleteConfigByIds(configIds []int64) int

	// LoadingConfigCache 加载参数缓存数据
	LoadingConfigCache()

	// ClearConfigCache 清空参数缓存数据
	ClearConfigCache()

	// ResetConfigCache 重置参数缓存数据
	ResetConfigCache()

	// CheckConfigKeyUnique 校验参数键名是否唯一
	CheckConfigKeyUnique(config domain.SysConfig) bool
}
