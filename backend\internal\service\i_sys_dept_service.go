package service

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/vo"
)

// ISysDeptService 部门管理服务接口
type ISysDeptService interface {
	// SelectDeptList 查询部门管理数据
	SelectDeptList(dept domain.SysDept) []domain.SysDept

	// SelectDeptTreeList 查询部门树结构信息
	SelectDeptTreeList(dept domain.SysDept) []vo.TreeSelect

	// BuildDeptTree 构建前端所需要树结构
	BuildDeptTree(depts []domain.SysDept) []domain.SysDept

	// BuildDeptTreeSelect 构建前端所需要下拉树结构
	BuildDeptTreeSelect(depts []domain.SysDept) []vo.TreeSelect

	// SelectDeptListByRoleId 根据角色ID查询部门树信息
	SelectDeptListByRoleId(roleId int64) []int64

	// SelectDeptById 根据部门ID查询信息
	SelectDeptById(deptId int64) domain.SysDept

	// SelectDeptByName 根据部门名称查询信息
	SelectDeptByName(deptName string) domain.SysDept

	// SelectNormalChildrenDeptById 根据ID查询所有子部门（正常状态）
	SelectNormalChildrenDeptById(deptId int64) int

	// HasChildByDeptId 是否存在部门子节点
	HasChildByDeptId(deptId int64) bool

	// CheckDeptExistUser 查询部门是否存在用户
	CheckDeptExistUser(deptId int64) bool

	// CheckDeptNameUnique 校验部门名称是否唯一
	CheckDeptNameUnique(dept domain.SysDept) bool

	// CheckDeptDataScope 校验部门是否有数据权限
	CheckDeptDataScope(deptId int64)

	// InsertDept 新增保存部门信息
	InsertDept(dept domain.SysDept) int

	// UpdateDept 修改保存部门信息
	UpdateDept(dept domain.SysDept) int

	// DeleteDeptById 删除部门管理信息
	DeleteDeptById(deptId int64) int
}
