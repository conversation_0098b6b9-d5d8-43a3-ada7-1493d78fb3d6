package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysDictTypeService 字典类型服务接口
type ISysDictTypeService interface {
	// SelectDictTypeList 根据条件分页查询字典类型
	SelectDictTypeList(dictType domain.SysDictType) []domain.SysDictType

	// SelectDictTypeAll 查询所有字典类型
	SelectDictTypeAll() []domain.SysDictType

	// SelectDictDataByType 根据字典类型查询字典数据
	SelectDictDataByType(dictType string) []domain.SysDictData

	// SelectDictTypeById 根据字典类型ID查询信息
	SelectDictTypeById(dictId int64) domain.SysDictType

	// SelectDictTypeByType 根据字典类型查询信息
	SelectDictTypeByType(dictType string) domain.SysDictType

	// DeleteDictTypeByIds 批量删除字典信息
	DeleteDictTypeByIds(dictIds []int64) int

	// LoadingDictCache 加载字典缓存数据
	LoadingDictCache()

	// ClearDictCache 清空字典缓存数据
	ClearDictCache()

	// ResetDictCache 重置字典缓存数据
	ResetDictCache()

	// InsertDictType 新增保存字典类型信息
	InsertDictType(dictType domain.SysDictType) int

	// UpdateDictType 修改保存字典类型信息
	UpdateDictType(dictType domain.SysDictType) int

	// CheckDictTypeUnique 校验字典类型称是否唯一
	CheckDictTypeUnique(dictType domain.SysDictType) bool
}
