package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysJobLogService 定时任务调度日志服务接口
type ISysJobLogService interface {
	// SelectJobLogList 获取任务调度日志集合
	SelectJobLogList(jobLog domain.SysJobLog) []domain.SysJobLog

	// SelectJobLogById 获取任务调度日志详细信息
	SelectJobLogById(jobLogId int64) domain.SysJobLog

	// AddJobLog 新增任务日志
	AddJobLog(jobLog domain.SysJobLog)

	// DeleteJobLogByIds 批量删除调度日志
	DeleteJobLogByIds(logIds []int64) int

	// DeleteJobLogById 删除调度日志
	DeleteJobLogById(jobId int64) int

	// CleanJobLog 清空任务日志
	CleanJobLog()
}
