package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysJobService 定时任务调度服务接口
type ISysJobService interface {
	// SelectJobList 查询定时任务调度集合
	SelectJobList(job domain.SysJob) []domain.SysJob

	// SelectJobById 查询定时任务调度信息
	SelectJobById(jobId int64) domain.SysJob

	// PauseJob 暂停任务
	PauseJob(job domain.SysJob) int

	// ResumeJob 恢复任务
	ResumeJob(job domain.SysJob) int

	// DeleteJob 删除任务
	DeleteJob(job domain.SysJob) int

	// DeleteJobByIds 批量删除调度信息
	DeleteJobByIds(jobIds []int64)

	// ChangeStatus 任务状态修改
	ChangeStatus(job domain.SysJob) int

	// Run 立即运行任务
	Run(job domain.SysJob) bool

	// InsertJob 新增调度任务
	InsertJob(job domain.SysJob) int

	// UpdateJob 修改调度任务
	UpdateJob(job domain.SysJob) int

	// CheckCronExpressionIsValid 校验cron表达式是否有效
	CheckCronExpressionIsValid(cronExpression string) bool
}
