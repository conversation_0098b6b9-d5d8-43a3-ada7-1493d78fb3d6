package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysLogininforService 系统访问日志情况信息 服务层
type ISysLogininforService interface {
	// InsertLogininfor 新增系统登录日志
	InsertLogininfor(logininfor domain.SysLogininfor)

	// SelectLogininforList 查询系统登录日志集合
	SelectLogininforList(logininfor domain.SysLogininfor) []domain.SysLogininfor

	// DeleteLogininforByIds 批量删除系统登录日志
	DeleteLogininforByIds(infoIds []int64) int

	// CleanLogininfor 清空系统登录日志
	CleanLogininfor()
}
