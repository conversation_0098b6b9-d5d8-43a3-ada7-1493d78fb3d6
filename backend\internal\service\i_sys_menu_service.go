package service

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/vo"
)

// ISysMenuService 菜单服务接口
type ISysMenuService interface {
	// SelectMenuListByUserId 根据用户查询系统菜单列表
	SelectMenuListByUserId(userId int64) []domain.SysMenu

	// SelectMenuList 根据用户查询系统菜单列表
	SelectMenuList(menu domain.SysMenu, userId int64) []domain.SysMenu

	// SelectMenuPermsByUserId 根据用户ID查询权限
	SelectMenuPermsByUserId(userId int64) map[string]struct{}

	// SelectMenuPermsByRoleId 根据角色ID查询权限
	SelectMenuPermsByRoleId(roleId int64) map[string]struct{}

	// SelectMenuTreeByUserId 根据用户ID查询菜单树信息
	SelectMenuTreeByUserId(userId int64) []domain.SysMenu

	// SelectMenuListByRoleId 根据角色ID查询菜单树信息
	SelectMenuListByRoleId(roleId int64) []int64

	// BuildMenus 构建前端路由所需要的菜单
	BuildMenus(menus []domain.SysMenu) []vo.RouterVo

	// BuildMenuTree 构建前端所需要树结构
	BuildMenuTree(menus []domain.SysMenu) []domain.SysMenu

	// BuildMenuTreeSelect 构建前端所需要下拉树结构
	BuildMenuTreeSelect(menus []domain.SysMenu) []vo.TreeSelect

	// SelectMenuById 根据菜单ID查询信息
	SelectMenuById(menuId int64) domain.SysMenu

	// HasChildByMenuId 是否存在菜单子节点
	HasChildByMenuId(menuId int64) bool

	// CheckMenuExistRole 查询菜单是否存在角色
	CheckMenuExistRole(menuId int64) bool

	// InsertMenu 新增保存菜单信息
	InsertMenu(menu domain.SysMenu) int

	// UpdateMenu 修改保存菜单信息
	UpdateMenu(menu domain.SysMenu) int

	// DeleteMenuById 删除菜单管理信息
	DeleteMenuById(menuId int64) int

	// CheckMenuNameUnique 校验菜单名称是否唯一
	CheckMenuNameUnique(menu domain.SysMenu) bool
}
