package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysNoticeService 公告服务接口
type ISysNoticeService interface {
	// SelectNoticeById 查询公告信息
	SelectNoticeById(noticeId int64) domain.SysNotice

	// SelectNoticeList 查询公告列表
	SelectNoticeList(notice domain.SysNotice) []domain.SysNotice

	// InsertNotice 新增公告
	InsertNotice(notice domain.SysNotice) int

	// UpdateNotice 修改公告
	UpdateNotice(notice domain.SysNotice) int

	// DeleteNoticeById 删除公告信息
	DeleteNoticeById(noticeId int64) int

	// DeleteNoticeByIds 批量删除公告信息
	DeleteNoticeByIds(noticeIds []int64) int
}
