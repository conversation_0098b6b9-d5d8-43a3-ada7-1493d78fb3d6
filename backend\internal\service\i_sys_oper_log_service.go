package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysOperLogService 操作日志 服务层
type ISysOperLogService interface {
	// InsertOperlog 新增操作日志
	InsertOperlog(operLog domain.SysOperLog)

	// SelectOperLogList 查询系统操作日志集合
	SelectOperLogList(operLog domain.SysOperLog) []domain.SysOperLog

	// DeleteOperLogByIds 批量删除系统操作日志
	DeleteOperLogByIds(operIds []int64) int

	// SelectOperLogById 查询操作日志详细
	SelectOperLogById(operId int64) domain.SysOperLog

	// CleanOperLog 清空操作日志
	CleanOperLog()
}
