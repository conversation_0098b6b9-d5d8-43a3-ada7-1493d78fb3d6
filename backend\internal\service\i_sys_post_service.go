package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysPostService 岗位信息服务接口
type ISysPostService interface {
	// SelectPostList 查询岗位信息集合
	SelectPostList(post domain.SysPost) []domain.SysPost

	// SelectPostAll 查询所有岗位
	SelectPostAll() []domain.SysPost

	// SelectPostById 通过岗位ID查询岗位信息
	SelectPostById(postId int64) domain.SysPost

	// SelectPostListByUserId 根据用户ID获取岗位选择框列表
	SelectPostListByUserId(userId int64) []int64

	// CheckPostNameUnique 校验岗位名称
	CheckPostNameUnique(post domain.SysPost) bool

	// CheckPostCodeUnique 校验岗位编码
	CheckPostCodeUnique(post domain.SysPost) bool

	// CountUserPostById 通过岗位ID查询岗位使用数量
	CountUserPostById(postId int64) int

	// DeletePostById 删除岗位信息
	DeletePostById(postId int64) int

	// DeletePostByIds 批量删除岗位信息
	DeletePostByIds(postIds []int64) int

	// InsertPost 新增保存岗位信息
	InsertPost(post domain.SysPost) int

	// UpdatePost 修改保存岗位信息
	UpdatePost(post domain.SysPost) int
}
