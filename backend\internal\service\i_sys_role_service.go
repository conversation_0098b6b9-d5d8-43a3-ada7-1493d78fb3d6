package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysRoleService 角色服务接口
type ISysRoleService interface {
	// SelectRoleList 根据条件分页查询角色数据
	SelectRoleList(role domain.SysRole) []domain.SysRole

	// SelectRolesByUserId 根据用户ID查询角色列表
	SelectRolesByUserId(userId int64) []domain.SysRole

	// SelectRolePermissionByUserId 根据用户ID查询角色权限
	SelectRolePermissionByUserId(userId int64) map[string]struct{}

	// SelectRoleAll 查询所有角色
	SelectRoleAll() []domain.SysRole

	// SelectRoleListByUserId 根据用户ID获取角色选择框列表
	SelectRoleListByUserId(userId int64) []int64

	// SelectRoleById 通过角色ID查询角色
	SelectRoleById(roleId int64) domain.SysRole

	// CheckRoleNameUnique 校验角色名称是否唯一
	CheckRoleNameUnique(role domain.SysRole) bool

	// CheckRoleKeyUnique 校验角色权限是否唯一
	CheckRoleKeyUnique(role domain.SysRole) bool

	// CheckRoleAllowed 校验角色是否允许操作
	CheckRoleAllowed(role domain.SysRole)

	// CheckRoleDataScope 校验角色是否有数据权限
	CheckRoleDataScope(roleIds ...int64)

	// CountUserRoleByRoleId 通过角色ID查询角色使用数量
	CountUserRoleByRoleId(roleId int64) int

	// InsertRole 新增保存角色信息
	InsertRole(role domain.SysRole) int

	// UpdateRole 修改保存角色信息
	UpdateRole(role domain.SysRole) int

	// UpdateRoleStatus 修改角色状态
	UpdateRoleStatus(role domain.SysRole) int

	// AuthDataScope 修改数据权限信息
	AuthDataScope(role domain.SysRole) int

	// DeleteRoleById 通过角色ID删除角色
	DeleteRoleById(roleId int64) int

	// DeleteRoleByIds 批量删除角色信息
	DeleteRoleByIds(roleIds []int64) int

	// DeleteAuthUser 取消授权用户角色
	DeleteAuthUser(userRole domain.SysUserRole) int

	// DeleteAuthUsers 批量取消授权用户角色
	DeleteAuthUsers(roleId int64, userIds []int64) int

	// InsertAuthUsers 批量选择授权用户角色
	InsertAuthUsers(roleId int64, userIds []int64) int
}
