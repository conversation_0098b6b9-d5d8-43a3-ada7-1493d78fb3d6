package service

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
)

// ISysUserOnlineService 在线用户 服务层
type ISysUserOnlineService interface {
	// SelectOnlineByIpaddr 通过登录地址查询信息
	SelectOnlineByIpaddr(ipaddr string, user model.LoginUser) domain.SysUserOnline

	// SelectOnlineByUserName 通过用户名称查询信息
	SelectOnlineByUserName(userName string, user model.LoginUser) domain.SysUserOnline

	// SelectOnlineByInfo 通过登录地址/用户名称查询信息
	SelectOnlineByInfo(ipaddr string, userName string, user model.LoginUser) domain.SysUserOnline

	// LoginUserToUserOnline 登录用户转在线用户
	LoginUserToUserOnline(user model.LoginUser) domain.SysUserOnline
}
