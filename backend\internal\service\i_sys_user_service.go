package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysUserService 用户服务接口
type ISysUserService interface {
	// SelectUserList 根据条件分页查询用户列表
	SelectUserList(user domain.SysUser) []domain.SysUser

	// SelectAllocatedList 根据条件分页查询已分配用户角色列表
	SelectAllocatedList(user domain.SysUser) []domain.SysUser

	// SelectUnallocatedList 根据条件分页查询未分配用户角色列表
	SelectUnallocatedList(user domain.SysUser) []domain.SysUser

	// SelectUserByUserName 通过用户名查询用户
	SelectUserByUserName(userName string) domain.SysUser

	// SelectUserById 通过用户ID查询用户
	SelectUserById(userId int64) domain.SysUser

	// SelectUserRoleGroup 根据用户ID查询用户所属角色组
	SelectUserRoleGroup(userName string) string

	// SelectUserPostGroup 根据用户ID查询用户所属岗位组
	SelectUserPostGroup(userName string) string

	// CheckUserNameUnique 校验用户名称是否唯一
	CheckUserNameUnique(user domain.SysUser) bool

	// CheckPhoneUnique 校验手机号码是否唯一
	CheckPhoneUnique(user domain.SysUser) bool

	// CheckEmailUnique 校验email是否唯一
	CheckEmailUnique(user domain.SysUser) bool

	// CheckUserAllowed 校验用户是否允许操作
	CheckUserAllowed(user domain.SysUser)

	// CheckUserDataScope 校验用户是否有数据权限
	CheckUserDataScope(userId int64)

	// InsertUser 新增用户信息
	InsertUser(user domain.SysUser) int

	// RegisterUser 注册用户信息
	RegisterUser(user domain.SysUser) bool

	// UpdateUser 修改用户信息
	UpdateUser(user domain.SysUser) int

	// InsertUserAuth 用户授权角色
	InsertUserAuth(userId int64, roleIds []int64)

	// UpdateUserStatus 修改用户状态
	UpdateUserStatus(user domain.SysUser) int

	// UpdateUserProfile 修改用户基本信息
	UpdateUserProfile(user domain.SysUser) int

	// UpdateUserAvatar 修改用户头像
	UpdateUserAvatar(userId int64, avatar string) bool

	// ResetPwd 重置用户密码
	ResetPwd(user domain.SysUser) int

	// ResetUserPwd 重置用户密码
	ResetUserPwd(userId int64, password string) int

	// DeleteUserById 通过用户ID删除用户
	DeleteUserById(userId int64) int

	// DeleteUserByIds 批量删除用户信息
	DeleteUserByIds(userIds []int64) int

	// ImportUser 导入用户数据
	ImportUser(userList []domain.SysUser, isUpdateSupport bool, operName string) string
}
