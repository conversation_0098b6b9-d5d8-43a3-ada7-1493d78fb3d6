package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysDictDataServiceImpl 字典数据服务实现
type SysDictDataServiceImpl struct {
	logger       *zap.Logger
	dictDataRepo repository.DictDataRepository
}

// NewSysDictDataServiceImpl 创建字典数据服务实现
func NewSysDictDataServiceImpl(logger *zap.Logger, dictDataRepo repository.DictDataRepository) service.ISysDictDataService {
	return &SysDictDataServiceImpl{
		logger:       logger,
		dictDataRepo: dictDataRepo,
	}
}

// SelectDictDataList 根据条件分页查询字典数据
func (s *SysDictDataServiceImpl) SelectDictDataList(dictData domain.SysDictData) []domain.SysDictData {
	dictDataPtr := &domain.SysDictData{
		DictType:  dictData.DictType,
		DictLabel: dictData.DictLabel,
		Status:    dictData.Status,
	}

	dictDataList, err := s.dictDataRepo.SelectDictDataList(dictDataPtr)
	if err != nil {
		s.logger.Error("查询字典数据列表失败", zap.Error(err))
		return []domain.SysDictData{}
	}
	return dictDataList
}

// SelectDictLabel 根据字典类型和字典键值查询字典数据信息
func (s *SysDictDataServiceImpl) SelectDictLabel(dictType string, dictValue string) string {
	label, err := s.dictDataRepo.SelectDictLabel(dictType, dictValue)
	if err != nil {
		s.logger.Error("查询字典标签失败", zap.String("dictType", dictType), zap.String("dictValue", dictValue), zap.Error(err))
		return ""
	}
	return label
}

// SelectDictDataById 根据字典数据ID查询信息
func (s *SysDictDataServiceImpl) SelectDictDataById(dictCode int64) domain.SysDictData {
	dictData, err := s.dictDataRepo.SelectDictDataById(dictCode)
	if err != nil {
		s.logger.Error("查询字典数据失败", zap.Int64("dictCode", dictCode), zap.Error(err))
		return domain.SysDictData{}
	}
	if dictData == nil {
		return domain.SysDictData{}
	}
	return *dictData
}

// DeleteDictDataByIds 批量删除字典数据信息
func (s *SysDictDataServiceImpl) DeleteDictDataByIds(dictCodes []int64) int {
	if len(dictCodes) == 0 {
		return 0
	}

	err := s.dictDataRepo.DeleteDictDataByIds(dictCodes)
	if err != nil {
		s.logger.Error("批量删除字典数据失败", zap.Error(err))
		return 0
	}

	// 返回删除的行数
	return len(dictCodes)
}

// InsertDictData 新增保存字典数据信息
func (s *SysDictDataServiceImpl) InsertDictData(dictData domain.SysDictData) int {
	dictDataPtr := &domain.SysDictData{
		DictSort:  dictData.DictSort,
		DictLabel: dictData.DictLabel,
		DictValue: dictData.DictValue,
		DictType:  dictData.DictType,
		CssClass:  dictData.CssClass,
		ListClass: dictData.ListClass,
		IsDefault: dictData.IsDefault,
		Status:    dictData.Status,
	}

	// 设置BaseEntity字段
	dictDataPtr.CreateBy = dictData.CreateBy
	dictDataPtr.CreateTime = dictData.CreateTime
	dictDataPtr.UpdateBy = dictData.UpdateBy
	dictDataPtr.UpdateTime = dictData.UpdateTime
	dictDataPtr.Remark = dictData.Remark

	err := s.dictDataRepo.InsertDictData(dictDataPtr)
	if err != nil {
		s.logger.Error("新增字典数据失败", zap.Error(err))
		return 0
	}
	return 1
}

// UpdateDictData 修改保存字典数据信息
func (s *SysDictDataServiceImpl) UpdateDictData(dictData domain.SysDictData) int {
	dictDataPtr := &domain.SysDictData{
		DictCode:  dictData.DictCode,
		DictSort:  dictData.DictSort,
		DictLabel: dictData.DictLabel,
		DictValue: dictData.DictValue,
		DictType:  dictData.DictType,
		CssClass:  dictData.CssClass,
		ListClass: dictData.ListClass,
		IsDefault: dictData.IsDefault,
		Status:    dictData.Status,
	}

	// 设置BaseEntity字段
	dictDataPtr.UpdateBy = dictData.UpdateBy
	dictDataPtr.UpdateTime = dictData.UpdateTime
	dictDataPtr.Remark = dictData.Remark

	err := s.dictDataRepo.UpdateDictData(dictDataPtr)
	if err != nil {
		s.logger.Error("修改字典数据失败", zap.Error(err))
		return 0
	}
	return 1
}
