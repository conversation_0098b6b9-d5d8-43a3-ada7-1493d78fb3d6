package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysJobLogServiceImpl 定时任务调度日志服务实现
type SysJobLogServiceImpl struct {
	// 定时任务日志仓储
	JobLogRepository repository.JobLogRepository
}

// NewSysJobLogService 创建定时任务调度日志服务
func NewSysJobLogService(jobLogRepository repository.JobLogRepository) service.ISysJobLogService {
	return &SysJobLogServiceImpl{
		JobLogRepository: jobLogRepository,
	}
}

// SelectJobLogList 获取任务调度日志集合
func (s *SysJobLogServiceImpl) SelectJobLogList(jobLog domain.SysJobLog) []domain.SysJobLog {
	jobLogs, err := s.JobLogRepository.SelectJobLogList(&jobLog)
	if err != nil {
		return []domain.SysJobLog{}
	}
	return jobLogs
}

// SelectJobLogById 获取任务调度日志详细信息
func (s *SysJobLogServiceImpl) SelectJobLogById(jobLogId int64) domain.SysJobLog {
	jobLog, err := s.JobLogRepository.SelectJobLogById(jobLogId)
	if err != nil {
		return domain.SysJobLog{}
	}
	return *jobLog
}

// AddJobLog 新增任务日志
func (s *SysJobLogServiceImpl) AddJobLog(jobLog domain.SysJobLog) {
	s.JobLogRepository.InsertJobLog(&jobLog)
}

// DeleteJobLogByIds 批量删除调度日志
func (s *SysJobLogServiceImpl) DeleteJobLogByIds(logIds []int64) int {
	err := s.JobLogRepository.DeleteJobLogByIds(logIds)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteJobLogById 删除调度日志
func (s *SysJobLogServiceImpl) DeleteJobLogById(jobId int64) int {
	err := s.JobLogRepository.DeleteJobLogById(jobId)
	if err != nil {
		return 0
	}
	return 1
}

// CleanJobLog 清空任务日志
func (s *SysJobLogServiceImpl) CleanJobLog() {
	s.JobLogRepository.CleanJobLog()
}
