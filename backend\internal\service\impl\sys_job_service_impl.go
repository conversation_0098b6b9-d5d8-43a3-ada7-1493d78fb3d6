package impl

import (
	"regexp"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysJobServiceImpl 定时任务调度服务实现
type SysJobServiceImpl struct {
	// 定时任务仓储
	JobRepository repository.JobRepository
}

// NewSysJobService 创建定时任务调度服务
func NewSysJobService(jobRepository repository.JobRepository) service.ISysJobService {
	return &SysJobServiceImpl{
		JobRepository: jobRepository,
	}
}

// SelectJobList 查询定时任务调度集合
func (s *SysJobServiceImpl) SelectJobList(job domain.SysJob) []domain.SysJob {
	jobs, err := s.JobRepository.SelectJobList(&job)
	if err != nil {
		return []domain.SysJob{}
	}
	return jobs
}

// SelectJobById 查询定时任务调度信息
func (s *SysJobServiceImpl) SelectJobById(jobId int64) domain.SysJob {
	job, err := s.JobRepository.SelectJobById(jobId)
	if err != nil {
		return domain.SysJob{}
	}
	return *job
}

// PauseJob 暂停任务
func (s *SysJobServiceImpl) PauseJob(job domain.SysJob) int {
	job.Status = "1"
	err := s.JobRepository.UpdateJob(&job)
	if err != nil {
		return 0
	}
	return 1
}

// ResumeJob 恢复任务
func (s *SysJobServiceImpl) ResumeJob(job domain.SysJob) int {
	job.Status = "0"
	err := s.JobRepository.UpdateJob(&job)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteJob 删除任务
func (s *SysJobServiceImpl) DeleteJob(job domain.SysJob) int {
	err := s.JobRepository.DeleteJobById(job.JobId)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteJobByIds 批量删除调度信息
func (s *SysJobServiceImpl) DeleteJobByIds(jobIds []int64) {
	for _, jobId := range jobIds {
		job, err := s.JobRepository.SelectJobById(jobId)
		if err != nil || job == nil {
			continue
		}
		s.DeleteJob(*job)
	}
}

// ChangeStatus 任务状态修改
func (s *SysJobServiceImpl) ChangeStatus(job domain.SysJob) int {
	newJob, err := s.JobRepository.SelectJobById(job.JobId)
	if err != nil || newJob == nil {
		return 0
	}

	newJob.Status = job.Status
	err = s.JobRepository.UpdateJob(newJob)
	if err != nil {
		return 0
	}
	return 1
}

// Run 立即运行任务
func (s *SysJobServiceImpl) Run(job domain.SysJob) bool {
	// TODO: 实现任务执行逻辑
	return true
}

// InsertJob 新增调度任务
func (s *SysJobServiceImpl) InsertJob(job domain.SysJob) int {
	// 校验cron表达式
	if !s.CheckCronExpressionIsValid(job.CronExpression) {
		return 0
	}

	err := s.JobRepository.InsertJob(&job)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateJob 修改调度任务
func (s *SysJobServiceImpl) UpdateJob(job domain.SysJob) int {
	// 校验cron表达式
	if !s.CheckCronExpressionIsValid(job.CronExpression) {
		return 0
	}

	err := s.JobRepository.UpdateJob(&job)
	if err != nil {
		return 0
	}
	return 1
}

// CheckCronExpressionIsValid 校验cron表达式是否有效
func (s *SysJobServiceImpl) CheckCronExpressionIsValid(cronExpression string) bool {
	// 简单的cron表达式校验，实际项目中可能需要更复杂的校验
	cronPattern := `^(\*|([0-9]|[1-5][0-9])) (\*|([0-9]|[1-5][0-9])) (\*|([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|[12][0-9]|3[01])) (\*|([1-9]|1[0-2])) (\*|([0-6]))$`
	match, _ := regexp.MatchString(cronPattern, cronExpression)
	return match
}
