package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysLogininforServiceImpl 系统访问日志情况信息服务实现
type SysLogininforServiceImpl struct {
	// 系统访问日志仓储
	LogininforRepository repository.LogininforRepository
}

// NewSysLogininforService 创建系统访问日志情况信息服务
func NewSysLogininforService(logininforRepository repository.LogininforRepository) service.ISysLogininforService {
	return &SysLogininforServiceImpl{
		LogininforRepository: logininforRepository,
	}
}

// InsertLogininfor 新增系统登录日志
func (s *SysLogininforServiceImpl) InsertLogininfor(logininfor domain.SysLogininfor) {
	s.LogininforRepository.InsertLogininfor(&logininfor)
}

// SelectLogininforList 查询系统登录日志集合
func (s *SysLogininforServiceImpl) SelectLogininforList(logininfor domain.SysLogininfor) []domain.SysLogininfor {
	logininforList, err := s.LogininforRepository.SelectLogininforList(&logininfor)
	if err != nil {
		return []domain.SysLogininfor{}
	}
	return logininforList
}

// DeleteLogininforByIds 批量删除系统登录日志
func (s *SysLogininforServiceImpl) DeleteLogininforByIds(infoIds []int64) int {
	err := s.LogininforRepository.DeleteLogininforByIds(infoIds)
	if err != nil {
		return 0
	}
	return 1
}

// CleanLogininfor 清空系统登录日志
func (s *SysLogininforServiceImpl) CleanLogininfor() {
	s.LogininforRepository.CleanLogininfor()
}
