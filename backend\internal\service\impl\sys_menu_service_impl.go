package impl

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/vo"
)

// SysMenuServiceImpl 菜单服务实现
type SysMenuServiceImpl struct {
	// 权限字符串前缀
	PREMISSION_STRING string
	// 菜单仓储
	MenuRepository repository.MenuRepository
	// 角色仓储
	RoleRepository repository.RoleRepository
	// 角色菜单仓储
	RoleMenuRepository repository.RoleMenuRepository
}

// NewSysMenuService 创建菜单服务
func NewSysMenuService(menuRepository repository.MenuRepository, roleRepository repository.RoleRepository, roleMenuRepository repository.RoleMenuRepository) service.ISysMenuService {
	return &SysMenuServiceImpl{
		PREMISSION_STRING:  "perms[\"%s\"]",
		MenuRepository:     menuRepository,
		RoleRepository:     roleRepository,
		RoleMenuRepository: roleMenuRepository,
	}
}

// SelectMenuListByUserId 根据用户查询系统菜单列表
func (s *SysMenuServiceImpl) SelectMenuListByUserId(userId int64) []domain.SysMenu {
	menus, err := s.MenuRepository.SelectMenuListByUserId(nil, userId)
	if err != nil {
		return []domain.SysMenu{}
	}
	return menus
}

// SelectMenuList 根据用户查询系统菜单列表
func (s *SysMenuServiceImpl) SelectMenuList(menu domain.SysMenu, userId int64) []domain.SysMenu {
	var menus []domain.SysMenu
	var err error
	
	// 管理员显示所有菜单信息
	if userId == 1 {
		menus, err = s.MenuRepository.SelectMenuList(&menu)
	} else {
		menus, err = s.MenuRepository.SelectMenuListByUserId(&menu, userId)
	}
	
	if err != nil {
		return []domain.SysMenu{}
	}
	return menus
}

// SelectMenuPermsByUserId 根据用户ID查询权限
func (s *SysMenuServiceImpl) SelectMenuPermsByUserId(userId int64) map[string]struct{} {
	perms, err := s.MenuRepository.SelectMenuPermsByUserId(userId)
	if err != nil {
		return make(map[string]struct{})
	}
	
	permsSet := make(map[string]struct{})
	for _, perm := range perms {
		if perm != "" {
			permList := strings.Split(perm, ",")
			for _, p := range permList {
				if p != "" {
					permsSet[p] = struct{}{}
				}
			}
		}
	}
	return permsSet
}

// SelectMenuPermsByRoleId 根据角色ID查询权限
func (s *SysMenuServiceImpl) SelectMenuPermsByRoleId(roleId int64) map[string]struct{} {
	perms, err := s.MenuRepository.SelectMenuPermsByRoleId(roleId)
	if err != nil {
		return make(map[string]struct{})
	}
	
	permsSet := make(map[string]struct{})
	for _, perm := range perms {
		if perm != "" {
			permList := strings.Split(perm, ",")
			for _, p := range permList {
				if p != "" {
					permsSet[p] = struct{}{}
				}
			}
		}
	}
	return permsSet
}

// SelectMenuTreeByUserId 根据用户ID查询菜单树信息
func (s *SysMenuServiceImpl) SelectMenuTreeByUserId(userId int64) []domain.SysMenu {
	menus, err := s.MenuRepository.SelectMenuTreeByUserId(userId)
	if err != nil {
		return []domain.SysMenu{}
	}
	return s.getChildPerms(menus, 0)
}

// SelectMenuListByRoleId 根据角色ID查询菜单树信息
func (s *SysMenuServiceImpl) SelectMenuListByRoleId(roleId int64) []int64 {
	menuIds, err := s.MenuRepository.SelectMenuListByRoleId(roleId, true)
	if err != nil {
		return []int64{}
	}
	return menuIds
}

// BuildMenus 构建前端路由所需要的菜单
func (s *SysMenuServiceImpl) BuildMenus(menus []domain.SysMenu) []vo.RouterVo {
	routers := make([]vo.RouterVo, 0)
	for _, menu := range menus {
		router := vo.RouterVo{}
		router.Hidden = menu.Visible == "1"
		router.Name = s.getRouteName(menu)
		router.Path = s.getRouterPath(menu)
		router.Component = s.getComponent(menu)
		router.Query = menu.Query
		router.Meta = vo.MetaVo{
			Title:   menu.MenuName,
			Icon:    menu.Icon,
			NoCache: menu.IsCache == "1",
		}
		
		// 内链地址
		if s.isInnerLink(menu) {
			router.Meta.Link = menu.Path
			router.Path = "inner-link"
			router.Component = "InnerLink"
			router.Name = "InnerLink_" + strconv.FormatInt(menu.MenuId, 10)
		}
		
		cMenus := s.getChildList(menus, menu)
		if len(cMenus) > 0 && menu.MenuType == "M" {
			router.AlwaysShow = true
			router.Redirect = "noRedirect"
			router.Children = s.buildMenus(cMenus)
		} else if s.isMenuFrame(menu) {
			childrenList := make([]vo.RouterVo, 0)
			children := vo.RouterVo{}
			children.Path = menu.Path
			children.Component = menu.Component
			children.Name = s.getRouteName(menu)
			children.Meta = vo.MetaVo{
				Title:   menu.MenuName,
				Icon:    menu.Icon,
				NoCache: menu.IsCache == "1",
				Link:    s.isInnerLink(menu) ? menu.Path : "",
			}
			children.Query = menu.Query
			childrenList = append(childrenList, children)
			router.Children = childrenList
		} else if menu.ParentId == 0 && s.isInnerLink(menu) {
			router.Meta = vo.MetaVo{
				Title: menu.MenuName,
				Icon:  menu.Icon,
			}
			router.Path = "inner-link"
			router.Component = "InnerLink"
			router.Name = "InnerLink_" + strconv.FormatInt(menu.MenuId, 10)
			router.Query = menu.Query
			router.Meta.Link = menu.Path
		} else if menu.ParentId == 0 && s.isParentView(menu) {
			// 一级目录
			childrenList := make([]vo.RouterVo, 0)
			children := vo.RouterVo{}
			children.Path = menu.Path
			children.Component = menu.Component
			children.Name = s.getRouteName(menu)
			children.Meta = vo.MetaVo{
				Title:   menu.MenuName,
				Icon:    menu.Icon,
				NoCache: menu.IsCache == "1",
			}
			children.Query = menu.Query
			childrenList = append(childrenList, children)
			router.Children = childrenList
		}
		
		routers = append(routers, router)
	}
	return routers
}

// BuildMenuTree 构建前端所需要树结构
func (s *SysMenuServiceImpl) BuildMenuTree(menus []domain.SysMenu) []domain.SysMenu {
	return s.buildMenuTree(menus, 0)
}

// BuildMenuTreeSelect 构建前端所需要下拉树结构
func (s *SysMenuServiceImpl) BuildMenuTreeSelect(menus []domain.SysMenu) []vo.TreeSelect {
	menuTrees := s.BuildMenuTree(menus)
	return s.buildMenuTreeSelect(menuTrees)
}

// SelectMenuById 根据菜单ID查询信息
func (s *SysMenuServiceImpl) SelectMenuById(menuId int64) domain.SysMenu {
	menu, err := s.MenuRepository.SelectMenuById(menuId)
	if err != nil {
		return domain.SysMenu{}
	}
	return *menu
}

// HasChildByMenuId 是否存在菜单子节点
func (s *SysMenuServiceImpl) HasChildByMenuId(menuId int64) bool {
	hasChild, err := s.MenuRepository.HasChildByMenuId(menuId)
	if err != nil {
		return false
	}
	return hasChild
}

// CheckMenuExistRole 查询菜单是否存在角色
func (s *SysMenuServiceImpl) CheckMenuExistRole(menuId int64) bool {
	// TODO: 实现查询菜单是否存在角色
	return false
}

// InsertMenu 新增保存菜单信息
func (s *SysMenuServiceImpl) InsertMenu(menu domain.SysMenu) int {
	err := s.MenuRepository.InsertMenu(&menu)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateMenu 修改保存菜单信息
func (s *SysMenuServiceImpl) UpdateMenu(menu domain.SysMenu) int {
	err := s.MenuRepository.UpdateMenu(&menu)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteMenuById 删除菜单管理信息
func (s *SysMenuServiceImpl) DeleteMenuById(menuId int64) int {
	err := s.MenuRepository.DeleteMenuById(menuId)
	if err != nil {
		return 0
	}
	return 1
}

// CheckMenuNameUnique 校验菜单名称是否唯一
func (s *SysMenuServiceImpl) CheckMenuNameUnique(menu domain.SysMenu) bool {
	existMenu, err := s.MenuRepository.CheckMenuNameUnique(menu.MenuName, menu.ParentId)
	if err != nil {
		return false
	}
	
	if existMenu != nil && existMenu.MenuId != menu.MenuId {
		return false
	}
	return true
}

// getRouteName 获取路由名称
func (s *SysMenuServiceImpl) getRouteName(menu domain.SysMenu) string {
	routerName := menu.RouteName
	if routerName == "" {
		routerName = s.getRouteNameByPath(menu.Path, menu.MenuName)
	}
	return routerName
}

// getRouteNameByPath 获取路由名称
func (s *SysMenuServiceImpl) getRouteNameByPath(path string, menuName string) string {
	if path != "" && menuName != "" {
		return strings.ReplaceAll(path, "/", "_")
	}
	return "no_name"
}

// getRouterPath 获取路由地址
func (s *SysMenuServiceImpl) getRouterPath(menu domain.SysMenu) string {
	routerPath := menu.Path
	// 非外链并且是一级目录（类型为目录）
	if menu.ParentId == 0 && menu.MenuType == "M" && menu.IsFrame == "1" {
		routerPath = "/" + menu.Path
	} else if s.isMenuFrame(menu) {
		// 非外链并且是一级目录（类型为菜单）
		routerPath = "/"
	}
	return routerPath
}

// getComponent 获取组件信息
func (s *SysMenuServiceImpl) getComponent(menu domain.SysMenu) string {
	component := menu.Component
	if component == "" {
		if menu.ParentId == 0 && s.isInnerLink(menu) {
			component = "InnerLink"
		} else if menu.ParentId == 0 && !s.isInnerLink(menu) {
			component = "Layout"
		} else if menu.MenuType == "M" {
			component = "ParentView"
		} else {
			component = "InnerLink"
		}
	}
	return component
}

// isMenuFrame 是否为菜单内部跳转
func (s *SysMenuServiceImpl) isMenuFrame(menu domain.SysMenu) bool {
	return menu.ParentId == 0 && menu.MenuType == "C" && menu.IsFrame == "1"
}

// isInnerLink 是否为内链组件
func (s *SysMenuServiceImpl) isInnerLink(menu domain.SysMenu) bool {
	return menu.IsFrame == "0" && strings.HasPrefix(menu.Path, "http")
}

// isParentView 是否为parent_view组件
func (s *SysMenuServiceImpl) isParentView(menu domain.SysMenu) bool {
	return menu.ParentId != 0 && menu.MenuType == "M"
}

// getChildPerms 根据父节点的ID获取所有子节点
func (s *SysMenuServiceImpl) getChildPerms(menus []domain.SysMenu, parentId int64) []domain.SysMenu {
	childList := make([]domain.SysMenu, 0)
	for _, menu := range menus {
		if menu.ParentId == parentId {
			s.recursionFn(menus, &menu)
			childList = append(childList, menu)
		}
	}
	return childList
}

// recursionFn 递归列表
func (s *SysMenuServiceImpl) recursionFn(list []domain.SysMenu, menu *domain.SysMenu) {
	// 得到子节点列表
	childList := s.getChildList(list, *menu)
	menu.Children = childList
	for i := 0; i < len(childList); i++ {
		child := childList[i]
		// 判断是否有子节点
		if s.hasChild(list, child) {
			s.recursionFn(list, &childList[i])
		}
	}
}

// getChildList 得到子节点列表
func (s *SysMenuServiceImpl) getChildList(list []domain.SysMenu, menu domain.SysMenu) []domain.SysMenu {
	childList := make([]domain.SysMenu, 0)
	for _, child := range list {
		if child.ParentId == menu.MenuId {
			childList = append(childList, child)
		}
	}
	return childList
}

// hasChild 判断是否有子节点
func (s *SysMenuServiceImpl) hasChild(list []domain.SysMenu, menu domain.SysMenu) bool {
	return len(s.getChildList(list, menu)) > 0
}

// innerLinkReplaceEach 内链域名特殊字符替换
func (s *SysMenuServiceImpl) innerLinkReplaceEach(path string) string {
	return path
}

// buildMenus 构建路由
func (s *SysMenuServiceImpl) buildMenus(menus []domain.SysMenu) []vo.RouterVo {
	routers := make([]vo.RouterVo, 0)
	for _, menu := range menus {
		router := vo.RouterVo{}
		router.Hidden = menu.Visible == "1"
		router.Name = s.getRouteName(menu)
		router.Path = menu.Path
		router.Component = menu.Component
		router.Meta = vo.MetaVo{
			Title:   menu.MenuName,
			Icon:    menu.Icon,
			NoCache: menu.IsCache == "1",
			Link:    s.isInnerLink(menu) ? menu.Path : "",
		}
		
		cMenus := s.getChildList(menus, menu)
		if len(cMenus) > 0 && menu.MenuType == "M" {
			router.AlwaysShow = true
			router.Redirect = "noRedirect"
			router.Children = s.buildMenus(cMenus)
		}
		
		routers = append(routers, router)
	}
	return routers
}

// buildMenuTree 构建菜单树
func (s *SysMenuServiceImpl) buildMenuTree(menus []domain.SysMenu, parentId int64) []domain.SysMenu {
	menuTree := make([]domain.SysMenu, 0)
	for _, menu := range menus {
		if menu.ParentId == parentId {
			childList := s.buildMenuTree(menus, menu.MenuId)
			menu.Children = childList
			menuTree = append(menuTree, menu)
		}
	}
	return menuTree
}

// buildMenuTreeSelect 构建下拉树结构
func (s *SysMenuServiceImpl) buildMenuTreeSelect(menus []domain.SysMenu) []vo.TreeSelect {
	treeSelects := make([]vo.TreeSelect, 0)
	for _, menu := range menus {
		treeSelect := vo.TreeSelect{
			ID:    menu.MenuId,
			Label: menu.MenuName,
		}
		if len(menu.Children) > 0 {
			treeSelect.Children = s.buildMenuTreeSelect(menu.Children)
		}
		treeSelects = append(treeSelects, treeSelect)
	}
	return treeSelects
} 