package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysNoticeServiceImpl 公告服务实现
type SysNoticeServiceImpl struct {
	// 公告仓储
	NoticeRepository repository.NoticeRepository
}

// NewSysNoticeService 创建公告服务
func NewSysNoticeService(noticeRepository repository.NoticeRepository) service.ISysNoticeService {
	return &SysNoticeServiceImpl{
		NoticeRepository: noticeRepository,
	}
}

// SelectNoticeById 查询公告信息
func (s *SysNoticeServiceImpl) SelectNoticeById(noticeId int64) domain.SysNotice {
	notice, err := s.NoticeRepository.SelectNoticeById(noticeId)
	if err != nil {
		return domain.SysNotice{}
	}
	return *notice
}

// SelectNoticeList 查询公告列表
func (s *SysNoticeServiceImpl) SelectNoticeList(notice domain.SysNotice) []domain.SysNotice {
	notices, err := s.NoticeRepository.SelectNoticeList(&notice)
	if err != nil {
		return []domain.SysNotice{}
	}
	return notices
}

// InsertNotice 新增公告
func (s *SysNoticeServiceImpl) InsertNotice(notice domain.SysNotice) int {
	err := s.NoticeRepository.InsertNotice(&notice)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateNotice 修改公告
func (s *SysNoticeServiceImpl) UpdateNotice(notice domain.SysNotice) int {
	err := s.NoticeRepository.UpdateNotice(&notice)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteNoticeById 删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeById(noticeId int64) int {
	err := s.NoticeRepository.DeleteNoticeById(noticeId)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteNoticeByIds 批量删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeByIds(noticeIds []int64) int {
	err := s.NoticeRepository.DeleteNoticeByIds(noticeIds)
	if err != nil {
		return 0
	}
	return 1
}
