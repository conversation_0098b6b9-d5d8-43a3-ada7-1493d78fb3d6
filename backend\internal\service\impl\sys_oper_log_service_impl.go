package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysOperLogServiceImpl 操作日志服务实现
type SysOperLogServiceImpl struct {
	// 操作日志仓储
	OperLogRepository repository.OperLogRepository
}

// NewSysOperLogService 创建操作日志服务
func NewSysOperLogService(operLogRepository repository.OperLogRepository) service.ISysOperLogService {
	return &SysOperLogServiceImpl{
		OperLogRepository: operLogRepository,
	}
}

// InsertOperlog 新增操作日志
func (s *SysOperLogServiceImpl) InsertOperlog(operLog domain.SysOperLog) {
	s.OperLogRepository.InsertOperLog(&operLog)
}

// SelectOperLogList 查询系统操作日志集合
func (s *SysOperLogServiceImpl) SelectOperLogList(operLog domain.SysOperLog) []domain.SysOperLog {
	operLogs, err := s.OperLogRepository.SelectOperLogList(&operLog)
	if err != nil {
		return []domain.SysOperLog{}
	}
	return operLogs
}

// DeleteOperLogByIds 批量删除系统操作日志
func (s *SysOperLogServiceImpl) DeleteOperLogByIds(operIds []int64) int {
	err := s.OperLogRepository.DeleteOperLogByIds(operIds)
	if err != nil {
		return 0
	}
	return 1
}

// SelectOperLogById 查询操作日志详细
func (s *SysOperLogServiceImpl) SelectOperLogById(operId int64) domain.SysOperLog {
	operLog, err := s.OperLogRepository.SelectOperLogById(operId)
	if err != nil {
		return domain.SysOperLog{}
	}
	return *operLog
}

// CleanOperLog 清空操作日志
func (s *SysOperLogServiceImpl) CleanOperLog() {
	s.OperLogRepository.CleanOperLog()
}
