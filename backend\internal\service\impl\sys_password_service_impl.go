package impl

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysPasswordServiceImpl 密码服务实现
type SysPasswordServiceImpl struct {
	logger *zap.Logger
	// 密码加密盐值
	salt string
}

// NewSysPasswordServiceImpl 创建密码服务实现
func NewSysPasswordServiceImpl(logger *zap.Logger, salt string) service.SysPasswordService {
	return &SysPasswordServiceImpl{
		logger: logger,
		salt:   salt,
	}
}

// EncryptPassword 加密密码
func (s *SysPasswordServiceImpl) EncryptPassword(password string) string {
	// 加盐MD5加密
	return s.md5Encrypt(fmt.Sprintf("%s%s", password, s.salt))
}

// MatchPassword 匹配密码
func (s *SysPasswordServiceImpl) MatchPassword(rawPassword string, encodedPassword string) bool {
	// 加密后比较
	return strings.EqualFold(s.EncryptPassword(rawPassword), encodedPassword)
}

// ValidatePassword 验证密码
func (s *SysPasswordServiceImpl) ValidatePassword(password string) error {
	if password == "" {
		return errors.New("密码不能为空")
	}

	// 密码长度验证
	if len(password) < 6 || len(password) > 20 {
		return errors.New("密码长度必须在6到20个字符之间")
	}

	// 密码复杂度验证
	// 至少包含一个数字、一个小写字母、一个大写字母
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)

	if !hasNumber || !hasLower || !hasUpper {
		return errors.New("密码必须包含数字、小写字母和大写字母")
	}

	return nil
}

// md5Encrypt MD5加密
func (s *SysPasswordServiceImpl) md5Encrypt(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}
