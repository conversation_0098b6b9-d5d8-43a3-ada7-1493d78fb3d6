package impl

import (
	"strings"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SysPermissionServiceImpl 系统权限服务实现
type SysPermissionServiceImpl struct {
	logger       *zap.Logger
	roleService  service.ISysRoleService
	menuService  service.ISysMenuService
	deptService  service.ISysDeptService
	userService  service.ISysUserService
	db           *gorm.DB
	roleDeptRepo repository.RoleDeptRepository
}

// NewSysPermissionServiceImpl 创建系统权限服务实现
func NewSysPermissionServiceImpl(
	logger *zap.Logger,
	roleService service.ISysRoleService,
	menuService service.ISysMenuService,
	deptService service.ISysDeptService,
	userService service.ISysUserService,
	db *gorm.DB,
	roleDeptRepo repository.RoleDeptRepository,
) service.SysPermissionService {
	return &SysPermissionServiceImpl{
		logger:       logger,
		roleService:  roleService,
		menuService:  menuService,
		deptService:  deptService,
		userService:  userService,
		db:           db,
		roleDeptRepo: roleDeptRepo,
	}
}

// GetRolePermission 获取角色数据权限
func (s *SysPermissionServiceImpl) GetRolePermission(userId int64) {
	// 获取用户
	user := s.userService.SelectUserById(userId)
	if user.UserId == 0 {
		s.logger.Error("用户不存在", zap.Int64("userId", userId))
		return
	}

	// 管理员拥有所有权限
	if userId == 1 {
		// 设置用户数据权限为全部数据权限
		user.SetDataScope("1") // 全部数据权限
		return
	}

	// 获取用户角色列表
	roles := s.roleService.SelectRolesByUserId(userId)
	if len(roles) == 0 {
		// 设置用户数据权限为仅本人数据权限
		user.SetDataScope("5") // 仅本人数据权限
		return
	}

	// 获取角色数据权限
	dataScope := "5" // 默认仅本人数据权限
	for _, role := range roles {
		if role.DataScope == "1" { // 全部数据权限
			dataScope = "1"
			break
		} else if role.DataScope == "2" { // 自定义数据权限
			dataScope = "2"
		} else if role.DataScope == "3" && dataScope != "1" && dataScope != "2" { // 本部门数据权限
			dataScope = "3"
		} else if role.DataScope == "4" && dataScope != "1" && dataScope != "2" && dataScope != "3" { // 本部门及以下数据权限
			dataScope = "4"
		}
	}

	// 设置用户数据权限
	user.SetDataScope(dataScope)
	s.logger.Info("设置用户数据权限", zap.Int64("userId", userId), zap.String("dataScope", dataScope))
}

// SetRolePermission 设置角色数据权限
func (s *SysPermissionServiceImpl) SetRolePermission(roleId int64, deptIds []int64) int {
	if roleId <= 0 {
		return 0
	}

	// 开启事务
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色与部门关联
		err := s.roleDeptRepo.DeleteRoleDeptByRoleId(roleId)
		if err != nil {
			return err
		}

		// 新增角色和部门信息
		if deptIds != nil && len(deptIds) > 0 {
			// 批量新增角色部门信息
			var roleDepts []domain.SysRoleDept
			for _, deptId := range deptIds {
				roleDepts = append(roleDepts, domain.SysRoleDept{
					RoleId: roleId,
					DeptId: deptId,
				})
			}
			err = s.roleDeptRepo.BatchRoleDept(roleDepts)
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		s.logger.Error("设置角色数据权限失败", zap.Int64("roleId", roleId), zap.Error(err))
		return 0
	}

	return 1
}

// HasPermission 判断用户是否拥有某个权限
func (s *SysPermissionServiceImpl) HasPermission(userId int64, permission string) bool {
	// 超级管理员拥有所有权限
	if userId == 1 {
		return true
	}

	// 如果权限为空则视为无权限
	if permission == "" {
		return false
	}

	// 获取用户权限列表
	permissions, err := s.getUserPermissions(userId)
	if err != nil {
		s.logger.Error("获取用户权限列表失败", zap.Int64("userId", userId), zap.Error(err))
		return false
	}

	// 判断是否包含权限
	for _, perm := range permissions {
		if strings.EqualFold(permission, perm) {
			return true
		}
	}

	return false
}

// getUserPermissions 获取用户权限列表
func (s *SysPermissionServiceImpl) getUserPermissions(userId int64) ([]string, error) {
	// 超级管理员拥有所有权限
	if userId == 1 {
		return []string{"*:*:*"}, nil
	}

	// 查询用户拥有的角色权限
	rolePerms := s.roleService.SelectRolePermissionByUserId(userId)

	// 查询用户拥有的菜单权限
	menuPerms := s.menuService.SelectMenuPermsByUserId(userId)

	// 合并权限
	allPerms := make(map[string]struct{})
	for perm := range rolePerms {
		allPerms[perm] = struct{}{}
	}
	for perm := range menuPerms {
		allPerms[perm] = struct{}{}
	}

	// 转换为字符串列表
	var permissions []string
	for perm := range allPerms {
		if perm != "" {
			permissions = append(permissions, perm)
		}
	}

	return permissions, nil
}
