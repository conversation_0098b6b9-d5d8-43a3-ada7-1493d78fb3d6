package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
)

// SysPostServiceImpl 岗位服务实现
type SysPostServiceImpl struct {
	// 岗位仓储
	PostRepository repository.PostRepository
	// 用户岗位仓储
	UserPostRepository repository.UserPostRepository
}

// NewSysPostService 创建岗位服务
func NewSysPostService(postRepository repository.PostRepository, userPostRepository repository.UserPostRepository) service.ISysPostService {
	return &SysPostServiceImpl{
		PostRepository:     postRepository,
		UserPostRepository: userPostRepository,
	}
}

// SelectPostList 查询岗位信息集合
func (s *SysPostServiceImpl) SelectPostList(post domain.SysPost) []domain.SysPost {
	posts, err := s.PostRepository.SelectPostList(&post)
	if err != nil {
		return []domain.SysPost{}
	}
	return posts
}

// SelectPostAll 查询所有岗位
func (s *SysPostServiceImpl) SelectPostAll() []domain.SysPost {
	posts, err := s.PostRepository.SelectPostAll()
	if err != nil {
		return []domain.SysPost{}
	}
	return posts
}

// SelectPostById 通过岗位ID查询岗位信息
func (s *SysPostServiceImpl) SelectPostById(postId int64) domain.SysPost {
	post, err := s.PostRepository.SelectPostById(postId)
	if err != nil {
		return domain.SysPost{}
	}
	return *post
}

// SelectPostListByUserId 根据用户ID获取岗位选择框列表
func (s *SysPostServiceImpl) SelectPostListByUserId(userId int64) []int64 {
	postIds, err := s.PostRepository.SelectPostListByUserId(userId)
	if err != nil {
		return []int64{}
	}
	return postIds
}

// CheckPostNameUnique 校验岗位名称
func (s *SysPostServiceImpl) CheckPostNameUnique(post domain.SysPost) bool {
	existPost, err := s.PostRepository.CheckPostNameUnique(post.PostName)
	if err != nil {
		return false
	}

	if existPost != nil && existPost.PostId != post.PostId {
		return false
	}
	return true
}

// CheckPostCodeUnique 校验岗位编码
func (s *SysPostServiceImpl) CheckPostCodeUnique(post domain.SysPost) bool {
	existPost, err := s.PostRepository.CheckPostCodeUnique(post.PostCode)
	if err != nil {
		return false
	}

	if existPost != nil && existPost.PostId != post.PostId {
		return false
	}
	return true
}

// CountUserPostById 通过岗位ID查询岗位使用数量
func (s *SysPostServiceImpl) CountUserPostById(postId int64) int {
	count, err := s.UserPostRepository.CountUserPostById(postId)
	if err != nil {
		return 0
	}
	return count
}

// DeletePostById 删除岗位信息
func (s *SysPostServiceImpl) DeletePostById(postId int64) int {
	err := s.PostRepository.DeletePostById(postId)
	if err != nil {
		return 0
	}
	return 1
}

// DeletePostByIds 批量删除岗位信息
func (s *SysPostServiceImpl) DeletePostByIds(postIds []int64) int {
	err := s.PostRepository.DeletePostByIds(postIds)
	if err != nil {
		return 0
	}
	return 1
}

// InsertPost 新增保存岗位信息
func (s *SysPostServiceImpl) InsertPost(post domain.SysPost) int {
	err := s.PostRepository.InsertPost(&post)
	if err != nil {
		return 0
	}
	return 1
}

// UpdatePost 修改保存岗位信息
func (s *SysPostServiceImpl) UpdatePost(post domain.SysPost) int {
	err := s.PostRepository.UpdatePost(&post)
	if err != nil {
		return 0
	}
	return 1
}
