package impl

import (
	"errors"
	"fmt"
	"strings"

	"github.com/ruoyi/backend/internal/database"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SysRoleServiceImpl 角色服务实现
type SysRoleServiceImpl struct {
	logger            *zap.Logger
	roleRepo          repository.RoleRepository
	roleMenuRepo      repository.RoleMenuRepository
	userRoleRepo      repository.UserRoleRepository
	roleDeptRepo      repository.RoleDeptRepository
	transactionHelper database.TransactionHelper
}

// NewSysRoleServiceImpl 创建角色服务实现
func NewSysRoleServiceImpl(
	logger *zap.Logger,
	roleRepo repository.RoleRepository,
	roleMenuRepo repository.RoleMenuRepository,
	userRoleRepo repository.UserRoleRepository,
	roleDeptRepo repository.RoleDeptRepository,
	transactionHelper database.TransactionHelper,
) service.ISysRoleService {
	return &SysRoleServiceImpl{
		logger:            logger,
		roleRepo:          roleRepo,
		roleMenuRepo:      roleMenuRepo,
		userRoleRepo:      userRoleRepo,
		roleDeptRepo:      roleDeptRepo,
		transactionHelper: transactionHelper,
	}
}

// SelectRoleList 根据条件分页查询角色数据
func (s *SysRoleServiceImpl) SelectRoleList(role domain.SysRole) []domain.SysRole {
	roles, err := s.roleRepo.SelectRoleList(&role)
	if err != nil {
		s.logger.Error("查询角色列表失败", zap.Error(err))
		return []domain.SysRole{}
	}
	return roles
}

// SelectRolesByUserId 根据用户ID查询角色列表
func (s *SysRoleServiceImpl) SelectRolesByUserId(userId int64) []domain.SysRole {
	roles, err := s.roleRepo.SelectRolePermissionByUserId(userId)
	if err != nil {
		s.logger.Error("根据用户ID查询角色列表失败", zap.Int64("userId", userId), zap.Error(err))
		return []domain.SysRole{}
	}
	return roles
}

// SelectRolePermissionByUserId 根据用户ID查询角色权限
func (s *SysRoleServiceImpl) SelectRolePermissionByUserId(userId int64) map[string]struct{} {
	roles, err := s.roleRepo.SelectRolePermissionByUserId(userId)
	if err != nil {
		s.logger.Error("根据用户ID查询角色权限失败", zap.Int64("userId", userId), zap.Error(err))
		return map[string]struct{}{}
	}

	permsSet := make(map[string]struct{})
	for _, role := range roles {
		if role.Status == "0" && role.DelFlag == "0" {
			perms := strings.Split(role.RoleKey, ",")
			for _, perm := range perms {
				if perm != "" {
					permsSet[perm] = struct{}{}
				}
			}
		}
	}
	return permsSet
}

// SelectRoleAll 查询所有角色
func (s *SysRoleServiceImpl) SelectRoleAll() []domain.SysRole {
	roles, err := s.roleRepo.SelectRoleAll()
	if err != nil {
		s.logger.Error("查询所有角色失败", zap.Error(err))
		return []domain.SysRole{}
	}
	return roles
}

// SelectRoleListByUserId 根据用户ID获取角色选择框列表
func (s *SysRoleServiceImpl) SelectRoleListByUserId(userId int64) []int64 {
	roleIds, err := s.roleRepo.SelectRoleListByUserId(userId)
	if err != nil {
		s.logger.Error("根据用户ID获取角色选择框列表失败", zap.Int64("userId", userId), zap.Error(err))
		return []int64{}
	}
	return roleIds
}

// SelectRoleById 通过角色ID查询角色
func (s *SysRoleServiceImpl) SelectRoleById(roleId int64) domain.SysRole {
	role, err := s.roleRepo.SelectRoleById(roleId)
	if err != nil || role == nil {
		s.logger.Error("通过角色ID查询角色失败", zap.Int64("roleId", roleId), zap.Error(err))
		return domain.SysRole{}
	}

	// 查询角色关联的菜单ID列表
	menuIds, err := s.roleMenuRepo.SelectMenuListByRoleId(roleId)
	if err == nil && len(menuIds) > 0 {
		role.MenuIds = menuIds
	}

	// 查询角色关联的部门ID列表
	deptIds, err := s.roleDeptRepo.SelectRoleDeptByRoleId(roleId)
	if err == nil && len(deptIds) > 0 {
		role.DeptIds = deptIds
	}

	return *role
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (s *SysRoleServiceImpl) CheckRoleNameUnique(role domain.SysRole) bool {
	existRole, err := s.roleRepo.CheckRoleNameUnique(role.RoleName)
	if err != nil {
		s.logger.Error("校验角色名称是否唯一失败", zap.String("roleName", role.RoleName), zap.Error(err))
		return false
	}

	if existRole != nil && existRole.RoleId > 0 && existRole.RoleId != role.RoleId {
		return false
	}
	return true
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (s *SysRoleServiceImpl) CheckRoleKeyUnique(role domain.SysRole) bool {
	existRole, err := s.roleRepo.CheckRoleKeyUnique(role.RoleKey)
	if err != nil {
		s.logger.Error("校验角色权限是否唯一失败", zap.String("roleKey", role.RoleKey), zap.Error(err))
		return false
	}

	if existRole != nil && existRole.RoleId > 0 && existRole.RoleId != role.RoleId {
		return false
	}
	return true
}

// CheckRoleAllowed 校验角色是否允许操作
func (s *SysRoleServiceImpl) CheckRoleAllowed(role domain.SysRole) {
	if role.RoleId == 1 {
		panic(errors.New("不允许操作超级管理员角色"))
	}
}

// CheckRoleDataScope 校验角色是否有数据权限
func (s *SysRoleServiceImpl) CheckRoleDataScope(roleIds ...int64) {
	// TODO: 实现校验角色是否有数据权限
}

// CountUserRoleByRoleId 通过角色ID查询角色使用数量
func (s *SysRoleServiceImpl) CountUserRoleByRoleId(roleId int64) int {
	count, err := s.userRoleRepo.CountUserRoleByRoleId(roleId)
	if err != nil {
		s.logger.Error("通过角色ID查询角色使用数量失败", zap.Int64("roleId", roleId), zap.Error(err))
		return 0
	}
	return int(count)
}

// InsertRole 新增保存角色信息
func (s *SysRoleServiceImpl) InsertRole(role domain.SysRole) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		roleRepoTx := s.roleRepo.WithTx(tx).(repository.RoleRepository)

		// 新增角色信息
		if err := roleRepoTx.InsertRole(&role); err != nil {
			return err
		}

		// 新增角色与菜单关联
		if err := s.insertRoleMenu(tx, role); err != nil {
			return err
		}

		// 新增角色与部门关联
		if err := s.insertRoleDept(tx, role); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("新增保存角色信息失败", zap.Error(err))
		return 0
	}
	return result
}

// UpdateRole 修改保存角色信息
func (s *SysRoleServiceImpl) UpdateRole(role domain.SysRole) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		roleRepoTx := s.roleRepo.WithTx(tx).(repository.RoleRepository)
		roleMenuRepoTx := s.roleMenuRepo.WithTx(tx).(repository.RoleMenuRepository)
		roleDeptRepoTx := s.roleDeptRepo.WithTx(tx).(repository.RoleDeptRepository)

		// 修改角色信息
		if err := roleRepoTx.UpdateRole(&role); err != nil {
			return err
		}

		// 删除角色与菜单关联
		if err := roleMenuRepoTx.DeleteRoleMenuByRoleId(role.RoleId); err != nil {
			return err
		}

		// 新增角色与菜单关联
		if err := s.insertRoleMenu(tx, role); err != nil {
			return err
		}

		// 删除角色与部门关联
		if err := roleDeptRepoTx.DeleteRoleDeptByRoleId(role.RoleId); err != nil {
			return err
		}

		// 新增角色与部门关联
		if err := s.insertRoleDept(tx, role); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("修改保存角色信息失败", zap.Error(err))
		return 0
	}
	return result
}

// UpdateRoleStatus 修改角色状态
func (s *SysRoleServiceImpl) UpdateRoleStatus(role domain.SysRole) int {
	err := s.roleRepo.UpdateRole(&role)
	if err != nil {
		s.logger.Error("修改角色状态失败", zap.Error(err))
		return 0
	}
	return 1
}

// AuthDataScope 修改数据权限信息
func (s *SysRoleServiceImpl) AuthDataScope(role domain.SysRole) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		roleRepoTx := s.roleRepo.WithTx(tx).(repository.RoleRepository)
		roleDeptRepoTx := s.roleDeptRepo.WithTx(tx).(repository.RoleDeptRepository)

		// 修改角色信息
		if err := roleRepoTx.UpdateRole(&role); err != nil {
			return err
		}

		// 删除角色与部门关联
		if err := roleDeptRepoTx.DeleteRoleDeptByRoleId(role.RoleId); err != nil {
			return err
		}

		// 新增角色与部门关联
		if err := s.insertRoleDept(tx, role); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("修改数据权限信息失败", zap.Error(err))
		return 0
	}
	return result
}

// DeleteRoleById 通过角色ID删除角色
func (s *SysRoleServiceImpl) DeleteRoleById(roleId int64) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		roleRepoTx := s.roleRepo.WithTx(tx).(repository.RoleRepository)
		roleMenuRepoTx := s.roleMenuRepo.WithTx(tx).(repository.RoleMenuRepository)
		roleDeptRepoTx := s.roleDeptRepo.WithTx(tx).(repository.RoleDeptRepository)

		// 删除角色与菜单关联
		if err := roleMenuRepoTx.DeleteRoleMenuByRoleId(roleId); err != nil {
			return err
		}

		// 删除角色与部门关联
		if err := roleDeptRepoTx.DeleteRoleDeptByRoleId(roleId); err != nil {
			return err
		}

		// 删除角色
		if err := roleRepoTx.DeleteRoleById(roleId); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("通过角色ID删除角色失败", zap.Error(err))
		return 0
	}
	return result
}

// DeleteRoleByIds 批量删除角色信息
func (s *SysRoleServiceImpl) DeleteRoleByIds(roleIds []int64) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		roleRepoTx := s.roleRepo.WithTx(tx).(repository.RoleRepository)
		roleMenuRepoTx := s.roleMenuRepo.WithTx(tx).(repository.RoleMenuRepository)
		roleDeptRepoTx := s.roleDeptRepo.WithTx(tx).(repository.RoleDeptRepository)

		for _, roleId := range roleIds {
			// 校验角色是否允许操作
			role := domain.SysRole{RoleId: roleId}
			s.CheckRoleAllowed(role)

			// 校验角色是否有用户使用
			count := s.CountUserRoleByRoleId(roleId)
			if count > 0 {
				return fmt.Errorf("角色已分配，不能删除")
			}

			// 删除角色与菜单关联
			if err := roleMenuRepoTx.DeleteRoleMenuByRoleId(roleId); err != nil {
				return err
			}

			// 删除角色与部门关联
			if err := roleDeptRepoTx.DeleteRoleDeptByRoleId(roleId); err != nil {
				return err
			}
		}

		// 批量删除角色
		if err := roleRepoTx.DeleteRoleByIds(roleIds); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("批量删除角色失败", zap.Error(err))
		return 0
	}
	return result
}

// DeleteAuthUser 取消授权用户角色
func (s *SysRoleServiceImpl) DeleteAuthUser(userRole domain.SysUserRole) int {
	err := s.userRoleRepo.DeleteUserRole(userRole)
	if err != nil {
		s.logger.Error("取消授权用户角色失败", zap.Error(err))
		return 0
	}
	return 1
}

// DeleteAuthUsers 批量取消授权用户角色
func (s *SysRoleServiceImpl) DeleteAuthUsers(roleId int64, userIds []int64) int {
	err := s.userRoleRepo.DeleteAuthUsers(roleId, userIds)
	if err != nil {
		s.logger.Error("批量取消授权用户角色失败", zap.Error(err))
		return 0
	}
	return 1
}

// InsertAuthUsers 批量选择授权用户角色
func (s *SysRoleServiceImpl) InsertAuthUsers(roleId int64, userIds []int64) int {
	err := s.userRoleRepo.InsertAuthUsers(roleId, userIds)
	if err != nil {
		s.logger.Error("批量选择授权用户角色失败", zap.Error(err))
		return 0
	}
	return 1
}

// insertRoleMenu 新增角色菜单信息
func (s *SysRoleServiceImpl) insertRoleMenu(tx *gorm.DB, role domain.SysRole) error {
	// 新增角色与菜单关联
	if role.MenuIds != nil && len(role.MenuIds) > 0 {
		// 批量新增角色菜单关联
		roleMenuList := make([]domain.SysRoleMenu, 0, len(role.MenuIds))
		for _, menuId := range role.MenuIds {
			roleMenu := domain.SysRoleMenu{
				RoleId: role.RoleId,
				MenuId: menuId,
			}
			roleMenuList = append(roleMenuList, roleMenu)
		}

		roleMenuRepoTx := s.roleMenuRepo.WithTx(tx).(repository.RoleMenuRepository)
		if err := roleMenuRepoTx.BatchRoleMenu(roleMenuList); err != nil {
			s.logger.Error("批量新增角色菜单关联失败", zap.Error(err))
			return err
		}
	}
	return nil
}

// insertRoleDept 新增角色部门信息
func (s *SysRoleServiceImpl) insertRoleDept(tx *gorm.DB, role domain.SysRole) error {
	// 新增角色与部门关联
	if role.DeptIds != nil && len(role.DeptIds) > 0 {
		// 批量新增角色部门关联
		roleDeptList := make([]domain.SysRoleDept, 0, len(role.DeptIds))
		for _, deptId := range role.DeptIds {
			roleDept := domain.SysRoleDept{
				RoleId: role.RoleId,
				DeptId: deptId,
			}
			roleDeptList = append(roleDeptList, roleDept)
		}

		roleDeptRepoTx := s.roleDeptRepo.WithTx(tx).(repository.RoleDeptRepository)
		if err := roleDeptRepoTx.BatchRoleDept(roleDeptList); err != nil {
			s.logger.Error("批量新增角色部门关联失败", zap.Error(err))
			return err
		}
	}
	return nil
}
