package impl

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
)

// SysUserOnlineServiceImpl 在线用户服务实现
type SysUserOnlineServiceImpl struct {
}

// NewSysUserOnlineService 创建在线用户服务
func NewSysUserOnlineService() service.ISysUserOnlineService {
	return &SysUserOnlineServiceImpl{}
}

// SelectOnlineByIpaddr 通过登录地址查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByIpaddr(ipaddr string, user model.LoginUser) domain.SysUserOnline {
	if ipaddr == "" || user.GetToken() == "" {
		return domain.SysUserOnline{}
	}

	userOnline := s.LoginUserToUserOnline(user)
	if userOnline.Ipaddr == ipaddr {
		return userOnline
	}

	return domain.SysUserOnline{}
}

// SelectOnlineByUserName 通过用户名称查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByUserName(userName string, user model.LoginUser) domain.SysUserOnline {
	if userName == "" || user.GetToken() == "" {
		return domain.SysUserOnline{}
	}

	userOnline := s.LoginUserToUserOnline(user)
	if userOnline.UserName == userName {
		return userOnline
	}

	return domain.SysUserOnline{}
}

// SelectOnlineByInfo 通过登录地址/用户名称查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByInfo(ipaddr string, userName string, user model.LoginUser) domain.SysUserOnline {
	if (ipaddr == "" && userName == "") || user.GetToken() == "" {
		return domain.SysUserOnline{}
	}

	userOnline := s.LoginUserToUserOnline(user)

	if ipaddr != "" && userName != "" {
		if userOnline.Ipaddr == ipaddr && userOnline.UserName == userName {
			return userOnline
		}
	} else if ipaddr != "" {
		if userOnline.Ipaddr == ipaddr {
			return userOnline
		}
	} else if userName != "" {
		if userOnline.UserName == userName {
			return userOnline
		}
	}

	return domain.SysUserOnline{}
}

// LoginUserToUserOnline 登录用户转在线用户
func (s *SysUserOnlineServiceImpl) LoginUserToUserOnline(user model.LoginUser) domain.SysUserOnline {
	if user.GetToken() == "" {
		return domain.SysUserOnline{}
	}

	userOnline := domain.SysUserOnline{
		TokenId:       user.GetToken(),
		UserName:      user.GetUsername(),
		Ipaddr:        user.GetIpaddr(),
		LoginLocation: user.GetLoginLocation(),
		Browser:       user.GetBrowser(),
		Os:            user.GetOs(),
		LoginTime:     user.GetLoginTime(),
	}

	return userOnline
}
