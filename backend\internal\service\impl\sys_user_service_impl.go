package impl

import (
	"errors"
	"fmt"
	"time"

	"github.com/ruoyi/backend/internal/database"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SysUserServiceImpl 用户服务实现
type SysUserServiceImpl struct {
	logger            *zap.Logger
	userRepo          repository.UserRepository
	roleRepo          repository.RoleRepository
	postRepo          repository.PostRepository
	userRoleRepo      repository.UserRoleRepository
	userPostRepo      repository.UserPostRepository
	configService     service.ISysConfigService
	deptService       service.ISysDeptService
	passwordService   service.SysPasswordService
	transactionHelper database.TransactionHelper
}

// NewSysUserServiceImpl 创建用户服务实现
func NewSysUserServiceImpl(
	logger *zap.Logger,
	userRepo repository.UserRepository,
	roleRepo repository.RoleRepository,
	postRepo repository.PostRepository,
	userRoleRepo repository.UserRoleRepository,
	userPostRepo repository.UserPostRepository,
	configService service.ISysConfigService,
	deptService service.ISysDeptService,
	passwordService service.SysPasswordService,
	transactionHelper database.TransactionHelper,
) service.ISysUserService {
	return &SysUserServiceImpl{
		logger:            logger,
		userRepo:          userRepo,
		roleRepo:          roleRepo,
		postRepo:          postRepo,
		userRoleRepo:      userRoleRepo,
		userPostRepo:      userPostRepo,
		configService:     configService,
		deptService:       deptService,
		passwordService:   passwordService,
		transactionHelper: transactionHelper,
	}
}

// SelectUserList 根据条件分页查询用户列表
func (s *SysUserServiceImpl) SelectUserList(user domain.SysUser) []domain.SysUser {
	users, err := s.userRepo.SelectUserList(&user)
	if err != nil {
		s.logger.Error("查询用户列表失败", zap.Error(err))
		return []domain.SysUser{}
	}
	return users
}

// SelectAllocatedList 根据条件分页查询已分配用户角色列表
func (s *SysUserServiceImpl) SelectAllocatedList(user domain.SysUser) []domain.SysUser {
	users, err := s.userRepo.SelectAllocatedList(&user)
	if err != nil {
		s.logger.Error("查询已分配用户角色列表失败", zap.Error(err))
		return []domain.SysUser{}
	}
	return users
}

// SelectUnallocatedList 根据条件分页查询未分配用户角色列表
func (s *SysUserServiceImpl) SelectUnallocatedList(user domain.SysUser) []domain.SysUser {
	users, err := s.userRepo.SelectUnallocatedList(&user)
	if err != nil {
		s.logger.Error("查询未分配用户角色列表失败", zap.Error(err))
		return []domain.SysUser{}
	}
	return users
}

// SelectUserByUserName 通过用户名查询用户
func (s *SysUserServiceImpl) SelectUserByUserName(userName string) domain.SysUser {
	user, err := s.userRepo.SelectUserByUserName(userName)
	if err != nil || user == nil {
		s.logger.Error("通过用户名查询用户失败", zap.String("userName", userName), zap.Error(err))
		return domain.SysUser{}
	}
	return *user
}

// SelectUserById 通过用户ID查询用户
func (s *SysUserServiceImpl) SelectUserById(userId int64) domain.SysUser {
	user, err := s.userRepo.SelectUserById(userId)
	if err != nil || user == nil {
		s.logger.Error("通过用户ID查询用户失败", zap.Int64("userId", userId), zap.Error(err))
		return domain.SysUser{}
	}
	return *user
}

// SelectUserRoleGroup 根据用户ID查询用户所属角色组
func (s *SysUserServiceImpl) SelectUserRoleGroup(userName string) string {
	// 查询用户所属角色组
	user, err := s.userRepo.SelectUserByUserName(userName)
	if err != nil || user == nil {
		s.logger.Error("通过用户名查询用户失败", zap.String("userName", userName), zap.Error(err))
		return ""
	}

	// TODO: 实现查询用户所属角色组
	return ""
}

// SelectUserPostGroup 根据用户ID查询用户所属岗位组
func (s *SysUserServiceImpl) SelectUserPostGroup(userName string) string {
	// 查询用户所属岗位组
	user, err := s.userRepo.SelectUserByUserName(userName)
	if err != nil || user == nil {
		s.logger.Error("通过用户名查询用户失败", zap.String("userName", userName), zap.Error(err))
		return ""
	}

	// TODO: 实现查询用户所属岗位组
	return ""
}

// CheckUserNameUnique 校验用户名称是否唯一
func (s *SysUserServiceImpl) CheckUserNameUnique(user domain.SysUser) bool {
	existUser, err := s.userRepo.CheckUserNameUnique(user.UserName)
	if err != nil {
		s.logger.Error("校验用户名称是否唯一失败", zap.String("userName", user.UserName), zap.Error(err))
		return false
	}

	if existUser != nil && existUser.UserId > 0 && existUser.UserId != user.UserId {
		return false
	}
	return true
}

// CheckPhoneUnique 校验手机号码是否唯一
func (s *SysUserServiceImpl) CheckPhoneUnique(user domain.SysUser) bool {
	existUser, err := s.userRepo.CheckPhoneUnique(user.Phonenumber)
	if err != nil {
		s.logger.Error("校验手机号码是否唯一失败", zap.String("phonenumber", user.Phonenumber), zap.Error(err))
		return false
	}

	if existUser != nil && existUser.UserId > 0 && existUser.UserId != user.UserId {
		return false
	}
	return true
}

// CheckEmailUnique 校验email是否唯一
func (s *SysUserServiceImpl) CheckEmailUnique(user domain.SysUser) bool {
	existUser, err := s.userRepo.CheckEmailUnique(user.Email)
	if err != nil {
		s.logger.Error("校验email是否唯一失败", zap.String("email", user.Email), zap.Error(err))
		return false
	}

	if existUser != nil && existUser.UserId > 0 && existUser.UserId != user.UserId {
		return false
	}
	return true
}

// CheckUserAllowed 校验用户是否允许操作
func (s *SysUserServiceImpl) CheckUserAllowed(user domain.SysUser) {
	if user.UserId == 1 {
		panic(errors.New("不允许操作超级管理员用户"))
	}
}

// CheckUserDataScope 校验用户是否有数据权限
func (s *SysUserServiceImpl) CheckUserDataScope(userId int64) {
	// TODO: 实现校验用户是否有数据权限
}

// InsertUser 新增用户信息
func (s *SysUserServiceImpl) InsertUser(user domain.SysUser) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		// 新增用户信息
		userRepoTx := s.userRepo.WithTx(tx).(repository.UserRepository)
		if err := userRepoTx.InsertUser(&user); err != nil {
			return err
		}

		// 新增用户与角色关联
		s.insertUserRole(tx, user)

		// 新增用户与岗位关联
		s.insertUserPost(tx, user)

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("新增用户信息失败", zap.Error(err))
		return 0
	}
	return result
}

// RegisterUser 注册用户信息
func (s *SysUserServiceImpl) RegisterUser(user domain.SysUser) bool {
	err := s.userRepo.InsertUser(&user)
	if err != nil {
		s.logger.Error("注册用户信息失败", zap.Error(err))
		return false
	}
	return true
}

// UpdateUser 修改用户信息
func (s *SysUserServiceImpl) UpdateUser(user domain.SysUser) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		userRepoTx := s.userRepo.WithTx(tx).(repository.UserRepository)
		userRoleRepoTx := s.userRoleRepo.WithTx(tx).(repository.UserRoleRepository)
		userPostRepoTx := s.userPostRepo.WithTx(tx).(repository.UserPostRepository)

		// 删除用户与角色关联
		if err := userRoleRepoTx.DeleteUserRoleByUserId(user.UserId); err != nil {
			return err
		}

		// 新增用户与角色关联
		s.insertUserRole(tx, user)

		// 删除用户与岗位关联
		if err := userPostRepoTx.DeleteUserPostByUserId(user.UserId); err != nil {
			return err
		}

		// 新增用户与岗位关联
		s.insertUserPost(tx, user)

		// 更新用户信息
		if err := userRepoTx.UpdateUser(&user); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("修改用户信息失败", zap.Error(err))
		return 0
	}
	return result
}

// InsertUserAuth 用户授权角色
func (s *SysUserServiceImpl) InsertUserAuth(userId int64, roleIds []int64) {
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		userRoleRepoTx := s.userRoleRepo.WithTx(tx).(repository.UserRoleRepository)

		// 删除用户与角色关联
		if err := userRoleRepoTx.DeleteUserRoleByUserId(userId); err != nil {
			return err
		}

		// 新增用户与角色关联
		s.insertUserRoleArr(tx, userId, roleIds)

		return nil
	})

	if err != nil {
		s.logger.Error("用户授权角色失败", zap.Error(err))
	}
}

// UpdateUserStatus 修改用户状态
func (s *SysUserServiceImpl) UpdateUserStatus(user domain.SysUser) int {
	err := s.userRepo.UpdateUser(&user)
	if err != nil {
		s.logger.Error("修改用户状态失败", zap.Error(err))
		return 0
	}
	return 1
}

// UpdateUserProfile 修改用户基本信息
func (s *SysUserServiceImpl) UpdateUserProfile(user domain.SysUser) int {
	err := s.userRepo.UpdateUser(&user)
	if err != nil {
		s.logger.Error("修改用户基本信息失败", zap.Error(err))
		return 0
	}
	return 1
}

// UpdateUserAvatar 修改用户头像
func (s *SysUserServiceImpl) UpdateUserAvatar(userId int64, avatar string) bool {
	err := s.userRepo.UpdateUserAvatar(userId, avatar)
	if err != nil {
		s.logger.Error("修改用户头像失败", zap.Error(err))
		return false
	}
	return true
}

// ResetPwd 重置用户密码
func (s *SysUserServiceImpl) ResetPwd(user domain.SysUser) int {
	err := s.userRepo.UpdateUser(&user)
	if err != nil {
		s.logger.Error("重置用户密码失败", zap.Error(err))
		return 0
	}
	return 1
}

// ResetUserPwd 重置用户密码
func (s *SysUserServiceImpl) ResetUserPwd(userId int64, password string) int {
	err := s.userRepo.ResetUserPwd(userId, password)
	if err != nil {
		s.logger.Error("重置用户密码失败", zap.Error(err))
		return 0
	}
	return 1
}

// DeleteUserById 通过用户ID删除用户
func (s *SysUserServiceImpl) DeleteUserById(userId int64) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		userRepoTx := s.userRepo.WithTx(tx).(repository.UserRepository)
		userRoleRepoTx := s.userRoleRepo.WithTx(tx).(repository.UserRoleRepository)
		userPostRepoTx := s.userPostRepo.WithTx(tx).(repository.UserPostRepository)

		// 删除用户与角色关联
		if err := userRoleRepoTx.DeleteUserRoleByUserId(userId); err != nil {
			return err
		}

		// 删除用户与岗位关联
		if err := userPostRepoTx.DeleteUserPostByUserId(userId); err != nil {
			return err
		}

		// 删除用户
		if err := userRepoTx.DeleteUserById(userId); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("删除用户失败", zap.Error(err))
		return 0
	}
	return result
}

// DeleteUserByIds 批量删除用户信息
func (s *SysUserServiceImpl) DeleteUserByIds(userIds []int64) int {
	var result int
	err := s.transactionHelper.Transaction(func(tx *gorm.DB) error {
		userRepoTx := s.userRepo.WithTx(tx).(repository.UserRepository)
		userRoleRepoTx := s.userRoleRepo.WithTx(tx).(repository.UserRoleRepository)
		userPostRepoTx := s.userPostRepo.WithTx(tx).(repository.UserPostRepository)

		for _, userId := range userIds {
			// 校验用户是否允许操作
			user := domain.SysUser{UserId: userId}
			s.CheckUserAllowed(user)

			// 删除用户与角色关联
			if err := userRoleRepoTx.DeleteUserRoleByUserId(userId); err != nil {
				return err
			}

			// 删除用户与岗位关联
			if err := userPostRepoTx.DeleteUserPostByUserId(userId); err != nil {
				return err
			}
		}

		// 批量删除用户
		if err := userRepoTx.DeleteUserByIds(userIds); err != nil {
			return err
		}

		result = 1
		return nil
	})

	if err != nil {
		s.logger.Error("批量删除用户失败", zap.Error(err))
		return 0
	}
	return result
}

// ImportUser 导入用户数据
func (s *SysUserServiceImpl) ImportUser(userList []domain.SysUser, isUpdateSupport bool, operName string) string {
	successNum := 0
	failureNum := 0
	successMsg := ""
	failureMsg := ""

	for _, user := range userList {
		// 验证是否存在这个用户
		existUser := s.SelectUserByUserName(user.UserName)
		if existUser.UserId > 0 {
			if isUpdateSupport {
				// 执行更新操作
				user.UserId = existUser.UserId
				user.UpdateBy = operName
				updateTime := time.Now()
				user.UpdateTime = &updateTime
				s.UpdateUser(user)
				successNum++
				successMsg += fmt.Sprintf("<br/>%d、账号 %s 更新成功", successNum, user.UserName)
			} else {
				failureNum++
				failureMsg += fmt.Sprintf("<br/>%d、账号 %s 已存在", failureNum, user.UserName)
			}
			continue
		} else {
			// 执行新增操作
			user.CreateBy = operName
			createTime := time.Now()
			user.CreateTime = &createTime
			// 加密密码
			user.Password = s.passwordService.EncryptPassword(user.Password)
			s.InsertUser(user)
			successNum++
			successMsg += fmt.Sprintf("<br/>%d、账号 %s 导入成功", successNum, user.UserName)
		}
	}

	if failureNum > 0 {
		failureMsg = fmt.Sprintf("很抱歉，导入失败！共 %d 条数据格式不正确，错误如下：%s", failureNum, failureMsg)
		return failureMsg
	} else {
		successMsg = fmt.Sprintf("恭喜您，数据已全部导入成功！共 %d 条，数据如下：%s", successNum, successMsg)
	}
	return successMsg
}

// insertUserRole 新增用户角色信息
func (s *SysUserServiceImpl) insertUserRole(tx *gorm.DB, user domain.SysUser) {
	// 新增用户与角色关联
	if user.RoleIds != nil && len(user.RoleIds) > 0 {
		s.insertUserRoleArr(tx, user.UserId, user.RoleIds)
	}
}

// insertUserRoleArr 批量新增用户角色信息
func (s *SysUserServiceImpl) insertUserRoleArr(tx *gorm.DB, userId int64, roleIds []int64) {
	if roleIds == nil || len(roleIds) == 0 {
		return
	}

	// 批量新增用户角色关联
	userRoleList := make([]domain.SysUserRole, 0, len(roleIds))
	for _, roleId := range roleIds {
		userRole := domain.SysUserRole{
			UserId: userId,
			RoleId: roleId,
		}
		userRoleList = append(userRoleList, userRole)
	}

	userRoleRepoTx := s.userRoleRepo.WithTx(tx).(repository.UserRoleRepository)
	if err := userRoleRepoTx.BatchUserRole(userRoleList); err != nil {
		s.logger.Error("批量新增用户角色关联失败", zap.Error(err))
	}
}

// insertUserPost 新增用户岗位信息
func (s *SysUserServiceImpl) insertUserPost(tx *gorm.DB, user domain.SysUser) {
	// 新增用户与岗位关联
	if user.PostIds != nil && len(user.PostIds) > 0 {
		// 批量新增用户岗位关联
		userPostList := make([]domain.SysUserPost, 0, len(user.PostIds))
		for _, postId := range user.PostIds {
			userPost := domain.SysUserPost{
				UserId: user.UserId,
				PostId: postId,
			}
			userPostList = append(userPostList, userPost)
		}

		userPostRepoTx := s.userPostRepo.WithTx(tx).(repository.UserPostRepository)
		if err := userPostRepoTx.BatchUserPost(userPostList); err != nil {
			s.logger.Error("批量新增用户岗位关联失败", zap.Error(err))
		}
	}
}
