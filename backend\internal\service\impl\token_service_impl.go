package impl

import (
	"fmt"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/utils"
	pkgutils "github.com/ruoyi/backend/pkg/utils"
	"go.uber.org/zap"
)

const (
	// TokenPrefix Token前缀
	TokenPrefix = "Bearer "

	// TokenHeader Token请求头
	TokenHeader = "Authorization"

	// TokenKey Token缓存key前缀
	TokenKey = "login_tokens:"
)

// TokenServiceImpl Token服务实现
type TokenServiceImpl struct {
	logger       *zap.Logger
	jwtConfig    *config.JWTConfig
	redisService redis.RedisService
}

// NewTokenServiceImpl 创建Token服务实现
func NewTokenServiceImpl(logger *zap.Logger, jwtConfig *config.JWTConfig, redisService redis.RedisService) service.TokenService {
	return &TokenServiceImpl{
		logger:       logger,
		jwtConfig:    jwtConfig,
		redisService: redisService,
	}
}

// GetLoginUser 获取登录用户
func (s *TokenServiceImpl) GetLoginUser(ctx *gin.Context) *model.LoginUser {
	token := s.GetToken(ctx)
	if token == "" {
		return nil
	}

	claims := s.ParseToken(token)
	if claims == nil {
		return nil
	}

	uuid := claims.GetUUID()
	tokenKey := s.GetTokenKey(uuid)
	userJson, err := s.redisService.Get(tokenKey)
	if err != nil || userJson == "" {
		s.logger.Error("从Redis获取用户信息失败", zap.String("uuid", uuid), zap.Error(err))
		return nil
	}

	// 从Redis中获取用户信息并反序列化为LoginUser对象
	var loginUser model.LoginUser
	err = pkgutils.FromJSON(userJson, &loginUser)
	if err != nil {
		s.logger.Error("反序列化用户信息失败", zap.String("uuid", uuid), zap.Error(err))
		return nil
	}

	return &loginUser
}

// SetLoginUser 设置登录用户
func (s *TokenServiceImpl) SetLoginUser(loginUser *model.LoginUser) {
	if loginUser != nil && loginUser.GetToken() != "" {
		tokenKey := s.GetTokenKey(loginUser.GetToken())
		expireTime := s.jwtConfig.GetExpireDuration()

		// 将LoginUser对象序列化为JSON字符串并存入Redis
		userJson, err := pkgutils.ToJSON(loginUser)
		if err != nil {
			s.logger.Error("序列化用户信息失败", zap.Error(err))
			return
		}

		err = s.redisService.Set(tokenKey, userJson, expireTime)
		if err != nil {
			s.logger.Error("存储用户信息到Redis失败", zap.Error(err))
		}
	}
}

// DelLoginUser 删除登录用户
func (s *TokenServiceImpl) DelLoginUser(token string) {
	if token != "" {
		claims := s.ParseToken(token)
		if claims != nil {
			uuid := claims.GetUUID()
			tokenKey := s.GetTokenKey(uuid)
			s.redisService.Del(tokenKey)
		}
	}
}

// CreateToken 创建Token
func (s *TokenServiceImpl) CreateToken(loginUser *model.LoginUser) string {
	uuid := utils.GenerateUUID()
	loginUser.SetToken(uuid)

	s.RefreshToken(loginUser)

	claims := map[string]interface{}{
		"userId":         loginUser.GetUserId(),
		"username":       loginUser.GetUser().UserName,
		"uuid":           uuid,
		"loginTime":      loginUser.GetLoginTime(),
		"expirationTime": loginUser.GetExpireTime(),
	}

	return s.CreateJWTToken(claims)
}

// VerifyToken 验证Token有效期，相差不足20分钟自动刷新缓存
func (s *TokenServiceImpl) VerifyToken(loginUser *model.LoginUser) {
	expireTime := loginUser.GetExpireTime()
	currentTime := time.Now().Unix()
	// 相差不足20分钟，自动刷新缓存
	if expireTime-currentTime <= 20*60 {
		s.RefreshToken(loginUser)
	}
}

// RefreshToken 刷新Token有效期
func (s *TokenServiceImpl) RefreshToken(loginUser *model.LoginUser) {
	loginUser.SetLoginTime(time.Now().Unix())
	expireTime := time.Now().Add(s.jwtConfig.GetExpireDuration()).Unix()
	loginUser.SetExpireTime(expireTime)

	// 缓存用户信息
	tokenKey := s.GetTokenKey(loginUser.GetToken())
	expiration := s.jwtConfig.GetExpireDuration()

	// 将LoginUser对象序列化为JSON字符串并存入Redis
	userJson, err := pkgutils.ToJSON(loginUser)
	if err != nil {
		s.logger.Error("序列化用户信息失败", zap.Error(err))
		return
	}

	err = s.redisService.Set(tokenKey, userJson, expiration)
	if err != nil {
		s.logger.Error("刷新用户信息到Redis失败", zap.Error(err))
	}
}

// SetUserAgent 设置用户代理信息
func (s *TokenServiceImpl) SetUserAgent(loginUser *model.LoginUser) {
	if loginUser == nil {
		return
	}

	// 从请求上下文中获取用户代理信息
	// 这里需要一个请求上下文参数，但当前方法没有，可以在调用此方法前先获取
	// 或者修改方法签名增加context参数
	// 暂时设置一些默认值
	loginUser.Browser = "Unknown Browser"
	loginUser.Os = "Unknown OS"
	loginUser.Ipaddr = utils.GetLocalIP()
	loginUser.LoginLocation = "内网IP"
}

// CreateJWTToken 创建JWT Token
func (s *TokenServiceImpl) CreateJWTToken(claims map[string]interface{}) string {
	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = jwt.MapClaims(claims)
	tokenString, err := token.SignedString([]byte(s.jwtConfig.Secret))
	if err != nil {
		s.logger.Error("生成Token失败", zap.Error(err))
		return ""
	}
	return tokenString
}

// ParseToken 解析Token
func (s *TokenServiceImpl) ParseToken(token string) *model.Claims {
	token = strings.TrimPrefix(token, TokenPrefix)
	tokenClaims, err := jwt.ParseWithClaims(token, &model.Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.jwtConfig.Secret), nil
	})

	if err != nil {
		s.logger.Error("解析Token失败", zap.Error(err))
		return nil
	}

	if claims, ok := tokenClaims.Claims.(*model.Claims); ok && tokenClaims.Valid {
		return claims
	}

	return nil
}

// GetUsernameFromToken 从Token中获取用户名
func (s *TokenServiceImpl) GetUsernameFromToken(token string) string {
	claims := s.ParseToken(token)
	if claims != nil {
		return claims.GetUsername()
	}
	return ""
}

// GetToken 获取请求Token
func (s *TokenServiceImpl) GetToken(ctx *gin.Context) string {
	token := ctx.GetHeader(TokenHeader)
	if token != "" && strings.HasPrefix(token, TokenPrefix) {
		return token
	}
	return ""
}

// GetTokenKey 获取Token缓存key
func (s *TokenServiceImpl) GetTokenKey(uuid string) string {
	return fmt.Sprintf("%s%s", TokenKey, uuid)
}
