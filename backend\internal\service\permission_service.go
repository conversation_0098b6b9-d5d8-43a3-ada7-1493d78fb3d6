package service

// PermissionService 权限服务接口
type PermissionService interface {
	// HasPermission 判断是否有权限
	HasPermission(permission string) bool

	// HasRole 判断是否有角色
	HasRole(role string) bool

	// HasAnyRole 判断是否有任意一个角色
	HasAnyRole(roles ...string) bool

	// HasAllRoles 判断是否有所有角色
	HasAllRoles(roles ...string) bool
}

// PermissionServiceImpl 权限服务实现
type PermissionServiceImpl struct {
	tokenService TokenService
}

// NewPermissionServiceImpl 创建权限服务实现
func NewPermissionServiceImpl(tokenService TokenService) PermissionService {
	return &PermissionServiceImpl{
		tokenService: tokenService,
	}
}

// HasPermission 判断是否有权限
func (s *PermissionServiceImpl) HasPermission(permission string) bool {
	// 超级管理员拥有所有权限
	if s.HasRole("admin") {
		return true
	}

	// TODO: 实现权限判断逻辑
	return true
}

// HasRole 判断是否有角色
func (s *PermissionServiceImpl) HasRole(role string) bool {
	// TODO: 实现角色判断逻辑
	return true
}

// HasAnyRole 判断是否有任意一个角色
func (s *PermissionServiceImpl) HasAnyRole(roles ...string) bool {
	// TODO: 实现角色判断逻辑
	return true
}

// HasAllRoles 判断是否有所有角色
func (s *PermissionServiceImpl) HasAllRoles(roles ...string) bool {
	// TODO: 实现角色判断逻辑
	return true
}
