package service

import (
	"github.com/ruoyi/backend/internal/model"
)

// SysLoginService 系统登录服务
type SysLoginService interface {
	// Login 登录验证
	Login(username string, password string, code string, uuid string) (string, error)

	// ValidateCaptcha 验证验证码
	ValidateCaptcha(username string, code string, uuid string) error

	// LoginPreCheck 登录前置校验
	LoginPreCheck(username string, password string) error

	// RecordLoginInfo 记录登录信息
	RecordLoginInfo(userId int64) error

	// Logout 退出登录
	Logout(token string) error

	// GetUserInfo 获取用户信息
	GetUserInfo(userId int64) (map[string]interface{}, error)

	// GetRouters 获取路由信息
	GetRouters(userId int64) ([]model.Router, error)

	// GenerateCaptcha 生成验证码
	GenerateCaptcha() (string, string, error)
}
