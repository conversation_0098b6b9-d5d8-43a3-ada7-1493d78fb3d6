package service

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"
)

// SysPasswordService 密码服务接口
type SysPasswordService interface {
	// EncryptPassword 加密密码
	EncryptPassword(password string) string

	// MatchPassword 匹配密码
	MatchPassword(rawPassword string, encodedPassword string) bool

	// ValidatePassword 验证密码
	ValidatePassword(password string) error
}

// SysPasswordServiceImpl 密码服务实现
type SysPasswordServiceImpl struct {
	// 密码加密盐值
	salt string
}

// NewSysPasswordServiceImpl 创建密码服务实现
func NewSysPasswordServiceImpl(salt string) SysPasswordService {
	return &SysPasswordServiceImpl{
		salt: salt,
	}
}

// EncryptPassword 加密密码
func (s *SysPasswordServiceImpl) EncryptPassword(password string) string {
	// 加盐MD5加密
	return s.md5Encrypt(fmt.Sprintf("%s%s", password, s.salt))
}

// MatchPassword 匹配密码
func (s *SysPasswordServiceImpl) MatchPassword(rawPassword string, encodedPassword string) bool {
	// 加密后比较
	return strings.EqualFold(s.EncryptPassword(rawPassword), encodedPassword)
}

// ValidatePassword 验证密码
func (s *SysPasswordServiceImpl) ValidatePassword(password string) error {
	// TODO: 实现密码强度验证
	return nil
}

// md5Encrypt MD5加密
func (s *SysPasswordServiceImpl) md5Encrypt(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}
