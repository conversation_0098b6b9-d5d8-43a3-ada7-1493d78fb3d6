package service

import (
	"errors"
	"time"

	"github.com/ruoyi/backend/internal/domain"
	"go.uber.org/zap"
)

// SysRegisterService 注册服务接口
type SysRegisterService interface {
	// Register 注册
	Register(user domain.SysUser) error

	// ValidateUsername 验证用户名
	ValidateUsername(username string) error

	// ValidateEmail 验证邮箱
	ValidateEmail(email string) error

	// ValidatePhone 验证手机号
	ValidatePhone(phone string) error
}

// SysRegisterServiceImpl 注册服务实现
type SysRegisterServiceImpl struct {
	logger          *zap.Logger
	userService     ISysUserService
	passwordService SysPasswordService
	configService   ISysConfigService
}

// NewSysRegisterServiceImpl 创建注册服务实现
func NewSysRegisterServiceImpl(
	logger *zap.Logger,
	userService ISysUserService,
	passwordService SysPasswordService,
	configService ISysConfigService,
) SysRegisterService {
	return &SysRegisterServiceImpl{
		logger:          logger,
		userService:     userService,
		passwordService: passwordService,
		configService:   configService,
	}
}

// Register 注册
func (s *SysRegisterServiceImpl) Register(user domain.SysUser) error {
	// 验证用户名
	if err := s.ValidateUsername(user.UserName); err != nil {
		return err
	}

	// 验证邮箱
	if err := s.ValidateEmail(user.Email); err != nil {
		return err
	}

	// 验证手机号
	if err := s.ValidatePhone(user.Phonenumber); err != nil {
		return err
	}

	// 验证密码
	if err := s.passwordService.ValidatePassword(user.Password); err != nil {
		return err
	}

	// 设置用户默认值
	user.CreateBy = "注册用户"
	currentTime := time.Now()
	user.CreateTime = &currentTime
	user.Status = "0"
	user.DelFlag = "0"

	// 加密密码
	user.Password = s.passwordService.EncryptPassword(user.Password)

	// 注册用户
	if !s.userService.RegisterUser(user) {
		return errors.New("注册失败，请联系管理员")
	}

	return nil
}

// ValidateUsername 验证用户名
func (s *SysRegisterServiceImpl) ValidateUsername(username string) error {
	// 检查是否允许注册
	config := s.configService.SelectConfigByKey("sys.account.registerUser")
	if config.ConfigId == 0 || config.ConfigValue != "true" {
		return errors.New("当前系统没有开启注册功能")
	}

	// 用户名不能为空
	if username == "" {
		return errors.New("用户名不能为空")
	}

	// 用户名长度限制
	if len(username) < 2 || len(username) > 20 {
		return errors.New("用户名长度必须在2到20个字符之间")
	}

	// 用户名唯一性校验
	user := domain.SysUser{
		UserName: username,
	}
	if !s.userService.CheckUserNameUnique(user) {
		return errors.New("保存用户'" + username + "'失败，注册账号已存在")
	}

	return nil
}

// ValidateEmail 验证邮箱
func (s *SysRegisterServiceImpl) ValidateEmail(email string) error {
	// 邮箱可以为空
	if email == "" {
		return nil
	}

	// 邮箱唯一性校验
	user := domain.SysUser{
		Email: email,
	}
	if !s.userService.CheckEmailUnique(user) {
		return errors.New("保存用户失败，邮箱账号已存在")
	}

	return nil
}

// ValidatePhone 验证手机号
func (s *SysRegisterServiceImpl) ValidatePhone(phone string) error {
	// 手机号可以为空
	if phone == "" {
		return nil
	}

	// 手机号唯一性校验
	user := domain.SysUser{
		Phonenumber: phone,
	}
	if !s.userService.CheckPhoneUnique(user) {
		return errors.New("保存用户失败，手机号码已存在")
	}

	return nil
}
