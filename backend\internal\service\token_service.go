package service

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/model"
)

// TokenService Service interface
type TokenService interface {
	GetLoginUser(ctx *gin.Context) *model.LoginUser
	SetLoginUser(loginUser *model.LoginUser)
	DelLoginUser(token string)
	CreateToken(loginUser *model.LoginUser) string
	VerifyToken(loginUser *model.LoginUser)
	RefreshToken(loginUser *model.LoginUser)
	SetUserAgent(loginUser *model.LoginUser)
	CreateJWTToken(claims map[string]interface{}) string
	ParseToken(token string) *model.Claims
	GetUsernameFromToken(token string) string
	GetToken(ctx *gin.Context) string
	GetTokenKey(uuid string) string
}
