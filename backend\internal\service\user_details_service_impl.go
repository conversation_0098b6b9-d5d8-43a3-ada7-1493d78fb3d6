package service

import (
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// UserDetailsService 用户详情服务接口
type UserDetailsService interface {
	// LoadUserByUsername 根据用户名加载用户
	LoadUserByUsername(username string) *model.LoginUser

	// CreateLoginUser 创建登录用户
	CreateLoginUser(user domain.SysUser) *model.LoginUser
}

// UserDetailsServiceImpl 用户详情服务实现
type UserDetailsServiceImpl struct {
	logger      *zap.Logger
	userService ISysUserService
}

// NewUserDetailsServiceImpl 创建用户详情服务实现
func NewUserDetailsServiceImpl(
	logger *zap.Logger,
	userService ISysUserService,
) UserDetailsService {
	return &UserDetailsServiceImpl{
		logger:      logger,
		userService: userService,
	}
}

// LoadUserByUsername 根据用户名加载用户
func (s *UserDetailsServiceImpl) LoadUserByUsername(username string) *model.LoginUser {
	// 查询用户信息
	user := s.userService.SelectUserByUserName(username)
	if user.UserId == 0 {
		s.logger.Error("登录用户不存在", zap.String("username", username))
		return nil
	}

	// 创建登录用户
	return s.CreateLoginUser(user)
}

// CreateLoginUser 创建登录用户
func (s *UserDetailsServiceImpl) CreateLoginUser(user domain.SysUser) *model.LoginUser {
	// 创建登录用户
	loginUser := &model.LoginUser{
		UserId:      user.UserId,
		DeptId:      user.DeptId,
		User:        user,
		Permissions: []string{"*:*:*"}, // 默认拥有所有权限
	}

	return loginUser
}
