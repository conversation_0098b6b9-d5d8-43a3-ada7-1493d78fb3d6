package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysConfigController 参数配置信息控制器
type SysConfigController struct {
	controller.BaseController
	configService service.ISysConfigService
}

// NewSysConfigController 创建参数配置控制器
func NewSysConfigController(
	logger *zap.Logger,
	configService service.ISysConfigService,
) *SysConfigController {
	return &SysConfigController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		configService: configService,
	}
}

// RegisterRoutes 注册路由
func (c *SysConfigController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:configId", c.GetInfo)
	r.GET("/configKey/:configKey", c.GetConfigKey)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:configIds", c.Remove)
	r.DELETE("/refreshCache", c.RefreshCache)
}

// List 获取参数配置列表
func (c *SysConfigController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	config := &domain.SysConfig{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取参数配置列表
	list := c.configService.SelectConfigList(*config)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// Export 导出参数配置
func (c *SysConfigController) Export(ctx *gin.Context) {
	// 构建查询条件
	config := &domain.SysConfig{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取参数配置列表
	list := c.configService.SelectConfigList(*config)

	// TODO: 实现Excel导出
	c.SuccessWithMessage(ctx, "导出成功")
}

// GetInfo 根据参数编号获取详细信息
func (c *SysConfigController) GetInfo(ctx *gin.Context) {
	configIdStr := ctx.Param("configId")
	configId, err := strconv.ParseInt(configIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "参数ID格式错误")
		return
	}

	// 获取参数配置信息
	config := c.configService.SelectConfigById(configId)
	c.SuccessWithData(ctx, config)
}

// GetConfigKey 根据参数键名查询参数值
func (c *SysConfigController) GetConfigKey(ctx *gin.Context) {
	configKey := ctx.Param("configKey")

	// 获取参数值
	value := c.configService.SelectConfigByKey(configKey)
	c.SuccessWithData(ctx, value)
}

// Add 新增参数配置
func (c *SysConfigController) Add(ctx *gin.Context) {
	var config domain.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验参数键名唯一性
	if !c.configService.CheckConfigKeyUnique(config) {
		c.ErrorWithMessage(ctx, "新增参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置创建者
	config.CreateBy = c.GetUsername(ctx)

	// 新增参数配置
	rows := c.configService.InsertConfig(config)
	c.ToAjax(ctx, rows)
}

// Edit 修改参数配置
func (c *SysConfigController) Edit(ctx *gin.Context) {
	var config domain.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验参数键名唯一性
	if !c.configService.CheckConfigKeyUnique(config) {
		c.ErrorWithMessage(ctx, "修改参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置更新者
	config.UpdateBy = c.GetUsername(ctx)

	// 更新参数配置
	rows := c.configService.UpdateConfig(config)
	c.ToAjax(ctx, rows)
}

// Remove 删除参数配置
func (c *SysConfigController) Remove(ctx *gin.Context) {
	configIdsStr := ctx.Param("configIds")
	configIds := strings.Split(configIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(configIds))
	for _, idStr := range configIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 删除参数配置
	c.configService.DeleteConfigByIds(ids)
	c.Success(ctx)
}

// RefreshCache 刷新参数缓存
func (c *SysConfigController) RefreshCache(ctx *gin.Context) {
	c.configService.ResetConfigCache()
	c.Success(ctx)
}
