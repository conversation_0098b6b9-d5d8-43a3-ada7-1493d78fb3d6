package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysDictTypeController 字典类型控制器
type SysDictTypeController struct {
	controller.BaseController
	dictTypeService service.ISysDictTypeService
}

// NewSysDictTypeController 创建字典类型控制器
func NewSysDictTypeController(
	logger *zap.Logger,
	dictTypeService service.ISysDictTypeService,
) *SysDictTypeController {
	return &SysDictTypeController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		dictTypeService: dictTypeService,
	}
}

// RegisterRoutes 注册路由
func (c *SysDictTypeController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:dictId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:dictIds", c.Remove)
	r.DELETE("/refreshCache", c.RefreshCache)
	r.GET("/optionselect", c.Optionselect)
}

// List 获取字典类型列表
func (c *SysDictTypeController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	dictType := &domain.SysDictType{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取字典类型列表
	list := c.dictTypeService.SelectDictTypeList(*dictType)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// Export 导出字典类型
func (c *SysDictTypeController) Export(ctx *gin.Context) {
	// 构建查询条件
	dictType := &domain.SysDictType{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取字典类型列表
	list := c.dictTypeService.SelectDictTypeList(*dictType)

	// TODO: 实现Excel导出
	c.SuccessWithMessage(ctx, "导出成功")
}

// GetInfo 根据字典类型编号获取详细信息
func (c *SysDictTypeController) GetInfo(ctx *gin.Context) {
	dictIdStr := ctx.Param("dictId")
	dictId, err := strconv.ParseInt(dictIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "字典类型ID格式错误")
		return
	}

	// 获取字典类型信息
	dictType := c.dictTypeService.SelectDictTypeById(dictId)
	c.SuccessWithData(ctx, dictType)
}

// Add 新增字典类型
func (c *SysDictTypeController) Add(ctx *gin.Context) {
	var dictType domain.SysDictType
	if err := ctx.ShouldBindJSON(&dictType); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验字典类型唯一性
	if !c.dictTypeService.CheckDictTypeUnique(dictType) {
		c.ErrorWithMessage(ctx, "新增字典'"+dictType.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置创建者
	dictType.CreateBy = c.GetUsername(ctx)

	// 新增字典类型
	rows := c.dictTypeService.InsertDictType(dictType)
	c.ToAjax(ctx, rows)
}

// Edit 修改字典类型
func (c *SysDictTypeController) Edit(ctx *gin.Context) {
	var dictType domain.SysDictType
	if err := ctx.ShouldBindJSON(&dictType); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验字典类型唯一性
	if !c.dictTypeService.CheckDictTypeUnique(dictType) {
		c.ErrorWithMessage(ctx, "修改字典'"+dictType.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置更新者
	dictType.UpdateBy = c.GetUsername(ctx)

	// 更新字典类型
	rows := c.dictTypeService.UpdateDictType(dictType)
	c.ToAjax(ctx, rows)
}

// Remove 删除字典类型
func (c *SysDictTypeController) Remove(ctx *gin.Context) {
	dictIdsStr := ctx.Param("dictIds")
	dictIds := strings.Split(dictIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(dictIds))
	for _, idStr := range dictIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 删除字典类型
	c.dictTypeService.DeleteDictTypeByIds(ids)
	c.Success(ctx)
}

// RefreshCache 刷新字典缓存
func (c *SysDictTypeController) RefreshCache(ctx *gin.Context) {
	c.dictTypeService.ResetDictCache()
	c.Success(ctx)
}

// Optionselect 获取字典选择框列表
func (c *SysDictTypeController) Optionselect(ctx *gin.Context) {
	dictTypes := c.dictTypeService.SelectDictTypeAll()
	c.SuccessWithData(ctx, dictTypes)
}
