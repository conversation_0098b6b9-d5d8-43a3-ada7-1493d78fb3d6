package system

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/config"
	"go.uber.org/zap"
)

// SysIndexController 首页控制器
type SysIndexController struct {
	controller.BaseController
	appConfig *config.AppConfig
}

// NewSysIndexController 创建首页控制器
func NewSysIndexController(
	logger *zap.Logger,
	appConfig *config.AppConfig,
) *SysIndexController {
	return &SysIndexController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		appConfig: appConfig,
	}
}

// RegisterRoutes 注册路由
func (c *SysIndexController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/", c.Index)
}

// Index 访问首页，提示语
func (c *SysIndexController) Index(ctx *gin.Context) {
	welcomeMsg := fmt.Sprintf("欢迎使用%s后台管理框架，当前版本：v%s，请通过前端地址访问。",
		c.appConfig.GetName(), c.appConfig.GetVersion())
	ctx.String(200, welcomeMsg)
}
