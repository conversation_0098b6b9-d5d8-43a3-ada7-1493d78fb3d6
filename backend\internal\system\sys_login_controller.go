package system

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysLoginController 登录验证控制器
type SysLoginController struct {
	controller.BaseController
	loginService service.SysLoginService
	tokenService service.TokenService
}

// NewSysLoginController 创建登录控制器
func NewSysLoginController(
	logger *zap.Logger,
	loginService service.SysLoginService,
	tokenService service.TokenService,
) *SysLoginController {
	return &SysLoginController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		loginService: loginService,
		tokenService: tokenService,
	}
}

// RegisterRoutes 注册路由
func (c *SysLoginController) RegisterRoutes(r *gin.RouterGroup) {
	r.POST("/login", c.Login)
	r.GET("/getInfo", c.GetInfo)
	r.GET("/getRouters", c.GetRouters)
	r.POST("/logout", c.Logout)
	r.GET("/captchaImage", c.GetCaptchaImage)
}

// Login 登录方法
func (c *SysLoginController) Login(ctx *gin.Context) {
	var loginBody model.LoginBody
	if err := ctx.ShouldBindJSON(&loginBody); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 用户登录
	token, err := c.loginService.Login(loginBody.Username, loginBody.Password, loginBody.Code, loginBody.Uuid)
	if err != nil {
		c.ErrorWithMessage(ctx, err.Error())
		return
	}

	c.SuccessWithData(ctx, map[string]string{
		"token": token,
	})
}

// GetInfo 获取用户信息
func (c *SysLoginController) GetInfo(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := c.GetUserId(ctx)
	if userId == 0 {
		c.ErrorWithMessage(ctx, "获取用户信息失败")
		return
	}

	// 获取用户信息
	userInfo, err := c.loginService.GetUserInfo(userId)
	if err != nil {
		c.ErrorWithMessage(ctx, err.Error())
		return
	}

	c.SuccessWithData(ctx, userInfo)
}

// GetRouters 获取路由信息
func (c *SysLoginController) GetRouters(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := c.GetUserId(ctx)
	if userId == 0 {
		c.ErrorWithMessage(ctx, "获取路由信息失败")
		return
	}

	// 获取路由信息
	routers, err := c.loginService.GetRouters(userId)
	if err != nil {
		c.ErrorWithMessage(ctx, err.Error())
		return
	}

	c.SuccessWithData(ctx, routers)
}

// Logout 退出登录
func (c *SysLoginController) Logout(ctx *gin.Context) {
	// 从请求头中获取token
	token := ctx.GetHeader("Authorization")
	if token == "" {
		c.Success(ctx)
		return
	}

	// 去除Bearer前缀
	if len(token) > 7 && token[:7] == "Bearer " {
		token = token[7:]
	}

	// 退出登录
	err := c.loginService.Logout(token)
	if err != nil {
		c.ErrorWithMessage(ctx, err.Error())
		return
	}

	c.Success(ctx)
}

// GetCaptchaImage 获取验证码
func (c *SysLoginController) GetCaptchaImage(ctx *gin.Context) {
	// 生成验证码
	uuid, img, err := c.loginService.GenerateCaptcha()
	if err != nil {
		c.ErrorWithMessage(ctx, err.Error())
		return
	}

	c.SuccessWithData(ctx, map[string]interface{}{
		"uuid": uuid,
		"img":  img,
	})
}
