package system

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/pkg/utils"
	"go.uber.org/zap"
)

// SysMenuController 菜单信息控制器
type SysMenuController struct {
	controller.BaseController
	menuService service.ISysMenuService
}

// NewSysMenuController 创建菜单控制器
func NewSysMenuController(
	logger *zap.Logger,
	menuService service.ISysMenuService,
) *SysMenuController {
	return &SysMenuController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		menuService: menuService,
	}
}

// RegisterRoutes 注册路由
func (c *SysMenuController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.GET("/:menuId", c.GetInfo)
	r.GET("/treeselect", c.Treeselect)
	r.GET("/roleMenuTreeselect/:roleId", c.RoleMenuTreeselect)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:menuId", c.Remove)
}

// List 获取菜单列表
func (c *SysMenuController) List(ctx *gin.Context) {
	// 构建查询条件
	menu := &domain.SysMenu{}
	// 从请求中绑定参数
	if err := c.BindQuery(ctx, menu); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取菜单列表
	menus := c.menuService.SelectMenuList(*menu, c.GetUserId(ctx))
	c.SuccessWithData(ctx, menus)
}

// GetInfo 根据菜单编号获取详细信息
func (c *SysMenuController) GetInfo(ctx *gin.Context) {
	menuIdStr := ctx.Param("menuId")
	menuId, err := strconv.ParseInt(menuIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "菜单ID格式错误")
		return
	}

	// 获取菜单信息
	menu := c.menuService.SelectMenuById(menuId)
	c.SuccessWithData(ctx, menu)
}

// Treeselect 获取菜单下拉树列表
func (c *SysMenuController) Treeselect(ctx *gin.Context) {
	// 构建查询条件
	menu := &domain.SysMenu{}
	// 从请求中绑定参数
	if err := c.BindQuery(ctx, menu); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取菜单列表
	menus := c.menuService.SelectMenuList(*menu, c.GetUserId(ctx))

	// 构建树形结构
	treeSelect := c.menuService.BuildMenuTreeSelect(menus)
	c.SuccessWithData(ctx, treeSelect)
}

// RoleMenuTreeselect 加载对应角色菜单列表树
func (c *SysMenuController) RoleMenuTreeselect(ctx *gin.Context) {
	roleIdStr := ctx.Param("roleId")
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 获取菜单列表
	emptyMenu := domain.SysMenu{}
	menus := c.menuService.SelectMenuList(emptyMenu, c.GetUserId(ctx))

	// 获取角色已选择的菜单
	checkedKeys := c.menuService.SelectMenuListByRoleId(roleId)

	// 构建树形结构
	treeSelect := c.menuService.BuildMenuTreeSelect(menus)

	c.SuccessWithData(ctx, map[string]interface{}{
		"checkedKeys": checkedKeys,
		"menus":       treeSelect,
	})
}

// Add 新增菜单
func (c *SysMenuController) Add(ctx *gin.Context) {
	var menu domain.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验菜单名称唯一性
	if !c.menuService.CheckMenuNameUnique(menu) {
		c.ErrorWithMessage(ctx, "新增菜单'"+menu.MenuName+"'失败，菜单名称已存在")
		return
	}

	// 校验外链地址
	if menu.IsFrame == "1" && !utils.IsHTTP(menu.Path) {
		c.ErrorWithMessage(ctx, "新增菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头")
		return
	}

	// 设置创建者
	menu.CreateBy = c.GetUsername(ctx)

	// 新增菜单
	rows := c.menuService.InsertMenu(menu)
	c.ToAjax(ctx, int64(rows))
}

// Edit 修改菜单
func (c *SysMenuController) Edit(ctx *gin.Context) {
	var menu domain.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验菜单名称唯一性
	if !c.menuService.CheckMenuNameUnique(menu) {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，菜单名称已存在")
		return
	}

	// 校验外链地址
	if menu.IsFrame == "1" && !utils.IsHTTP(menu.Path) {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头")
		return
	}

	// 校验上级菜单不能选择自己
	if menu.MenuId == menu.ParentId {
		c.ErrorWithMessage(ctx, "修改菜单'"+menu.MenuName+"'失败，上级菜单不能选择自己")
		return
	}

	// 设置更新者
	menu.UpdateBy = c.GetUsername(ctx)

	// 更新菜单
	rows := c.menuService.UpdateMenu(menu)
	c.ToAjax(ctx, int64(rows))
}

// Remove 删除菜单
func (c *SysMenuController) Remove(ctx *gin.Context) {
	menuIdStr := ctx.Param("menuId")
	menuId, err := strconv.ParseInt(menuIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "菜单ID格式错误")
		return
	}

	// 检查是否存在子菜单
	if c.menuService.HasChildByMenuId(menuId) {
		c.Warn(ctx, "存在子菜单,不允许删除")
		return
	}

	// 检查菜单是否已分配
	if c.menuService.CheckMenuExistRole(menuId) {
		c.Warn(ctx, "菜单已分配,不允许删除")
		return
	}

	// 删除菜单
	rows := c.menuService.DeleteMenuById(menuId)
	c.ToAjax(ctx, int64(rows))
}
