package system

import (
	"path"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/pkg/utils"
	"go.uber.org/zap"
)

// SysProfileController 个人信息控制器
type SysProfileController struct {
	controller.BaseController
	userService  service.ISysUserService
	tokenService service.TokenService
	appConfig    *config.AppConfig
}

// NewSysProfileController 创建个人信息控制器
func NewSysProfileController(
	logger *zap.Logger,
	userService service.ISysUserService,
	tokenService service.TokenService,
	appConfig *config.AppConfig,
) *SysProfileController {
	return &SysProfileController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		userService:  userService,
		tokenService: tokenService,
		appConfig:    appConfig,
	}
}

// RegisterRoutes 注册路由
func (c *SysProfileController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("", c.Profile)
	r.PUT("", c.UpdateProfile)
	r.PUT("/updatePwd", c.UpdatePwd)
	r.POST("/avatar", c.Avatar)
}

// Profile 获取个人信息
func (c *SysProfileController) Profile(ctx *gin.Context) {
	// 获取登录用户信息
	loginUser := c.GetLoginUser(ctx)
	user := loginUser.User

	// 构建返回结果
	result := c.Success(ctx)
	result.Data = user
	result.Put("roleGroup", c.userService.SelectUserRoleGroup(loginUser.Username))
	result.Put("postGroup", c.userService.SelectUserPostGroup(loginUser.Username))
}

// UpdateProfile 修改个人信息
func (c *SysProfileController) UpdateProfile(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 获取当前登录用户
	loginUser := c.GetLoginUser(ctx)
	currentUser := loginUser.User

	// 更新用户信息
	currentUser.NickName = user.NickName
	currentUser.Email = user.Email
	currentUser.Phonenumber = user.Phonenumber
	currentUser.Sex = user.Sex

	// 校验手机号唯一性
	if user.Phonenumber != "" && !c.userService.CheckPhoneUnique(currentUser) {
		c.ErrorWithMessage(ctx, "修改用户'"+loginUser.Username+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(currentUser) {
		c.ErrorWithMessage(ctx, "修改用户'"+loginUser.Username+"'失败，邮箱账号已存在")
		return
	}

	// 更新用户信息
	if c.userService.UpdateUserProfile(currentUser) > 0 {
		// 更新缓存用户信息
		c.tokenService.SetLoginUser(loginUser)
		c.Success(ctx)
		return
	}

	c.ErrorWithMessage(ctx, "修改个人信息异常，请联系管理员")
}

// UpdatePwd 修改密码
func (c *SysProfileController) UpdatePwd(ctx *gin.Context) {
	var params map[string]string
	if err := ctx.ShouldBindJSON(&params); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	oldPassword := params["oldPassword"]
	newPassword := params["newPassword"]

	// 获取当前登录用户
	loginUser := c.GetLoginUser(ctx)
	userId := loginUser.UserId
	password := loginUser.Password

	// 校验旧密码
	if !utils.MatchesPassword(oldPassword, password) {
		c.ErrorWithMessage(ctx, "修改密码失败，旧密码错误")
		return
	}

	// 校验新密码不能与旧密码相同
	if utils.MatchesPassword(newPassword, password) {
		c.ErrorWithMessage(ctx, "新密码不能与旧密码相同")
		return
	}

	// 加密新密码
	newPassword = utils.EncryptPassword(newPassword)

	// 更新密码
	if c.userService.ResetUserPwd(userId, newPassword) > 0 {
		// 更新缓存用户密码和密码最后更新时间
		loginUser.User.PwdUpdateDate = time.Now()
		loginUser.User.Password = newPassword
		c.tokenService.SetLoginUser(loginUser)
		c.Success(ctx)
		return
	}

	c.ErrorWithMessage(ctx, "修改密码异常，请联系管理员")
}

// Avatar 上传头像
func (c *SysProfileController) Avatar(ctx *gin.Context) {
	file, err := ctx.FormFile("avatarfile")
	if err != nil || file == nil {
		c.ErrorWithMessage(ctx, "请选择要上传的文件")
		return
	}

	// 获取当前登录用户
	loginUser := c.GetLoginUser(ctx)

	// 上传文件
	avatarPath := c.appConfig.GetAvatarPath()
	fileName, err := utils.UploadFile(ctx, file, avatarPath, utils.ImageExtension)
	if err != nil {
		c.ErrorWithMessage(ctx, "上传图片异常，请联系管理员")
		return
	}

	// 更新用户头像
	avatar := path.Join(avatarPath, fileName)
	if c.userService.UpdateUserAvatar(loginUser.UserId, avatar) {
		// 删除旧头像
		oldAvatar := loginUser.User.Avatar
		if oldAvatar != "" {
			utils.DeleteFile(path.Join(c.appConfig.GetProfile(), utils.StripPrefix(oldAvatar)))
		}

		// 更新缓存用户头像
		loginUser.User.Avatar = avatar
		c.tokenService.SetLoginUser(loginUser)

		result := c.Success(ctx)
		result.Put("imgUrl", avatar)
		return
	}

	c.ErrorWithMessage(ctx, "上传图片异常，请联系管理员")
}
