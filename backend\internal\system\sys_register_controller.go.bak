package system

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/pkg/utils"
	"go.uber.org/zap"
)

// SysRegisterController 注册验证控制器
type SysRegisterController struct {
	controller.BaseController
	registerService service.SysRegisterService
	configService   service.ISysConfigService
}

// NewSysRegisterController 创建注册控制器
func NewSysRegisterController(
	logger *zap.Logger,
	registerService service.SysRegisterService,
	configService service.ISysConfigService,
) *SysRegisterController {
	return &SysRegisterController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		registerService: registerService,
		configService:   configService,
	}
}

// RegisterRoutes 注册路由
func (c *SysRegisterController) RegisterRoutes(r *gin.RouterGroup) {
	r.POST("/register", c.Register)
}

// Register 用户注册
func (c *SysRegisterController) Register(ctx *gin.Context) {
	var user model.RegisterBody
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查系统是否开启注册功能
	if !("true" == c.configService.SelectConfigByKey("sys.account.registerUser")) {
		c.ErrorWithMessage(ctx, "当前系统没有开启注册功能！")
		return
	}

	// 执行注册
	msg := c.registerService.Register(user)
	if utils.IsEmpty(msg) {
		c.Success(ctx)
	} else {
		c.ErrorWithMessage(ctx, msg)
	}
}
