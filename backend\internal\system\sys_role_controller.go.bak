package system

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

// SysRoleController 角色信息控制器
type SysRoleController struct {
	controller.BaseController
	roleService       service.ISysRoleService
	tokenService      service.TokenService
	permissionService service.SysPermissionService
	userService       service.ISysUserService
	deptService       service.ISysDeptService
}

// NewSysRoleController 创建角色控制器
func NewSysRoleController(
	logger *zap.Logger,
	roleService service.ISysRoleService,
	tokenService service.TokenService,
	permissionService service.SysPermissionService,
	userService service.ISysUserService,
	deptService service.ISysDeptService,
) *SysRoleController {
	return &SysRoleController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		roleService:       roleService,
		tokenService:      tokenService,
		permissionService: permissionService,
		userService:       userService,
		deptService:       deptService,
	}
}

// RegisterRoutes 注册路由
func (c *SysRoleController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:roleId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.PUT("/dataScope", c.DataScope)
	r.PUT("/changeStatus", c.ChangeStatus)
	r.DELETE("/:roleIds", c.Remove)
	r.GET("/optionselect", c.Optionselect)
	r.GET("/authUser/allocatedList", c.AllocatedList)
	r.GET("/authUser/unallocatedList", c.UnallocatedList)
	r.PUT("/authUser/cancel", c.CancelAuthUser)
	r.PUT("/authUser/cancelAll", c.CancelAuthUserAll)
	r.PUT("/authUser/selectAll", c.SelectAuthUserAll)
	r.GET("/deptTree/:roleId", c.DeptTree)
}

// List 获取角色列表
func (c *SysRoleController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	role := &domain.SysRole{}
	// 从请求中绑定参数
	if err := c.BindQuery(ctx, role); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取角色列表
	list := c.roleService.SelectRoleList(*role)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// Export 导出角色
func (c *SysRoleController) Export(ctx *gin.Context) {
	// 构建查询条件
	role := &domain.SysRole{}
	// 从请求中绑定参数
	if err := c.BindForm(ctx, role); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取角色列表
	list := c.roleService.SelectRoleList(*role)

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			c.Logger.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 创建Sheet
	sheetName := "角色数据"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		c.ErrorWithMessage(ctx, "创建Excel Sheet失败")
		return
	}
	f.SetActiveSheet(index)

	// 设置表头
	headers := []string{"角色序号", "角色名称", "角色权限", "角色排序", "数据范围", "角色状态", "创建时间", "备注"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c%d", 'A'+i, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for i, r := range list {
		rowIndex := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIndex), r.RoleId)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIndex), r.RoleName)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIndex), r.RoleKey)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIndex), r.RoleSort)

		// 数据范围转换
		var dataScope string
		switch r.DataScope {
		case "1":
			dataScope = "所有数据权限"
		case "2":
			dataScope = "自定义数据权限"
		case "3":
			dataScope = "本部门数据权限"
		case "4":
			dataScope = "本部门及以下数据权限"
		case "5":
			dataScope = "仅本人数据权限"
		default:
			dataScope = "未知"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIndex), dataScope)

		// 状态转换
		var status string
		switch r.Status {
		case "0":
			status = "正常"
		case "1":
			status = "停用"
		default:
			status = "未知"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIndex), status)

		f.SetCellValue(sheetName, fmt.Sprintf("G%d", rowIndex), r.CreateTime)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", rowIndex), r.Remark)
	}

	// 设置文件名
	fileName := fmt.Sprintf("角色数据_%s.xlsx", time.Now().Format("20060102150405"))

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(ctx.Writer); err != nil {
		c.ErrorWithMessage(ctx, "导出Excel失败")
		return
	}
}

// GetInfo 根据角色编号获取详细信息
func (c *SysRoleController) GetInfo(ctx *gin.Context) {
	roleIdStr := ctx.Param("roleId")
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 检查数据权限
	c.roleService.CheckRoleDataScope(roleId)

	// 获取角色信息
	role := c.roleService.SelectRoleById(roleId)
	c.SuccessWithData(ctx, role)
}

// Add 新增角色
func (c *SysRoleController) Add(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	if !c.roleService.CheckRoleNameUnique(role) {
		c.ErrorWithMessage(ctx, "新增角色'"+role.RoleName+"'失败，角色名称已存在")
		return
	} else if !c.roleService.CheckRoleKeyUnique(role) {
		c.ErrorWithMessage(ctx, "新增角色'"+role.RoleName+"'失败，角色权限已存在")
		return
	}

	// 设置创建者
	role.CreateBy = c.GetUsername(ctx)

	// 新增角色
	rows := c.roleService.InsertRole(role)
	c.ToAjax(ctx, rows)
}

// Edit 修改保存角色
func (c *SysRoleController) Edit(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作角色
	c.roleService.CheckRoleAllowed(role)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(role.RoleId)

	if !c.roleService.CheckRoleNameUnique(role) {
		c.ErrorWithMessage(ctx, "修改角色'"+role.RoleName+"'失败，角色名称已存在")
		return
	} else if !c.roleService.CheckRoleKeyUnique(role) {
		c.ErrorWithMessage(ctx, "修改角色'"+role.RoleName+"'失败，角色权限已存在")
		return
	}

	// 设置更新者
	role.UpdateBy = c.GetUsername(ctx)

	// 更新角色
	rows := c.roleService.UpdateRole(role)

	// 更新缓存用户权限
	if rows > 0 {
		// 获取当前用户ID
		userId := c.GetUserId(ctx)

		// 如果不是管理员，更新当前用户的权限
		if userId > 0 {
			// 获取当前登录用户
			loginUser, err := c.tokenService.GetLoginUser(ctx)
			if err == nil && loginUser != nil {
				// 刷新用户权限
				c.permissionService.SetRolePermission(loginUser.User, loginUser.User.GetRoles())
				// 更新缓存
				c.tokenService.SetLoginUser(loginUser)
			}
		}

		c.Success(ctx)
	} else {
		c.ErrorWithMessage(ctx, "修改角色'"+role.RoleName+"'失败，请联系管理员")
	}
}

// DataScope 修改保存数据权限
func (c *SysRoleController) DataScope(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作角色
	c.roleService.CheckRoleAllowed(role)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(role.RoleId)

	// 保存数据权限
	rows := c.roleService.AuthDataScope(role)
	c.ToAjax(ctx, rows)
}

// ChangeStatus 状态修改
func (c *SysRoleController) ChangeStatus(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作角色
	c.roleService.CheckRoleAllowed(role)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(role.RoleId)

	// 设置更新者
	role.UpdateBy = c.GetUsername(ctx)

	// 修改角色状态
	rows := c.roleService.UpdateRoleStatus(role)
	c.ToAjax(ctx, rows)
}

// Remove 删除角色
func (c *SysRoleController) Remove(ctx *gin.Context) {
	roleIdsStr := ctx.Param("roleIds")
	roleIds := strings.Split(roleIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(roleIds))
	for _, idStr := range roleIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 删除角色
	rows := c.roleService.DeleteRoleByIds(ids)
	c.ToAjax(ctx, rows)
}

// Optionselect 获取角色选择框列表
func (c *SysRoleController) Optionselect(ctx *gin.Context) {
	roles := c.roleService.SelectRoleAll()
	c.SuccessWithData(ctx, roles)
}

// AllocatedList 查询已分配用户角色列表
func (c *SysRoleController) AllocatedList(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	user := &domain.SysUser{}
	// 从请求中绑定参数
	if err := c.BindQuery(ctx, user); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取已分配用户列表
	list := c.userService.SelectAllocatedList(user)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// UnallocatedList 查询未分配用户角色列表
func (c *SysRoleController) UnallocatedList(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	user := &domain.SysUser{}
	// 从请求中绑定参数
	if err := c.BindQuery(ctx, user); err != nil {
		c.Logger.Error("绑定参数失败", zap.Error(err))
	}

	// 调用服务获取未分配用户列表
	list := c.userService.SelectUnallocatedList(user)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// CancelAuthUser 取消授权用户
func (c *SysRoleController) CancelAuthUser(ctx *gin.Context) {
	var userRole domain.SysUserRole
	if err := ctx.ShouldBindJSON(&userRole); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 取消授权
	rows := c.roleService.DeleteAuthUser(userRole)
	c.ToAjax(ctx, rows)
}

// CancelAuthUserAll 批量取消授权用户
func (c *SysRoleController) CancelAuthUserAll(ctx *gin.Context) {
	roleIdStr := ctx.Query("roleId")
	userIdsStr := ctx.Query("userIds")

	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 将用户ID字符串转换为int64数组
	userIds := make([]int64, 0)
	if userIdsStr != "" {
		for _, idStr := range strings.Split(userIdsStr, ",") {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				continue
			}
			userIds = append(userIds, id)
		}
	}

	// 批量取消授权
	rows := c.roleService.DeleteAuthUsers(roleId, userIds)
	c.ToAjax(ctx, rows)
}

// SelectAuthUserAll 批量选择用户授权
func (c *SysRoleController) SelectAuthUserAll(ctx *gin.Context) {
	roleIdStr := ctx.Query("roleId")
	userIdsStr := ctx.Query("userIds")

	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(roleId)

	// 将用户ID字符串转换为int64数组
	userIds := make([]int64, 0)
	if userIdsStr != "" {
		for _, idStr := range strings.Split(userIdsStr, ",") {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				continue
			}
			userIds = append(userIds, id)
		}
	}

	// 批量授权
	rows := c.roleService.InsertAuthUsers(roleId, userIds)
	c.ToAjax(ctx, rows)
}

// DeptTree 获取部门树
func (c *SysRoleController) DeptTree(ctx *gin.Context) {
	roleIdStr := ctx.Param("roleId")
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	// 获取部门树
	deptTree := c.deptService.SelectDeptTreeList(&domain.SysDept{})

	// 获取角色已选择的部门
	checkedKeys := c.deptService.SelectDeptListByRoleId(roleId)

	c.SuccessWithData(ctx, map[string]interface{}{
		"deptTree":    deptTree,
		"checkedKeys": checkedKeys,
	})
}
