package system

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/pkg/utils"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

// SysUserController 用户信息控制器
type SysUserController struct {
	controller.BaseController
	userService service.ISysUserService
	roleService service.ISysRoleService
	postService service.ISysPostService
	deptService service.ISysDeptService
}

// NewSysUserController 创建用户控制器
func NewSysUserController(
	logger *zap.Logger,
	userService service.ISysUserService,
	roleService service.ISysRoleService,
	postService service.ISysPostService,
	deptService service.ISysDeptService,
) *SysUserController {
	return &SysUserController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		userService: userService,
		roleService: roleService,
		postService: postService,
		deptService: deptService,
	}
}

// RegisterRoutes 注册路由
func (c *SysUserController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.POST("/importData", c.ImportData)
	r.POST("/importTemplate", c.ImportTemplate)
	r.GET("/:userId", c.GetInfo)
	r.GET("/", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:userIds", c.Remove)
	r.PUT("/resetPwd", c.ResetPwd)
	r.PUT("/changeStatus", c.ChangeStatus)
	r.GET("/authRole/:userId", c.AuthRole)
	r.PUT("/authRole", c.InsertAuthRole)
	r.GET("/deptTree", c.DeptTree)
}

// List 获取用户列表
func (c *SysUserController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	user := domain.SysUser{}
	// 从请求中绑定参数
	user.UserName = ctx.Query("userName")
	user.Status = ctx.Query("status")
	user.Phonenumber = ctx.Query("phonenumber")

	// 绑定部门ID
	deptId := ctx.Query("deptId")
	if deptId != "" {
		id, err := strconv.ParseInt(deptId, 10, 64)
		if err == nil {
			user.DeptId = id
		}
	}

	// 绑定日期范围
	params := ctx.Request.URL.Query()
	beginTime := params.Get("beginTime")
	endTime := params.Get("endTime")
	if beginTime != "" && endTime != "" {
		user.Params = make(map[string]interface{})
		user.Params["beginTime"] = beginTime
		user.Params["endTime"] = endTime
	}

	// 调用服务获取用户列表
	list := c.userService.SelectUserList(user)
	total := int64(len(list))

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, total))
}

// Export 导出用户
func (c *SysUserController) Export(ctx *gin.Context) {
	// 构建查询条件
	user := domain.SysUser{}
	// 从请求中绑定参数
	user.UserName = ctx.PostForm("userName")
	user.Status = ctx.PostForm("status")
	user.Phonenumber = ctx.PostForm("phonenumber")

	// 绑定部门ID
	deptId := ctx.PostForm("deptId")
	if deptId != "" {
		id, err := strconv.ParseInt(deptId, 10, 64)
		if err == nil {
			user.DeptId = id
		}
	}

	// 绑定日期范围
	beginTime := ctx.PostForm("beginTime")
	endTime := ctx.PostForm("endTime")
	if beginTime != "" && endTime != "" {
		user.Params = make(map[string]interface{})
		user.Params["beginTime"] = beginTime
		user.Params["endTime"] = endTime
	}

	// 调用服务获取用户列表
	list := c.userService.SelectUserList(user)

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			c.Logger.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 创建Sheet
	sheetName := "用户数据"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		c.ErrorWithMessage(ctx, "创建Excel Sheet失败")
		return
	}
	f.SetActiveSheet(index)

	// 设置表头
	headers := []string{"用户序号", "登录名称", "用户名称", "用户邮箱", "手机号码", "用户性别", "部门名称", "岗位", "角色", "帐号状态", "创建时间", "备注"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c%d", 'A'+i, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for i, u := range list {
		rowIndex := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIndex), u.UserId)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIndex), u.UserName)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIndex), u.NickName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIndex), u.Email)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIndex), u.Phonenumber)

		// 性别转换
		var sex string
		switch u.Sex {
		case "0":
			sex = "男"
		case "1":
			sex = "女"
		default:
			sex = "未知"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIndex), sex)

		// 部门名称
		deptName := ""
		if u.Dept != nil {
			deptName = u.Dept.DeptName
		}
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", rowIndex), deptName)

		// 岗位和角色暂时留空，需要在服务层实现获取
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", rowIndex), "")
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", rowIndex), "")

		// 状态转换
		var status string
		switch u.Status {
		case "0":
			status = "正常"
		case "1":
			status = "停用"
		default:
			status = "未知"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", rowIndex), status)

		f.SetCellValue(sheetName, fmt.Sprintf("K%d", rowIndex), u.CreateTime)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", rowIndex), u.Remark)
	}

	// 设置文件名
	fileName := fmt.Sprintf("用户数据_%s.xlsx", time.Now().Format("20060102150405"))

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(ctx.Writer); err != nil {
		c.ErrorWithMessage(ctx, "导出Excel失败")
		return
	}
}

// ImportData 导入数据
func (c *SysUserController) ImportData(ctx *gin.Context) {
	file, _, err := ctx.Request.FormFile("file")
	if err != nil {
		c.ErrorWithMessage(ctx, "获取上传文件失败")
		return
	}
	defer file.Close()

	updateSupport := ctx.DefaultPostForm("updateSupport", "false") == "true"

	// 读取Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		c.ErrorWithMessage(ctx, "解析Excel文件失败")
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			c.Logger.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 获取第一个Sheet
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		c.ErrorWithMessage(ctx, "读取Excel数据失败")
		return
	}

	// 检查数据行数
	if len(rows) <= 1 {
		c.ErrorWithMessage(ctx, "Excel文件中没有数据")
		return
	}

	// 解析数据
	userList := make([]domain.SysUser, 0, len(rows)-1)
	for i, row := range rows {
		// 跳过表头
		if i == 0 {
			continue
		}

		// 确保行数据至少有必要的列
		if len(row) < 5 {
			continue
		}

		user := domain.SysUser{
			UserName:    row[1], // 登录名称
			NickName:    row[2], // 用户名称
			Email:       row[3], // 用户邮箱
			Phonenumber: row[4], // 手机号码
		}

		// 设置性别
		if len(row) > 5 {
			switch row[5] {
			case "男":
				user.Sex = "0"
			case "女":
				user.Sex = "1"
			default:
				user.Sex = "2" // 未知
			}
		}

		// 设置部门
		if len(row) > 6 && row[6] != "" {
			// 根据部门名称查询部门ID
			deptName := row[6]
			deptId := c.GetDeptIdByName(ctx, deptName, c.deptService)
			if deptId > 0 {
				user.DeptId = deptId
			}
		}

		// 设置状态
		if len(row) > 9 {
			switch row[9] {
			case "正常":
				user.Status = "0"
			case "停用":
				user.Status = "1"
			default:
				user.Status = "0" // 默认正常
			}
		} else {
			user.Status = "0" // 默认正常
		}

		// 设置备注
		if len(row) > 11 {
			user.Remark = row[11]
		}

		// 设置默认密码
		encryptedPassword, err := utils.EncryptPassword("123456")
		if err == nil {
			user.Password = encryptedPassword
		}

		userList = append(userList, user)
	}

	// 调用服务导入用户
	operName := c.GetUsername(ctx)
	message := c.userService.ImportUser(userList, updateSupport, operName)

	c.SuccessWithMessage(ctx, message)
}

// ImportTemplate 导入模板
func (c *SysUserController) ImportTemplate(ctx *gin.Context) {
	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			c.Logger.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 创建Sheet
	sheetName := "用户数据"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		c.ErrorWithMessage(ctx, "创建Excel Sheet失败")
		return
	}
	f.SetActiveSheet(index)

	// 设置表头
	headers := []string{"用户序号", "登录名称", "用户名称", "用户邮箱", "手机号码", "用户性别", "部门名称", "岗位名称", "角色名称", "帐号状态", "创建时间", "备注"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c%d", 'A'+i, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置文件名
	fileName := "用户数据导入模板.xlsx"

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(ctx.Writer); err != nil {
		c.ErrorWithMessage(ctx, "导出模板失败")
		return
	}
}

// GetInfo 根据用户编号获取详细信息
func (c *SysUserController) GetInfo(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	result := model.AjaxResult{}

	if userIdStr != "" {
		userId, err := strconv.ParseInt(userIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "用户ID格式错误")
			return
		}

		// 检查数据权限
		c.userService.CheckUserDataScope(userId)

		// 获取用户信息
		user := c.userService.SelectUserById(userId)

		// 获取岗位
		postIds := c.postService.SelectPostListByUserId(userId)

		// 获取角色
		roleIds := c.roleService.SelectRoleListByUserId(userId)

		result = model.AjaxResult{
			Code: http.StatusOK,
			Msg:  "操作成功",
			Data: map[string]interface{}{
				"data":    user,
				"postIds": postIds,
				"roleIds": roleIds,
			},
		}
	}

	// 获取所有角色
	roles := c.roleService.SelectRoleAll()

	// 获取所有岗位
	posts := c.postService.SelectPostAll()

	// 如果result.Data为nil，初始化它
	if result.Data == nil {
		result.Data = make(map[string]interface{})
	}

	// 将角色和岗位添加到result.Data中
	if dataMap, ok := result.Data.(map[string]interface{}); ok {
		dataMap["roles"] = roles
		dataMap["posts"] = posts
	}

	ctx.JSON(http.StatusOK, result)
}

// Add 新增用户
func (c *SysUserController) Add(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptId)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(user.RoleIds...)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号唯一性
	if user.Phonenumber != "" && !c.userService.CheckPhoneUnique(user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置创建者
	user.CreateBy = c.GetUsername(ctx)

	// 加密密码
	if user.Password != "" {
		encryptedPassword, err := utils.EncryptPassword(user.Password)
		if err != nil {
			c.ErrorWithMessage(ctx, "密码加密失败")
			return
		}
		user.Password = encryptedPassword
	}

	// 新增用户
	rows := c.userService.InsertUser(user)
	c.ToAjax(ctx, int64(rows))
}

// Edit 修改用户
func (c *SysUserController) Edit(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 检查部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptId)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(user.RoleIds...)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号唯一性
	if user.Phonenumber != "" && !c.userService.CheckPhoneUnique(user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 更新用户
	rows := c.userService.UpdateUser(user)
	c.ToAjax(ctx, int64(rows))
}

// Remove 删除用户
func (c *SysUserController) Remove(ctx *gin.Context) {
	userIdsStr := ctx.Param("userIds")
	userIds := strings.Split(userIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(userIds))
	for _, idStr := range userIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}

		// 不能删除当前用户
		if id == c.GetUserId(ctx) {
			c.ErrorWithMessage(ctx, "当前用户不能删除")
			return
		}

		ids = append(ids, id)
	}

	// 删除用户
	rows := c.userService.DeleteUserByIds(ids)
	c.ToAjax(ctx, int64(rows))
}

// ResetPwd 重置密码
func (c *SysUserController) ResetPwd(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 加密密码
	if user.Password != "" {
		encryptedPassword, err := utils.EncryptPassword(user.Password)
		if err != nil {
			c.ErrorWithMessage(ctx, "密码加密失败")
			return
		}
		user.Password = encryptedPassword
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 重置密码
	rows := c.userService.ResetPwd(user)
	c.ToAjax(ctx, int64(rows))
}

// ChangeStatus 状态修改
func (c *SysUserController) ChangeStatus(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户状态
	rows := c.userService.UpdateUserStatus(user)
	c.ToAjax(ctx, int64(rows))
}

// AuthRole 根据用户编号获取授权角色
func (c *SysUserController) AuthRole(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "用户ID格式错误")
		return
	}

	// 获取用户信息
	user := c.userService.SelectUserById(userId)

	// 获取用户角色
	roles := c.roleService.SelectRolesByUserId(userId)

	c.SuccessWithData(ctx, map[string]interface{}{
		"user":  user,
		"roles": roles,
	})
}

// InsertAuthRole 用户授权角色
func (c *SysUserController) InsertAuthRole(ctx *gin.Context) {
	userIdStr := ctx.PostForm("userId")
	roleIdsStr := ctx.PostForm("roleIds")

	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "用户ID格式错误")
		return
	}

	// 将角色ID字符串转换为int64数组
	roleIds := make([]int64, 0)
	if roleIdsStr != "" {
		for _, idStr := range strings.Split(roleIdsStr, ",") {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				continue
			}
			roleIds = append(roleIds, id)
		}
	}

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(roleIds...)

	// 用户授权角色
	c.userService.InsertUserAuth(userId, roleIds)
	c.Success(ctx)
}

// DeptTree 获取部门树列表
func (c *SysUserController) DeptTree(ctx *gin.Context) {
	dept := domain.SysDept{}
	// 从请求中绑定参数
	dept.DeptName = ctx.Query("deptName")
	dept.Status = ctx.Query("status")

	// 获取部门树列表
	depts := c.deptService.SelectDeptTreeList(dept)
	c.SuccessWithData(ctx, depts)
}
