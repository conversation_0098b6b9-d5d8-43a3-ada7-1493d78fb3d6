package tool

import (
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
)

// 在文件开头添加环境检查
func init() {
	if os.Getenv("APP_ENV") == "prod" {
		log.Println("警告: 生产环境中检测到测试控制器，建议禁用")
	}
}

// TestController Controller
type TestController struct {
	// TODO: Add service dependencies
}

// RegisterTestController Register routes
func RegisterTestController(r *gin.RouterGroup) {
	controller := &TestController{}

	r.GET("/user_list", controller.UserList)
	r.GET("/get_user", controller.GetUser)
	r.POST("/save", controller.Save)
	r.PUT("/update", controller.Update)
	r.DELETE("/delete", controller.Delete)
}

// UserList Handle request
func (c *TestController) UserList(ctx *gin.Context) {
	if os.Getenv("APP_ENV") == "prod" {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code": 403,
			"msg":  "生产环境中测试接口已禁用",
		})
		return
	}

	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// GetUser Handle request
func (c *TestController) GetUser(ctx *gin.Context) {
	if os.Getenv("APP_ENV") == "prod" {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code": 403,
			"msg":  "生产环境中测试接口已禁用",
		})
		return
	}

	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Save Handle request
func (c *TestController) Save(ctx *gin.Context) {
	if os.Getenv("APP_ENV") == "prod" {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code": 403,
			"msg":  "生产环境中测试接口已禁用",
		})
		return
	}

	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Update Handle request
func (c *TestController) Update(ctx *gin.Context) {
	if os.Getenv("APP_ENV") == "prod" {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code": 403,
			"msg":  "生产环境中测试接口已禁用",
		})
		return
	}

	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Delete Handle request
func (c *TestController) Delete(ctx *gin.Context) {
	if os.Getenv("APP_ENV") == "prod" {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code": 403,
			"msg":  "生产环境中测试接口已禁用",
		})
		return
	}

	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}
