﻿package main

import (
    "fmt"
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    fmt.Println("Starting minimal WOSM backend...")
    
    // 鍒涘缓Gin寮曟搸
    r := gin.Default()
    
    // 鍋ュ悍妫€鏌ユ帴鍙?    r.GET("/health", func(c *gin.Context) {
        c.JSO<PERSON>(http.StatusOK, gin.H{
            "status":  "up",
            "service": "WOSM-Backend",
            "version": "1.0.0",
        })
    })
    
    // 绠€鍗曠殑API鎺ュ彛
    r.GET("/api/test", func(c *gin.Context) {
        c.<PERSON>(http.StatusOK, gin.H{
            "message": "WOSM Backend is running!",
            "data":    "Java to Go migration successful",
        })
    })
    
    fmt.Println("Server starting on http://localhost:8080")
    fmt.Println("Health check: http://localhost:8080/health")
    fmt.Println("Test API: http://localhost:8080/api/test")
    fmt.Println("Press Ctrl+C to stop")
    
    // 鍚姩鏈嶅姟鍣?    r.Run(":8080")
}
