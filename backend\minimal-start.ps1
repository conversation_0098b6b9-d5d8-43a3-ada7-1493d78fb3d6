# 最小化启动脚本 - 尝试构建最基本的版本
Write-Host "=== Minimal Java to Go Migration Startup ===" -ForegroundColor Green

# 检查Go环境
Write-Host "Checking Go environment..." -ForegroundColor Yellow
try {
    $goVersion = go version
    Write-Host "Go environment OK: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "Go not installed. Please install Go first." -ForegroundColor Red
    pause
    exit 1
}

# 尝试使用现有的main.go
Write-Host "Trying to build with existing main.go..." -ForegroundColor Yellow
if (Test-Path "main.go") {
    Write-Host "Found main.go, attempting to build..." -ForegroundColor Cyan
    go build -o wosm-backend-main.exe main.go
    
    if (Test-Path "wosm-backend-main.exe") {
        Write-Host "Build successful with main.go!" -ForegroundColor Green
        Write-Host "Starting application..." -ForegroundColor Green
        .\wosm-backend-main.exe
        pause
        exit 0
    } else {
        Write-Host "Build failed with main.go, trying cmd/main.go..." -ForegroundColor Yellow
    }
}

# 尝试使用cmd/main.go
if (Test-Path "cmd\main.go") {
    Write-Host "Found cmd/main.go, attempting to build..." -ForegroundColor Cyan
    go build -o wosm-backend-cmd.exe cmd\main.go
    
    if (Test-Path "wosm-backend-cmd.exe") {
        Write-Host "Build successful with cmd/main.go!" -ForegroundColor Green
        Write-Host "Starting application..." -ForegroundColor Green
        .\wosm-backend-cmd.exe
        pause
        exit 0
    } else {
        Write-Host "Build failed with cmd/main.go..." -ForegroundColor Red
    }
}

# 如果都失败了，创建一个最简单的main.go
Write-Host "Creating minimal main.go..." -ForegroundColor Yellow

$minimalMain = @"
package main

import (
    "fmt"
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    fmt.Println("Starting minimal WOSM backend...")
    
    // 创建Gin引擎
    r := gin.Default()
    
    // 健康检查接口
    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":  "up",
            "service": "WOSM-Backend",
            "version": "1.0.0",
        })
    })
    
    // 简单的API接口
    r.GET("/api/test", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "WOSM Backend is running!",
            "data":    "Java to Go migration successful",
        })
    })
    
    fmt.Println("Server starting on http://localhost:8080")
    fmt.Println("Health check: http://localhost:8080/health")
    fmt.Println("Test API: http://localhost:8080/api/test")
    fmt.Println("Press Ctrl+C to stop")
    
    // 启动服务器
    r.Run(":8080")
}
"@

$minimalMain | Out-File -FilePath "minimal-main.go" -Encoding UTF8

Write-Host "Building minimal version..." -ForegroundColor Yellow
go build -o wosm-backend-minimal.exe minimal-main.go

if (Test-Path "wosm-backend-minimal.exe") {
    Write-Host "Minimal build successful!" -ForegroundColor Green
    Write-Host "Starting minimal WOSM backend..." -ForegroundColor Green
    Write-Host ""
    Write-Host "Available endpoints:" -ForegroundColor Cyan
    Write-Host "  Health check: http://localhost:8080/health" -ForegroundColor White
    Write-Host "  Test API: http://localhost:8080/api/test" -ForegroundColor White
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""
    
    .\wosm-backend-minimal.exe
} else {
    Write-Host "Even minimal build failed. Checking dependencies..." -ForegroundColor Red
    Write-Host "Running go mod tidy..." -ForegroundColor Yellow
    go mod tidy
    
    Write-Host "Trying minimal build again..." -ForegroundColor Yellow
    go build -o wosm-backend-minimal.exe minimal-main.go
    
    if (Test-Path "wosm-backend-minimal.exe") {
        Write-Host "Minimal build successful after tidy!" -ForegroundColor Green
        .\wosm-backend-minimal.exe
    } else {
        Write-Host "Build still failed. Please check Go installation and dependencies." -ForegroundColor Red
        Write-Host "Try running: go mod tidy && go build minimal-main.go" -ForegroundColor Yellow
    }
}

pause
