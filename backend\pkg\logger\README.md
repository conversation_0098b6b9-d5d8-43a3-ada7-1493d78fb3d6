# 日志系统

本日志系统提供了统一的日志接口和多种日志实现，支持不同的日志输出方式和格式。

## 主要特性

- 统一的`Logger`接口
- 多种日志实现（Zap、文件、控制台等）
- 日志级别控制
- 结构化日志
- 上下文日志
- 命名日志记录器
- 日志轮转
- 日志格式化

## 快速开始

### 基本用法

```go
import "path/to/pkg/logger"

// 使用默认日志记录器
logger.Info("这是一条信息")
logger.Debug("这是一条调试信息")
logger.Warn("这是一条警告信息")
logger.Error("这是一条错误信息")

// 使用带有键值对的日志
logger.Infow("带有键值对的信息", "key1", "value1", "key2", 42)

// 使用格式化日志
logger.Infof("格式化日志: %s, %d", "字符串", 100)
```

### 创建自定义日志记录器

```go
// 创建一个Zap日志记录器
zapLogger, err := logger.GetLogger(logger.ZapLoggerType,
    logger.WithLevel(logger.DebugLevel),
    logger.WithFormat("console"),
    logger.WithDevelopment(true),
)
if err != nil {
    logger.Fatal("无法创建Zap日志记录器", err)
}
zapLogger.Debug("这是一条Zap日志记录器的调试信息")

// 创建一个文件日志记录器
fileLogger, err := logger.GetLogger(logger.FileLoggerType,
    logger.WithLevel(logger.InfoLevel),
    logger.WithFormat("json"),
    logger.WithOutputPaths("./logs/app.log"),
)
if err != nil {
    logger.Fatal("无法创建文件日志记录器", err)
}
fileLogger.Info("这是一条文件日志记录器的信息")
```

### 使用上下文和命名日志记录器

```go
// 使用上下文日志
ctx := context.Background()
ctxLogger := logger.DefaultLogger.WithContext(ctx)
ctxLogger.Info("这是一条带有上下文的日志")

// 使用带有名称的日志记录器
namedLogger := logger.DefaultLogger.WithName("MyComponent")
namedLogger.Info("这是一条带有组件名称的日志")

// 使用带有键值对的日志记录器
kvLogger := logger.DefaultLogger.WithValues("userID", "123", "requestID", "abc-123")
kvLogger.Info("这是一条带有固定键值对的日志")
```

### 使用命名的日志记录器

```go
// 创建一个命名的日志记录器
namedFileLogger, err := logger.GetOrCreateLogger("api-server", logger.FileLoggerType,
    logger.WithLevel(logger.InfoLevel),
    logger.WithFormat("json"),
    logger.WithOutputPaths("./logs/api-server.log"),
)
if err != nil {
    logger.Fatal("无法创建命名的日志记录器", err)
}
namedFileLogger.Info("这是一条命名的日志记录器的信息")

// 必须获取日志记录器（如果失败则panic）
consoleLogger := logger.MustGetLogger(logger.ConsoleLoggerType)
consoleLogger.Info("这是一条控制台日志记录器的信息")
```

## 日志级别

日志系统支持以下日志级别：

- `DebugLevel`: 调试级别，用于开发调试
- `InfoLevel`: 信息级别，用于记录一般信息
- `WarnLevel`: 警告级别，用于记录警告信息
- `ErrorLevel`: 错误级别，用于记录错误信息
- `FatalLevel`: 致命级别，记录日志后程序会退出
- `PanicLevel`: Panic级别，记录日志后会触发panic

## 日志类型

系统支持以下日志类型：

- `ZapLoggerType`: 基于Zap的日志记录器
- `FileLoggerType`: 文件日志记录器
- `ConsoleLoggerType`: 控制台日志记录器
- `RotateFileLoggerType`: 轮转文件日志记录器
- `DailyFileLoggerType`: 每日轮转文件日志记录器

## 配置选项

可以使用以下选项配置日志记录器：

- `WithLevel`: 设置日志级别
- `WithFormat`: 设置日志格式（"json"或"console"）
- `WithOutputPaths`: 设置输出路径
- `WithErrorOutputPaths`: 设置错误输出路径
- `WithDevelopment`: 设置是否为开发模式
- `WithDisableCaller`: 设置是否禁用调用者信息
- `WithDisableStacktrace`: 设置是否禁用堆栈跟踪
- `WithTimeFormat`: 设置时间格式

## 扩展

如果需要添加新的日志实现，只需实现`Logger`接口并使用`RegisterLogger`函数注册即可：

```go
// 注册自定义日志记录器
logger.RegisterLogger(MyLoggerType, NewMyLogger)
```
