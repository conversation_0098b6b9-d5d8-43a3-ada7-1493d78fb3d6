package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// FileLoggerOptions 文件日志选项
type FileLoggerOptions struct {
	// Filename 日志文件路径
	Filename string
	// MaxSize 日志文件最大大小，单位MB
	MaxSize int
	// MaxBackups 最大备份数量
	MaxBackups int
	// MaxAge 最大保存天数
	MaxAge int
	// Compress 是否压缩
	Compress bool
	// Level 日志级别
	Level LogLevel
	// Format 日志格式 (json or console)
	Format string
	// TimeFormat 时间格式
	TimeFormat string
}

// CreateFileLogger 创建一个新的文件日志记录器
func CreateFileLogger(opts FileLoggerOptions) Logger {
	// 设置默认值
	if opts.MaxSize == 0 {
		opts.MaxSize = 100
	}
	if opts.MaxBackups == 0 {
		opts.MaxBackups = 10
	}
	if opts.MaxAge == 0 {
		opts.MaxAge = 30
	}
	if opts.Format == "" {
		opts.Format = "json"
	}
	if opts.TimeFormat == "" {
		opts.TimeFormat = "2006-01-02 15:04:05"
	}

	// 确保目录存在
	dir := filepath.Dir(opts.Filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建日志轮转器
	fileWriter := &lumberjack.Logger{
		Filename:   opts.Filename,
		MaxSize:    opts.MaxSize,
		MaxBackups: opts.MaxBackups,
		MaxAge:     opts.MaxAge,
		Compress:   opts.Compress,
	}

	// 创建编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.TimeEncoderOfLayout(opts.TimeFormat),
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建编码器
	var encoder zapcore.Encoder
	if opts.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建核心
	core := zapcore.NewCore(
		encoder,
		zapcore.AddSync(fileWriter),
		zap.NewAtomicLevelAt(zapcore.Level(opts.Level-1)), // zap的级别比我们定义的低1
	)

	// 创建zap logger
	logger := zap.New(
		core,
		zap.AddCaller(),
		zap.AddCallerSkip(1),
	)

	return &zapLogger{
		logger: logger.Sugar(),
	}
}

// NewRotateFileLoggerWithOptions 使用Option创建一个按日期轮转的文件日志记录器
func NewRotateFileLoggerWithOptions(baseDir string, opts ...Option) Logger {
	options := &LoggerOptions{
		Config: LogConfig{
			Level:             InfoLevel,
			Format:            "json",
			Development:       false,
			DisableCaller:     false,
			DisableStacktrace: false,
			TimeFormat:        "2006-01-02 15:04:05",
		},
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 确保目录存在
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建当前日期的日志文件
	now := time.Now()
	filename := filepath.Join(baseDir, fmt.Sprintf("%s.log", now.Format("2006-01-02")))

	// 创建文件日志记录器
	return CreateFileLogger(FileLoggerOptions{
		Filename:   filename,
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
		Level:      options.Config.Level,
		Format:     options.Config.Format,
		TimeFormat: options.Config.TimeFormat,
	})
}

// NewDailyFileLoggerWithOptions 使用Option创建一个每日轮转的文件日志记录器
func NewDailyFileLoggerWithOptions(baseDir, appName string, opts ...Option) Logger {
	options := &LoggerOptions{
		Config: LogConfig{
			Level:             InfoLevel,
			Format:            "json",
			Development:       false,
			DisableCaller:     false,
			DisableStacktrace: false,
			TimeFormat:        "2006-01-02 15:04:05",
		},
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 确保目录存在
	logDir := filepath.Join(baseDir, appName, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建当前日期的日志文件
	now := time.Now()
	filename := filepath.Join(logDir, fmt.Sprintf("%s_%s.log", appName, now.Format("2006-01-02")))

	// 创建文件日志记录器
	return CreateFileLogger(FileLoggerOptions{
		Filename:   filename,
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
		Level:      options.Config.Level,
		Format:     options.Config.Format,
		TimeFormat: options.Config.TimeFormat,
	})
}

// 工厂方法适配器
func newRotateFileLogger(opts ...Option) Logger {
	// 默认日志目录
	baseDir := "./logs"

	// 检查选项中是否有自定义路径
	options := &LoggerOptions{}
	for _, opt := range opts {
		opt(options)
	}

	// 如果有自定义输出路径，使用第一个作为基础目录
	if len(options.Config.OutputPaths) > 0 && options.Config.OutputPaths[0] != "stdout" && options.Config.OutputPaths[0] != "stderr" {
		baseDir = options.Config.OutputPaths[0]
	}

	return NewRotateFileLoggerWithOptions(baseDir, opts...)
}

// 工厂方法适配器
func newDailyFileLogger(opts ...Option) Logger {
	// 默认日志目录和应用名
	baseDir := "./logs"
	appName := "app"

	// 检查选项中是否有自定义路径
	options := &LoggerOptions{}
	for _, opt := range opts {
		opt(options)
	}

	// 如果有自定义输出路径，使用第一个作为基础目录
	if len(options.Config.OutputPaths) > 0 && options.Config.OutputPaths[0] != "stdout" && options.Config.OutputPaths[0] != "stderr" {
		path := options.Config.OutputPaths[0]
		baseDir = filepath.Dir(path)
		appName = filepath.Base(path)
	}

	return NewDailyFileLoggerWithOptions(baseDir, appName, opts...)
}

func init() {
	// 注册文件日志创建器
	RegisterLogger(FileLoggerType, func(opts ...Option) Logger {
		options := &LoggerOptions{}
		for _, opt := range opts {
			opt(options)
		}

		// 默认文件路径
		filename := "./logs/app.log"
		if len(options.Config.OutputPaths) > 0 && options.Config.OutputPaths[0] != "stdout" && options.Config.OutputPaths[0] != "stderr" {
			filename = options.Config.OutputPaths[0]
		}

		return CreateFileLogger(FileLoggerOptions{
			Filename:   filename,
			MaxSize:    100,
			MaxBackups: 10,
			MaxAge:     30,
			Compress:   true,
			Level:      options.Config.Level,
			Format:     options.Config.Format,
			TimeFormat: options.Config.TimeFormat,
		})
	})

	// 注册轮转日志创建器
	RegisterLogger(RotateFileLoggerType, newRotateFileLogger)

	// 注册每日日志创建器
	RegisterLogger(DailyFileLoggerType, newDailyFileLogger)
}
