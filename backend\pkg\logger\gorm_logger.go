package logger

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GormLogger GORM日志适配器
type GormLogger struct {
	Logger Logger
	Config logger.Config
}

// NewGormLogger 创建GORM日志适配器
func NewGormLogger(opts ...gormLoggerOption) logger.Interface {
	config := logger.Config{
		SlowThreshold:             200 * time.Millisecond,
		LogLevel:                  logger.Info,
		IgnoreRecordNotFoundError: true,
		Colorful:                  false,
	}

	options := &gormLoggerOptions{
		config:       config,
		customLogger: DefaultLogger,
	}

	for _, opt := range opts {
		opt(options)
	}

	return &GormLogger{
		Logger: options.customLogger,
		Config: options.config,
	}
}

// LogMode 设置日志级别
func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.Config.LogLevel = level
	return &newLogger
}

// Info 输出信息日志
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.Config.LogLevel >= logger.Info {
		l.Logger.Infof(msg, data...)
	}
}

// Warn 输出警告日志
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.Config.LogLevel >= logger.Warn {
		l.Logger.Warnf(msg, data...)
	}
}

// Error 输出错误日志
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.Config.LogLevel >= logger.Error {
		l.Logger.Errorf(msg, data...)
	}
}

// Trace 输出跟踪日志
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.Config.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	// 记录错误日志
	if err != nil && l.Config.LogLevel >= logger.Error && (!errors.Is(err, gorm.ErrRecordNotFound) || !l.Config.IgnoreRecordNotFoundError) {
		l.Logger.Errorf("SQL错误: %s, 耗时: %v, 影响行数: %d, 错误: %v", sql, elapsed, rows, err)
		return
	}

	// 记录慢查询
	if l.Config.SlowThreshold != 0 && elapsed > l.Config.SlowThreshold && l.Config.LogLevel >= logger.Warn {
		l.Logger.Warnf("SQL慢查询: %s, 耗时: %v, 影响行数: %d", sql, elapsed, rows)
		return
	}

	// 记录普通查询
	if l.Config.LogLevel >= logger.Info {
		l.Logger.Debugf("SQL: %s, 耗时: %v, 影响行数: %d", sql, elapsed, rows)
	}
}

// gormLoggerOptions GORM日志选项
type gormLoggerOptions struct {
	config       logger.Config
	customLogger Logger
}

// gormLoggerOption GORM日志选项函数
type gormLoggerOption func(*gormLoggerOptions)

// WithGormLogLevel 设置GORM日志级别
func WithGormLogLevel(level logger.LogLevel) gormLoggerOption {
	return func(o *gormLoggerOptions) {
		o.config.LogLevel = level
	}
}

// WithGormSlowThreshold 设置GORM慢查询阈值
func WithGormSlowThreshold(threshold time.Duration) gormLoggerOption {
	return func(o *gormLoggerOptions) {
		o.config.SlowThreshold = threshold
	}
}

// WithGormIgnoreRecordNotFoundError 设置GORM是否忽略记录不存在错误
func WithGormIgnoreRecordNotFoundError(ignore bool) gormLoggerOption {
	return func(o *gormLoggerOptions) {
		o.config.IgnoreRecordNotFoundError = ignore
	}
}

// WithGormColorful 设置GORM是否启用彩色日志
func WithGormColorful(colorful bool) gormLoggerOption {
	return func(o *gormLoggerOptions) {
		o.config.Colorful = colorful
	}
}

// WithCustomLogger 设置自定义日志记录器
func WithCustomLogger(logger Logger) gormLoggerOption {
	return func(o *gormLoggerOptions) {
		o.customLogger = logger
	}
}
