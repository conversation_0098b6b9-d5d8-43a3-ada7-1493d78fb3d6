package logger

import (
	"fmt"
	"sync"
)

// LoggerType 日志类型
type LoggerType string

const (
	// ZapLoggerType Zap日志类型
	ZapLoggerType LoggerType = "zap"
	// FileLoggerType 文件日志类型
	FileLoggerType LoggerType = "file"
	// ConsoleLoggerType 控制台日志类型
	ConsoleLoggerType LoggerType = "console"
	// RotateFileLoggerType 轮转文件日志类型
	RotateFileLoggerType LoggerType = "rotate"
	// DailyFileLoggerType 每日文件日志类型
	DailyFileLoggerType LoggerType = "daily"
)

// LoggerCreator 日志创建器
type LoggerCreator func(opts ...Option) Logger

var (
	// 日志创建器映射
	loggerCreators = make(map[LoggerType]LoggerCreator)
	// 日志创建器锁
	creatorMutex = &sync.RWMutex{}
	// 默认日志创建器
	defaultCreator LoggerCreator
	// 日志实例缓存
	loggerInstances = make(map[string]Logger)
	// 日志实例缓存锁
	instanceMutex = &sync.RWMutex{}
)

// New 创建新的日志记录器的函数类型
var New = NewLogger

// RegisterLogger 注册日志创建器
func RegisterLogger(loggerType LoggerType, creator LoggerCreator) {
	creatorMutex.Lock()
	defer creatorMutex.Unlock()
	loggerCreators[loggerType] = creator

	// 如果是第一个注册的创建器，设为默认
	if defaultCreator == nil {
		defaultCreator = creator
	}
}

// SetDefaultLogger 设置默认日志创建器
func SetDefaultLogger(loggerType LoggerType) error {
	creatorMutex.RLock()
	creator, exists := loggerCreators[loggerType]
	creatorMutex.RUnlock()

	if !exists {
		return fmt.Errorf("日志类型 %s 未注册", loggerType)
	}

	defaultCreator = creator
	return nil
}

// NewLogger 创建新的日志记录器
func NewLogger(opts ...Option) Logger {
	if defaultCreator == nil {
		panic("未设置默认日志创建器")
	}
	return defaultCreator(opts...)
}

// GetLogger 获取指定类型的日志记录器
func GetLogger(loggerType LoggerType, opts ...Option) (Logger, error) {
	creatorMutex.RLock()
	creator, exists := loggerCreators[loggerType]
	creatorMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("日志类型 %s 未注册", loggerType)
	}

	return creator(opts...), nil
}

// GetOrCreateLogger 获取或创建命名的日志记录器
func GetOrCreateLogger(name string, loggerType LoggerType, opts ...Option) (Logger, error) {
	// 先尝试从缓存获取
	instanceMutex.RLock()
	logger, exists := loggerInstances[name]
	instanceMutex.RUnlock()

	if exists {
		return logger, nil
	}

	// 创建新的日志记录器
	creatorMutex.RLock()
	creator, creatorExists := loggerCreators[loggerType]
	creatorMutex.RUnlock()

	if !creatorExists {
		return nil, fmt.Errorf("日志类型 %s 未注册", loggerType)
	}

	// 创建并添加到缓存
	logger = creator(opts...)

	instanceMutex.Lock()
	loggerInstances[name] = logger
	instanceMutex.Unlock()

	return logger, nil
}

// MustGetLogger 必须获取指定类型的日志记录器，如果失败则panic
func MustGetLogger(loggerType LoggerType, opts ...Option) Logger {
	logger, err := GetLogger(loggerType, opts...)
	if err != nil {
		panic(err)
	}
	return logger
}

// MustGetOrCreateLogger 必须获取或创建命名的日志记录器，如果失败则panic
func MustGetOrCreateLogger(name string, loggerType LoggerType, opts ...Option) Logger {
	logger, err := GetOrCreateLogger(name, loggerType, opts...)
	if err != nil {
		panic(err)
	}
	return logger
}

// init 初始化
func init() {
	// 注册内置日志创建器
	RegisterLogger(ZapLoggerType, NewZapLogger)
	RegisterLogger(ConsoleLoggerType, func(opts ...Option) Logger {
		options := &LoggerOptions{
			Config: LogConfig{
				Level:             InfoLevel,
				Format:            "console",
				OutputPaths:       []string{"stdout"},
				ErrorOutputPaths:  []string{"stderr"},
				Development:       true,
				DisableCaller:     false,
				DisableStacktrace: false,
				TimeFormat:        "2006-01-02 15:04:05",
			},
		}

		// 应用选项
		for _, opt := range opts {
			opt(options)
		}

		return NewZapLogger(
			WithLevel(options.Config.Level),
			WithFormat("console"),
			WithOutputPaths("stdout"),
			WithErrorOutputPaths("stderr"),
			WithDevelopment(true),
		)
	})
}
