package logger

import (
	"context"
)

// LogLevel 日志级别
type LogLevel int

// 日志级别常量
const (
	// DebugLevel 调试级别
	DebugLevel LogLevel = iota + 1
	// InfoLevel 信息级别
	InfoLevel
	// WarnLevel 警告级别
	WarnLevel
	// ErrorLevel 错误级别
	ErrorLevel
	// FatalLevel 致命级别
	FatalLevel
	// PanicLevel panic级别
	PanicLevel
)

// Logger 日志接口
// 定义了日志记录器应该具有的基本方法
type Logger interface {
	// Debug 记录调试级别的日志
	Debug(args ...interface{})
	// Debugf 记录调试级别的格式化日志
	Debugf(format string, args ...interface{})
	// Debugw 记录调试级别的键值对日志
	Debugw(msg string, keysAndValues ...interface{})

	// Info 记录信息级别的日志
	Info(args ...interface{})
	// Infof 记录信息级别的格式化日志
	Infof(format string, args ...interface{})
	// Infow 记录信息级别的键值对日志
	Infow(msg string, keysAndValues ...interface{})

	// Warn 记录警告级别的日志
	Warn(args ...interface{})
	// Warnf 记录警告级别的格式化日志
	Warnf(format string, args ...interface{})
	// Warnw 记录警告级别的键值对日志
	Warnw(msg string, keysAndValues ...interface{})

	// Error 记录错误级别的日志
	Error(args ...interface{})
	// Errorf 记录错误级别的格式化日志
	Errorf(format string, args ...interface{})
	// Errorw 记录错误级别的键值对日志
	Errorw(msg string, keysAndValues ...interface{})

	// Fatal 记录致命级别的日志
	Fatal(args ...interface{})
	// Fatalf 记录致命级别的格式化日志
	Fatalf(format string, args ...interface{})
	// Fatalw 记录致命级别的键值对日志
	Fatalw(msg string, keysAndValues ...interface{})

	// WithContext 创建带有上下文的日志记录器
	WithContext(ctx context.Context) Logger
	// WithValues 创建带有键值对的日志记录器
	WithValues(keysAndValues ...interface{}) Logger
	// WithName 创建带有名称的日志记录器
	WithName(name string) Logger
	// Sync 同步日志
	Sync() error
}

// LogConfig 日志配置
type LogConfig struct {
	// Level 日志级别
	Level LogLevel
	// Format 日志格式 (json 或 console)
	Format string
	// OutputPaths 输出路径
	OutputPaths []string
	// ErrorOutputPaths 错误输出路径
	ErrorOutputPaths []string
	// Development 是否为开发模式
	Development bool
	// DisableCaller 是否禁用调用者信息
	DisableCaller bool
	// DisableStacktrace 是否禁用堆栈跟踪
	DisableStacktrace bool
	// TimeFormat 时间格式
	TimeFormat string
}

// LoggerOptions 日志选项
type LoggerOptions struct {
	// Config 日志配置
	Config LogConfig
}

// Option 日志选项函数
type Option func(*LoggerOptions)

// WithLevel 设置日志级别
func WithLevel(level LogLevel) Option {
	return func(o *LoggerOptions) {
		o.Config.Level = level
	}
}

// WithFormat 设置日志格式
func WithFormat(format string) Option {
	return func(o *LoggerOptions) {
		o.Config.Format = format
	}
}

// WithOutputPaths 设置输出路径
func WithOutputPaths(paths ...string) Option {
	return func(o *LoggerOptions) {
		o.Config.OutputPaths = paths
	}
}

// WithErrorOutputPaths 设置错误输出路径
func WithErrorOutputPaths(paths ...string) Option {
	return func(o *LoggerOptions) {
		o.Config.ErrorOutputPaths = paths
	}
}

// WithDevelopment 设置开发模式
func WithDevelopment(development bool) Option {
	return func(o *LoggerOptions) {
		o.Config.Development = development
	}
}

// WithDisableCaller 设置是否禁用调用者信息
func WithDisableCaller(disable bool) Option {
	return func(o *LoggerOptions) {
		o.Config.DisableCaller = disable
	}
}

// WithDisableStacktrace 设置是否禁用堆栈跟踪
func WithDisableStacktrace(disable bool) Option {
	return func(o *LoggerOptions) {
		o.Config.DisableStacktrace = disable
	}
}

// WithTimeFormat 设置时间格式
func WithTimeFormat(format string) Option {
	return func(o *LoggerOptions) {
		o.Config.TimeFormat = format
	}
}
