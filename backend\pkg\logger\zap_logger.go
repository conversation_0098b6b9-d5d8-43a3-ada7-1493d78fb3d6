package logger

import (
	"context"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func init() {
	// 设置New函数为创建ZapLogger的函数
	New = NewZapLogger
}

// zapLogger 是基于zap的日志记录器实现
type zapLogger struct {
	logger *zap.SugaredLogger
}

// NewZapLogger 创建一个新的基于zap的日志记录器
func NewZapLogger(opts ...Option) Logger {
	options := &LoggerOptions{
		Config: LogConfig{
			Level:             InfoLevel,
			Format:            "console",
			OutputPaths:       []string{"stdout"},
			ErrorOutputPaths:  []string{"stderr"},
			Development:       false,
			DisableCaller:     false,
			DisableStacktrace: false,
			TimeFormat:        "2006-01-02 15:04:05",
		},
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 创建zap logger配置
	zapConfig := zap.Config{
		Level:             zap.NewAtomicLevelAt(zapcore.Level(options.Config.Level - 1)), // zap的级别比我们定义的低1
		Development:       options.Config.Development,
		DisableCaller:     options.Config.DisableCaller,
		DisableStacktrace: options.Config.DisableStacktrace,
		Sampling:          nil,
		Encoding:          options.Config.Format,
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			FunctionKey:    zapcore.OmitKey,
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.CapitalLevelEncoder,
			EncodeTime:     zapcore.TimeEncoderOfLayout(options.Config.TimeFormat),
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      options.Config.OutputPaths,
		ErrorOutputPaths: options.Config.ErrorOutputPaths,
	}

	// 构建zap logger
	logger, err := zapConfig.Build()
	if err != nil {
		panic("无法创建zap logger: " + err.Error())
	}

	return &zapLogger{
		logger: logger.Sugar(),
	}
}

// Debug 实现Debug方法
func (l *zapLogger) Debug(args ...interface{}) {
	l.logger.Debug(args...)
}

// Debugf 实现Debugf方法
func (l *zapLogger) Debugf(format string, args ...interface{}) {
	l.logger.Debugf(format, args...)
}

// Debugw 实现Debugw方法
func (l *zapLogger) Debugw(msg string, keysAndValues ...interface{}) {
	l.logger.Debugw(msg, keysAndValues...)
}

// Info 实现Info方法
func (l *zapLogger) Info(args ...interface{}) {
	l.logger.Info(args...)
}

// Infof 实现Infof方法
func (l *zapLogger) Infof(format string, args ...interface{}) {
	l.logger.Infof(format, args...)
}

// Infow 实现Infow方法
func (l *zapLogger) Infow(msg string, keysAndValues ...interface{}) {
	l.logger.Infow(msg, keysAndValues...)
}

// Warn 实现Warn方法
func (l *zapLogger) Warn(args ...interface{}) {
	l.logger.Warn(args...)
}

// Warnf 实现Warnf方法
func (l *zapLogger) Warnf(format string, args ...interface{}) {
	l.logger.Warnf(format, args...)
}

// Warnw 实现Warnw方法
func (l *zapLogger) Warnw(msg string, keysAndValues ...interface{}) {
	l.logger.Warnw(msg, keysAndValues...)
}

// Error 实现Error方法
func (l *zapLogger) Error(args ...interface{}) {
	l.logger.Error(args...)
}

// Errorf 实现Errorf方法
func (l *zapLogger) Errorf(format string, args ...interface{}) {
	l.logger.Errorf(format, args...)
}

// Errorw 实现Errorw方法
func (l *zapLogger) Errorw(msg string, keysAndValues ...interface{}) {
	l.logger.Errorw(msg, keysAndValues...)
}

// Fatal 实现Fatal方法
func (l *zapLogger) Fatal(args ...interface{}) {
	l.logger.Fatal(args...)
}

// Fatalf 实现Fatalf方法
func (l *zapLogger) Fatalf(format string, args ...interface{}) {
	l.logger.Fatalf(format, args...)
}

// Fatalw 实现Fatalw方法
func (l *zapLogger) Fatalw(msg string, keysAndValues ...interface{}) {
	l.logger.Fatalw(msg, keysAndValues...)
}

// WithContext 实现WithContext方法
func (l *zapLogger) WithContext(ctx context.Context) Logger {
	return &zapLogger{
		logger: l.logger.With("ctx", ctx),
	}
}

// WithValues 实现WithValues方法
func (l *zapLogger) WithValues(keysAndValues ...interface{}) Logger {
	return &zapLogger{
		logger: l.logger.With(keysAndValues...),
	}
}

// WithName 实现WithName方法
func (l *zapLogger) WithName(name string) Logger {
	return &zapLogger{
		logger: l.logger.Named(name),
	}
}

// Sync 实现Sync方法
func (l *zapLogger) Sync() error {
	return l.logger.Sync()
}

// DefaultLogger 默认日志记录器
var DefaultLogger = NewZapLogger()

// Debug 使用默认日志记录器输出调试级别的日志
func Debug(args ...interface{}) {
	DefaultLogger.Debug(args...)
}

// Debugf 使用默认日志记录器输出调试级别的格式化日志
func Debugf(format string, args ...interface{}) {
	DefaultLogger.Debugf(format, args...)
}

// Debugw 使用默认日志记录器输出调试级别的键值对日志
func Debugw(msg string, keysAndValues ...interface{}) {
	DefaultLogger.Debugw(msg, keysAndValues...)
}

// Info 使用默认日志记录器输出信息级别的日志
func Info(args ...interface{}) {
	DefaultLogger.Info(args...)
}

// Infof 使用默认日志记录器输出信息级别的格式化日志
func Infof(format string, args ...interface{}) {
	DefaultLogger.Infof(format, args...)
}

// Infow 使用默认日志记录器输出信息级别的键值对日志
func Infow(msg string, keysAndValues ...interface{}) {
	DefaultLogger.Infow(msg, keysAndValues...)
}

// Warn 使用默认日志记录器输出警告级别的日志
func Warn(args ...interface{}) {
	DefaultLogger.Warn(args...)
}

// Warnf 使用默认日志记录器输出警告级别的格式化日志
func Warnf(format string, args ...interface{}) {
	DefaultLogger.Warnf(format, args...)
}

// Warnw 使用默认日志记录器输出警告级别的键值对日志
func Warnw(msg string, keysAndValues ...interface{}) {
	DefaultLogger.Warnw(msg, keysAndValues...)
}

// Error 使用默认日志记录器输出错误级别的日志
func Error(args ...interface{}) {
	DefaultLogger.Error(args...)
}

// Errorf 使用默认日志记录器输出错误级别的格式化日志
func Errorf(format string, args ...interface{}) {
	DefaultLogger.Errorf(format, args...)
}

// Errorw 使用默认日志记录器输出错误级别的键值对日志
func Errorw(msg string, keysAndValues ...interface{}) {
	DefaultLogger.Errorw(msg, keysAndValues...)
}

// Fatal 使用默认日志记录器输出致命级别的日志
func Fatal(args ...interface{}) {
	DefaultLogger.Fatal(args...)
}

// Fatalf 使用默认日志记录器输出致命级别的格式化日志
func Fatalf(format string, args ...interface{}) {
	DefaultLogger.Fatalf(format, args...)
}

// Fatalw 使用默认日志记录器输出致命级别的键值对日志
func Fatalw(msg string, keysAndValues ...interface{}) {
	DefaultLogger.Fatalw(msg, keysAndValues...)
}
