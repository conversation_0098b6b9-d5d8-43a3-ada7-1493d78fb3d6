package utils

import (
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"
)

// 默认除法精度
const DEF_DIV_SCALE = 10

// Add 加法，精确计算两个浮点数的和
func Add(v1, v2 float64) float64 {
	// 使用big.Float提供精确计算
	a := big.NewFloat(v1)
	b := big.NewFloat(v2)
	result := new(big.Float).Add(a, b)

	// 转换回float64
	val, _ := result.Float64()
	return val
}

// Sub 减法，精确计算两个浮点数的差
func Sub(v1, v2 float64) float64 {
	// 使用big.Float提供精确计算
	a := big.NewFloat(v1)
	b := big.NewFloat(v2)
	result := new(big.Float).Sub(a, b)

	// 转换回float64
	val, _ := result.Float64()
	return val
}

// Mul 乘法，精确计算两个浮点数的积
func Mul(v1, v2 float64) float64 {
	// 使用big.Float提供精确计算
	a := big.NewFloat(v1)
	b := big.NewFloat(v2)
	result := new(big.Float).Mul(a, b)

	// 转换回float64
	val, _ := result.Float64()
	return val
}

// Div 除法，精确计算两个浮点数的商，使用默认精度
func Div(v1, v2 float64) float64 {
	return DivWithScale(v1, v2, DEF_DIV_SCALE)
}

// DivWithScale 除法，精确计算两个浮点数的商，使用指定精度
func DivWithScale(v1, v2 float64, scale int) float64 {
	if v2 == 0 {
		panic("除数不能为0")
	}

	// 使用big.Float提供精确计算
	a := big.NewFloat(v1)
	b := big.NewFloat(v2)
	result := new(big.Float).Quo(a, b)

	// 转换回float64
	val, _ := result.Float64()

	// 根据精度进行四舍五入
	return Round(val, scale)
}

// Round 四舍五入，对浮点数按指定精度进行四舍五入
func Round(v float64, scale int) float64 {
	if scale < 0 {
		panic("精度不能小于0")
	}

	// 四舍五入到指定小数位
	format := "%." + strconv.Itoa(scale) + "f"
	s := fmt.Sprintf(format, v)

	// 去除末尾多余的0
	if scale > 0 && strings.Contains(s, ".") {
		s = strings.TrimRight(s, "0")
		s = strings.TrimRight(s, ".")
	}

	// 转换回float64
	result, _ := strconv.ParseFloat(s, 64)
	return result
}

// Ceiling 向上取整，得到大于或等于指定数的最小整数
func Ceiling(v float64) float64 {
	return math.Ceil(v)
}

// Floor 向下取整，得到小于或等于指定数的最大整数
func Floor(v float64) float64 {
	return math.Floor(v)
}

// Abs 绝对值，获取浮点数的绝对值
func Abs(v float64) float64 {
	return math.Abs(v)
}

// Max 取最大值，获取两个数中的较大值
func Max(v1, v2 float64) float64 {
	return math.Max(v1, v2)
}

// Min 取最小值，获取两个数中的较小值
func Min(v1, v2 float64) float64 {
	return math.Min(v1, v2)
}

// Pow 幂运算，计算x的y次方
func Pow(x, y float64) float64 {
	return math.Pow(x, y)
}

// Sqrt 开平方，计算平方根
func Sqrt(x float64) float64 {
	return math.Sqrt(x)
}

// IsZero 判断浮点数是否为零（考虑精度）
func IsZero(v float64) bool {
	return math.Abs(v) < 1e-9
}

// IsEqual 判断两个浮点数是否相等（考虑精度）
func IsEqual(v1, v2 float64) bool {
	return math.Abs(v1-v2) < 1e-9
}

// FormatCurrency 格式化货币，保留两位小数
func FormatCurrency(v float64) string {
	return FormatNumber(v, 2)
}

// FormatNumber 格式化数字，保留指定位小数
func FormatNumber(v float64, scale int) string {
	// 四舍五入到指定小数位
	format := "%." + strconv.Itoa(scale) + "f"
	return fmt.Sprintf(format, v)
}

// ParseCurrency 解析货币字符串为浮点数
func ParseCurrency(s string) (float64, error) {
	// 移除可能存在的货币符号和千位分隔符
	s = strings.ReplaceAll(s, "$", "")
	s = strings.ReplaceAll(s, "¥", "")
	s = strings.ReplaceAll(s, "€", "")
	s = strings.ReplaceAll(s, ",", "")

	return strconv.ParseFloat(s, 64)
}
