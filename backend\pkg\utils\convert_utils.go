package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// StrToInt 字符串转整数
func StrToInt(str string) (int, error) {
	return strconv.Atoi(str)
}

// StrToInt64 字符串转int64
func StrToInt64(str string) (int64, error) {
	return strconv.ParseInt(str, 10, 64)
}

// StrToUint64 字符串转uint64
func StrToUint64(str string) (uint64, error) {
	return strconv.ParseUint(str, 10, 64)
}

// StrToFloat32 字符串转float32
func StrToFloat32(str string) (float32, error) {
	f, err := strconv.ParseFloat(str, 32)
	return float32(f), err
}

// StrToFloat64 字符串转float64
func StrToFloat64(str string) (float64, error) {
	return strconv.ParseFloat(str, 64)
}

// StrToBool 字符串转布尔值
func StrToBool(str string) (bool, error) {
	return strconv.ParseBool(str)
}

// IntToStr 整数转字符串
func IntToStr(i int) string {
	return strconv.Itoa(i)
}

// Int64ToStr int64转字符串
func Int64ToStr(i int64) string {
	return strconv.FormatInt(i, 10)
}

// Uint64ToStr uint64转字符串
func Uint64ToStr(i uint64) string {
	return strconv.FormatUint(i, 10)
}

// Float32ToStr float32转字符串
func Float32ToStr(f float32) string {
	return strconv.FormatFloat(float64(f), 'f', -1, 32)
}

// Float64ToStr float64转字符串
func Float64ToStr(f float64) string {
	return strconv.FormatFloat(f, 'f', -1, 64)
}

// BoolToStr 布尔值转字符串
func BoolToStr(b bool) string {
	return strconv.FormatBool(b)
}

// StrToIntWithDefault 字符串转整数，带默认值
func StrToIntWithDefault(str string, defaultValue int) int {
	result, err := StrToInt(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// StrToInt64WithDefault 字符串转int64，带默认值
func StrToInt64WithDefault(str string, defaultValue int64) int64 {
	result, err := StrToInt64(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// StrToUint64WithDefault 字符串转uint64，带默认值
func StrToUint64WithDefault(str string, defaultValue uint64) uint64 {
	result, err := StrToUint64(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// StrToFloat32WithDefault 字符串转float32，带默认值
func StrToFloat32WithDefault(str string, defaultValue float32) float32 {
	result, err := StrToFloat32(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// StrToFloat64WithDefault 字符串转float64，带默认值
func StrToFloat64WithDefault(str string, defaultValue float64) float64 {
	result, err := StrToFloat64(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// StrToBoolWithDefault 字符串转布尔值，带默认值
func StrToBoolWithDefault(str string, defaultValue bool) bool {
	result, err := StrToBool(str)
	if err != nil {
		return defaultValue
	}
	return result
}

// ToJSON 将对象转换为JSON字符串
func ToJSON(obj interface{}) (string, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 将JSON字符串转换为对象
func FromJSON(jsonStr string, obj interface{}) error {
	return json.Unmarshal([]byte(jsonStr), obj)
}

// ToMap 将对象转换为map
func ToMap(obj interface{}) (map[string]interface{}, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	return result, err
}

// MapToStruct 将map转换为结构体
func MapToStruct(m map[string]interface{}, obj interface{}) error {
	data, err := json.Marshal(m)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, obj)
}

// StructToStruct 将一个结构体转换为另一个结构体
func StructToStruct(src, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dst)
}

// SliceToString 将切片转换为字符串
func SliceToString(slice interface{}, sep string) string {
	if slice == nil {
		return ""
	}

	v := reflect.ValueOf(slice)
	if v.Kind() != reflect.Slice && v.Kind() != reflect.Array {
		return fmt.Sprintf("%v", slice)
	}

	var result []string
	for i := 0; i < v.Len(); i++ {
		result = append(result, fmt.Sprintf("%v", v.Index(i).Interface()))
	}

	return strings.Join(result, sep)
}

// StringToSlice 将字符串转换为切片
func StringToSlice(str, sep string) []string {
	if str == "" {
		return nil
	}

	return strings.Split(str, sep)
}

// ToInterface 将任意类型转换为interface{}
func ToInterface(value interface{}) interface{} {
	return value
}

// ToInterfaceSlice 将任意切片转换为[]interface{}
func ToInterfaceSlice(slice interface{}) ([]interface{}, error) {
	s := reflect.ValueOf(slice)
	if s.Kind() != reflect.Slice && s.Kind() != reflect.Array {
		return nil, fmt.Errorf("not a slice or array")
	}

	result := make([]interface{}, s.Len())
	for i := 0; i < s.Len(); i++ {
		result[i] = s.Index(i).Interface()
	}

	return result, nil
}

// BytesToStr 字节数组转字符串
func BytesToStr(bytes []byte) string {
	return string(bytes)
}

// StrToBytes 字符串转字节数组
func StrToBytes(str string) []byte {
	return []byte(str)
}
