package utils

import (
	"fmt"
	"strconv"
	"time"
)

const (
	// YYYY 年格式
	YYYY = "2006"
	// YYYY_MM 年月格式
	YYYY_MM = "2006-01"
	// YYYY_MM_DD 年月日格式
	YYYY_MM_DD = "2006-01-02"
	// YYYYMMDDHHMMSS 年月日时分秒格式（无分隔符）
	YYYYMMDDHHMMSS = "20060102150405"
	// YYYY_MM_DD_HH_MM_SS 年月日时分秒格式（有分隔符）
	YYYY_MM_DD_HH_MM_SS = "2006-01-02 15:04:05"
	// HH_MM_SS 时分秒格式
	HH_MM_SS = "15:04:05"
)

// GetNowTime 获取当前时间
func GetNowTime() time.Time {
	return time.Now()
}

// GetNowDate 获取当前日期字符串（年月日）
func GetNowDate() string {
	return time.Now().Format(YYYY_MM_DD)
}

// GetNowDateTime 获取当前日期时间字符串（年月日时分秒）
func GetNowDateTime() string {
	return time.Now().Format(YYYY_MM_DD_HH_MM_SS)
}

// GetDate 获取日期字符串（年月日）
func GetDate() string {
	return time.Now().Format(YYYY_MM_DD)
}

// GetTime 获取时间字符串（时分秒）
func GetTime() string {
	return time.Now().Format(HH_MM_SS)
}

// DateTimeNow 获取当前日期时间字符串，使用默认格式
func DateTimeNow() string {
	return time.Now().Format(YYYY_MM_DD_HH_MM_SS)
}

// DateTimeNowWithFormat 获取当前日期时间字符串，使用指定格式
func DateTimeNowWithFormat(format string) string {
	return time.Now().Format(format)
}

// FormatDateTime 格式化日期时间
func FormatDateTime(t time.Time, format string) string {
	return t.Format(format)
}

// ParseDateTime 解析日期时间字符串
func ParseDateTime(dateStr, format string) (time.Time, error) {
	return time.Parse(format, dateStr)
}

// DatePath 获取日期路径（年/月/日）
func DatePath() string {
	now := time.Now()
	return fmt.Sprintf("%d/%d/%d", now.Year(), now.Month(), now.Day())
}

// GetServerStartDate 获取服务器启动时间
var serverStartTime = time.Now()

func GetServerStartDate() time.Time {
	return serverStartTime
}

// DifferentDaysByMillisecond 计算两个日期相差的天数
func DifferentDaysByMillisecond(date1, date2 time.Time) int {
	// 将时间统一到UTC
	date1 = date1.UTC()
	date2 = date2.UTC()

	// 忽略时分秒，只保留日期部分
	date1 = time.Date(date1.Year(), date1.Month(), date1.Day(), 0, 0, 0, 0, time.UTC)
	date2 = time.Date(date2.Year(), date2.Month(), date2.Day(), 0, 0, 0, 0, time.UTC)

	// 计算相差的天数
	diff := int(date2.Sub(date1).Hours() / 24)
	if diff < 0 {
		diff = -diff
	}
	return diff
}

// TimeDistance 计算两个时间的距离
func TimeDistance(endDate, startDate time.Time) string {
	diff := endDate.Sub(startDate)

	// 计算相差的天数、小时数、分钟数和秒数
	days := int(diff.Hours() / 24)
	hours := int(diff.Hours()) % 24
	minutes := int(diff.Minutes()) % 60
	seconds := int(diff.Seconds()) % 60

	// 构建时间距离字符串
	var result string
	if days > 0 {
		result += strconv.Itoa(days) + "天"
	}
	if hours > 0 {
		result += strconv.Itoa(hours) + "小时"
	}
	if minutes > 0 {
		result += strconv.Itoa(minutes) + "分钟"
	}
	if seconds > 0 {
		result += strconv.Itoa(seconds) + "秒"
	}

	if result == "" {
		result = "0秒"
	}

	return result
}

// AddDays 日期加上指定天数
func AddDays(t time.Time, days int) time.Time {
	return t.AddDate(0, 0, days)
}

// AddMonths 日期加上指定月数
func AddMonths(t time.Time, months int) time.Time {
	return t.AddDate(0, months, 0)
}

// AddYears 日期加上指定年数
func AddYears(t time.Time, years int) time.Time {
	return t.AddDate(years, 0, 0)
}

// BeginOfDay 获取一天的开始时间
func BeginOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// EndOfDay 获取一天的结束时间
func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// BeginOfMonth 获取一个月的开始时间
func BeginOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// EndOfMonth 获取一个月的结束时间
func EndOfMonth(t time.Time) time.Time {
	return BeginOfMonth(t).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// BeginOfYear 获取一年的开始时间
func BeginOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
}

// EndOfYear 获取一年的结束时间
func EndOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 12, 31, 23, 59, 59, 999999999, t.Location())
}

// IsLeapYear 判断是否是闰年
func IsLeapYear(year int) bool {
	return year%4 == 0 && (year%100 != 0 || year%400 == 0)
}

// GetWeekday 获取星期几
func GetWeekday(t time.Time) string {
	weekdays := []string{"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"}
	return weekdays[t.Weekday()]
}

// GetQuarter 获取季度
func GetQuarter(t time.Time) int {
	month := t.Month()
	switch {
	case month <= 3:
		return 1
	case month <= 6:
		return 2
	case month <= 9:
		return 3
	default:
		return 4
	}
}
