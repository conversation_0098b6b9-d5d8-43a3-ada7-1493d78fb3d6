package utils

import (
	"regexp"
	"strings"
)

// DesensitizedType 脱敏类型
type DesensitizedType int

const (
	// PASSWORD 密码
	PASSWORD DesensitizedType = iota
	// CHINESE_NAME 中文姓名
	CHINESE_NAME
	// ID_CARD 身份证号
	ID_CARD
	// PHONE 手机号
	PHONE
	// EMAIL 电子邮件
	EMAIL
	// BANK_CARD 银行卡号
	BANK_CARD
	// ADDRESS 地址
	ADDRESS
	// CAR_LICENSE 车牌号
	CAR_LICENSE
)

// Password 密码脱敏
// 将密码全部转为*号
func Password(password string) string {
	if password == "" {
		return ""
	}
	return strings.Repeat("*", len(password))
}

// ChineseName 中文姓名脱敏
// 只显示第一个汉字，其他替换为*
// 例如：张三 -> 张*
func ChineseName(name string) string {
	if name == "" {
		return ""
	}

	nameRune := []rune(name)
	if len(nameRune) == 1 {
		return name
	}

	// 保留第一个字符
	return string(nameRune[0]) + strings.Repeat("*", len(nameRune)-1)
}

// IdCard 身份证号脱敏
// 保留前6位和后4位，中间用*代替
// 例如：110101199001011234 -> 110101********1234
func IdCard(idCard string) string {
	if idCard == "" {
		return ""
	}

	// 清除空格等字符
	idCard = strings.ReplaceAll(idCard, " ", "")
	if len(idCard) < 8 {
		return strings.Repeat("*", len(idCard))
	}

	// 身份证号前6位和后4位不脱敏
	return idCard[:6] + strings.Repeat("*", len(idCard)-10) + idCard[len(idCard)-4:]
}

// Phone 手机号脱敏
// 保留前3位和后4位，中间4位用*代替
// 例如：13812345678 -> 138****5678
func Phone(phone string) string {
	if phone == "" {
		return ""
	}

	// 清除空格、连字符等字符
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "-", "")

	if len(phone) < 7 {
		return strings.Repeat("*", len(phone))
	}

	// 手机号前3位和后4位不脱敏
	return phone[:3] + strings.Repeat("*", len(phone)-7) + phone[len(phone)-4:]
}

// Email 电子邮件脱敏
// 邮箱前缀仅显示第一个字母，其他隐藏，@及后面的地址显示
// 例如：<EMAIL> -> t***@example.com
func Email(email string) string {
	if email == "" {
		return ""
	}

	// 拆分邮箱
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return strings.Repeat("*", len(email))
	}

	prefix := parts[0]
	if len(prefix) == 0 {
		return email
	}

	// 邮箱前缀仅显示第一个字符
	if len(prefix) == 1 {
		return prefix + "@" + parts[1]
	}
	return prefix[:1] + strings.Repeat("*", len(prefix)-1) + "@" + parts[1]
}

// BankCard 银行卡号脱敏
// 保留前6位和后4位，中间用*代替
// 例如：6222021234567890123 -> 622202******0123
func BankCard(bankCard string) string {
	if bankCard == "" {
		return ""
	}

	// 清除空格等字符
	bankCard = strings.ReplaceAll(bankCard, " ", "")
	if len(bankCard) < 10 {
		return strings.Repeat("*", len(bankCard))
	}

	// 银行卡号前6位和后4位不脱敏
	return bankCard[:6] + strings.Repeat("*", len(bankCard)-10) + bankCard[len(bankCard)-4:]
}

// Address 地址脱敏
// 只显示到地区，不显示详细地址，比如：北京市海淀区****
func Address(address string) string {
	if address == "" {
		return ""
	}

	// 匹配中国地区格式：省市区/县
	reg := regexp.MustCompile(`^(北京市|天津市|上海市|重庆市|.*?省)?(.*?市)?(.*?区|.*?县)?`)
	matches := reg.FindStringSubmatch(address)

	if len(matches) == 0 {
		// 未匹配到地区格式，保留前6个字符
		if len(address) <= 6 {
			return address
		}
		return address[:6] + "****"
	}

	// 拼接地区信息
	region := ""
	for i := 1; i < len(matches); i++ {
		if matches[i] != "" {
			region += matches[i]
		}
	}

	if region == "" {
		if len(address) <= 6 {
			return address
		}
		return address[:6] + "****"
	}

	return region + "****"
}

// CarLicense 车牌号脱敏
// 保留车牌号的前两位和后一位，中间用*代替
// 例如：京A12345 -> 京A****5
func CarLicense(carLicense string) string {
	if carLicense == "" {
		return ""
	}

	// 清除空格等字符
	carLicense = strings.ReplaceAll(carLicense, " ", "")
	if len(carLicense) < 4 {
		return carLicense
	}

	// 车牌号前两位和最后一位不脱敏
	return carLicense[:2] + strings.Repeat("*", len(carLicense)-3) + carLicense[len(carLicense)-1:]
}

// Desensitize 通用脱敏方法
func Desensitize(value string, desensitizedType DesensitizedType) string {
	switch desensitizedType {
	case PASSWORD:
		return Password(value)
	case CHINESE_NAME:
		return ChineseName(value)
	case ID_CARD:
		return IdCard(value)
	case PHONE:
		return Phone(value)
	case EMAIL:
		return Email(value)
	case BANK_CARD:
		return BankCard(value)
	case ADDRESS:
		return Address(value)
	case CAR_LICENSE:
		return CarLicense(value)
	default:
		return value
	}
}
