package utils

import (
	"encoding/json"
	"strings"
)

// 常量定义
const (
	// 字典缓存键前缀
	DictKeyPrefix = "dict:"
	// 分隔符
	DictSeparator = ","
)

// SysDictData 系统字典数据结构
type SysDictData struct {
	DictCode   int64  `json:"dictCode"`   // 字典编码
	DictSort   int    `json:"dictSort"`   // 字典排序
	DictLabel  string `json:"dictLabel"`  // 字典标签
	DictValue  string `json:"dictValue"`  // 字典键值
	DictType   string `json:"dictType"`   // 字典类型
	CssClass   string `json:"cssClass"`   // 样式属性
	ListClass  string `json:"listClass"`  // 表格回显样式
	IsDefault  string `json:"isDefault"`  // 是否默认
	Status     string `json:"status"`     // 状态
	CreateBy   string `json:"createBy"`   // 创建者
	CreateTime string `json:"createTime"` // 创建时间
	UpdateBy   string `json:"updateBy"`   // 更新者
	UpdateTime string `json:"updateTime"` // 更新时间
	Remark     string `json:"remark"`     // 备注
}

// 字典缓存
var dictCache = make(map[string][]SysDictData)

// SetDictCache 设置字典缓存
func SetDictCache(dictType string, dictDatas []SysDictData) {
	dictCache[GetCacheKey(dictType)] = dictDatas
}

// GetDictCache 获取字典缓存
func GetDictCache(dictType string) []SysDictData {
	key := GetCacheKey(dictType)
	if dictDatas, ok := dictCache[key]; ok {
		return dictDatas
	}
	return nil
}

// GetDictLabel 根据字典类型和值获取字典标签
func GetDictLabel(dictType, dictValue string) string {
	return GetDictLabelWithSeparator(dictType, dictValue, DictSeparator)
}

// GetDictValue 根据字典类型和标签获取字典值
func GetDictValue(dictType, dictLabel string) string {
	return GetDictValueWithSeparator(dictType, dictLabel, DictSeparator)
}

// GetDictLabelWithSeparator 根据字典类型和值获取字典标签，使用指定分隔符
func GetDictLabelWithSeparator(dictType, dictValue, separator string) string {
	if dictValue == "" {
		return ""
	}

	dictDatas := GetDictCache(dictType)
	if dictDatas == nil {
		return ""
	}

	valueArr := strings.Split(dictValue, separator)
	var labelArr []string

	for _, value := range valueArr {
		for _, dictData := range dictDatas {
			if dictData.DictValue == value {
				labelArr = append(labelArr, dictData.DictLabel)
				break
			}
		}
	}

	return strings.Join(labelArr, separator)
}

// GetDictValueWithSeparator 根据字典类型和标签获取字典值，使用指定分隔符
func GetDictValueWithSeparator(dictType, dictLabel, separator string) string {
	if dictLabel == "" {
		return ""
	}

	dictDatas := GetDictCache(dictType)
	if dictDatas == nil {
		return ""
	}

	labelArr := strings.Split(dictLabel, separator)
	var valueArr []string

	for _, label := range labelArr {
		for _, dictData := range dictDatas {
			if dictData.DictLabel == label {
				valueArr = append(valueArr, dictData.DictValue)
				break
			}
		}
	}

	return strings.Join(valueArr, separator)
}

// GetDictValues 获取字典类型下所有的值
func GetDictValues(dictType string) string {
	dictDatas := GetDictCache(dictType)
	if dictDatas == nil {
		return ""
	}

	var values []string
	for _, dictData := range dictDatas {
		values = append(values, dictData.DictValue)
	}

	return strings.Join(values, DictSeparator)
}

// GetDictLabels 获取字典类型下所有的标签
func GetDictLabels(dictType string) string {
	dictDatas := GetDictCache(dictType)
	if dictDatas == nil {
		return ""
	}

	var labels []string
	for _, dictData := range dictDatas {
		labels = append(labels, dictData.DictLabel)
	}

	return strings.Join(labels, DictSeparator)
}

// RemoveDictCache 删除字典缓存
func RemoveDictCache(dictType string) {
	delete(dictCache, GetCacheKey(dictType))
}

// ClearDictCache 清空字典缓存
func ClearDictCache() {
	dictCache = make(map[string][]SysDictData)
}

// GetCacheKey 获取缓存键
func GetCacheKey(dictType string) string {
	return DictKeyPrefix + dictType
}

// MarshalDictData 序列化字典数据
func MarshalDictData(dictDatas []SysDictData) (string, error) {
	data, err := json.Marshal(dictDatas)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// UnmarshalDictData 反序列化字典数据
func UnmarshalDictData(data string) ([]SysDictData, error) {
	var dictDatas []SysDictData
	err := json.Unmarshal([]byte(data), &dictDatas)
	if err != nil {
		return nil, err
	}
	return dictDatas, nil
}
