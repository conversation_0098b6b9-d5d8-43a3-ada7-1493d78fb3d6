package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"io"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

// MD5 计算字符串的MD5哈希值
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// SHA1 计算字符串的SHA1哈希值
func SHA1(str string) string {
	h := sha1.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// SHA256 计算字符串的SHA256哈希值
func SHA256(str string) string {
	h := sha256.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// SHA512 计算字符串的SHA512哈希值
func SHA512(str string) string {
	h := sha512.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// Base64Encode Base64编码
func Base64Encode(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// Base64Decode Base64解码
func Base64Decode(str string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(str)
}

// EncryptPassword 使用bcrypt加密密码
func EncryptPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// MatchesPassword 验证密码是否匹配
func MatchesPassword(rawPassword, encodedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(encodedPassword), []byte(rawPassword))
	return err == nil
}

// AESEncrypt AES加密
func AESEncrypt(plaintext []byte, key []byte) ([]byte, error) {
	// 创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 创建随机数
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

// AESDecrypt AES解密
func AESDecrypt(ciphertext []byte, key []byte) ([]byte, error) {
	// 创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 检查密文长度
	if len(ciphertext) < gcm.NonceSize() {
		return nil, errors.New("ciphertext too short")
	}

	// 提取随机数
	nonce, ciphertext := ciphertext[:gcm.NonceSize()], ciphertext[gcm.NonceSize():]

	// 解密
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// GenerateRandomKey 生成随机密钥
func GenerateRandomKey(length int) ([]byte, error) {
	key := make([]byte, length)
	_, err := rand.Read(key)
	if err != nil {
		return nil, err
	}
	return key, nil
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) (string, error) {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)

	// 生成随机字节
	randomBytes := make([]byte, length)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", err
	}

	// 将随机字节映射到字符集
	for i, b := range randomBytes {
		result[i] = chars[b%byte(len(chars))]
	}

	return string(result), nil
}

// HexEncode 十六进制编码
func HexEncode(data []byte) string {
	return hex.EncodeToString(data)
}

// HexDecode 十六进制解码
func HexDecode(str string) ([]byte, error) {
	return hex.DecodeString(str)
}

// MaskSensitiveInfo 掩码敏感信息
func MaskSensitiveInfo(info string, start, end int, maskChar string) string {
	if info == "" {
		return ""
	}

	infoLen := len(info)

	// 处理边界情况
	if start < 0 {
		start = 0
	}
	if end > infoLen {
		end = infoLen
	}
	if start > end {
		start, end = end, start
	}

	// 构建掩码后的字符串
	var result string
	if start > 0 {
		result += info[:start]
	}

	for i := start; i < end; i++ {
		result += maskChar
	}

	if end < infoLen {
		result += info[end:]
	}

	return result
}

// MaskPhone 掩码手机号
func MaskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return MaskSensitiveInfo(phone, 3, 7, "*")
}

// MaskEmail 掩码邮箱
func MaskEmail(email string) string {
	if email == "" {
		return ""
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}

	username := parts[0]
	domain := parts[1]

	// 掩码用户名
	var maskedUsername string
	if len(username) <= 2 {
		maskedUsername = username
	} else {
		maskedUsername = username[:1] + strings.Repeat("*", len(username)-2) + username[len(username)-1:]
	}

	return maskedUsername + "@" + domain
}

// MaskIDCard 掩码身份证号
func MaskIDCard(idCard string) string {
	if len(idCard) != 18 {
		return idCard
	}
	return MaskSensitiveInfo(idCard, 6, 14, "*")
}

// MaskBankCard 掩码银行卡号
func MaskBankCard(bankCard string) string {
	if len(bankCard) < 8 {
		return bankCard
	}
	return MaskSensitiveInfo(bankCard, 4, len(bankCard)-4, "*")
}
