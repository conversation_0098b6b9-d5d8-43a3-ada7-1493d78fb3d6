package utils

import (
	"errors"
	"fmt"
	"runtime"
	"strings"
)

// GetExceptionMessage 获取异常消息
func GetExceptionMessage(err error) string {
	if err == nil {
		return ""
	}
	return err.Error()
}

// GetRootErrorMessage 获取根异常消息
func GetRootErrorMessage(err error) string {
	if err == nil {
		return ""
	}

	// 查找根原因
	var rootErr error = err
	for {
		unwrapped := errors.Unwrap(rootErr)
		if unwrapped == nil {
			break
		}
		rootErr = unwrapped
	}

	return rootErr.Error()
}

// WrapError 包装错误，添加上下文信息
func WrapError(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// FormatError 格式化错误信息，带调用栈
func FormatError(err error) string {
	if err == nil {
		return ""
	}

	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("Error: %s\n", err.Error()))

	// 获取调用栈
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(3, pcs[:])
	frames := runtime.CallersFrames(pcs[:n])

	sb.WriteString("Stack trace:\n")
	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			sb.WriteString(fmt.Sprintf("- %s:%d: %s\n", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}

	return sb.String()
}

// PrintError 打印错误信息到控制台
func PrintError(err error) {
	if err == nil {
		return
	}
	fmt.Println(FormatError(err))
}

// IsBusinessException 判断是否是业务异常
func IsBusinessException(err error) bool {
	if err == nil {
		return false
	}
	var be *BusinessException
	return errors.As(err, &be)
}

// NewBusinessException 创建业务异常
func NewBusinessException(message string) *BusinessException {
	return &BusinessException{
		Message: message,
	}
}

// NewBusinessExceptionWithCode 创建带错误码的业务异常
func NewBusinessExceptionWithCode(code string, message string) *BusinessException {
	return &BusinessException{
		Code:    code,
		Message: message,
	}
}

// BusinessException 业务异常
type BusinessException struct {
	// 错误码
	Code string
	// 错误消息
	Message string
}

// Error 实现error接口
func (e *BusinessException) Error() string {
	if e.Code != "" {
		return fmt.Sprintf("[%s] %s", e.Code, e.Message)
	}
	return e.Message
}

// ValidationException 数据验证异常
type ValidationException struct {
	// 验证字段
	Field string
	// 错误消息
	Message string
}

// Error 实现error接口
func (e *ValidationException) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("字段 [%s] 验证失败: %s", e.Field, e.Message)
	}
	return e.Message
}

// NewValidationException 创建数据验证异常
func NewValidationException(message string) *ValidationException {
	return &ValidationException{
		Message: message,
	}
}

// NewValidationExceptionWithField 创建带字段的数据验证异常
func NewValidationExceptionWithField(field, message string) *ValidationException {
	return &ValidationException{
		Field:   field,
		Message: message,
	}
}

// IsValidationException 判断是否是数据验证异常
func IsValidationException(err error) bool {
	if err == nil {
		return false
	}
	var ve *ValidationException
	return errors.As(err, &ve)
}
