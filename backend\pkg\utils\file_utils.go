package utils

import (
	"bufio"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"io"
	"io/ioutil"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
)

// FileInfo 文件信息
type FileInfo struct {
	Name      string `json:"name"`      // 文件名称
	Path      string `json:"path"`      // 文件路径
	Size      int64  `json:"size"`      // 文件大小
	Extension string `json:"extension"` // 文件扩展名
	IsDir     bool   `json:"isDir"`     // 是否是目录
}

// FileExists 判断文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}

// IsDir 判断是否是目录
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

// IsFile 判断是否是文件
func IsFile(path string) bool {
	return FileExists(path) && !IsDir(path)
}

// CreateDir 创建目录
func CreateDir(path string) error {
	return os.MkdirAll(path, os.ModePerm)
}

// DeleteFile 删除文件
func DeleteFile(path string) error {
	return os.Remove(path)
}

// DeleteDir 删除目录
func DeleteDir(path string) error {
	return os.RemoveAll(path)
}

// GetFileSize 获取文件大小
func GetFileSize(path string) int64 {
	info, err := os.Stat(path)
	if err != nil {
		return 0
	}
	return info.Size()
}

// GetFileExt 获取文件扩展名
func GetFileExt(filename string) string {
	return filepath.Ext(filename)
}

// GetSuffix 获取文件后缀（不含.）
func GetSuffix(filename string) string {
	ext := GetFileExt(filename)
	if ext != "" && len(ext) > 1 {
		return ext[1:] // 去掉.
	}
	return ""
}

// GetFileName 获取文件名
func GetFileName(path string) string {
	return filepath.Base(path)
}

// GetFileNameWithoutExt 获取文件名（不含扩展名）
func GetFileNameWithoutExt(filename string) string {
	ext := filepath.Ext(filename)
	return filename[0 : len(filename)-len(ext)]
}

// GetFilePath 获取文件路径
func GetFilePath(path string) string {
	return filepath.Dir(path)
}

// GetTempDir 获取系统临时目录
func GetTempDir() string {
	return os.TempDir()
}

// ReadFile 读取文件内容
func ReadFile(path string) ([]byte, error) {
	return ioutil.ReadFile(path)
}

// ReadFileToString 读取文件内容为字符串
func ReadFileToString(path string) (string, error) {
	data, err := ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// ReadLines 读取文件行
func ReadLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	return lines, scanner.Err()
}

// WriteFile 写入文件内容
func WriteFile(path string, data []byte) error {
	dir := filepath.Dir(path)
	if !FileExists(dir) {
		err := CreateDir(dir)
		if err != nil {
			return err
		}
	}
	return ioutil.WriteFile(path, data, 0644)
}

// WriteLines 写入文件行
func WriteLines(path string, lines []string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	for _, line := range lines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			return err
		}
	}
	return writer.Flush()
}

// AppendFile 追加文件内容
func AppendFile(path string, data []byte) error {
	file, err := os.OpenFile(path, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.Write(data)
	return err
}

// CopyFile 复制文件
func CopyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// GetFileMD5 获取文件MD5
func GetFileMD5(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// GetFileSHA1 获取文件SHA1
func GetFileSHA1(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha1.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// GetAllFiles 获取目录下所有文件
func GetAllFiles(dirPath string) ([]FileInfo, error) {
	var files []FileInfo

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		files = append(files, FileInfo{
			Name:      info.Name(),
			Path:      path,
			Size:      info.Size(),
			Extension: filepath.Ext(info.Name()),
			IsDir:     info.IsDir(),
		})

		return nil
	})

	return files, err
}

// GetFiles 获取目录下的文件
func GetFiles(dirPath string) ([]FileInfo, error) {
	var files []FileInfo

	fileInfos, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return nil, err
	}

	for _, info := range fileInfos {
		files = append(files, FileInfo{
			Name:      info.Name(),
			Path:      filepath.Join(dirPath, info.Name()),
			Size:      info.Size(),
			Extension: filepath.Ext(info.Name()),
			IsDir:     info.IsDir(),
		})
	}

	return files, nil
}

// SaveUploadedFile 保存上传的文件
func SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	// 创建目标文件
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	// 拷贝文件内容
	_, err = io.Copy(out, src)
	return err
}

// IsAllowedFileType 判断文件类型是否允许
func IsAllowedFileType(filename string, allowedExts []string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return false
	}

	// 如果ext以.开头，去掉.
	if ext[0] == '.' {
		ext = ext[1:]
	}

	for _, allowedExt := range allowedExts {
		allowedExt = strings.ToLower(allowedExt)
		// 如果allowedExt以.开头，去掉.
		if len(allowedExt) > 0 && allowedExt[0] == '.' {
			allowedExt = allowedExt[1:]
		}

		if ext == allowedExt {
			return true
		}
	}

	return false
}
