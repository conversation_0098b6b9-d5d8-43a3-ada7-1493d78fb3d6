package utils

import (
	"math"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// 常量定义
const (
	// 页码参数名
	PageNumParam = "pageNum"
	// 每页数量参数名
	PageSizeParam = "pageSize"
	// 排序字段参数名
	OrderByColumnParam = "orderByColumn"
	// 排序方向参数名
	IsAscParam = "isAsc"
	// 默认页码
	DefaultPageNum = 1
	// 默认每页数量
	DefaultPageSize = 10
	// 默认是否启用计数
	DefaultCount = true
)

// PageRequest 分页请求参数
type PageRequest struct {
	// 当前页码
	PageNum int
	// 每页数量
	PageSize int
	// 排序字段
	OrderByColumn string
	// 排序方向（asc/desc）
	IsAsc string
	// 是否查询总数
	NeedCount bool
}

// PageResult 分页结果
type PageResult struct {
	// 当前页码
	PageNum int `json:"pageNum"`
	// 每页数量
	PageSize int `json:"pageSize"`
	// 总记录数
	Total int64 `json:"total"`
	// 总页数
	Pages int `json:"pages"`
	// 数据列表
	List interface{} `json:"list"`
}

// GetPageRequest 获取分页请求参数
func GetPageRequest(c *gin.Context) *PageRequest {
	// 获取页码
	pageNumStr := c.Query(PageNumParam)
	pageNum := DefaultPageNum
	if pageNumStr != "" {
		num, err := strconv.Atoi(pageNumStr)
		if err == nil && num > 0 {
			pageNum = num
		}
	}

	// 获取每页数量
	pageSizeStr := c.Query(PageSizeParam)
	pageSize := DefaultPageSize
	if pageSizeStr != "" {
		size, err := strconv.Atoi(pageSizeStr)
		if err == nil && size > 0 {
			pageSize = size
		}
	}

	// 获取排序字段
	orderByColumn := c.Query(OrderByColumnParam)

	// 获取排序方向
	isAsc := c.Query(IsAscParam)

	return &PageRequest{
		PageNum:       pageNum,
		PageSize:      pageSize,
		OrderByColumn: orderByColumn,
		IsAsc:         isAsc,
		NeedCount:     DefaultCount,
	}
}

// StartPage 开始分页
func StartPage(c *gin.Context) *PageRequest {
	return GetPageRequest(c)
}

// BuildPageResult 构建分页结果
func BuildPageResult(list interface{}, total int64, pageRequest *PageRequest) *PageResult {
	return &PageResult{
		PageNum:  pageRequest.PageNum,
		PageSize: pageRequest.PageSize,
		Total:    total,
		Pages:    int(math.Ceil(float64(total) / float64(pageRequest.PageSize))),
		List:     list,
	}
}

// GetPageParams 获取分页参数
func GetPageParams(pageRequest *PageRequest) (offset, limit int) {
	offset = (pageRequest.PageNum - 1) * pageRequest.PageSize
	limit = pageRequest.PageSize
	return
}

// GetOrderBy 获取排序条件
func GetOrderBy(pageRequest *PageRequest) string {
	if pageRequest.OrderByColumn == "" {
		return ""
	}

	// 转换为下划线格式（驼峰转下划线）
	column := ToUnderScoreCase(pageRequest.OrderByColumn)

	// 处理排序方向
	orderBy := column
	if pageRequest.IsAsc == "desc" {
		orderBy += " DESC"
	} else {
		orderBy += " ASC"
	}

	return orderBy
}

// ToUnderScoreCase 驼峰转下划线
func ToUnderScoreCase(str string) string {
	var result []byte
	for i, b := range str {
		if i > 0 && b >= 'A' && b <= 'Z' {
			result = append(result, '_')
		}
		result = append(result, byte(b))
	}
	return strings.ToLower(string(result))
}
