package utils

import (
	"strings"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// 常量定义
const (
	// 管理员ID
	AdminID int64 = 1
	// Token键名
	TokenKey = "token"
	// 上下文用户键名
	LoginUserKey = "login_user"
)

// GetUserId 获取当前用户ID
func GetUserId(c *gin.Context) int64 {
	user := GetLoginUser(c)
	if user != nil {
		return user.UserId
	}
	return 0
}

// GetDeptId 获取当前部门ID
func GetDeptId(c *gin.Context) int64 {
	user := GetLoginUser(c)
	if user != nil {
		return user.DeptId
	}
	return 0
}

// GetUsername 获取当前用户名
func GetUsername(c *gin.Context) string {
	user := GetLoginUser(c)
	if user != nil {
		return user.Username
	}
	return ""
}

// GetLoginUser 获取当前登录用户
func GetLoginUser(c *gin.Context) *LoginUser {
	if c == nil {
		return nil
	}
	user, exists := c.Get(LoginUserKey)
	if !exists {
		return nil
	}
	if loginUser, ok := user.(*LoginUser); ok {
		return loginUser
	}
	return nil
}

// EncryptPassword 密码加密
func EncryptPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// MatchesPassword 密码匹配
func MatchesPassword(rawPassword, encodedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(encodedPassword), []byte(rawPassword))
	return err == nil
}

// IsAdmin 判断是否是管理员
func IsAdmin(userId int64) bool {
	return userId != 0 && userId == AdminID
}

// IsAdmin 判断当前用户是否是管理员
func IsAdminUser(c *gin.Context) bool {
	return IsAdmin(GetUserId(c))
}

// HasPermi 判断用户是否拥有某个权限
func HasPermi(c *gin.Context, permission string) bool {
	if IsAdminUser(c) {
		return true
	}

	if permission == "" {
		return false
	}

	user := GetLoginUser(c)
	if user == nil || user.Permissions == nil {
		return false
	}

	return HasPermissions(user.Permissions, permission)
}

// HasPermissions 判断权限列表中是否包含某个权限
func HasPermissions(authorities []string, permission string) bool {
	if len(authorities) == 0 || permission == "" {
		return false
	}

	for _, authority := range authorities {
		if authority == "*:*:*" {
			return true
		}

		if matchPermission(authority, permission) {
			return true
		}
	}
	return false
}

// HasRole 判断用户是否拥有某个角色
func HasRole(c *gin.Context, role string) bool {
	if IsAdminUser(c) {
		return true
	}

	if role == "" {
		return false
	}

	user := GetLoginUser(c)
	if user == nil || user.Roles == nil {
		return false
	}

	return HasRoles(user.Roles, role)
}

// HasRoles 判断角色列表中是否包含某个角色
func HasRoles(roles []string, role string) bool {
	if len(roles) == 0 || role == "" {
		return false
	}

	for _, r := range roles {
		if r == "admin" {
			return true
		}

		if strings.EqualFold(r, role) {
			return true
		}
	}
	return false
}

// matchPermission 匹配权限
func matchPermission(pattern, permission string) bool {
	patternParts := strings.Split(pattern, ":")
	permissionParts := strings.Split(permission, ":")

	if len(patternParts) != 3 || len(permissionParts) != 3 {
		return false
	}

	// 模块匹配
	if patternParts[0] != "*" && patternParts[0] != permissionParts[0] {
		return false
	}

	// 功能匹配
	if patternParts[1] != "*" && patternParts[1] != permissionParts[1] {
		return false
	}

	// 操作匹配
	if patternParts[2] != "*" && patternParts[2] != permissionParts[2] {
		return false
	}

	return true
}

// LoginUser 登录用户模型
type LoginUser struct {
	UserId      int64    `json:"userId"`
	DeptId      int64    `json:"deptId"`
	Username    string   `json:"username"`
	Permissions []string `json:"permissions"`
	Roles       []string `json:"roles"`
}
