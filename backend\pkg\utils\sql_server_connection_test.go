package utils

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/denisenkom/go-mssqldb"
)

// SQLServerConfig SQL Server配置
type SQLServerConfig struct {
	Host     string // 主机地址
	Port     int    // 端口号
	User     string // 用户名
	Password string // 密码
	Database string // 数据库名
}

// BuildConnectionString 构建连接字符串
func (c *SQLServerConfig) BuildConnectionString() string {
	return fmt.Sprintf("server=%s;port=%d;user id=%s;password=%s;database=%s",
		c.Host, c.Port, c.User, c.Password, c.Database)
}

// TestConnection 测试SQL Server连接
func TestConnection(config *SQLServerConfig) error {
	// 构建连接字符串
	connStr := config.BuildConnectionString()

	// 打开连接
	db, err := sql.Open("sqlserver", connStr)
	if err != nil {
		return fmt.Errorf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 设置连接参数
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)

	// 测试连接
	err = db.Ping()
	if err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return nil
}

// TestConnectionWithDSN 使用DSN字符串测试连接
func TestConnectionWithDSN(dsn string) error {
	// 打开连接
	db, err := sql.Open("sqlserver", dsn)
	if err != nil {
		return fmt.Errorf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 设置连接参数
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)

	// 测试连接
	err = db.Ping()
	if err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return nil
}

// GetServerVersion 获取SQL Server版本
func GetServerVersion(config *SQLServerConfig) (string, error) {
	// 构建连接字符串
	connStr := config.BuildConnectionString()

	// 打开连接
	db, err := sql.Open("sqlserver", connStr)
	if err != nil {
		return "", fmt.Errorf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 查询版本
	var version string
	err = db.QueryRow("SELECT @@VERSION").Scan(&version)
	if err != nil {
		return "", fmt.Errorf("查询数据库版本失败: %v", err)
	}

	return version, nil
}

// ListDatabases 列出所有数据库
func ListDatabases(config *SQLServerConfig) ([]string, error) {
	// 构建连接字符串
	connStr := config.BuildConnectionString()

	// 打开连接
	db, err := sql.Open("sqlserver", connStr)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 查询数据库列表
	rows, err := db.Query("SELECT name FROM sys.databases WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb') ORDER BY name")
	if err != nil {
		return nil, fmt.Errorf("查询数据库列表失败: %v", err)
	}
	defer rows.Close()

	// 处理结果
	var databases []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, fmt.Errorf("扫描数据库名称失败: %v", err)
		}
		databases = append(databases, name)
	}

	// 检查是否有错误
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历数据库列表失败: %v", err)
	}

	return databases, nil
}

// TestQuery 测试查询
func TestQuery(config *SQLServerConfig, query string) ([]map[string]interface{}, error) {
	// 构建连接字符串
	connStr := config.BuildConnectionString()

	// 打开连接
	db, err := sql.Open("sqlserver", connStr)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 执行查询
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("执行查询失败: %v", err)
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("获取列信息失败: %v", err)
	}

	// 准备结果集
	var results []map[string]interface{}

	// 处理每一行
	for rows.Next() {
		// 创建一个值数组，用于存储扫描结果
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range columns {
			valuePtrs[i] = &values[i]
		}

		// 扫描当前行
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("扫描行数据失败: %v", err)
		}

		// 构建行数据映射
		rowData := make(map[string]interface{})
		for i, col := range columns {
			var v interface{}
			val := values[i]
			b, ok := val.([]byte)
			if ok {
				v = string(b)
			} else {
				v = val
			}
			rowData[col] = v
		}

		// 添加到结果集
		results = append(results, rowData)
	}

	// 检查是否有错误
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %v", err)
	}

	return results, nil
}

// Main 主函数，用于命令行调用
func SQLServerConnectionTestMain(args []string) {
	// 默认配置
	config := &SQLServerConfig{
		Host:     "localhost",
		Port:     1433,
		User:     "sa",
		Password: "Password123!",
		Database: "master",
	}

	// 解析命令行参数
	if len(args) >= 5 {
		config.Host = args[0]
		port, err := fmt.Sscanf(args[1], "%d", &config.Port)
		if err != nil || port != 1 {
			config.Port = 1433
		}
		config.User = args[2]
		config.Password = args[3]
		config.Database = args[4]
	} else if len(args) > 0 {
		fmt.Println("用法: SQLServerConnectionTest <host> <port> <user> <password> <database>")
		return
	}

	// 测试连接
	fmt.Printf("正在连接到 SQL Server: %s:%d/%s\n", config.Host, config.Port, config.Database)
	err := TestConnection(config)
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		return
	}

	fmt.Println("连接成功!")

	// 获取版本信息
	version, err := GetServerVersion(config)
	if err != nil {
		fmt.Printf("获取版本信息失败: %v\n", err)
	} else {
		fmt.Printf("SQL Server 版本: %s\n", version)
	}
}
