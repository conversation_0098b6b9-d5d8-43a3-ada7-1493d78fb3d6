package utils

import (
	"bytes"
	"fmt"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"
	"unicode"
)

const (
	// NULLSTR 空字符串
	NULLSTR = ""
	// SEPARATOR 分隔符
	SEPARATOR = ','
	// ASTERISK 星号
	ASTERISK = '*'
)

// IsEmpty 判断字符串是否为空
func IsEmpty(str string) bool {
	return str == "" || len(strings.TrimSpace(str)) == 0
}

// IsNotEmpty 判断字符串是否不为空
func IsNotEmpty(str string) bool {
	return !IsEmpty(str)
}

// IsNull 判断对象是否为空
func IsNull(obj interface{}) bool {
	if obj == nil {
		return true
	}

	v := reflect.ValueOf(obj)
	switch v.Kind() {
	case reflect.String:
		return v.Len() == 0
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	case reflect.Interface, reflect.Ptr:
		return v.IsNil()
	}

	return false
}

// IsNotNull 判断对象是否不为空
func IsNotNull(obj interface{}) bool {
	return !IsNull(obj)
}

// IsArray 判断对象是否为数组
func IsArray(obj interface{}) bool {
	if obj == nil {
		return false
	}

	v := reflect.ValueOf(obj)
	kind := v.Kind()
	return kind == reflect.Array || kind == reflect.Slice
}

// Trim 去除字符串两端空格
func Trim(str string) string {
	return strings.TrimSpace(str)
}

// Hide 隐藏字符串指定位置的字符
func Hide(str string, startInclude int, endExclude int) string {
	if IsEmpty(str) {
		return str
	}

	runes := []rune(str)
	length := len(runes)

	if startInclude < 0 {
		startInclude = 0
	}
	if endExclude > length {
		endExclude = length
	}
	if startInclude > endExclude {
		return str
	}

	builder := bytes.Buffer{}
	for i := 0; i < length; i++ {
		if i >= startInclude && i < endExclude {
			builder.WriteRune('*')
		} else {
			builder.WriteRune(runes[i])
		}
	}
	return builder.String()
}

// Substring 截取字符串
func Substring(str string, start int, end ...int) string {
	length := len(str)

	// 处理负数索引
	if start < 0 {
		start = length + start
	}

	if start > length {
		return ""
	}

	if len(end) > 0 {
		endIndex := end[0]

		// 处理负数索引
		if endIndex < 0 {
			endIndex = length + endIndex
		}

		if endIndex > length {
			endIndex = length
		}

		if endIndex < start {
			return ""
		}

		if start < 0 {
			start = 0
		}

		return str[start:endIndex]
	}

	return str[start:]
}

// SubstringBetweenLast 获取最后一个指定字符串之间的子字符串
func SubstringBetweenLast(str, open, close string) string {
	if IsEmpty(str) || IsEmpty(open) || IsEmpty(close) {
		return ""
	}

	lastOpenIndex := strings.LastIndex(str, open)
	if lastOpenIndex == -1 {
		return ""
	}

	startIndex := lastOpenIndex + len(open)
	closeIndex := strings.Index(str[startIndex:], close)
	if closeIndex == -1 {
		return ""
	}

	return str[startIndex : startIndex+closeIndex]
}

// HasText 判断字符串是否有文本内容
func HasText(str string) bool {
	return IsNotEmpty(str)
}

// Format 格式化字符串
func Format(template string, params ...interface{}) string {
	if IsEmpty(template) {
		return ""
	}

	if len(params) == 0 {
		return template
	}

	return fmt.Sprintf(template, params...)
}

// IsHTTP 判断链接是否为HTTP链接
func IsHTTP(link string) bool {
	if IsEmpty(link) {
		return false
	}

	return strings.HasPrefix(strings.ToLower(link), "http://") || strings.HasPrefix(strings.ToLower(link), "https://")
}

// Str2List 字符串转列表
func Str2List(str, sep string) []string {
	return Str2ListWithOptions(str, sep, true, true)
}

// Str2ListWithOptions 字符串转列表，带选项
func Str2ListWithOptions(str, sep string, filterBlank, trim bool) []string {
	if IsEmpty(str) {
		return nil
	}

	// 使用指定分隔符分割字符串
	split := strings.Split(str, sep)
	result := make([]string, 0, len(split))

	for _, s := range split {
		if filterBlank && IsEmpty(s) {
			continue
		}

		if trim {
			s = Trim(s)
		}

		result = append(result, s)
	}

	return result
}

// ContainsAny 判断集合中是否包含指定字符串中的任意一个字符
func ContainsAny(collection []string, str string) bool {
	if len(collection) == 0 || IsEmpty(str) {
		return false
	}

	for _, item := range collection {
		if strings.Contains(str, item) {
			return true
		}
	}

	return false
}

// ContainsAnyIgnoreCase 判断集合中是否包含指定字符串中的任意一个字符（忽略大小写）
func ContainsAnyIgnoreCase(cs string, searchCharSequences string) bool {
	if IsEmpty(cs) || IsEmpty(searchCharSequences) {
		return false
	}

	csLower := strings.ToLower(cs)
	searchLower := strings.ToLower(searchCharSequences)

	return strings.Contains(csLower, searchLower)
}

// ToUnderScoreCase 将驼峰式命名转换为下划线命名
func ToUnderScoreCase(str string) string {
	if IsEmpty(str) {
		return str
	}

	var result bytes.Buffer
	for i, r := range str {
		if unicode.IsUpper(r) {
			if i > 0 {
				result.WriteByte('_')
			}
			result.WriteRune(unicode.ToLower(r))
		} else {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// InStringIgnoreCase 判断字符串是否在另一个字符串中（忽略大小写）
func InStringIgnoreCase(str, s string) bool {
	if IsEmpty(str) || IsEmpty(s) {
		return false
	}
	return strings.Contains(strings.ToLower(str), strings.ToLower(s))
}

// ToCamelCase 将下划线命名转换为驼峰式命名
func ToCamelCase(str string) string {
	if IsEmpty(str) {
		return str
	}

	var result bytes.Buffer
	upperNext := false

	for _, r := range str {
		if r == '_' {
			upperNext = true
		} else if upperNext {
			result.WriteRune(unicode.ToUpper(r))
			upperNext = false
		} else {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// ConvertToCamelCase 将下划线命名转换为首字母大写的驼峰式命名
func ConvertToCamelCase(str string) string {
	if IsEmpty(str) {
		return str
	}

	camel := ToCamelCase(str)
	if len(camel) > 0 {
		return string(unicode.ToUpper(rune(camel[0]))) + camel[1:]
	}

	return camel
}

// Matches 判断字符串是否匹配字符串列表中的任意一个
func Matches(str string, strs []string) bool {
	if IsEmpty(str) || len(strs) == 0 {
		return false
	}

	for _, pattern := range strs {
		if IsMatch(pattern, str) {
			return true
		}
	}

	return false
}

// IsMatch 判断字符串是否匹配通配符表达式
func IsMatch(pattern, str string) bool {
	if pattern == str {
		return true
	}

	// 将通配符表达式转换为正则表达式
	regexPattern := "^"
	for i := 0; i < len(pattern); i++ {
		switch pattern[i] {
		case '*':
			regexPattern += ".*"
		case '?':
			regexPattern += "."
		case '(', ')', '[', ']', '{', '}', '+', '.', '\\', '^', '$', '|':
			regexPattern += "\\" + string(pattern[i])
		default:
			regexPattern += string(pattern[i])
		}
	}
	regexPattern += "$"

	// 编译正则表达式
	regex, err := regexp.Compile(regexPattern)
	if err != nil {
		return false
	}

	// 判断是否匹配
	return regex.MatchString(str)
}

// Padl 数字左填充
func Padl(num interface{}, size int) string {
	return PadlChar(fmt.Sprintf("%v", num), size, '0')
}

// PadlChar 字符串左填充
func PadlChar(s string, size int, ch rune) string {
	if len(s) >= size {
		return s
	}

	padStr := strings.Repeat(string(ch), size-len(s))
	return padStr + s
}

// GetFileNameWithoutExtension 获取文件名（不含扩展名）
func GetFileNameWithoutExtension(fileName string) string {
	base := filepath.Base(fileName)
	ext := filepath.Ext(base)
	return base[0 : len(base)-len(ext)]
}

// GetFileExtension 获取文件扩展名
func GetFileExtension(fileName string) string {
	return filepath.Ext(fileName)
}
