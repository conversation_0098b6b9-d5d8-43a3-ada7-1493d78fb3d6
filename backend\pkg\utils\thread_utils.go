package utils

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"
)

// Logger 日志接口
type Logger interface {
	Debug(format string, args ...interface{})
	Info(format string, args ...interface{})
	Warn(format string, args ...interface{})
	Error(format string, args ...interface{})
	Fatal(format string, args ...interface{})
}

// 默认日志实现
type defaultLogger struct{}

func (l *defaultLogger) Debug(format string, args ...interface{}) {
	fmt.Printf("[DEBUG] "+format+"\n", args...)
}

func (l *defaultLogger) Info(format string, args ...interface{}) {
	fmt.Printf("[INFO] "+format+"\n", args...)
}

func (l *defaultLogger) Warn(format string, args ...interface{}) {
	fmt.Printf("[WARN] "+format+"\n", args...)
}

func (l *defaultLogger) Error(format string, args ...interface{}) {
	fmt.Printf("[ERROR] "+format+"\n", args...)
}

func (l *defaultLogger) Fatal(format string, args ...interface{}) {
	fmt.Printf("[FATAL] "+format+"\n", args...)
}

// logger 日志记录器
var logger Logger = &defaultLogger{}

// SetLogger 设置日志记录器
func SetLogger(l Logger) {
	if l != nil {
		logger = l
	}
}

// Sleep 线程睡眠
func Sleep(milliseconds int64) {
	time.Sleep(time.Duration(milliseconds) * time.Millisecond)
}

// ShutdownAndAwaitTermination 关闭线程池并等待终止
func ShutdownAndAwaitTermination(wg *sync.WaitGroup, timeout time.Duration) {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建通道用于通知完成
	done := make(chan struct{})

	// 启动goroutine等待所有任务完成
	go func() {
		wg.Wait()
		close(done)
	}()

	// 等待完成或超时
	select {
	case <-done:
		logger.Info("线程池已正常关闭")
	case <-ctx.Done():
		logger.Warn("线程池关闭超时")
	}
}

// PrintException 打印异常信息
func PrintException(r interface{}, err interface{}) {
	logger.Error("线程执行异常: %v", err)
	logger.Error("异常堆栈: %s", debug.Stack())
}

// SafeGo 安全地启动goroutine
func SafeGo(f func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				PrintException(nil, r)
			}
		}()
		f()
	}()
}

// SafeGoWithRecover 安全地启动goroutine，带恢复函数
func SafeGoWithRecover(f func(), recover func(interface{})) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				if recover != nil {
					recover(r)
				} else {
					PrintException(nil, r)
				}
			}
		}()
		f()
	}()
}

// RunWithTimeout 运行带超时的任务
func RunWithTimeout(f func() interface{}, timeout time.Duration) (result interface{}, ok bool) {
	resultChan := make(chan interface{}, 1)
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 启动任务
	go func() {
		defer func() {
			if r := recover(); r != nil {
				PrintException(nil, r)
				resultChan <- nil
			}
		}()
		resultChan <- f()
	}()

	// 等待结果或超时
	select {
	case result = <-resultChan:
		return result, true
	case <-ctx.Done():
		logger.Warn("任务执行超时")
		return nil, false
	}
}

// NewGoPool 创建新的goroutine池
func NewGoPool(maxWorkers int) *GoPool {
	return &GoPool{
		maxWorkers:  maxWorkers,
		taskQueue:   make(chan func(), 100),
		workerGroup: &sync.WaitGroup{},
	}
}

// GoPool goroutine池
type GoPool struct {
	maxWorkers  int
	taskQueue   chan func()
	workerGroup *sync.WaitGroup
	started     bool
	mutex       sync.Mutex
}

// Start 启动线程池
func (p *GoPool) Start() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.started {
		return
	}

	p.started = true
	for i := 0; i < p.maxWorkers; i++ {
		p.workerGroup.Add(1)
		go func(workerId int) {
			defer p.workerGroup.Done()
			for task := range p.taskQueue {
				func() {
					defer func() {
						if r := recover(); r != nil {
							PrintException(nil, r)
						}
					}()
					task()
				}()
			}
		}(i)
	}
}

// Submit 提交任务
func (p *GoPool) Submit(task func()) {
	if !p.started {
		p.Start()
	}
	p.taskQueue <- task
}

// Shutdown 关闭线程池
func (p *GoPool) Shutdown() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.started {
		return
	}

	close(p.taskQueue)
	p.started = false
}

// AwaitTermination 等待线程池终止
func (p *GoPool) AwaitTermination(timeout time.Duration) bool {
	done := make(chan struct{})
	go func() {
		p.workerGroup.Wait()
		close(done)
	}()

	select {
	case <-done:
		return true
	case <-time.After(timeout):
		return false
	}
}
