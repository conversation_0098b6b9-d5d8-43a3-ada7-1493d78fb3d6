package main

import (
    "fmt"
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    fmt.Println("Starting minimal WOSM backend...")
    
    // Create Gin engine
    r := gin.Default()
    
    // Health check endpoint
    r.GET("/health", func(c *gin.Context) {
        c.<PERSON><PERSON>(http.StatusOK, gin.H{
            "status":  "up",
            "service": "WOSM-Backend",
            "version": "1.0.0",
        })
    })
    
    // Simple API endpoint
    r.GET("/api/test", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "WOSM Backend is running!",
            "data":    "Java to Go migration successful",
        })
    })
    
    fmt.Println("Server starting on http://localhost:8080")
    fmt.Println("Health check: http://localhost:8080/health")
    fmt.Println("Test API: http://localhost:8080/api/test")
    fmt.Println("Press Ctrl+C to stop")
    
    // Start server
    r.Run(":8080")
}
