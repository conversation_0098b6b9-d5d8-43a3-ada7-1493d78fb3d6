# 简化版一键启动脚本
Write-Host "=== Java到Go迁移项目启动 ===" -ForegroundColor Green

# 检查Go环境
Write-Host "检查Go环境..." -ForegroundColor Yellow
try {
    $goVersion = go version
    Write-Host "Go环境正常: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "Go环境未安装，请先安装Go" -ForegroundColor Red
    pause
    exit 1
}

# 检查必要文件
Write-Host "检查项目文件..." -ForegroundColor Yellow
if (-not (Test-Path "go.mod")) {
    Write-Host "go.mod文件不存在" -ForegroundColor Red
    pause
    exit 1
}

if (-not (Test-Path "cmd\main.go")) {
    Write-Host "main.go文件不存在" -ForegroundColor Red
    pause
    exit 1
}

# 整理依赖
Write-Host "整理Go依赖..." -ForegroundColor Yellow
go mod tidy

# 构建项目
Write-Host "构建项目..." -ForegroundColor Yellow
go build -o wosm-backend.exe cmd\main.go

if (Test-Path "wosm-backend.exe") {
    Write-Host "构建成功!" -ForegroundColor Green
} else {
    Write-Host "构建失败" -ForegroundColor Red
    pause
    exit 1
}

# 启动应用
Write-Host "启动应用..." -ForegroundColor Green
Write-Host "应用将在 http://localhost:8080 启动" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止应用" -ForegroundColor Yellow
Write-Host ""

.\wosm-backend.exe

Write-Host "应用已停止" -ForegroundColor Yellow
pause
