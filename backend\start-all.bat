@echo off
setlocal

echo ===================================
echo RuoYi-Go 一键启动脚本 (Windows)
echo ===================================

set PROJECT_DIR=%~dp0
cd %PROJECT_DIR%

echo 1. 启动Redis服务...
start "Redis启动" cmd /c start-redis.bat

echo 2. 检查SQL Server服务...
start "SQL Server启动" cmd /c start-sqlserver.bat

echo 等待服务启动完成...
timeout /t 5 > nul

echo 3. 启动后端服务...
echo 注意: 请确保在configs/config.yaml中设置了正确的数据库和Redis连接信息
echo.
echo 按任意键启动后端服务...
pause > nul

call start-dev.bat

endlocal 