@echo off
setlocal

echo ===================================
echo RuoYi-Go 开发环境启动脚本 (Windows)
echo ===================================

:: 设置变量
set CONFIG_DIR=configs
set PROJECT_DIR=%~dp0
cd %PROJECT_DIR%

:: 检查Go是否安装
where go >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Go。请确保Go已安装并添加到PATH中。
    pause
    exit /b 1
)

:: 显示Go版本
echo 使用的Go版本:
go version

:: 下载依赖
echo 下载依赖...
go mod tidy

:: 创建日志和上传目录
if not exist logs mkdir logs
if not exist uploads mkdir uploads

:: 启动应用
echo 启动应用...
echo 按Ctrl+C可以停止服务
go run cmd/main.go

pause 