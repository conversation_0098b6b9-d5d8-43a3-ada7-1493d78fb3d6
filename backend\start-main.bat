@echo off
title WOSM Go Backend - Main Project
echo ================================
echo   WOSM Go Backend (Main Project)
echo ================================
echo.

echo Setting environment variables...

REM 数据库配置
set DB_HOST=localhost
set DB_PORT=1433
set DB_NAME=wosm
set DB_USERNAME=sa
set DB_PASSWORD=F@2233
set DB_TYPE=sqlserver

REM Redis配置 (可选，如果没有Redis可以注释掉)
set REDIS_HOST=localhost
set REDIS_PORT=6379
set REDIS_PASSWORD=
set REDIS_DB=0

REM JWT配置
set JWT_SECRET=wosm-secret-key-2024-main-project
set JWT_ISSUER=wosm-main

REM 应用配置
set APP_ENV=dev
set SERVER_HOST=0.0.0.0
set SERVER_PORT=8080

echo Environment variables set.
echo.
echo Starting WOSM Go Backend (Main Project)...
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo Server: http://%SERVER_HOST%:%SERVER_PORT%
echo.

REM 停止现有进程
taskkill /f /im main.exe 2>nul
taskkill /f /im wosm-complete.exe 2>nul
timeout /t 2 /nobreak >nul

REM 启动主项目
go run main.go

pause
