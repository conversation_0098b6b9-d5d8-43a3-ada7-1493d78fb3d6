Write-Host "================================" -ForegroundColor Green
Write-Host "  WOSM Go Backend (Main Project)" -ForegroundColor Green  
Write-Host "================================" -ForegroundColor Green
Write-Host ""

Write-Host "Setting environment variables..." -ForegroundColor Yellow

# 数据库配置
$env:DB_HOST = "localhost"
$env:DB_PORT = "1433"
$env:DB_NAME = "wosm"
$env:DB_USERNAME = "sa"
$env:DB_PASSWORD = "F@2233"
$env:DB_TYPE = "sqlserver"

# Redis配置
$env:REDIS_HOST = "localhost"
$env:REDIS_PORT = "6379"
$env:REDIS_PASSWORD = ""
$env:REDIS_DB = "0"

# JWT配置
$env:JWT_SECRET = "wosm-secret-key-2024-main-project"
$env:JWT_ISSUER = "wosm-main"

# 应用配置
$env:APP_ENV = "dev"
$env:SERVER_HOST = "0.0.0.0"
$env:SERVER_PORT = "8080"

Write-Host "Environment variables set." -ForegroundColor Green
Write-Host ""
Write-Host "Starting WOSM Go Backend (Main Project)..." -ForegroundColor Yellow
Write-Host "Database: $($env:DB_HOST):$($env:DB_PORT)/$($env:DB_NAME)" -ForegroundColor Gray
Write-Host "Server: http://$($env:SERVER_HOST):$($env:SERVER_PORT)" -ForegroundColor Gray
Write-Host ""

# 停止现有进程
Get-Process -Name "*main*" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "*wosm*" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# 启动主项目
try {
    go run main.go
} catch {
    Write-Host "Error starting main project: $_" -ForegroundColor Red
    Read-Host "Press Enter to continue"
}
