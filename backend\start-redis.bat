@echo off
setlocal

echo ===================================
echo RuoYi-Go Redis启动脚本 (Windows)
echo ===================================

:: 检查Docker Desktop是否运行
tasklist /fi "imagename eq Docker Desktop.exe" | find "Docker Desktop.exe" > nul
if %ERRORLEVEL% neq 0 (
    echo 警告: Docker Desktop似乎没有运行。请先启动Docker Desktop。
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo 正在等待Docker Desktop启动...
    timeout /t 10
)

:: 检查Docker是否安装
where docker >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Docker。请确保Docker已安装并添加到PATH中。
    pause
    exit /b 1
)

:: 显示Docker版本
echo 使用的Docker版本:
docker -v

:: 检查Docker是否正常运行
docker info > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: Docker引擎未运行。请确保Docker Desktop已启动并正常运行。
    pause
    exit /b 1
)

:: 检查Redis容器是否已存在
echo 检查Redis容器...
docker ps -a | findstr ruoyi-redis > nul
if %ERRORLEVEL% equ 0 (
    :: 检查容器是否已经在运行
    docker ps | findstr ruoyi-redis > nul
    if %ERRORLEVEL% equ 0 (
        echo Redis容器已在运行中...
    ) else (
        echo Redis容器已存在，正在启动...
        docker start ruoyi-redis
    )
) else (
    echo 创建并启动Redis容器...
    docker run --name ruoyi-redis -p 6379:6379 -d redis:6-alpine redis-server --appendonly yes
)

:: 检查容器是否成功启动
docker ps | findstr ruoyi-redis > nul
if %ERRORLEVEL% equ 0 (
    echo Redis已成功启动，端口: 6379
    echo 可以使用以下命令连接Redis: docker exec -it ruoyi-redis redis-cli
    
    :: 创建Redis测试脚本
    echo @echo off > redis-cli.bat
    echo docker exec -it ruoyi-redis redis-cli %%* >> redis-cli.bat
    echo Redis命令行工具已创建: redis-cli.bat
) else (
    echo Redis启动失败，请检查Docker服务是否正常运行。
)

echo.
echo 按任意键退出...
pause > nul
endlocal 