@echo off
setlocal

echo ===================================
echo RuoYi-Go SQL Server启动脚本 (Windows)
echo ===================================

:: 检查SQL Server是否已安装
sc query MSSQLSERVER > nul
if %ERRORLEVEL% equ 0 (
    echo 检测到本地SQL Server服务...
    
    :: 检查服务是否运行
    sc query MSSQLSERVER | find "RUNNING" > nul
    if %ERRORLEVEL% equ 0 (
        echo SQL Server服务已在运行中...
    ) else (
        echo 正在启动SQL Server服务...
        net start MSSQLSERVER
        if %ERRORLEVEL% neq 0 (
            echo 启动SQL Server服务失败，请以管理员身份运行此脚本。
            pause
            exit /b 1
        )
    )
    
    echo SQL Server服务已启动
    echo 请确保在配置文件中使用正确的连接信息。
    
) else (
    :: 如果本地没有SQL Server，尝试使用Docker
    echo 未检测到本地SQL Server服务，尝试使用Docker...
    
    :: 检查Docker是否安装
    where docker >nul 2>nul
    if %ERRORLEVEL% neq 0 (
        echo 错误: 未找到Docker。请确保Docker已安装并添加到PATH中。
        pause
        exit /b 1
    )
    
    :: 检查Docker是否正常运行
    docker info > nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo 错误: Docker引擎未运行。请确保Docker Desktop已启动并正常运行。
        pause
        exit /b 1
    )
    
    :: 检查SQL Server容器是否已存在
    echo 检查SQL Server容器...
    docker ps -a | findstr ruoyi-sqlserver > nul
    if %ERRORLEVEL% equ 0 (
        :: 检查容器是否已经在运行
        docker ps | findstr ruoyi-sqlserver > nul
        if %ERRORLEVEL% equ 0 (
            echo SQL Server容器已在运行中...
        ) else (
            echo SQL Server容器已存在，正在启动...
            docker start ruoyi-sqlserver
        )
    ) else (
        echo 创建并启动SQL Server容器...
        echo 注意: 这将下载并运行SQL Server 2019 Express版本，可能需要一些时间...
        
        :: 设置密码
        set /p SA_PASSWORD="请输入SQL Server SA账户的密码 (至少8个字符，包含大小写字母、数字和特殊字符): "
        
        :: 运行SQL Server容器
        docker run --name ruoyi-sqlserver -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=%SA_PASSWORD%" -e "MSSQL_PID=Express" -p 1433:1433 -d mcr.microsoft.com/mssql/server:2019-latest
    )
    
    :: 检查容器是否成功启动
    docker ps | findstr ruoyi-sqlserver > nul
    if %ERRORLEVEL% equ 0 (
        echo SQL Server已成功启动，端口: 1433
        echo 连接信息:
        echo   服务器: localhost,1433
        echo   用户名: sa
        echo   密码: 您设置的密码
        echo   数据库: master (默认)
        
        echo 请更新配置文件中的数据库连接信息。
    ) else (
        echo SQL Server启动失败，请检查Docker服务是否正常运行。
    )
)

echo.
echo 按任意键退出...
pause > nul
endlocal 