# Java to Go Migration Project Startup Script
Write-Host "=== Java to Go Migration Project Startup ===" -ForegroundColor Green

# Check Go environment
Write-Host "Checking Go environment..." -ForegroundColor Yellow
try {
    $goVersion = go version
    Write-Host "Go environment OK: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "Go not installed. Please install Go first." -ForegroundColor Red
    pause
    exit 1
}

# Check required files
Write-Host "Checking project files..." -ForegroundColor Yellow
if (-not (Test-Path "go.mod")) {
    Write-Host "go.mod file not found" -ForegroundColor Red
    pause
    exit 1
}

if (-not (Test-Path "cmd\main.go")) {
    Write-Host "main.go file not found" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "All required files found" -ForegroundColor Green

# Tidy dependencies
Write-Host "Tidying Go dependencies..." -ForegroundColor Yellow
go mod tidy

# Build project
Write-Host "Building project..." -ForegroundColor Yellow
go build -o wosm-backend.exe cmd\main.go

if (Test-Path "wosm-backend.exe") {
    Write-Host "Build successful!" -ForegroundColor Green
} else {
    Write-Host "Build failed" -ForegroundColor Red
    pause
    exit 1
}

# Start application
Write-Host "Starting application..." -ForegroundColor Green
Write-Host "Application will start at http://localhost:8080" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host ""

.\wosm-backend.exe

Write-Host "Application stopped" -ForegroundColor Yellow
pause
