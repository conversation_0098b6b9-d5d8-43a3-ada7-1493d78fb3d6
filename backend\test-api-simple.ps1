# WOSM Go Backend API Test Script

Write-Host "=== WOSM Go Backend API Test ===" -ForegroundColor Green
Write-Host ""

# Test Health Check
Write-Host "1. Testing Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8080/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health Check Success" -ForegroundColor Green
    Write-Host "   Service: $($health.service)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Health Check Failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test Login
Write-Host "2. Testing Login..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/demo/public/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10

    if ($loginResponse.code -eq 200) {
        Write-Host "✅ Login Success" -ForegroundColor Green
        $token = $loginResponse.data.token
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Login Failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login Request Failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test Get User Info
Write-Host "3. Testing Get User Info..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/demo/getInfo" -Headers $headers -TimeoutSec 10

    if ($userInfo.code -eq 200) {
        Write-Host "✅ Get User Info Success" -ForegroundColor Green
    } else {
        Write-Host "❌ Get User Info Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Get User Info Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test Get User List
Write-Host "4. Testing Get User List..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $userList = Invoke-RestMethod -Uri "http://localhost:8080/api/demo/system/user/list" -Headers $headers -TimeoutSec 10

    if ($userList.code -eq 200) {
        Write-Host "✅ Get User List Success" -ForegroundColor Green
        Write-Host "   Total Users: $($userList.data.total)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Get User List Failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Get User List Request Failed: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
Write-Host "Go Backend is working properly!" -ForegroundColor Cyan
