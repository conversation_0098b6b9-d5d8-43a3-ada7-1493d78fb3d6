# WOSM Go Backend Database CRUD Simple Test

Write-Host "=== WOSM Database CRUD Test ===" -ForegroundColor Green

# Login
Write-Host "1. Login..." -ForegroundColor Yellow
$loginBody = '{"username":"admin","password":"admin123"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json"
$token = $loginResponse.data.token
$headers = @{ Authorization = "Bearer $token" }
Write-Host "✅ Login Success" -ForegroundColor Green

# Read - Get User List
Write-Host "2. Read User List..." -ForegroundColor Yellow
$userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers
Write-Host "✅ Read Success - Total Users: $($userList.data.total)" -ForegroundColor Green

# Read - Get User Detail
Write-Host "3. Read User Detail..." -ForegroundColor Yellow
$userDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/1" -Headers $headers
Write-Host "✅ Read Detail Success - User: $($userDetail.data.data.userName)" -ForegroundColor Green

# Create - Add New User
Write-Host "4. Create New User..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$newUserJson = @"
{
    "loginName": "testuser_$timestamp",
    "userName": "测试用户",
    "email": "<EMAIL>",
    "phonenumber": "13800138000",
    "sex": "0",
    "status": "0",
    "deptId": 103,
    "password": "123456"
}
"@

try {
    $createResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $newUserJson -Headers $headers -ContentType "application/json"
    $testUserId = $createResult.data.userId
    Write-Host "✅ Create Success - New User ID: $testUserId" -ForegroundColor Green
} catch {
    Write-Host "❌ Create Failed: $_" -ForegroundColor Red
    $testUserId = $null
}

# Update - Modify User
if ($testUserId) {
    Write-Host "5. Update User..." -ForegroundColor Yellow
    $updateUserJson = @"
{
    "userId": $testUserId,
    "userName": "updated_testuser_$timestamp",
    "nickName": "更新测试用户",
    "email": "<EMAIL>",
    "phonenumber": "13900139000",
    "sex": "1",
    "status": "0",
    "deptId": 103
}
"@

    try {
        $updateResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Put -Body $updateUserJson -Headers $headers -ContentType "application/json"
        Write-Host "✅ Update Success - Updated User: $($updateResult.data.userName)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Update Failed: $_" -ForegroundColor Red
    }

    # Delete - Remove User
    Write-Host "6. Delete User..." -ForegroundColor Yellow
    try {
        $deleteResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/$testUserId" -Method Delete -Headers $headers
        Write-Host "✅ Delete Success - Deleted User ID: $testUserId" -ForegroundColor Green
    } catch {
        Write-Host "❌ Delete Failed: $_" -ForegroundColor Red
    }
}

# Final Check
Write-Host "7. Final Database State..." -ForegroundColor Yellow
$finalList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers
Write-Host "✅ Final Check - Total Users: $($finalList.data.total)" -ForegroundColor Green

Write-Host ""
Write-Host "=== CRUD Test Complete ===" -ForegroundColor Green
Write-Host "✅ Database Integration Working!" -ForegroundColor Cyan
