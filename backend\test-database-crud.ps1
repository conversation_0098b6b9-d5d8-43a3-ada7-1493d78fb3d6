# WOSM Go Backend Database CRUD Test Script

Write-Host "=== WOSM Go Backend Database CRUD Test ===" -ForegroundColor Green
Write-Host "Test Server: http://localhost:8080" -ForegroundColor Cyan
Write-Host "Database: SQL Server (wosm)" -ForegroundColor Cyan
Write-Host ""

# Test 1: Login and Get Token
Write-Host "1. Testing Login..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10

    if ($loginResponse.code -eq 200) {
        Write-Host "✅ Login Success" -ForegroundColor Green
        $token = $loginResponse.data.token
        $headers = @{ Authorization = "Bearer $token" }
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Login Failed: $($loginResponse.msg)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login Request Failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 2: Read - Get User List from Database
Write-Host "2. Testing Read - Get User List from Database..." -ForegroundColor Yellow
try {
    $userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers -TimeoutSec 10

    if ($userList.code -eq 200) {
        Write-Host "✅ Read User List Success" -ForegroundColor Green
        Write-Host "   Total Users: $($userList.data.total)" -ForegroundColor Gray
        Write-Host "   Users from Database:" -ForegroundColor Gray
        foreach ($user in $userList.data.rows) {
            Write-Host "   - ID: $($user.userId), Name: $($user.userName), Login: $($user.userName)" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Read User List Failed: $($userList.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Read User List Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 3: Read - Get User Detail from Database
Write-Host "3. Testing Read - Get User Detail from Database..." -ForegroundColor Yellow
try {
    $userDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/1" -Headers $headers -TimeoutSec 10

    if ($userDetail.code -eq 200) {
        Write-Host "✅ Read User Detail Success" -ForegroundColor Green
        $userData = $userDetail.data.data
        Write-Host "   User ID: $($userData.userId)" -ForegroundColor Gray
        Write-Host "   User Name: $($userData.userName)" -ForegroundColor Gray
        Write-Host "   Email: $($userData.email)" -ForegroundColor Gray
        Write-Host "   Status: $($userData.status)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Read User Detail Failed: $($userDetail.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Read User Detail Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 4: Create - Add New User to Database
Write-Host "4. Testing Create - Add New User to Database..." -ForegroundColor Yellow
try {
    $newUser = @{
        userName = "testuser_$(Get-Date -Format 'yyyyMMddHHmmss')"
        nickName = "测试用户"
        email = "<EMAIL>"
        phonenumber = "13800138000"
        sex = "0"
        status = "0"
        deptId = 103
    } | ConvertTo-Json

    $createResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $newUser -Headers $headers -ContentType "application/json" -TimeoutSec 10

    if ($createResult.code -eq 200) {
        Write-Host "✅ Create User Success" -ForegroundColor Green
        $createdUser = $createResult.data
        Write-Host "   Created User ID: $($createdUser.userId)" -ForegroundColor Gray
        Write-Host "   Created User Name: $($createdUser.userName)" -ForegroundColor Gray
        $testUserId = $createdUser.userId
    } else {
        Write-Host "❌ Create User Failed: $($createResult.msg)" -ForegroundColor Red
        $testUserId = $null
    }
} catch {
    Write-Host "❌ Create User Request Failed: $_" -ForegroundColor Red
    $testUserId = $null
}

Write-Host ""

# Test 5: Update - Modify User in Database
if ($testUserId) {
    Write-Host "5. Testing Update - Modify User in Database..." -ForegroundColor Yellow
    try {
        $updateUser = @{
            userId = $testUserId
            userName = "updated_testuser_$(Get-Date -Format 'yyyyMMddHHmmss')"
            nickName = "更新的测试用户"
            email = "<EMAIL>"
            phonenumber = "13900139000"
            sex = "1"
            status = "0"
            deptId = 103
        } | ConvertTo-Json

        $updateResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Put -Body $updateUser -Headers $headers -ContentType "application/json" -TimeoutSec 10

        if ($updateResult.code -eq 200) {
            Write-Host "✅ Update User Success" -ForegroundColor Green
            Write-Host "   Updated User ID: $($updateResult.data.userId)" -ForegroundColor Gray
            Write-Host "   Updated User Name: $($updateResult.data.userName)" -ForegroundColor Gray
        } else {
            Write-Host "❌ Update User Failed: $($updateResult.msg)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Update User Request Failed: $_" -ForegroundColor Red
    }
} else {
    Write-Host "5. Skipping Update Test - No test user created" -ForegroundColor Yellow
}

Write-Host ""

# Test 6: Delete - Remove User from Database
if ($testUserId) {
    Write-Host "6. Testing Delete - Remove User from Database..." -ForegroundColor Yellow
    try {
        $deleteResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/$testUserId" -Method Delete -Headers $headers -TimeoutSec 10

        if ($deleteResult.code -eq 200) {
            Write-Host "✅ Delete User Success" -ForegroundColor Green
            Write-Host "   Deleted User ID: $testUserId" -ForegroundColor Gray
        } else {
            Write-Host "❌ Delete User Failed: $($deleteResult.msg)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Delete User Request Failed: $_" -ForegroundColor Red
    }
} else {
    Write-Host "6. Skipping Delete Test - No test user created" -ForegroundColor Yellow
}

Write-Host ""

# Test 7: Verify Database State
Write-Host "7. Testing Final Database State..." -ForegroundColor Yellow
try {
    $finalUserList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers -TimeoutSec 10

    if ($finalUserList.code -eq 200) {
        Write-Host "✅ Final Database State Check Success" -ForegroundColor Green
        Write-Host "   Final Total Users: $($finalUserList.data.total)" -ForegroundColor Gray
        Write-Host "   Active Users in Database:" -ForegroundColor Gray
        foreach ($user in $finalUserList.data.rows) {
            Write-Host "   - ID: $($user.userId), Name: $($user.userName), Status: $($user.status)" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Final Database State Check Failed: $($finalUserList.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Final Database State Check Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 8: Test Pagination
Write-Host "8. Testing Pagination..." -ForegroundColor Yellow
try {
    $page1 = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list?pageNum=1`&pageSize=1" -Headers $headers -TimeoutSec 10

    if ($page1.code -eq 200) {
        Write-Host "✅ Pagination Test Success" -ForegroundColor Green
        Write-Host "   Page 1 Results: $($page1.data.rows.Count)" -ForegroundColor Gray
        Write-Host "   Total Records: $($page1.data.total)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Pagination Test Failed: $($page1.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Pagination Test Request Failed: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Database CRUD Test Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Successfully tested:" -ForegroundColor Green
Write-Host "  - Database Connection" -ForegroundColor White
Write-Host "  - User Authentication" -ForegroundColor White
Write-Host "  - Read Operations (List `& Detail)" -ForegroundColor White
Write-Host "  - Create Operations" -ForegroundColor White
Write-Host "  - Update Operations" -ForegroundColor White
Write-Host "  - Delete Operations (Soft Delete)" -ForegroundColor White
Write-Host "  - Pagination" -ForegroundColor White
Write-Host ""
Write-Host "🎉 Go Backend with SQL Server Database Integration Complete!" -ForegroundColor Cyan
