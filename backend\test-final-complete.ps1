# WOSM Go Backend Complete Functionality Test

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    WOSM Go Backend Complete Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$baseUrl = "http://localhost:8080"
$testResults = @()

# Test 1: Health Check
Write-Host "🔍 1. Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health Check Success" -ForegroundColor Green
    Write-Host "   Service: $($health.service)" -ForegroundColor Gray
    Write-Host "   Version: $($health.version)" -ForegroundColor Gray
    $testResults += "Health Check: ✅ PASS"
} catch {
    Write-Host "❌ Health Check Failed: $_" -ForegroundColor Red
    $testResults += "Health Check: ❌ FAIL"
}

Write-Host ""

# Test 2: Authentication
Write-Host "🔐 2. Authentication Test..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ Login Success" -ForegroundColor Green
        $token = $loginResponse.data.token
        $headers = @{ Authorization = "Bearer $token" }
        Write-Host "   Token Generated: $($token.Length) characters" -ForegroundColor Gray
        $testResults += "Authentication: ✅ PASS"
    } else {
        Write-Host "❌ Login Failed: $($loginResponse.msg)" -ForegroundColor Red
        $testResults += "Authentication: ❌ FAIL"
        exit 1
    }
} catch {
    Write-Host "❌ Authentication Failed: $_" -ForegroundColor Red
    $testResults += "Authentication: ❌ FAIL"
    exit 1
}

Write-Host ""

# Test 3: User Info API
Write-Host "👤 3. User Info API..." -ForegroundColor Yellow
try {
    $userInfo = Invoke-RestMethod -Uri "$baseUrl/api/getInfo" -Headers $headers -TimeoutSec 10
    if ($userInfo.code -eq 200) {
        Write-Host "✅ User Info Success" -ForegroundColor Green
        Write-Host "   User: $($userInfo.data.user.userName)" -ForegroundColor Gray
        Write-Host "   Roles: $($userInfo.data.roles -join ', ')" -ForegroundColor Gray
        $testResults += "User Info API: ✅ PASS"
    } else {
        Write-Host "❌ User Info Failed" -ForegroundColor Red
        $testResults += "User Info API: ❌ FAIL"
    }
} catch {
    Write-Host "❌ User Info Request Failed: $_" -ForegroundColor Red
    $testResults += "User Info API: ❌ FAIL"
}

Write-Host ""

# Test 4: Database Read Operations
Write-Host "📖 4. Database Read Operations..." -ForegroundColor Yellow
try {
    # Test User List
    $userList = Invoke-RestMethod -Uri "$baseUrl/api/system/user/list" -Headers $headers -TimeoutSec 10
    if ($userList.code -eq 200) {
        Write-Host "✅ User List Success" -ForegroundColor Green
        Write-Host "   Total Users: $($userList.data.total)" -ForegroundColor Gray
        
        # Test User Detail
        $userDetail = Invoke-RestMethod -Uri "$baseUrl/api/system/user/1" -Headers $headers -TimeoutSec 10
        if ($userDetail.code -eq 200) {
            Write-Host "✅ User Detail Success" -ForegroundColor Green
            Write-Host "   User ID: $($userDetail.data.data.userId)" -ForegroundColor Gray
            $testResults += "Database Read: ✅ PASS"
        } else {
            Write-Host "❌ User Detail Failed" -ForegroundColor Red
            $testResults += "Database Read: ❌ FAIL"
        }
    } else {
        Write-Host "❌ User List Failed" -ForegroundColor Red
        $testResults += "Database Read: ❌ FAIL"
    }
} catch {
    Write-Host "❌ Database Read Failed: $_" -ForegroundColor Red
    $testResults += "Database Read: ❌ FAIL"
}

Write-Host ""

# Test 5: Database CRUD Operations
Write-Host "💾 5. Database CRUD Operations..." -ForegroundColor Yellow
$crudSuccess = $true
$testUserId = $null

try {
    # CREATE
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $newUser = @{
        loginName = "testuser_$timestamp"
        userName = "测试用户_$timestamp"
        email = "test_$<EMAIL>"
        phonenumber = "13800138000"
        sex = "0"
        status = "0"
        deptId = 103
        password = "123456"
    } | ConvertTo-Json
    
    $createResult = Invoke-RestMethod -Uri "$baseUrl/api/system/user" -Method Post -Body $newUser -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($createResult.code -eq 200) {
        Write-Host "✅ CREATE Success" -ForegroundColor Green
        $testUserId = $createResult.data.userId
        Write-Host "   Created User ID: $testUserId" -ForegroundColor Gray
    } else {
        Write-Host "❌ CREATE Failed" -ForegroundColor Red
        $crudSuccess = $false
    }
    
    # UPDATE
    if ($testUserId) {
        $updateUser = @{
            userId = $testUserId
            loginName = "updated_testuser_$timestamp"
            userName = "更新测试用户_$timestamp"
            email = "updated_$<EMAIL>"
            phonenumber = "13900139000"
            sex = "1"
            status = "0"
            deptId = 103
            password = "654321"
        } | ConvertTo-Json
        
        $updateResult = Invoke-RestMethod -Uri "$baseUrl/api/system/user" -Method Put -Body $updateUser -Headers $headers -ContentType "application/json" -TimeoutSec 10
        if ($updateResult.code -eq 200) {
            Write-Host "✅ UPDATE Success" -ForegroundColor Green
        } else {
            Write-Host "❌ UPDATE Failed" -ForegroundColor Red
            $crudSuccess = $false
        }
    }
    
    # DELETE
    if ($testUserId) {
        $deleteResult = Invoke-RestMethod -Uri "$baseUrl/api/system/user/$testUserId" -Method Delete -Headers $headers -TimeoutSec 10
        if ($deleteResult.code -eq 200) {
            Write-Host "✅ DELETE Success" -ForegroundColor Green
        } else {
            Write-Host "❌ DELETE Failed" -ForegroundColor Red
            $crudSuccess = $false
        }
    }
    
    if ($crudSuccess) {
        $testResults += "Database CRUD: ✅ PASS"
    } else {
        $testResults += "Database CRUD: ❌ FAIL"
    }
    
} catch {
    Write-Host "❌ CRUD Operations Failed: $_" -ForegroundColor Red
    $testResults += "Database CRUD: ❌ FAIL"
}

Write-Host ""

# Test 6: System Management APIs
Write-Host "⚙️ 6. System Management APIs..." -ForegroundColor Yellow
try {
    # Test Role List
    $roleList = Invoke-RestMethod -Uri "$baseUrl/api/system/role/list" -Headers $headers -TimeoutSec 10
    if ($roleList.code -eq 200) {
        Write-Host "✅ Role Management Success" -ForegroundColor Green
        Write-Host "   Total Roles: $($roleList.data.total)" -ForegroundColor Gray
    }
    
    # Test Menu List
    $menuList = Invoke-RestMethod -Uri "$baseUrl/api/system/menu/list" -Headers $headers -TimeoutSec 10
    if ($menuList.code -eq 200) {
        Write-Host "✅ Menu Management Success" -ForegroundColor Green
        Write-Host "   Total Menus: $($menuList.data.Count)" -ForegroundColor Gray
    }
    
    # Test Router Info
    $routers = Invoke-RestMethod -Uri "$baseUrl/api/getRouters" -Headers $headers -TimeoutSec 10
    if ($routers.code -eq 200) {
        Write-Host "✅ Router Info Success" -ForegroundColor Green
        Write-Host "   Available Routes: $($routers.data.Count)" -ForegroundColor Gray
    }
    
    $testResults += "System Management: ✅ PASS"
} catch {
    Write-Host "❌ System Management Failed: $_" -ForegroundColor Red
    $testResults += "System Management: ❌ FAIL"
}

Write-Host ""

# Test 7: Pagination
Write-Host "📄 7. Pagination Test..." -ForegroundColor Yellow
try {
    $page1 = Invoke-RestMethod -Uri "$baseUrl/api/system/user/list?pageNum=1&pageSize=1" -Headers $headers -TimeoutSec 10
    if ($page1.code -eq 200) {
        Write-Host "✅ Pagination Success" -ForegroundColor Green
        Write-Host "   Page Size: $($page1.data.rows.Count)" -ForegroundColor Gray
        Write-Host "   Total Records: $($page1.data.total)" -ForegroundColor Gray
        $testResults += "Pagination: ✅ PASS"
    } else {
        Write-Host "❌ Pagination Failed" -ForegroundColor Red
        $testResults += "Pagination: ❌ FAIL"
    }
} catch {
    Write-Host "❌ Pagination Test Failed: $_" -ForegroundColor Red
    $testResults += "Pagination: ❌ FAIL"
}

Write-Host ""

# Test 8: Captcha
Write-Host "🔐 8. Captcha Test..." -ForegroundColor Yellow
try {
    $captcha = Invoke-RestMethod -Uri "$baseUrl/api/captchaImage" -Method Get -TimeoutSec 10
    if ($captcha.code -eq 200) {
        Write-Host "✅ Captcha Success" -ForegroundColor Green
        Write-Host "   UUID: $($captcha.data.uuid)" -ForegroundColor Gray
        $testResults += "Captcha: ✅ PASS"
    } else {
        Write-Host "❌ Captcha Failed" -ForegroundColor Red
        $testResults += "Captcha: ❌ FAIL"
    }
} catch {
    Write-Host "❌ Captcha Test Failed: $_" -ForegroundColor Red
    $testResults += "Captcha: ❌ FAIL"
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           TEST RESULTS SUMMARY" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

foreach ($result in $testResults) {
    if ($result -like "*✅*") {
        Write-Host $result -ForegroundColor Green
    } else {
        Write-Host $result -ForegroundColor Red
    }
}

$passCount = ($testResults | Where-Object { $_ -like "*✅*" }).Count
$totalCount = $testResults.Count

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "FINAL RESULT: $passCount/$totalCount TESTS PASSED" -ForegroundColor Cyan
if ($passCount -eq $totalCount) {
    Write-Host "🎉 ALL TESTS PASSED! GO BACKEND IS FULLY FUNCTIONAL!" -ForegroundColor Green
} else {
    Write-Host "⚠️  SOME TESTS FAILED. PLEASE CHECK THE RESULTS ABOVE." -ForegroundColor Yellow
}
Write-Host "========================================" -ForegroundColor Cyan
