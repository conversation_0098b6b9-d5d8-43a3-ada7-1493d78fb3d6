# WOSM Go Backend System API Complete Test Script

Write-Host "=== WOSM Go Backend System API Test ===" -ForegroundColor Green
Write-Host "Test Server: http://localhost:8080" -ForegroundColor Cyan
Write-Host ""

# Test 1: Health Check
Write-Host "1. Testing Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8080/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health Check Success" -ForegroundColor Green
    Write-Host "   Service: $($health.service)" -ForegroundColor Gray
    Write-Host "   Version: $($health.version)" -ForegroundColor Gray
    Write-Host "   Status: $($health.status)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Health Check Failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 2: Login
Write-Host "2. Testing Login..." -ForegroundColor Yellow
try {
    $loginBody = '{"username":"admin","password":"admin123"}'
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10

    if ($loginResponse.code -eq 200) {
        Write-Host "✅ Login Success" -ForegroundColor Green
        $token = $loginResponse.data.token
        Write-Host "   Token: $($token.Substring(0, 50))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Login Failed: $($loginResponse.msg)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login Request Failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 3: Get User Info
Write-Host "3. Testing Get User Info..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers $headers -TimeoutSec 10

    if ($userInfo.code -eq 200) {
        Write-Host "✅ Get User Info Success" -ForegroundColor Green
        $userData = $userInfo.data
        Write-Host "   User: $($userData.user.userName)" -ForegroundColor Gray
        Write-Host "   Roles: $($userData.roles -join ', ')" -ForegroundColor Gray
        Write-Host "   Permissions: $($userData.permissions -join ', ')" -ForegroundColor Gray
    } else {
        Write-Host "❌ Get User Info Failed: $($userInfo.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Get User Info Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 4: Get Routers
Write-Host "4. Testing Get Routers..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $routers = Invoke-RestMethod -Uri "http://localhost:8080/api/getRouters" -Headers $headers -TimeoutSec 10

    if ($routers.code -eq 200) {
        Write-Host "✅ Get Routers Success" -ForegroundColor Green
        $menuCount = $routers.data.Count
        Write-Host "   Menu Count: $menuCount" -ForegroundColor Gray
        foreach ($menu in $routers.data) {
            Write-Host "   - $($menu.meta.title) ($($menu.path))" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Get Routers Failed: $($routers.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Get Routers Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 5: User Management
Write-Host "5. Testing User Management..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    
    # Get User List
    $userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers -TimeoutSec 10
    if ($userList.code -eq 200) {
        Write-Host "✅ Get User List Success" -ForegroundColor Green
        Write-Host "   Total Users: $($userList.data.total)" -ForegroundColor Gray
        foreach ($user in $userList.data.rows) {
            Write-Host "   - $($user.userName) ($($user.loginName))" -ForegroundColor Gray
        }
    }
    
    # Get User Detail
    $userDetail = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/1" -Headers $headers -TimeoutSec 10
    if ($userDetail.code -eq 200) {
        Write-Host "✅ Get User Detail Success" -ForegroundColor Green
        Write-Host "   User ID: $($userDetail.data.data.userId)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ User Management Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 6: Role Management
Write-Host "6. Testing Role Management..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $roleList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/role/list" -Headers $headers -TimeoutSec 10

    if ($roleList.code -eq 200) {
        Write-Host "✅ Get Role List Success" -ForegroundColor Green
        Write-Host "   Total Roles: $($roleList.data.total)" -ForegroundColor Gray
        foreach ($role in $roleList.data.rows) {
            Write-Host "   - $($role.roleName) ($($role.roleKey))" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Get Role List Failed: $($roleList.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Role Management Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 7: Menu Management
Write-Host "7. Testing Menu Management..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    $menuList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/menu/list" -Headers $headers -TimeoutSec 10

    if ($menuList.code -eq 200) {
        Write-Host "✅ Get Menu List Success" -ForegroundColor Green
        Write-Host "   Total Menus: $($menuList.data.Count)" -ForegroundColor Gray
        foreach ($menu in $menuList.data) {
            Write-Host "   - $($menu.menuName) ($($menu.path))" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Get Menu List Failed: $($menuList.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Menu Management Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 8: Captcha
Write-Host "8. Testing Captcha..." -ForegroundColor Yellow
try {
    $captcha = Invoke-RestMethod -Uri "http://localhost:8080/api/captchaImage" -Method Get -TimeoutSec 10

    if ($captcha.code -eq 200) {
        Write-Host "✅ Get Captcha Success" -ForegroundColor Green
        Write-Host "   UUID: $($captcha.data.uuid)" -ForegroundColor Gray
        Write-Host "   Image: $($captcha.data.img.Substring(0, 50))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ Get Captcha Failed: $($captcha.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Get Captcha Request Failed: $_" -ForegroundColor Red
}

Write-Host ""

# Test 9: CRUD Operations
Write-Host "9. Testing CRUD Operations..." -ForegroundColor Yellow
try {
    $headers = @{ Authorization = "Bearer $token" }
    
    # Test Create User
    $createUserBody = '{"userName":"testuser","loginName":"test","email":"<EMAIL>","status":"0"}'
    $createResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Post -Body $createUserBody -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($createResult.code -eq 200) {
        Write-Host "✅ Create User Success" -ForegroundColor Green
    }
    
    # Test Update User
    $updateUserBody = '{"userId":1,"userName":"updateduser","loginName":"admin","email":"<EMAIL>","status":"0"}'
    $updateResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user" -Method Put -Body $updateUserBody -Headers $headers -ContentType "application/json" -TimeoutSec 10
    if ($updateResult.code -eq 200) {
        Write-Host "✅ Update User Success" -ForegroundColor Green
    }
    
    # Test Delete User
    $deleteResult = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/999" -Method Delete -Headers $headers -TimeoutSec 10
    if ($deleteResult.code -eq 200) {
        Write-Host "✅ Delete User Success" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ CRUD Operations Request Failed: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
Write-Host "System API testing completed!" -ForegroundColor Cyan
Write-Host ""
Write-Host "Available System API Endpoints:" -ForegroundColor Yellow
Write-Host "  GET  /health                    - Health Check" -ForegroundColor White
Write-Host "  POST /api/login                 - User Login" -ForegroundColor White
Write-Host "  GET  /api/captchaImage          - Get Captcha" -ForegroundColor White
Write-Host "  GET  /api/getInfo               - Get User Info (Auth Required)" -ForegroundColor White
Write-Host "  GET  /api/getRouters            - Get Routers (Auth Required)" -ForegroundColor White
Write-Host "  GET  /api/system/user/list      - Get User List (Auth Required)" -ForegroundColor White
Write-Host "  GET  /api/system/user/:id       - Get User Detail (Auth Required)" -ForegroundColor White
Write-Host "  POST /api/system/user           - Create User (Auth Required)" -ForegroundColor White
Write-Host "  PUT  /api/system/user           - Update User (Auth Required)" -ForegroundColor White
Write-Host "  DELETE /api/system/user/:ids    - Delete User (Auth Required)" -ForegroundColor White
Write-Host "  GET  /api/system/role/list      - Get Role List (Auth Required)" -ForegroundColor White
Write-Host "  GET  /api/system/menu/list      - Get Menu List (Auth Required)" -ForegroundColor White
Write-Host ""
Write-Host "Authentication: Bearer Token" -ForegroundColor Cyan
Write-Host "Default Account: admin / admin123" -ForegroundColor Cyan
