Write-Host "=== WOSM API Test ===" -ForegroundColor Green

# Test health check
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8080/health"
    Write-Host "Health check: OK" -ForegroundColor Green
    Write-Host "Service: $($health.service)" -ForegroundColor Gray
} catch {
    Write-Host "Health check failed: $_" -ForegroundColor Red
    exit 1
}

# Test login
try {
    $loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body $loginBody -ContentType "application/json"

    if ($loginResponse.code -eq 200) {
        Write-Host "Login: OK" -ForegroundColor Green
        $token = $loginResponse.data.token

        # Test user info
        $headers = @{ Authorization = "Bearer $token" }
        $userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers $headers
        Write-Host "User info: OK" -ForegroundColor Green
        Write-Host "User: $($userInfo.data.user.userName)" -ForegroundColor Gray

        # Test user list
        $userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers $headers
        Write-Host "User list: OK" -ForegroundColor Green
        Write-Host "Total users: $($userList.data.total)" -ForegroundColor Gray

        Write-Host "`nAll tests passed!" -ForegroundColor Green
    } else {
        Write-Host "Login failed: $($loginResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "API test failed: $_" -ForegroundColor Red
}
