package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	_ "github.com/denisenkom/go-mssqldb"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"golang.org/x/crypto/bcrypt"
)

type Config struct {
	DBHost     string
	DBPort     string
	DBName     string
	DBUser     string
	DBPassword string
	JWTSecret  string
	ServerPort string
}

type User struct {
	UserID     int64     `json:"userId"`
	LoginName  string    `json:"loginName"`
	UserName   string    `json:"userName"`
	Email      string    `json:"email"`
	Status     string    `json:"status"`
	CreateTime time.Time `json:"createTime"`
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type Claims struct {
	UserID   int64  `json:"userId"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

var (
	db     *sql.DB
	config *Config
)

func initConfig() *Config {
	return &Config{
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnv("DB_PORT", "1433"),
		DBName:     getEnv("DB_NAME", "wosm"),
		DBUser:     getEnv("DB_USER", "sa"),
		DBPassword: getEnv("DB_PASSWORD", "F@2233"),
		JWTSecret:  getEnv("JWT_SECRET", "wosm-secret-key-2024"),
		ServerPort: getEnv("SERVER_PORT", "8080"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func initDatabase() error {
	connString := fmt.Sprintf("server=%s;port=%s;database=%s;user id=%s;password=%s;encrypt=disable",
		config.DBHost, config.DBPort, config.DBName, config.DBUser, config.DBPassword)

	var err error
	db, err = sql.Open("sqlserver", connString)
	if err != nil {
		return fmt.Errorf("database connection failed: %v", err)
	}

	if err = db.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %v", err)
	}

	log.Println("Database connected successfully")
	return nil
}

func createTables() error {
	// 检查表是否存在，如果不存在则跳过创建（使用现有表）
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM sys_user WHERE login_name = 'admin'").Scan(&count)
	if err != nil {
		return fmt.Errorf("check admin user failed: %v", err)
	}

	if count == 0 {
		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
		_, err = db.Exec(`
            INSERT INTO sys_user (login_name, user_name, email, password, status, create_time)
            VALUES (@p1, @p2, @p3, @p4, @p5, @p6)`,
			"admin", "Administrator", "<EMAIL>", string(hashedPassword), "0", time.Now())
		if err != nil {
			return fmt.Errorf("create admin user failed: %v", err)
		}
		log.Println("Default admin user created (username: admin, password: admin123)")
	}

	log.Println("Database tables initialized")
	return nil
}

func generateToken(user *User) (string, error) {
	claims := &Claims{
		UserID:   user.UserID,
		Username: user.LoginName,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "wosm-backend",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(config.JWTSecret))
}

func validateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := c.GetHeader("Authorization")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "No token provided"})
			c.Abort()
			return
		}

		if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
			tokenString = tokenString[7:]
		}

		claims, err := validateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid token"})
			c.Abort()
			return
		}

		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Next()
	}
}

func login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Login bind error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "Invalid parameters"})
		return
	}

	log.Printf("Login attempt for user: %s", req.Username)

	var user User
	var hashedPassword string
	err := db.QueryRow(`
        SELECT user_id, login_name, user_name, email, password, status, create_time
        FROM sys_user WHERE login_name = @p1 AND status = '0'`,
		req.Username).Scan(&user.UserID, &user.LoginName, &user.UserName,
		&user.Email, &hashedPassword, &user.Status, &user.CreateTime)

	if err != nil {
		log.Printf("User not found or query error: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid username or password"})
		return
	}

	log.Printf("User found: %s, checking password", user.LoginName)

	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(req.Password)); err != nil {
		log.Printf("Password check failed: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid username or password"})
		return
	}

	log.Printf("Login successful for user: %s", user.LoginName)

	token, err := generateToken(&user)
	if err != nil {
		log.Printf("Token generation failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "Token generation failed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "Login successful",
		"data": gin.H{
			"token": token,
			"user":  user,
		},
	})
}

func getUserInfo(c *gin.Context) {
	userID := c.GetInt64("userID")

	var user User
	err := db.QueryRow(`
        SELECT user_id, login_name, user_name, email, status, create_time
        FROM sys_user WHERE user_id = @p1`, userID).Scan(
		&user.UserID, &user.LoginName, &user.UserName,
		&user.Email, &user.Status, &user.CreateTime)

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "Success",
		"data": gin.H{
			"user":        user,
			"roles":       []string{"admin"},
			"permissions": []string{"*:*:*"},
		},
	})
}

func getUserList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	offset := (page - 1) * size

	rows, err := db.Query(`
        SELECT user_id, login_name, user_name, email, status, create_time
        FROM sys_user ORDER BY create_time DESC
        OFFSET @p1 ROWS FETCH NEXT @p2 ROWS ONLY`, offset, size)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "Query failed"})
		return
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		rows.Scan(&user.UserID, &user.LoginName, &user.UserName,
			&user.Email, &user.Status, &user.CreateTime)
		users = append(users, user)
	}

	var total int
	db.QueryRow("SELECT COUNT(*) FROM sys_user").Scan(&total)

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "Success",
		"data": gin.H{
			"list":  users,
			"total": total,
			"page":  page,
			"size":  size,
		},
	})
}

func getRouters(c *gin.Context) {
	menus := []gin.H{
		{
			"name":      "System",
			"path":      "/system",
			"component": "Layout",
			"meta":      gin.H{"title": "System Management", "icon": "system"},
			"children": []gin.H{
				{
					"name":      "User",
					"path":      "/system/user",
					"component": "system/user/index",
					"meta":      gin.H{"title": "User Management", "icon": "user"},
				},
				{
					"name":      "Role",
					"path":      "/system/role",
					"component": "system/role/index",
					"meta":      gin.H{"title": "Role Management", "icon": "peoples"},
				},
			},
		},
		{
			"name":      "Monitor",
			"path":      "/monitor",
			"component": "Layout",
			"meta":      gin.H{"title": "System Monitor", "icon": "monitor"},
			"children": []gin.H{
				{
					"name":      "Online",
					"path":      "/monitor/online",
					"component": "monitor/online/index",
					"meta":      gin.H{"title": "Online Users", "icon": "online"},
				},
			},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "Success",
		"data": menus,
	})
}

func main() {
	log.Println("Starting WOSM Complete Backend Service...")

	config = initConfig()
	log.Printf("Config loaded - Database: %s:%s/%s", config.DBHost, config.DBPort, config.DBName)

	if err := initDatabase(); err != nil {
		log.Printf("Database initialization failed: %v", err)
		log.Println("Please ensure SQL Server is running and configured correctly")
		log.Println("Default config: localhost:1433/wosm, user: sa, password: F@2233")
		return
	}
	defer db.Close()

	if err := createTables(); err != nil {
		log.Printf("Create tables failed: %v", err)
		return
	}

	gin.SetMode(gin.DebugMode)
	r := gin.Default()

	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	public := r.Group("/api/public")
	{
		public.POST("/login", login)
	}

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "up",
			"service":   "WOSM-Backend-Complete",
			"version":   "1.0.0",
			"database":  "connected",
			"timestamp": time.Now().Unix(),
		})
	})

	api := r.Group("/api")
	api.Use(authMiddleware())
	{
		api.GET("/getInfo", getUserInfo)
		api.GET("/getRouters", getRouters)

		system := api.Group("/system")
		{
			system.GET("/user/list", getUserList)
		}
	}

	port := ":" + config.ServerPort
	log.Printf("Server started successfully!")
	log.Printf("Access URL: http://localhost:%s", config.ServerPort)
	log.Printf("Health check: http://localhost:%s/health", config.ServerPort)
	log.Printf("Login API: POST http://localhost:%s/api/public/login", config.ServerPort)
	log.Printf("Default account: admin / admin123")
	log.Println("Press Ctrl+C to stop")

	if err := r.Run(port); err != nil {
		log.Printf("Server start failed: %v", err)
	}
}
