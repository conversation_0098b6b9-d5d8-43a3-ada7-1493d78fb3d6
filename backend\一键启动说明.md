# 🚀 Java到Go迁移项目一键启动说明

## 📋 前置条件

在开始之前，请确保：

1. ✅ **Go环境** - Go 1.18或更高版本
2. ✅ **SQL Server 2012** - 服务正在运行
3. ✅ **数据库wosm** - 已创建（用户名：sa，密码：F@2233）
4. ✅ **PowerShell** - Windows PowerShell 5.0或更高版本

## 🎯 一键启动（推荐方式）

### 方法1：使用验证脚本（最简单）

```powershell
# 进入项目目录
cd D:\wosm\backend

# 运行一键启动验证脚本
.\一键启动验证.ps1
```

这个脚本会：
- ✅ 自动检查环境和依赖
- ✅ 自动修复常见问题
- ✅ 自动构建项目
- ✅ 自动启动应用

### 方法2：使用完整脚本

```powershell
# 进入项目目录
cd D:\wosm\backend

# 一键完成所有检查、修复和构建
.\项目检查和修复脚本.ps1 -Action all

# 启动应用
.\quick-start.bat
```

### 方法3：手动步骤

```powershell
# 进入项目目录
cd D:\wosm\backend

# 整理依赖
go mod tidy

# 构建项目
go build -o wosm-backend.exe cmd\main.go

# 启动应用
.\wosm-backend.exe
```

## 🔍 验证应用是否成功启动

### 1. 检查控制台输出

应用启动后，您应该看到类似以下的日志：

```
{"level":"info","ts":**********.789,"caller":"main.go:24","msg":"服务启动中..."}
{"level":"info","ts":**********.790,"caller":"database.go:82","msg":"数据库连接成功"}
{"level":"info","ts":**********.791,"caller":"main.go:95","msg":"服务启动成功","port":8080}
```

### 2. 测试健康检查接口

在新的PowerShell窗口中运行：

```powershell
# 测试健康检查
Invoke-RestMethod -Uri "http://localhost:8080/health"
```

预期响应：
```json
{
  "status": "up",
  "service": "RuoYi-Go",
  "version": "1.0.0"
}
```

### 3. 测试登录接口

```powershell
# 测试登录
$body = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body $body -ContentType "application/json"
```

## 🔧 常见问题解决

### 问题1：Go环境未安装

**错误信息：** `'go' 不是内部或外部命令`

**解决方案：**
1. 下载并安装Go：https://golang.org/dl/
2. 重启PowerShell
3. 验证安装：`go version`

### 问题2：SQL Server服务未运行

**错误信息：** `连接数据库失败`

**解决方案：**
```powershell
# 检查服务状态
Get-Service -Name "MSSQLSERVER"

# 启动服务
Start-Service -Name "MSSQLSERVER"
```

### 问题3：端口8080被占用

**错误信息：** `listen tcp :8080: bind: address already in use`

**解决方案：**
```powershell
# 查找占用进程
netstat -ano | findstr :8080

# 终止进程（替换<PID>为实际进程ID）
taskkill /PID <PID> /F
```

### 问题4：数据库连接失败

**错误信息：** `login error: Login failed for user 'sa'`

**解决方案：**
1. 确认SQL Server身份验证模式为"混合模式"
2. 确认sa用户已启用
3. 确认密码为F@2233
4. 确认数据库wosm已创建

## 📊 成功标志

当您看到以下情况时，说明项目已成功运行：

- ✅ 应用启动无错误
- ✅ 数据库连接成功
- ✅ 健康检查接口返回正常
- ✅ 登录接口可以正常使用
- ✅ 控制台显示"服务启动成功"

## 🎉 下一步

项目成功启动后，您可以：

1. **访问API接口** - 使用Postman或其他工具测试API
2. **查看日志** - 检查logs目录下的日志文件
3. **开发调试** - 修改代码后重新构建和启动
4. **部署生产** - 参考部署文档进行生产环境配置

## 📞 获取帮助

如果遇到问题：

1. 查看详细文档：`迁移状况分析与落地方案.md`
2. 运行诊断：`.\项目检查和修复脚本.ps1`
3. 查看快速指南：`快速启动指南.md`
4. 检查应用日志：`logs\ruoyi.log`

---

**🎊 恭喜！您已经成功启动了Java到Go的迁移项目！**
