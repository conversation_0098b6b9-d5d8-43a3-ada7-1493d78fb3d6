# 一键启动验证脚本
# 这个脚本会验证是否可以通过简单的命令启动项目

Write-Host "=== Java到Go迁移项目一键启动验证 ===" -ForegroundColor Green
Write-Host "项目路径: $PWD" -ForegroundColor Yellow

# 检查必要文件是否存在
$requiredFiles = @(
    "项目检查和修复脚本.ps1",
    "go.mod",
    "cmd\main.go",
    "configs\config.yaml"
)

Write-Host "`n=== 检查必要文件 ===" -ForegroundColor Cyan
$allFilesExist = $true

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Host "`n❌ 缺少必要文件，无法继续" -ForegroundColor Red
    Write-Host "请确保在正确的项目目录中运行此脚本" -ForegroundColor Yellow
    pause
    exit 1
}

# 检查Go环境
Write-Host "`n=== 检查Go环境 ===" -ForegroundColor Cyan
try {
    $goVersion = go version
    Write-Host "✅ Go环境: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Go环境未安装或未配置" -ForegroundColor Red
    Write-Host "请先安装Go 1.18或更高版本" -ForegroundColor Yellow
    pause
    exit 1
}

# 检查SQL Server服务
Write-Host "`n=== 检查SQL Server服务 ===" -ForegroundColor Cyan
try {
    $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
    if ($sqlService -and $sqlService.Status -eq "Running") {
        Write-Host "✅ SQL Server服务运行中" -ForegroundColor Green
    } else {
        Write-Host "⚠️  SQL Server服务未运行，尝试启动..." -ForegroundColor Yellow
        try {
            Start-Service -Name "MSSQLSERVER"
            Write-Host "✅ SQL Server服务已启动" -ForegroundColor Green
        } catch {
            Write-Host "❌ 无法启动SQL Server服务: $_" -ForegroundColor Red
            Write-Host "请手动启动SQL Server服务" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "⚠️  无法检查SQL Server服务状态" -ForegroundColor Yellow
}

# 执行一键流程
Write-Host "`n=== 开始一键启动流程 ===" -ForegroundColor Green

Write-Host "`n[1/3] 执行检查、修复和构建..." -ForegroundColor Yellow
try {
    if (Test-Path "项目检查和修复脚本.ps1") {
        & ".\项目检查和修复脚本.ps1" -Action all
        Write-Host "✅ 检查、修复和构建完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️  检查脚本不存在，手动执行基础操作..." -ForegroundColor Yellow
        
        # 手动执行基础操作
        Write-Host "整理依赖..." -ForegroundColor Gray
        go mod tidy
        
        Write-Host "构建项目..." -ForegroundColor Gray
        go build -o wosm-backend.exe cmd\main.go
        
        if (Test-Path "wosm-backend.exe") {
            Write-Host "✅ 项目构建成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 项目构建失败" -ForegroundColor Red
            pause
            exit 1
        }
    }
} catch {
    Write-Host "❌ 检查和构建过程出错: $_" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "`n[2/3] 检查构建结果..." -ForegroundColor Yellow
if (Test-Path "wosm-backend.exe") {
    $fileInfo = Get-Item "wosm-backend.exe"
    Write-Host "✅ 可执行文件已生成: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Green
} else {
    Write-Host "❌ 可执行文件不存在" -ForegroundColor Red
    pause
    exit 1
}

# 检查是否有快速启动脚本
Write-Host "`n[3/3] 准备启动应用..." -ForegroundColor Yellow
if (Test-Path "quick-start.bat") {
    Write-Host "✅ 发现快速启动脚本" -ForegroundColor Green
    Write-Host "`n🚀 即将启动应用..." -ForegroundColor Green
    Write-Host "应用将在 http://localhost:8080 启动" -ForegroundColor Cyan
    Write-Host "启动后可以访问 http://localhost:8080/health 检查服务状态" -ForegroundColor Cyan
    Write-Host "按任意键启动应用，或按 Ctrl+C 取消..." -ForegroundColor Yellow
    pause
    
    # 启动应用
    & ".\quick-start.bat"
} else {
    Write-Host "⚠️  快速启动脚本不存在，直接启动应用..." -ForegroundColor Yellow
    Write-Host "`n🚀 启动应用..." -ForegroundColor Green
    Write-Host "应用将在 http://localhost:8080 启动" -ForegroundColor Cyan
    Write-Host "按 Ctrl+C 停止应用" -ForegroundColor Yellow
    Write-Host ""
    
    # 直接启动
    & ".\wosm-backend.exe"
}

Write-Host "`n应用已停止" -ForegroundColor Yellow
pause
