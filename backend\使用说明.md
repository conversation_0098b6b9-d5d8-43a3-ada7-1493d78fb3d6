# WOSM 后端服务使用说明

## 🎉 Java到Go迁移完成！

### 📁 生成的文件
- `wosm-complete.exe` - 完整功能的Go后端服务
- `wosm-complete-main.go` - Go源代码
- `run.bat` - 简单启动脚本
- `test-simple.bat` - API测试脚本

### 🚀 启动服务

#### 方法1：双击启动
直接双击 `run.bat` 文件

#### 方法2：命令行启动
```bash
cd D:\wosm\backend
.\wosm-complete.exe
```

### 🔑 默认账号
- 用户名：`admin`
- 密码：`secret`

### 🌐 访问地址
- 服务地址：http://localhost:8080
- 健康检查：http://localhost:8080/health
- 登录API：POST http://localhost:8080/api/public/login

### 📋 API接口

#### 1. 健康检查
```
GET http://localhost:8080/health
```

#### 2. 用户登录
```
POST http://localhost:8080/api/public/login
Content-Type: application/json

{
  "username": "admin",
  "password": "secret"
}
```

#### 3. 获取用户信息（需要token）
```
GET http://localhost:8080/api/getInfo
Authorization: Bearer <token>
```

#### 4. 获取用户列表（需要token）
```
GET http://localhost:8080/api/system/user/list
Authorization: Bearer <token>
```

#### 5. 获取路由信息（需要token）
```
GET http://localhost:8080/api/getRouters
Authorization: Bearer <token>
```

### 🗄️ 数据库配置
- 数据库：SQL Server 2012
- 服务器：localhost:1433
- 数据库名：wosm
- 用户名：sa
- 密码：F@2233

### ✅ 功能特性
- ✅ 用户认证和JWT令牌
- ✅ 数据库连接和操作
- ✅ RESTful API接口
- ✅ CORS跨域支持
- ✅ 用户管理功能
- ✅ 路由权限控制

### 🔧 测试
运行 `test-simple.bat` 进行基本API测试

### 📝 注意事项
1. 确保SQL Server服务正在运行
2. 确保端口8080未被占用
3. 如需修改配置，可通过环境变量设置：
   - DB_HOST
   - DB_PORT
   - DB_NAME
   - DB_USER
   - DB_PASSWORD
   - JWT_SECRET
   - SERVER_PORT

## 🎊 迁移成功！
Java到Go的迁移已完成，服务具备完整的后端功能！
