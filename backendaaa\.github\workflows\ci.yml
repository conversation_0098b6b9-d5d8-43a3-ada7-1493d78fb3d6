name: CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.20'
          cache: true

      - name: Install golangci-lint
        run: |
          curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
          golangci-lint --version

      - name: Run golangci-lint
        working-directory: ./backend
        run: golangci-lint run ./...

  test:
    name: Test
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ruoyi
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:6
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.20'
          cache: true

      - name: Initialize Database
        run: |
          mysql -h127.0.0.1 -uroot -ppassword ruoyi < ./backend/script/init/ruoyi.sql || echo "Failed to initialize database"
        continue-on-error: true

      - name: Run Tests
        working-directory: ./backend
        run: go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
        env:
          MYSQL_HOST: localhost
          MYSQL_PORT: 3306
          MYSQL_DATABASE: ruoyi
          MYSQL_USERNAME: root
          MYSQL_PASSWORD: password
          REDIS_HOST: localhost
          REDIS_PORT: 6379

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.out
          flags: unittests
          fail_ci_if_error: false

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
      - uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.20'
          cache: true

      - name: Build
        working-directory: ./backend
        run: go build -v -o ruoyi-go ./main.go

      - name: Upload build artifact
        uses: actions/upload-artifact@v3
        with:
          name: ruoyi-go
          path: ./backend/ruoyi-go
  
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: ./backend
          push: true
          tags: yourusername/ruoyi-go:latest,yourusername/ruoyi-go:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max 