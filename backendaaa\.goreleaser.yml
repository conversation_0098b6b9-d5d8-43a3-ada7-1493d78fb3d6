project_name: ruoyi-go

before:
  hooks:
    - go mod tidy

builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64
    ignore:
      - goos: windows
        goarch: arm64
    main: ./main.go
    ldflags:
      - -s -w
      - -X github.com/wosm-project/backend/internal/pkg/version.Version={{.Version}}
      - -X github.com/wosm-project/backend/internal/pkg/version.Commit={{.Commit}}
      - -X github.com/wosm-project/backend/internal/pkg/version.BuildDate={{.Date}}

archives:
  - format: tar.gz
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    format_overrides:
      - goos: windows
        format: zip

checksum:
  name_template: 'checksums.txt'

snapshot:
  name_template: "{{ incpatch .Version }}-next"

changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
      - '^ci:'
      - '^chore:'
      - Merge pull request
      - Merge branch

dockers:
  - image_templates:
      - "yourusername/ruoyi-go:{{ .Version }}"
      - "yourusername/ruoyi-go:latest"
    dockerfile: Dockerfile
    build_flag_templates:
      - "--pull"
      - "--label=org.opencontainers.image.created={{.Date}}"
      - "--label=org.opencontainers.image.title={{.ProjectName}}"
      - "--label=org.opencontainers.image.revision={{.FullCommit}}"
      - "--label=org.opencontainers.image.version={{.Version}}"
    extra_files:
      - config/config.yaml

nfpms:
  - package_name: ruoyi-go
    homepage: https://github.com/yourusername/ruoyi-go
    maintainer: Your Name <<EMAIL>>
    description: Go implementation of RuoYi backend system
    license: MIT
    formats:
      - deb
      - rpm
    bindir: /usr/bin
    contents:
      - src: config/config.yaml
        dst: /etc/ruoyi-go/config.yaml
        type: config
      - src: script/init/ruoyi-go.service
        dst: /etc/systemd/system/ruoyi-go.service 