FROM golang:1.20-alpine AS builder

WORKDIR /app

# 安装必要的构建工具和依赖
RUN apk add --no-cache gcc musl-dev git

# 拷贝go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 拷贝源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-extldflags "-static"' -o ruoyi-go main.go

# 使用更小的基础镜像
FROM frolvlad/alpine-glibc:alpine-3.17

WORKDIR /app

# 安装SQL Server依赖 
RUN apk add --no-cache curl gnupg \
    && curl -O https://download.microsoft.com/download/e/4/e/e4e67866-dffd-428c-aac7-8d28ddafb39b/msodbcsql17_17.10.2.1-1_amd64.apk \
    && apk add --allow-untrusted msodbcsql17_17.10.2.1-1_amd64.apk \
    && rm msodbcsql17_17.10.2.1-1_amd64.apk

# 从构建阶段复制构建的可执行文件
COPY --from=builder /app/ruoyi-go .

# 拷贝配置文件
COPY config/config.yaml ./config/

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8080

# 设置时区
ENV TZ=Asia/Shanghai

# 启动应用
CMD ["./ruoyi-go"] 