# RuoYi-Go Backend

RuoYi-Go 是一个基于 Go 语言实现的快速开发框架，旨在提供与 Java 版 RuoYi 相同的功能，但具有更高的性能和更低的资源消耗。

## 技术栈

- **语言**: Go 1.20+
- **框架**: Gin 1.9+
- **ORM**: GORM 1.25+
- **数据库**: SQL Server 2012+
- **缓存**: Redis 6.0+
- **JWT**: 用于身份验证
- **Swagger**: API 文档
- **Viper**: 配置管理
- **Zap**: 日志管理

## 目录结构

```
backend/
├── cmd/             # 命令行应用程序代码
├── config/          # 配置文件和配置管理代码
├── deploy/          # 部署相关的配置和脚本
├── docs/            # 文档
│   ├── api/         # API 文档
│   └── guide/       # 开发指南
├── internal/        # 内部应用程序代码
│   ├── app/         # 应用程序代码
│   │   ├── controller/ # 控制器层
│   │   ├── middleware/ # 中间件
│   │   ├── model/      # 数据模型
│   │   ├── router/     # 路由定义
│   │   └── service/    # 服务层
│   └── pkg/         # 内部公共包
│       ├── cache/      # 缓存
│       ├── logger/     # 日志
│       ├── response/   # 响应处理
│       └── utils/      # 实用函数
├── logs/            # 日志文件
├── script/          # 脚本文件
│   ├── init/           # 初始化脚本
│   └── sql/            # SQL 脚本
├── tests/           # 测试代码
│   ├── benchmark/      # 基准测试
│   ├── integration/    # 集成测试
│   └── load/           # 负载测试
├── .gitignore       # Git 忽略文件
├── Dockerfile       # Docker 构建文件
├── docker-compose.yml # Docker Compose 配置
├── go.mod           # Go 模块定义
├── go.sum           # Go 模块校验和
├── main.go          # 主程序入口
└── README.md        # 项目说明
```

## 特性

- **系统管理**: 用户、角色、菜单、部门、岗位管理
- **系统监控**: 在线用户、服务监控、系统日志
- **系统工具**: 代码生成、系统接口
- **权限控制**: 基于RBAC的细粒度权限控制
- **多租户支持**: 可选的SaaS多租户模式
- **前后端分离**: 提供RESTful API接口
- **文件存储**: 支持本地和云存储
- **数据权限**: 基于部门的数据权限控制
- **操作审计**: 记录用户操作
- **多语言支持**: 国际化处理

## 快速开始

### 前提条件

- Go 1.20+
- SQL Server 2012+
- Redis 6.0+

### 获取代码

```bash
git clone https://github.com/your-username/ruoyi-go.git
cd ruoyi-go/backend
```

### 配置

1. 复制配置文件样例并根据需要修改

```bash
cp config/config.example.yaml config/config.yaml
```

2. 修改配置文件中的数据库和Redis连接信息

### 初始化数据库

1. 创建数据库

```sql
CREATE DATABASE ruoyi;
GO
```

2. 执行初始化SQL脚本

```bash
sqlcmd -S localhost -U sa -P YourStrongPassword123 -d ruoyi -i script/sql/ruoyi.sql
```

### 构建和运行

```bash
# 构建
go build -o ruoyi-go main.go

# 运行
./ruoyi-go
```

### 使用Docker

```bash
# 使用Docker Compose运行
docker-compose up -d
```

## 测试

### 运行集成测试

```bash
cd tests
./run_tests.sh
```

### 运行性能测试

```bash
cd tests
./run_benchmark.sh
```

### 运行负载测试

```bash
cd tests
./run_load_test.sh
```

## 部署

详细的部署指南请参考 [部署文档](deploy/README.md)。

## 开发指南

详细的开发指南请参考 [开发指南](docs/guide/DEVELOPMENT.md)。

## API文档

API文档请参考 [API文档](docs/api/README.md)。

## 贡献

欢迎贡献代码，提交问题和功能请求！

1. Fork 项目
2. 创建您的特性分支: `git checkout -b feature/amazing-feature`
3. 提交您的更改: `git commit -m 'Add some amazing feature'`
4. 推送到分支: `git push origin feature/amazing-feature`
5. 开启一个 Pull Request

## 许可证

该项目基于 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

如有任何问题或建议，请通过以下方式联系我们：

- 提交 [Issue](https://github.com/yourusername/ruoyi-go/issues)
- 发送邮件至 [<EMAIL>](mailto:<EMAIL>)

## 致谢

- [RuoYi](https://gitee.com/y_project/RuoYi) - 提供优秀的中后台管理系统架构
- [Gin](https://github.com/gin-gonic/gin) - 高性能Go Web框架
- [Gorm](https://github.com/go-gorm/gorm) - 优秀的Go ORM库
- 所有为本项目做出贡献的开发者 