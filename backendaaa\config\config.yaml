# 项目相关配置
system:
  # 名称
  name: RuoYi-Go
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  # 应用的访问路径
  contextPath: /
  # 运行模式
  runMode: debug

# 日志配置
log:
  level: debug
  path: logs/
  filename: ruoyi-go
  maxSize: 100  # MB
  maxBackups: 10
  maxAge: 30    # 天
  compress: true

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# 文件上传
upload:
  # 单个文件大小
  maxFileSize: 10MB
  # 设置总上传的文件大小
  maxRequestSize: 20MB

# Redis配置
redis:
  # 地址
  host: localhost
  # 端口，默认为6379
  port: 6379
  # 数据库索引
  database: 0
  # 密码
  password: ""
  # 连接超时时间(秒)
  timeout: 10
  # 连接池配置
  pool:
    # 连接池中的最小空闲连接
    minIdle: 0
    # 连接池中的最大空闲连接
    maxIdle: 8
    # 连接池的最大数据库连接数
    maxActive: 8
    # 连接池最大阻塞等待时间（使用负值表示没有限制）
    maxWait: -1

# Token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# 数据库配置
database:
  # 数据库类型 mysql/postgres/sqlite
  type: sqlserver
  # 驱动名称
  driverName: sqlserver
  # 服务器地址
  host: localhost
  # 服务器端口
  port: 1433
  # 数据库名
  database: wosm
  # 用户名
  username: sa
  # 密码
  password: F@2233
  # 连接参数
  params: "charset=utf8mb4&parseTime=True&loc=Local"
  # 空闲连接池中连接的最大数量
  maxIdleConns: 20
  # 打开数据库连接的最大数量
  maxOpenConns: 100
  # 连接可复用的最大时间
  connMaxLifetime: 30m

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成配置
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径
  packageName: com.ruoyi.module
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_ 