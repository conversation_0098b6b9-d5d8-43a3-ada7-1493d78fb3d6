apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: sqlserver
  namespace: ruoyi
spec:
  serviceName: sqlserver
  replicas: 1
  selector:
    matchLabels:
      app: sqlserver
  template:
    metadata:
      labels:
        app: sqlserver
    spec:
      containers:
      - name: sqlserver
        image: mcr.microsoft.com/mssql/server:2012-latest
        ports:
        - containerPort: 1433
          name: sqlserver
        env:
        - name: ACCEPT_EULA
          value: "Y"
        - name: SA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: db.password
        - name: MSSQL_PID
          value: "Express"
        volumeMounts:
        - name: sqlserver-data
          mountPath: /var/opt/mssql
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          exec:
            command: 
            - /bin/sh
            - -c
            - /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P $SA_PASSWORD -Q "SELECT 1"
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P $SA_PASSWORD -Q "SELECT 1"
          initialDelaySeconds: 20
          periodSeconds: 5
          timeoutSeconds: 2
      volumes:
      - name: sqlserver-data
        persistentVolumeClaim:
          claimName: sqlserver-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: sqlserver
  namespace: ruoyi
spec:
  ports:
  - port: 1433
    targetPort: 1433
    name: sqlserver
  selector:
    app: sqlserver
  clusterIP: None
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ruoyi
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:6.0
        ports:
        - containerPort: 6379
          name: redis
        args: ["--requirepass", "$(REDIS_PASSWORD)"]
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: redis.password
        volumeMounts:
        - name: redis-data
          mountPath: /data
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
          requests:
            cpu: "200m"
            memory: "512Mi"
        livenessProbe:
          exec:
            command: ["redis-cli", "ping"]
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command: ["redis-cli", "ping"]
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 2
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: ruoyi
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  selector:
    app: redis
  clusterIP: None 