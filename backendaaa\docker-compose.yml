version: '3.8'

services:
  ruoyi-go:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ruoyi-go
    restart: always
    ports:
      - "8080:8080"
    depends_on:
      - sqlserver
      - redis
    environment:
      - DB_HOST=sqlserver
      - DB_PORT=1433
      - DB_NAME=ruoyi
      - DB_USER=sa
      - DB_PASSWORD=YourStrongPassword123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - ruoyi-net

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2012-latest
    container_name: ruoyi-sqlserver
    restart: always
    ports:
      - "1433:1433"
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrongPassword123
      - MSSQL_PID=Express
    volumes:
      - sqlserver-data:/var/opt/mssql
    networks:
      - ruoyi-net

  redis:
    image: redis:6.0
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ruoyi-net

networks:
  ruoyi-net:
    driver: bridge

volumes:
  sqlserver-data:
  redis-data: 