# RuoYi-Go API 文档

本文档提供了 RuoYi-Go 后端 API 的详细说明。

## 基本信息

- **基础URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **认证方式**: JWT Token

## 认证

### 登录

**请求**:

```
POST /api/v1/login
```

**请求体**:

```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires": 86400
  }
}
```

### 登出

**请求**:

```
POST /api/v1/logout
```

**请求头**:

```
Authorization: Bearer {token}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 用户管理

### 获取用户列表

**请求**:

```
GET /api/v1/system/user/list
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型    | 是否必需 | 描述       |
|------------|---------|----------|------------|
| pageNum    | integer | 否       | 页码，默认 1  |
| pageSize   | integer | 否       | 每页大小，默认 10 |
| username   | string  | 否       | 用户名过滤    |
| phoneNumber| string  | 否       | 手机号过滤    |
| status     | string  | 否       | 状态过滤     |
| deptId     | integer | 否       | 部门ID过滤   |
| beginTime  | string  | 否       | 开始时间     |
| endTime    | string  | 否       | 结束时间     |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "userId": 1,
        "deptId": 103,
        "username": "admin",
        "nickname": "管理员",
        "email": "<EMAIL>",
        "phoneNumber": "15888888888",
        "gender": "1",
        "avatar": "",
        "status": "0",
        "delFlag": "0",
        "loginIp": "127.0.0.1",
        "loginDate": "2023-01-01T12:00:00Z",
        "createBy": "admin",
        "createTime": "2023-01-01T12:00:00Z",
        "updateBy": "admin",
        "updateTime": "2023-01-01T12:00:00Z",
        "remark": "管理员"
      }
    ]
  }
}
```

### 获取用户详情

**请求**:

```
GET /api/v1/system/user/{userId}
```

**请求头**:

```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名   | 类型    | 是否必需 | 描述    |
|---------|---------|----------|---------|
| userId  | integer | 是       | 用户ID   |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userId": 1,
    "deptId": 103,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "phoneNumber": "15888888888",
    "gender": "1",
    "avatar": "",
    "status": "0",
    "delFlag": "0",
    "loginIp": "127.0.0.1",
    "loginDate": "2023-01-01T12:00:00Z",
    "createBy": "admin",
    "createTime": "2023-01-01T12:00:00Z",
    "updateBy": "admin",
    "updateTime": "2023-01-01T12:00:00Z",
    "remark": "管理员",
    "roles": [
      {
        "roleId": 1,
        "roleName": "管理员",
        "roleKey": "admin",
        "roleSort": 1,
        "status": "0"
      }
    ],
    "posts": [
      {
        "postId": 1,
        "postName": "董事长",
        "postCode": "ceo",
        "postSort": 1,
        "status": "0"
      }
    ]
  }
}
```

### 添加用户

**请求**:

```
POST /api/v1/system/user
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求体**:

```json
{
  "deptId": 103,
  "username": "test",
  "nickname": "测试用户",
  "password": "123456",
  "email": "<EMAIL>",
  "phoneNumber": "15666666666",
  "gender": "1",
  "status": "0",
  "remark": "测试用户",
  "roleIds": [2],
  "postIds": [1]
}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 更新用户

**请求**:

```
PUT /api/v1/system/user
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求体**:

```json
{
  "userId": 2,
  "deptId": 103,
  "username": "test",
  "nickname": "测试用户更新",
  "email": "<EMAIL>",
  "phoneNumber": "15666666667",
  "gender": "1",
  "status": "0",
  "remark": "测试用户更新",
  "roleIds": [2],
  "postIds": [1]
}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 删除用户

**请求**:

```
DELETE /api/v1/system/user/{userIds}
```

**请求头**:

```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名    | 类型    | 是否必需 | 描述         |
|----------|---------|----------|--------------|
| userIds  | string  | 是       | 用户ID，多个ID用逗号分隔 |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 角色管理

### 获取角色列表

**请求**:

```
GET /api/v1/system/role/list
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 是否必需 | 描述       |
|-----------|---------|----------|------------|
| pageNum   | integer | 否       | 页码，默认 1  |
| pageSize  | integer | 否       | 每页大小，默认 10 |
| roleName  | string  | 否       | 角色名称过滤  |
| roleKey   | string  | 否       | 角色标识过滤  |
| status    | string  | 否       | 状态过滤     |
| beginTime | string  | 否       | 开始时间     |
| endTime   | string  | 否       | 结束时间     |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 10,
    "rows": [
      {
        "roleId": 1,
        "roleName": "管理员",
        "roleKey": "admin",
        "roleSort": 1,
        "dataScope": "1",
        "status": "0",
        "delFlag": "0",
        "createBy": "admin",
        "createTime": "2023-01-01T12:00:00Z",
        "updateBy": "admin",
        "updateTime": "2023-01-01T12:00:00Z",
        "remark": "管理员"
      }
    ]
  }
}
```

## 部门管理

### 获取部门树

**请求**:

```
GET /api/v1/system/dept/tree
```

**请求头**:

```
Authorization: Bearer {token}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "deptId": 100,
      "parentId": 0,
      "deptName": "公司",
      "orderNum": 0,
      "leader": "管理员",
      "phone": "15888888888",
      "email": "<EMAIL>",
      "status": "0",
      "children": [
        {
          "deptId": 101,
          "parentId": 100,
          "deptName": "技术部",
          "orderNum": 1,
          "leader": "技术负责人",
          "phone": "15666666666",
          "email": "<EMAIL>",
          "status": "0",
          "children": []
        }
      ]
    }
  ]
}
```

## 菜单管理

### 获取菜单树

**请求**:

```
GET /api/v1/system/menu/tree
```

**请求头**:

```
Authorization: Bearer {token}
```

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "menuId": 1,
      "parentId": 0,
      "menuName": "系统管理",
      "menuType": "M",
      "orderNum": 1,
      "perms": "",
      "path": "system",
      "component": "",
      "icon": "system",
      "status": "0",
      "visible": "0",
      "children": [
        {
          "menuId": 100,
          "parentId": 1,
          "menuName": "用户管理",
          "menuType": "C",
          "orderNum": 1,
          "perms": "system:user:list",
          "path": "user",
          "component": "system/user/index",
          "icon": "user",
          "status": "0",
          "visible": "0",
          "children": []
        }
      ]
    }
  ]
}
```

## 字典管理

### 获取字典类型列表

**请求**:

```
GET /api/v1/system/dict/type/list
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 是否必需 | 描述       |
|-----------|---------|----------|------------|
| pageNum   | integer | 否       | 页码，默认 1  |
| pageSize  | integer | 否       | 每页大小，默认 10 |
| dictName  | string  | 否       | 字典名称过滤  |
| dictType  | string  | 否       | 字典类型过滤  |
| status    | string  | 否       | 状态过滤     |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 10,
    "rows": [
      {
        "dictId": 1,
        "dictName": "用户性别",
        "dictType": "sys_user_sex",
        "status": "0",
        "createBy": "admin",
        "createTime": "2023-01-01T12:00:00Z",
        "updateBy": "admin",
        "updateTime": "2023-01-01T12:00:00Z",
        "remark": "用户性别列表"
      }
    ]
  }
}
```

## 日志管理

### 获取操作日志列表

**请求**:

```
GET /api/v1/system/log/oper/list
```

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 是否必需 | 描述       |
|-----------|---------|----------|------------|
| pageNum   | integer | 否       | 页码，默认 1  |
| pageSize  | integer | 否       | 每页大小，默认 10 |
| title     | string  | 否       | 标题过滤     |
| operName  | string  | 否       | 操作人过滤   |
| status    | integer | 否       | 状态过滤     |
| beginTime | string  | 否       | 开始时间     |
| endTime   | string  | 否       | 结束时间     |

**响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "operId": 1,
        "title": "用户管理",
        "businessType": 1,
        "method": "POST",
        "requestMethod": "POST",
        "operatorType": 1,
        "operName": "admin",
        "deptName": "技术部",
        "operUrl": "/api/v1/system/user",
        "operIp": "127.0.0.1",
        "operLocation": "内网IP",
        "operParam": "{\"username\":\"test\",...}",
        "jsonResult": "{\"code\":200,\"message\":\"操作成功\",\"data\":null}",
        "status": 0,
        "errorMsg": "",
        "operTime": "2023-01-01T12:00:00Z"
      }
    ]
  }
}
```

## 数据库信息

本系统使用 SQL Server 2012 作为数据库存储引擎，提供高性能、高可靠性的数据存储和访问。

### 数据库配置

系统配置文件中的数据库配置示例：

```yaml
database:
  type: sqlserver
  host: localhost
  port: 1433
  database: ruoyi
  username: sa
  password: YourStrongPassword123
  max_open_conns: 100
  max_idle_conns: 20
  conn_max_lifetime: 30m
```

### 主要数据表

系统包含以下主要数据表：

| 表名                | 描述               |
|--------------------|-------------------|
| sys_user           | 用户信息表          |
| sys_role           | 角色信息表          |
| sys_menu           | 菜单权限表          |
| sys_dept           | 部门表             |
| sys_post           | 岗位信息表          |
| sys_user_role      | 用户和角色关联表     |
| sys_role_menu      | 角色和菜单关联表     |
| sys_role_dept      | 角色和部门关联表     |
| sys_user_post      | 用户与岗位关联表     |
| sys_dict_type      | 字典类型表          |
| sys_dict_data      | 字典数据表          |
| sys_config         | 参数配置表          |
| sys_oper_log       | 操作日志记录        |
| sys_login_log      | 系统访问记录        |
| sys_job            | 定时任务调度表      |
| sys_job_log        | 定时任务调度日志表   |
| sys_notice         | 通知公告表          |

## 错误码

| 错误码 | 描述                 |
|--------|---------------------|
| 200    | 操作成功             |
| 400    | 请求参数错误         |
| 401    | 未授权              |
| 403    | 禁止访问            |
| 404    | 资源不存在          |
| 500    | 服务器内部错误       |
| 50001  | 用户名或密码错误     |
| 50002  | 账户已停用          |
| 50003  | 账户已锁定          |
| 50004  | 验证码错误          |
| 50005  | 权限不足            |
| 50006  | 数据已存在          |
| 50007  | 操作失败            |
| 50008  | 登录超时            |
| 50009  | 系统繁忙，请稍后再试 | 