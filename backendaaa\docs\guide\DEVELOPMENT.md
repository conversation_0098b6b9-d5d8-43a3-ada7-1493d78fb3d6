# RuoYi-Go 开发指南

本指南为开发者提供了 RuoYi-Go 项目的开发规范、流程和最佳实践，确保代码质量和一致性。

## 目录

- [开发环境搭建](#开发环境搭建)
- [开发规范](#开发规范)
- [架构概述](#架构概述)
- [数据库设计](#数据库设计)
- [代码生成](#代码生成)
- [API设计](#api设计)
- [安全性](#安全性)
- [测试规范](#测试规范)
- [文档规范](#文档规范)
- [Git提交规范](#git提交规范)
- [常见问题](#常见问题)

## 开发环境搭建

### 必备软件

- **Go**: 1.20 或更高版本
- **SQL Server**: 2012 或更高版本
- **Redis**: 6.0 或更高版本
- **Git**: 最新版本
- **Docker**: (可选) 用于容器化开发和测试
- **IDE**: 推荐使用 GoLand 或 VS Code 配合 Go 插件

### 环境配置步骤

1. **安装 Go**:
   - 从 [Go 官网](https://golang.org/dl/) 下载并安装
   - 配置 GOPATH 和 GOROOT 环境变量

2. **安装 SQL Server**:
   - 从 [Microsoft 官网](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) 下载并安装
   - 或使用 Docker: `docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=YourStrongPassword123" -p 1433:1433 -d mcr.microsoft.com/mssql/server:2012-latest`

3. **安装 Redis**:
   - 从 [Redis 官网](https://redis.io/download) 下载并安装
   - 或使用 Docker: `docker run -p 6379:6379 -d redis:6.0`

4. **克隆项目**:
   ```bash
   git clone https://github.com/your-username/ruoyi-go.git
   cd ruoyi-go/backend
   go mod tidy
   ```

5. **配置开发环境**:
   - 复制 `config/config.example.yaml` 为 `config/config.yaml`
   - 修改数据库和 Redis 连接信息

6. **初始化数据库**:
   - 创建数据库: 
     ```sql
     CREATE DATABASE ruoyi;
     GO
     ```
   - 执行初始化脚本:
     ```bash
     sqlcmd -S localhost -U sa -P YourStrongPassword123 -d ruoyi -i script/sql/ruoyi.sql
     ```

## 开发规范

### 代码风格

- 遵循 [Go 官方的代码规范](https://golang.org/doc/effective_go)
- 使用 `gofmt` 或 `goimports` 格式化代码
- 文件命名使用小写加下划线，如 `user_controller.go`
- 变量和函数命名使用驼峰命名法，如 `userService`

### 目录结构规范

```
backend/
├── cmd/                # 命令行应用程序代码
├── config/             # 配置文件和配置管理代码
├── deploy/             # 部署相关的配置和脚本
├── docs/               # 文档
├── internal/           # 内部应用程序代码
│   ├── app/            # 应用程序代码
│   │   ├── controller/ # 控制器层
│   │   ├── middleware/ # 中间件
│   │   ├── model/      # 数据模型
│   │   ├── router/     # 路由定义
│   │   └── service/    # 服务层
│   └── pkg/            # 内部公共包
├── logs/               # 日志文件
├── script/             # 脚本文件
└── tests/              # 测试代码
```

### 错误处理

- 使用错误包装返回更有上下文的错误
- 避免在非必要的情况下使用 panic
- 在日志中记录详细的错误信息
- 为用户返回友好的错误消息

## 架构概述

RuoYi-Go 采用分层架构设计:

1. **表示层 (Controller)**: 
   - 处理 HTTP 请求和响应
   - 参数验证和转换
   - 调用服务层方法

2. **服务层 (Service)**:
   - 实现业务逻辑
   - 事务管理
   - 调用多个仓库方法组合完成业务

3. **数据访问层 (Repository)**:
   - 数据库操作封装
   - 提供 CRUD 操作
   - 不包含业务逻辑

4. **模型层 (Model)**:
   - 定义数据结构
   - 字段验证
   - 数据库映射

## 数据库设计

### 命名规范

- 表名使用小写加下划线，如 `sys_user`
- 表名使用单数形式，如 `user` 而非 `users`
- 主键使用 `id` 或 `表名_id`，如 `user_id`
- 外键使用被引用表名加 `_id`，如 `role_id`
- 索引命名为 `idx_表名_字段名`，如 `idx_user_username`

### SQL Server 特性使用

- 利用存储过程和函数提高复杂查询性能
- 合理使用索引优化查询性能
- 使用 SQL Server 提供的数据类型如 `varchar` 代替通用 `text`
- 对于大文本内容，使用 `nvarchar(max)` 数据类型

### 事务管理

- 使用 GORM 提供的事务管理功能
- 确保事务的原子性、一致性、隔离性和持久性
- 使用适当的隔离级别

## 代码生成

RuoYi-Go 提供了代码生成工具，用于快速生成基础的 CRUD 代码:

```bash
go run cmd/gen/main.go -table=sys_user -module=system
```

生成的代码包括:
- 模型定义
- 数据访问层代码
- 服务层代码
- 控制器代码
- 路由注册代码

## API设计

### RESTful API 规范

- 使用资源命名 URI，如 `/api/v1/users`
- 使用 HTTP 方法表示操作:
  - GET: 获取资源
  - POST: 创建资源
  - PUT: 更新资源
  - DELETE: 删除资源
- 使用合适的 HTTP 状态码
- 分页、排序和筛选使用查询参数
- 版本控制使用 URI 路径，如 `/api/v1/users`

### 请求参数验证

- 使用结构体标签和验证器库进行参数验证
- 对敏感数据进行过滤和转义
- 使用 DTO (Data Transfer Object) 分离传输层和业务层

### 响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 安全性

### 认证与授权

- 使用 JWT 进行用户认证
- 基于 RBAC 模型实现权限控制
- 使用中间件验证请求权限
- 实现数据范围权限控制

### 数据安全

- 密码使用 bcrypt 等算法加密存储
- 敏感数据传输使用 HTTPS
- 防止 SQL 注入、XSS 和 CSRF 攻击
- 记录敏感操作的审计日志

## 测试规范

### 单元测试

- 为所有关键功能编写单元测试
- 使用 Go 标准库的 `testing` 包
- 使用 mock 对象模拟依赖
- 关注测试覆盖率

### 集成测试

- 验证多个组件之间的交互
- 包括数据库和缓存的集成测试
- 使用测试数据库或者内存数据库

### 性能测试

- 使用基准测试评估关键功能的性能
- 监控 CPU 和内存使用情况
- 识别并解决性能瓶颈

## 文档规范

### 代码注释

- 所有导出的函数、类型和变量都应该有文档注释
- 使用 [Godoc](https://blog.golang.org/godoc) 格式的注释
- 复杂的算法或业务逻辑应该有详细的注释

### API 文档

- 使用 Swagger 生成 API 文档
- 每个 API 端点都应该有描述、参数和响应的文档
- 提供示例请求和响应

## Git提交规范

- 使用语义化的提交消息
- 格式: `<类型>(<范围>): <描述>`
- 类型:
  - feat: 新功能
  - fix: 修复bug
  - docs: 文档更新
  - style: 代码风格修改
  - refactor: 代码重构
  - test: 测试
  - chore: 构建过程或辅助工具的变动
- 保持提交粒度适中，每个提交专注于一个功能或修复

## 常见问题

### 数据库连接问题

**问题**: 无法连接到 SQL Server 数据库。

**解决方案**:
- 确保 SQL Server 服务正在运行
- 检查连接字符串是否正确
- 确保防火墙允许 SQL Server 端口 (默认 1433)
- 检查 SQL Server 身份验证设置

### GORM 常见问题

**问题**: GORM 无法正确映射 SQL Server 表结构。

**解决方案**:
- 确保模型字段名称与数据库列名匹配
- 对于 SQL Server 特定类型，使用 GORM 标签指定
- 使用 `TableName` 方法指定表名

### Redis 连接问题

**问题**: 应用无法连接到 Redis。

**解决方案**:
- 确保 Redis 服务正在运行
- 检查 Redis 连接配置
- 验证 Redis 密码是否正确
- 确保防火墙允许 Redis 端口 (默认 6379) 