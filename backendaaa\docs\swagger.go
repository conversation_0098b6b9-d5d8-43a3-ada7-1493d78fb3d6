// Package docs 生成的swagger文档
//
// @title           WOSM API
// @version         1.0
// @description     This is WOSM server.
// @termsOfService  http://swagger.io/terms/
//
// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>
//
// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html
//
// @host      localhost:8080
// @BasePath  /api
//
// @securityDefinitions.basic  BasicAuth
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
//
// GenUIController API
// @Tags       GenUI
// @Router     /api/tool/gen [get]
// @Summary    查询代码生成列表
// @Security   ApiKeyAuth
// @Produce    json
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/{tableId} [get]
// @Summary    预览代码
// @Security   ApiKeyAuth
// @Produce    json
// @Param      tableId   path      int  true  "业务表ID"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/db/list [get]
// @Summary    查询据库列表
// @Security   ApiKeyAuth
// @Produce    json
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/column/{tableId} [get]
// @Summary    查询业务表字段列表
// @Security   ApiKeyAuth
// @Produce    json
// @Param      tableId   path      int  true  "业务表ID"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/importTable [post]
// @Summary    导入表结构
// @Security   ApiKeyAuth
// @Accept     json
// @Produce    json
// @Param      tables   body      []string  true  "表名称组"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/synchDb/{tableName} [get]
// @Summary    同步数据库
// @Security   ApiKeyAuth
// @Produce    json
// @Param      tableName   path      string  true  "表名称"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/batchGenCode [get]
// @Summary    批量生成代码
// @Security   ApiKeyAuth
// @Produce    json
// @Param      tables   query      string  true  "表名称组"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// @Router     /api/tool/gen/genCode/{tableName} [get]
// @Summary    生成代码
// @Security   ApiKeyAuth
// @Produce    json
// @Param      tableName   path      string  true  "表名称"
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
//
// SwaggerController API
// @Tags       Swagger
// @Router     /swagger/* [get]
// @Summary    Swagger API
// @Produce    html
//
// BuildController API
// @Tags       Build
// @Router     /api/tool/build/list [get]
// @Summary    查询表单构建器列表
// @Security   ApiKeyAuth
// @Produce    json
// @Success    200 {object} response.ResponseData
// @Failure    500 {object} response.ResponseData
package docs

import (
	"github.com/swaggo/swag"
)

var (
	// SwaggerInfo 保存swagger信息
	SwaggerInfo = &swag.Spec{
		Version:          "1.0",
		Host:             "localhost:8080",
		BasePath:         "/api",
		Schemes:          []string{},
		Title:            "WOSM API",
		Description:      "This is WOSM server.",
		InfoInstanceName: "swagger",
		SwaggerTemplate:  docTemplate,
	}

	// 文档模板
	docTemplate = `{
  "swagger": "2.0",
  "info": {
    "description": "{{.Description}}",
    "title": "{{.Title}}",
    "contact": {
      "name": "API Support",
      "url": "http://www.swagger.io/support",
      "email": "<EMAIL>"
    },
    "license": {
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
    },
    "termsOfService": "http://swagger.io/terms/",
    "version": "{{.Version}}"
  },
  "host": "{{.Host}}",
  "basePath": "{{.BasePath}}",
  "paths": {}
}`
)

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
