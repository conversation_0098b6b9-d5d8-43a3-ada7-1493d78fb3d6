swagger: "2.0"
info:
  title: "RuoYi-Go 后台管理系统API"
  description: "RuoYi-Go 后台管理系统API文档，兼容Java后端，为前端提供完整功能支持。"
  version: "1.0.0"
  contact:
    name: "RuoYi-Go Team"
    email: "<EMAIL>"
    url: "https://github.com/wosm-go/backend"
host: "localhost:8080"
basePath: "/"
schemes:
  - "http"
  - "https"
consumes:
  - "application/json"
produces:
  - "application/json"
paths:
  /login:
    post:
      summary: "用户登录"
      description: "用户登录接口"
      tags:
        - "登录"
      parameters:
        - in: "body"
          name: "body"
          description: "登录信息"
          required: true
          schema:
            type: "object"
            properties:
              username:
                type: "string"
                description: "用户名"
              password:
                type: "string"
                description: "密码"
              code:
                type: "string"
                description: "验证码"
              uuid:
                type: "string"
                description: "验证码标识"
      responses:
        "200":
          description: "登录成功"
          schema:
            type: "object"
            properties:
              code:
                type: "integer"
                description: "状态码"
              msg:
                type: "string"
                description: "消息"
              token:
                type: "string"
                description: "令牌"
  /getInfo:
    get:
      summary: "获取用户信息"
      description: "获取当前登录用户信息"
      tags:
        - "登录"
      parameters:
        - in: "header"
          name: "Authorization"
          description: "用户token"
          required: true
          type: "string"
      responses:
        "200":
          description: "获取成功"
          schema:
            type: "object"
            properties:
              code:
                type: "integer"
                description: "状态码"
              msg:
                type: "string"
                description: "消息"
              user:
                type: "object"
                description: "用户信息"
              roles:
                type: "array"
                description: "角色列表"
                items:
                  type: "string"
              permissions:
                type: "array"
                description: "权限列表"
                items:
                  type: "string"
  /getRouters:
    get:
      summary: "获取路由信息"
      description: "获取当前用户菜单路由信息"
      tags:
        - "登录"
      parameters:
        - in: "header"
          name: "Authorization"
          description: "用户token"
          required: true
          type: "string"
      responses:
        "200":
          description: "获取成功"
          schema:
            type: "object"
            properties:
              code:
                type: "integer"
                description: "状态码"
              msg:
                type: "string"
                description: "消息"
              data:
                type: "array"
                description: "路由列表"
                items:
                  type: "object"
definitions:
  SysUser:
    type: "object"
    properties:
      userId:
        type: "integer"
        format: "int64"
        description: "用户ID"
      deptId:
        type: "integer"
        format: "int64"
        description: "部门ID"
      userName:
        type: "string"
        description: "用户账号"
      nickName:
        type: "string"
        description: "用户昵称"
      email:
        type: "string"
        description: "用户邮箱"
      phonenumber:
        type: "string"
        description: "手机号码"
      sex:
        type: "string"
        description: "用户性别"
      avatar:
        type: "string"
        description: "头像地址"
      password:
        type: "string"
        description: "密码"
      status:
        type: "string"
        description: "帐号状态"
      delFlag:
        type: "string"
        description: "删除标志"
      loginIp:
        type: "string"
        description: "最后登录IP"
      loginDate:
        type: "string"
        format: "date-time"
        description: "最后登录时间"
      createBy:
        type: "string"
        description: "创建者"
      createTime:
        type: "string"
        format: "date-time"
        description: "创建时间"
      updateBy:
        type: "string"
        description: "更新者"
      updateTime:
        type: "string"
        format: "date-time"
        description: "更新时间"
      remark:
        type: "string"
        description: "备注"
      dept:
        $ref: "#/definitions/SysDept"
  SysDept:
    type: "object"
    properties:
      deptId:
        type: "integer"
        format: "int64"
        description: "部门ID"
      parentId:
        type: "integer"
        format: "int64"
        description: "父部门ID"
      ancestors:
        type: "string"
        description: "祖级列表"
      deptName:
        type: "string"
        description: "部门名称"
      orderNum:
        type: "integer"
        description: "显示顺序"
      leader:
        type: "string"
        description: "负责人"
      phone:
        type: "string"
        description: "联系电话"
      email:
        type: "string"
        description: "邮箱"
      status:
        type: "string"
        description: "部门状态"
      delFlag:
        type: "string"
        description: "删除标志"
      createBy:
        type: "string"
        description: "创建者"
      createTime:
        type: "string"
        format: "date-time"
        description: "创建时间"
      updateBy:
        type: "string"
        description: "更新者"
      updateTime:
        type: "string"
        format: "date-time"
        description: "更新时间"
  SysRole:
    type: "object"
    properties:
      roleId:
        type: "integer"
        format: "int64"
        description: "角色ID"
      roleName:
        type: "string"
        description: "角色名称"
      roleKey:
        type: "string"
        description: "角色权限字符串"
      roleSort:
        type: "integer"
        description: "显示顺序"
      dataScope:
        type: "string"
        description: "数据范围"
      status:
        type: "string"
        description: "角色状态"
      delFlag:
        type: "string"
        description: "删除标志"
      createBy:
        type: "string"
        description: "创建者"
      createTime:
        type: "string"
        format: "date-time"
        description: "创建时间"
      updateBy:
        type: "string"
        description: "更新者"
      updateTime:
        type: "string"
        format: "date-time"
        description: "更新时间"
      remark:
        type: "string"
        description: "备注"
  ResponseData:
    type: "object"
    properties:
      code:
        type: "integer"
        description: "状态码"
      msg:
        type: "string"
        description: "消息"
      data:
        type: "object"
        description: "数据" 