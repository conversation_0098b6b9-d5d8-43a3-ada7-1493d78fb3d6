package common

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BaseController web层通用数据处理，对应Java的BaseController
type BaseController struct {
}

// StartPage 设置请求分页数据
func (b *BaseController) StartPage(c *gin.Context) *Pagination {
	// 从查询参数中获取分页信息
	pageQuery := &PageQuery{
		PageNum:       1,
		PageSize:      DefaultPageSize,
		OrderByColumn: c.Query("orderByColumn"),
		IsAsc:         c.Query("isAsc"),
	}

	// 解析pageNum
	if pageNumStr := c.Query("pageNum"); pageNumStr != "" {
		if pageNum, err := strconv.Atoi(pageNumStr); err == nil && pageNum > 0 {
			pageQuery.PageNum = pageNum
		}
	}

	// 解析pageSize
	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			pageQuery.PageSize = pageSize
		}
	}

	return NewPagination(pageQuery)
}

// GetDataTable 响应请求分页数据
func (b *BaseController) GetDataTable(rows interface{}, total int64) *TableDataInfo {
	rspData := NewTableDataInfoWithData(rows, total)
	rspData.SetCode(Success)
	rspData.SetMsg("查询成功")
	return rspData
}

// Success 返回成功
func (b *BaseController) Success() *Response {
	return SuccessResponse()
}

// Error 返回失败消息
func (b *BaseController) Error() *Response {
	return ErrorResponse()
}

// SuccessMessage 返回成功消息
func (b *BaseController) SuccessMessage(message string) *Response {
	return SuccessMessageResponse(message)
}

// SuccessData 返回成功消息
func (b *BaseController) SuccessData(data interface{}) *Response {
	return SuccessDataResponse(data)
}

// ErrorMessage 返回失败消息
func (b *BaseController) ErrorMessage(message string) *Response {
	return ErrorMessageResponse(message)
}

// WarnMessage 返回警告消息
func (b *BaseController) WarnMessage(message string) *Response {
	return WarnResponse(message)
}

// ToAjax 响应返回结果
func (b *BaseController) ToAjax(rows int) *Response {
	return ToAjax(rows)
}

// ToAjaxBool 响应返回结果
func (b *BaseController) ToAjaxBool(result bool) *Response {
	return ToAjaxBool(result)
}

// GetLoginUser 获取登录用户
func (b *BaseController) GetLoginUser(c *gin.Context) interface{} {
	// 从上下文中获取登录用户
	// 实际应用中需要根据具体认证方式获取
	return c.Keys["user"]
}

// GetUserId 获取用户ID
func (b *BaseController) GetUserId(c *gin.Context) int64 {
	// 从上下文中获取用户ID
	// 实际应用中需要根据具体认证方式获取
	if userID, exists := c.Keys["userId"]; exists {
		if id, ok := userID.(int64); ok {
			return id
		}
	}
	return 0
}

// GetUsername 获取用户名
func (b *BaseController) GetUsername(c *gin.Context) string {
	// 从上下文中获取用户名
	// 实际应用中需要根据具体认证方式获取
	if username, exists := c.Keys["username"]; exists {
		if name, ok := username.(string); ok {
			return name
		}
	}
	return ""
}

// GetDeptId 获取部门ID
func (b *BaseController) GetDeptId(c *gin.Context) int64 {
	// 从上下文中获取部门ID
	// 实际应用中需要根据具体认证方式获取
	if deptID, exists := c.Keys["deptId"]; exists {
		if id, ok := deptID.(int64); ok {
			return id
		}
	}
	return 0
}

// JSON 以JSON格式响应
func (b *BaseController) JSON(c *gin.Context, status int, obj interface{}) {
	c.JSON(status, obj)
}

// Success 成功响应
func (b *BaseController) SuccessJSON(c *gin.Context, obj interface{}) {
	c.JSON(http.StatusOK, obj)
}

// CustomError 自定义错误响应
func (b *BaseController) ErrorJSON(c *gin.Context, message string) {
	c.JSON(http.StatusOK, ErrorMessageResponse(message))
}

// Forbidden 权限不足响应
func (b *BaseController) Forbidden(c *gin.Context) {
	c.JSON(http.StatusOK, ErrorCodeResponse(http.StatusForbidden, "没有权限，请联系管理员授权"))
}
