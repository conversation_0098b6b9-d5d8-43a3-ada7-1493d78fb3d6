package common

import (
	"net/http"
)

// Response 通用响应结构，对应Java中的AjaxResult
type Response struct {
	// 状态码
	Code int `json:"code"`
	// 返回内容
	Msg string `json:"msg"`
	// 数据对象
	Data interface{} `json:"data,omitempty"`
}

// 常量定义
const (
	// CodeTag 状态码
	CodeTag = "code"
	// MsgTag 返回内容
	MsgTag = "msg"
	// DataTag 数据对象
	DataTag = "data"

	// Success 成功状态码
	Success = http.StatusOK
	// Error 错误状态码
	Error = http.StatusInternalServerError
	// Warn 警告状态码
	Warn = 601
)

// NewResponse 初始化一个新创建的 Response 对象
func NewResponse(code int, msg string, data interface{}) *Response {
	return &Response{
		Code: code,
		Msg:  msg,
		Data: data,
	}
}

// SuccessResponse 返回成功消息
func SuccessResponse() *Response {
	return SuccessMessageResponse("操作成功")
}

// SuccessDataResponse 返回成功数据
func SuccessDataResponse(data interface{}) *Response {
	return SuccessMessageDataResponse("操作成功", data)
}

// SuccessMessageResponse 返回成功消息
func SuccessMessageResponse(msg string) *Response {
	return SuccessMessageDataResponse(msg, nil)
}

// SuccessMessageDataResponse 返回成功消息和数据
func SuccessMessageDataResponse(msg string, data interface{}) *Response {
	return NewResponse(Success, msg, data)
}

// WarnResponse 返回警告消息
func WarnResponse(msg string) *Response {
	return WarnDataResponse(msg, nil)
}

// WarnDataResponse 返回警告消息和数据
func WarnDataResponse(msg string, data interface{}) *Response {
	return NewResponse(Warn, msg, data)
}

// ErrorResponse 返回错误消息
func ErrorResponse() *Response {
	return ErrorMessageResponse("操作失败")
}

// ErrorMessageResponse 返回错误消息
func ErrorMessageResponse(msg string) *Response {
	return ErrorMessageDataResponse(msg, nil)
}

// ErrorMessageDataResponse 返回错误消息和数据
func ErrorMessageDataResponse(msg string, data interface{}) *Response {
	return NewResponse(Error, msg, data)
}

// ErrorCodeResponse 返回错误状态码和消息
func ErrorCodeResponse(code int, msg string) *Response {
	return NewResponse(code, msg, nil)
}

// IsSuccess 是否为成功消息
func (r *Response) IsSuccess() bool {
	return r.Code == Success
}

// IsWarn 是否为警告消息
func (r *Response) IsWarn() bool {
	return r.Code == Warn
}

// IsError 是否为错误消息
func (r *Response) IsError() bool {
	return r.Code == Error
}

// Put 添加自定义数据，方便链式调用
func (r *Response) Put(key string, value interface{}) *Response {
	// 这里我们需要将Response转换为map
	// 在Go中，我们可以考虑添加一个Map字段来支持这个功能
	return r
}

// ToAjax 根据影响行数返回操作结果
func ToAjax(rows int) *Response {
	if rows > 0 {
		return SuccessResponse()
	}
	return ErrorResponse()
}

// ToAjaxBool 根据布尔值返回操作结果
func ToAjaxBool(result bool) *Response {
	if result {
		return SuccessResponse()
	}
	return ErrorResponse()
}
