package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/internal/model"
	"backend/internal/utils"
)

// Response API响应结构
type Response struct {
	Code    int         `json:"code"` // 状态码
	Message string      `json:"msg"`  // 消息
	Data    interface{} `json:"data"` // 数据
}

// TableDataInfo 分页数据结构
type TableDataInfo struct {
	Code  int         `json:"code"`  // 状态码
	Msg   string      `json:"msg"`   // 消息
	Rows  interface{} `json:"rows"`  // 行数据
	Total int64       `json:"total"` // 总数
}

// BaseController 基础控制器
type BaseController struct {
}

// Success 返回成功
func (b *BaseController) Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "操作成功",
		Data:    data,
	})
}

// Error 返回错误
func (b *BaseController) Error(c *gin.Context, err error) {
	c.<PERSON>(http.StatusOK, Response{
		Code:    500,
		Message: err.Error(),
		Data:    nil,
	})
}

// ErrorJSON 返回错误，使用消息字符串
func (b *BaseController) ErrorJSON(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    500,
		Message: message,
		Data:    nil,
	})
}

// Warn 返回警告
func (b *BaseController) Warn(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    400,
		Message: message,
		Data:    nil,
	})
}

// Unauthorized 返回未授权
func (b *BaseController) Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    401,
		Message: message,
		Data:    nil,
	})
}

// Forbidden 返回禁止访问
func (b *BaseController) Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    403,
		Message: message,
		Data:    nil,
	})
}

// GetUserId 获取用户ID
func (b *BaseController) GetUserId(c *gin.Context) int64 {
	// 从上下文中获取用户信息
	loginUser, exists := c.Get("loginUser")
	if !exists {
		return 0
	}
	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		return 0
	}
	return user.User.UserID
}

// GetUsername 获取用户名
func (b *BaseController) GetUsername(c *gin.Context) string {
	// 从上下文中获取用户信息
	loginUser, exists := c.Get("loginUser")
	if !exists {
		return ""
	}
	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		return ""
	}
	return user.User.UserName
}

// ToAjax 将操作结果转换为统一返回格式
func (b *BaseController) ToAjax(c *gin.Context, rows int64, err error) {
	if err != nil {
		b.Error(c, err)
		return
	}
	if rows > 0 {
		b.Success(c, rows)
	} else {
		b.Error(c, utils.NewError("操作失败"))
	}
}

// JSON 返回JSON格式数据
func (b *BaseController) JSON(c *gin.Context, code int, obj interface{}) {
	c.JSON(http.StatusOK, obj)
}

// StartPage 设置请求分页数据
func (b *BaseController) StartPage(c *gin.Context) *PageInfo {
	// 获取分页参数
	pageNum, _ := strconv.Atoi(c.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// 获取排序字段
	orderByColumn := c.DefaultQuery("orderByColumn", "")
	isAsc := c.DefaultQuery("isAsc", "asc")

	// 创建分页信息
	pageInfo := &PageInfo{
		PageNum:  pageNum,
		PageSize: pageSize,
		OrderBy:  orderByColumn,
		IsAsc:    isAsc,
	}

	return pageInfo
}

// GetDataTable 响应请求分页数据
func (b *BaseController) GetDataTable(rows interface{}, total int64) *TableDataInfo {
	return &TableDataInfo{
		Code:  200,
		Msg:   "查询成功",
		Rows:  rows,
		Total: total,
	}
}

// SuccessJSON 返回成功JSON格式数据
func (b *BaseController) SuccessJSON(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, data)
}

// PageInfo 分页信息
type PageInfo struct {
	PageNum  int    // 页码
	PageSize int    // 每页数量
	OrderBy  string // 排序字段
	IsAsc    string // 排序方式
}
