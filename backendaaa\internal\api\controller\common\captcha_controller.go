package common

import (
	"backend/internal/api/controller"
	"backend/internal/constants"
	"backend/internal/service"
	"backend/internal/utils"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// CaptchaController 验证码操作处理
type CaptchaController struct {
	controller.BaseController
	configService service.SysConfigService
	redisCache    service.RedisCache
}

// NewCaptchaController 创建验证码控制器
func NewCaptchaController(configService service.SysConfigService, redisCache service.RedisCache) *CaptchaController {
	return &CaptchaController{
		configService: configService,
		redisCache:    redisCache,
	}
}

// GetCode 生成验证码
// @Summary 生成验证码
// @Description 生成验证码
// @Tags 通用
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /captchaImage [get]
func (c *CaptchaController) GetCode(ctx *gin.Context) {
	log.Println("开始生成验证码...")

	// 检查验证码是否启用
	captchaEnabled := c.configService.SelectCaptchaEnabled()
	log.Printf("验证码是否启用: %v", captchaEnabled)

	// 构建结果
	result := make(map[string]interface{})
	result["captchaEnabled"] = captchaEnabled

	// 如果验证码未启用，直接返回
	if !captchaEnabled {
		c.Success(ctx, result)
		return
	}

	// 清理旧的验证码
	oldKeys, _ := c.redisCache.Keys(constants.CAPTCHA_CODE_KEY + "*")
	log.Printf("清理前的验证码数量: %d", len(oldKeys))

	// 如果验证码数量超过20个，清理掉一些旧的
	if len(oldKeys) > 20 {
		log.Printf("验证码数量过多(%d)，主动清理一部分", len(oldKeys))
		// 随机清理一半的验证码
		for i, key := range oldKeys {
			if i%2 == 0 {
				c.redisCache.DeleteObject(key)
				log.Printf("主动清理验证码: %s", key)
			}
		}
	}

	// 生成UUID
	uuid := utils.SimpleUUID()
	verifyKey := constants.CAPTCHA_CODE_KEY + uuid
	log.Printf("生成新验证码UUID: %s, 完整key: %s", uuid, verifyKey)

	// 使用数学验证码驱动
	driver := utils.NewCustomMathDriver()

	// 生成验证码
	id, question, answer := driver.GenerateIdQuestionAnswer()
	item, err := driver.DrawCaptcha(question)
	if err != nil {
		log.Printf("生成验证码错误: %v", err)
		c.Error(ctx, err)
		return
	}

	// 获取base64编码
	b64s := item.EncodeB64string()

	// 确保base64编码不包含任何前缀，前端会添加完整的前缀
	b64s = strings.TrimPrefix(b64s, "data:image/png;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/jpeg;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/jpg;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/gif;base64,")

	log.Printf("验证码生成成功，ID: %s", id)
	log.Printf("验证码问题: %s, 答案: %s", question, answer)
	log.Printf("验证码base64编码长度: %d", len(b64s))

	// 检查base64编码是否为空
	if b64s == "" {
		log.Printf("警告: 生成的验证码base64编码为空!")
		// 使用一个简单的1x1像素透明PNG图片作为默认值
		b64s = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII="
	}

	// 将验证码存储到Redis，设置5分钟过期时间
	// 注意：确保存储的是字符串，而不是其他类型
	err = c.redisCache.SetCacheObject(verifyKey, answer, 5*time.Minute)
	if err != nil {
		log.Printf("存储验证码到Redis错误: %v", err)
		c.Error(ctx, err)
		return
	}

	// 检查是否成功存储到Redis
	storedValue, getErr := c.redisCache.GetCacheObject(verifyKey)
	if getErr != nil || storedValue == nil {
		log.Printf("验证存储到Redis失败，无法获取: %v", getErr)
	} else {
		log.Printf("验证码已成功存储到Redis，key=%s, 值=%v，类型=%T", verifyKey, storedValue, storedValue)
	}

	// 再次检查所有验证码
	allKeys, _ := c.redisCache.Keys(constants.CAPTCHA_CODE_KEY + "*")
	log.Printf("当前系统中的验证码总数: %d", len(allKeys))
	if len(allKeys) > 0 && len(allKeys) < 20 {
		log.Printf("所有验证码keys: %v", allKeys)
	}

	// 构建结果
	result["uuid"] = uuid
	result["img"] = b64s

	// 添加额外的CORS头
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	ctx.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")
	ctx.Header("Access-Control-Max-Age", "3600")

	// 处理OPTIONS请求
	if ctx.Request.Method == "OPTIONS" {
		ctx.AbortWithStatus(200)
		return
	}

	log.Println("验证码生成完成，准备返回结果")
	log.Printf("返回数据: uuid=%s, img是否为空=%v, img类型=%T", uuid, result["img"] == "", result["img"])

	// 直接使用标准AjaxResult格式返回，与Java后端保持一致
	ctx.JSON(200, gin.H{
		"code":           200,
		"msg":            "操作成功",
		"captchaEnabled": captchaEnabled,
		"uuid":           uuid,
		"img":            b64s,
	})
}
