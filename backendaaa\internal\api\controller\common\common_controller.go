package common

import (
	"backend/internal/api/controller"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CommonController 通用请求处理
type CommonController struct {
	controller.BaseController
	uploadPath   string
	downloadPath string
	resourcePath string
	serverUrl    string
}

// NewCommonController 创建通用控制器
func NewCommonController(uploadPath, downloadPath, resourcePath, serverUrl string) *CommonController {
	return &CommonController{
		uploadPath:   uploadPath,
		downloadPath: downloadPath,
		resourcePath: resourcePath,
		serverUrl:    serverUrl,
	}
}

// FileDownload 通用下载请求
// @Summary 通用下载请求
// @Description 通用下载请求
// @Tags 通用
// @Accept json
// @Produce octet-stream
// @Param fileName query string true "文件名称"
// @Param delete query bool false "是否删除"
// @Success 200 {object} controller.Response "成功"
// @Router /common/download [get]
func (c *CommonController) FileDownload(ctx *gin.Context) {
	fileName := ctx.Query("fileName")
	deleteFlag := ctx.Query("delete") == "true"

	// 检查文件名是否合法
	if !checkAllowDownload(fileName) {
		ctx.String(http.StatusInternalServerError, fmt.Sprintf("文件名称(%s)非法，不允许下载", fileName))
		return
	}

	// 构建文件路径
	realFileName := fmt.Sprintf("%d%s", time.Now().UnixNano(), fileName[strings.Index(fileName, "_")+1:])
	filePath := filepath.Join(c.downloadPath, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		ctx.String(http.StatusNotFound, "文件不存在")
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	setAttachmentResponseHeader(ctx, realFileName)

	// 发送文件
	ctx.File(filePath)

	// 如果需要删除文件
	if deleteFlag {
		os.Remove(filePath)
	}
}

// UploadFile 通用上传请求（单个）
// @Summary 通用上传请求（单个）
// @Description 通用上传请求（单个）
// @Tags 通用
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文件"
// @Success 200 {object} controller.Response "成功"
// @Router /common/upload [post]
func (c *CommonController) UploadFile(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 上传文件
	fileName, err := c.upload(c.uploadPath, file)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建URL
	url := c.serverUrl + fileName

	// 构建结果
	result := make(map[string]interface{})
	result["url"] = url
	result["fileName"] = fileName
	result["newFileName"] = filepath.Base(fileName)
	result["originalFilename"] = file.Filename

	c.Success(ctx, result)
}

// UploadFiles 通用上传请求（多个）
// @Summary 通用上传请求（多个）
// @Description 通用上传请求（多个）
// @Tags 通用
// @Accept multipart/form-data
// @Produce json
// @Param files formData file true "文件列表"
// @Success 200 {object} controller.Response "成功"
// @Router /common/uploads [post]
func (c *CommonController) UploadFiles(ctx *gin.Context) {
	// 获取上传的文件
	form, err := ctx.MultipartForm()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.Error(ctx, fmt.Errorf("未找到上传的文件"))
		return
	}

	// 上传文件
	var urls []string
	var fileNames []string
	var newFileNames []string
	var originalFilenames []string

	for _, file := range files {
		fileName, err := c.upload(c.uploadPath, file)
		if err != nil {
			c.Error(ctx, err)
			return
		}

		url := c.serverUrl + fileName
		urls = append(urls, url)
		fileNames = append(fileNames, fileName)
		newFileNames = append(newFileNames, filepath.Base(fileName))
		originalFilenames = append(originalFilenames, file.Filename)
	}

	// 构建结果
	result := make(map[string]interface{})
	result["urls"] = strings.Join(urls, ",")
	result["fileNames"] = strings.Join(fileNames, ",")
	result["newFileNames"] = strings.Join(newFileNames, ",")
	result["originalFilenames"] = strings.Join(originalFilenames, ",")

	c.Success(ctx, result)
}

// ResourceDownload 本地资源通用下载
// @Summary 本地资源通用下载
// @Description 本地资源通用下载
// @Tags 通用
// @Accept json
// @Produce octet-stream
// @Param resource query string true "资源路径"
// @Success 200 {object} controller.Response "成功"
// @Router /common/download/resource [get]
func (c *CommonController) ResourceDownload(ctx *gin.Context) {
	resource := ctx.Query("resource")

	// 检查文件名是否合法
	if !checkAllowDownload(resource) {
		ctx.String(http.StatusInternalServerError, fmt.Sprintf("资源文件(%s)非法，不允许下载", resource))
		return
	}

	// 构建文件路径
	downloadPath := filepath.Join(c.resourcePath, stripPrefix(resource))
	downloadName := filepath.Base(downloadPath)

	// 检查文件是否存在
	if _, err := os.Stat(downloadPath); os.IsNotExist(err) {
		ctx.String(http.StatusNotFound, "资源文件不存在")
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	setAttachmentResponseHeader(ctx, downloadName)

	// 发送文件
	ctx.File(downloadPath)
}

// checkAllowDownload 检查文件名是否合法
func checkAllowDownload(name string) bool {
	// 禁止包含 ../ 路径遍历
	if strings.Contains(name, "../") || strings.Contains(name, "..\\") {
		return false
	}

	// 检查文件后缀
	ext := filepath.Ext(name)
	if ext == "" {
		return false
	}

	// 禁止下载可执行文件和系统文件
	forbiddenExts := []string{
		".exe", ".dll", ".so", ".dylib", ".bat", ".cmd", ".sh",
		".msi", ".sys", ".com", ".bin", ".vbs", ".ps1",
	}

	extLower := strings.ToLower(ext)
	for _, forbiddenExt := range forbiddenExts {
		if extLower == forbiddenExt {
			return false
		}
	}

	// 允许的文件类型
	allowedExts := []string{
		// 文档类型
		".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".csv",
		// 图片类型
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
		// 压缩文件
		".zip", ".rar", ".7z", ".tar", ".gz",
		// 其他允许的类型
		".json", ".xml", ".html", ".css", ".js",
	}

	for _, allowedExt := range allowedExts {
		if extLower == allowedExt {
			return true
		}
	}

	// 默认不允许下载
	return false
}

// upload 上传文件
func (c *CommonController) upload(basePath string, file *multipart.FileHeader) (string, error) {
	// 检查文件类型
	ext := filepath.Ext(file.Filename)
	if ext == "" {
		return "", fmt.Errorf("文件没有扩展名")
	}

	// 允许的文件类型
	allowedExts := []string{
		// 文档类型
		".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".csv",
		// 图片类型
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
		// 压缩文件
		".zip", ".rar", ".7z", ".tar", ".gz",
		// 其他允许的类型
		".json", ".xml", ".html", ".css", ".js",
	}

	extLower := strings.ToLower(ext)
	extAllowed := false
	for _, allowedExt := range allowedExts {
		if extLower == allowedExt {
			extAllowed = true
			break
		}
	}

	if !extAllowed {
		return "", fmt.Errorf("不允许的文件类型: %s", ext)
	}

	// 校验文件大小
	if file.Size > 50*1024*1024 { // 最大50MB
		return "", fmt.Errorf("文件大小超过限制，最大允许50MB")
	}

	// 创建上传目录
	if err := os.MkdirAll(basePath, 0755); err != nil {
		return "", err
	}

	// 生成文件名
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), filepath.Ext(file.Filename))
	filePath := filepath.Join(basePath, fileName)

	// 保存文件
	if err := saveUploadedFile(file, filePath); err != nil {
		return "", err
	}

	return fileName, nil
}

// saveUploadedFile 保存上传的文件
func saveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// stripPrefix 去除前缀
func stripPrefix(name string) string {
	if strings.HasPrefix(name, "/profile") {
		return strings.TrimPrefix(name, "/profile")
	}
	return name
}

// setAttachmentResponseHeader 设置附件下载的响应头
func setAttachmentResponseHeader(ctx *gin.Context, realFileName string) {
	encodedFileName := url.PathEscape(realFileName)

	// 构建Content-Disposition头
	contentDisposition := fmt.Sprintf("attachment; filename=%s; filename*=utf-8''%s",
		encodedFileName, encodedFileName)

	// 设置响应头
	ctx.Header("Access-Control-Expose-Headers", "Content-Disposition,download-filename")
	ctx.Header("Content-Disposition", contentDisposition)
	ctx.Header("download-filename", encodedFileName)
}
