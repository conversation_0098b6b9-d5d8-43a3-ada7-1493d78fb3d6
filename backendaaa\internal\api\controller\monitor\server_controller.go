package monitor

import (
	"backend/internal/api/controller"
	"backend/internal/model/server"

	"github.com/gin-gonic/gin"
)

// ServerController 服务器监控控制器
type ServerController struct {
	controller.BaseController
}

// NewServerController 创建服务器监控控制器
func NewServerController() *ServerController {
	return &ServerController{}
}

// GetInfo 获取服务器信息
// @Summary 获取服务器信息
// @Description 获取服务器信息
// @Tags 服务器监控
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/server [get]
func (c *ServerController) GetInfo(ctx *gin.Context) {
	// 创建服务器信息对象
	serverInfo := &server.Server{}

	// 获取服务器信息
	err := serverInfo.CopyTo()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 返回服务器信息
	c.Success(ctx, serverInfo)
}
