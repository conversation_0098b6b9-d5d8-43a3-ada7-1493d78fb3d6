package monitor

import (
	"backend/internal/api/common"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysJobController 调度任务信息操作处理
type SysJobController struct {
	common.BaseController
	jobService service.SysJobService
}

// NewSysJobController 创建调度任务控制器
func NewSysJobController(jobService service.SysJobService) *SysJobController {
	return &SysJobController{
		jobService: jobService,
	}
}

// List 查询定时任务列表
// @Router /monitor/job/list [get]
func (c *SysJobController) List(ctx *gin.Context) {
	job := &model.SysJob{}

	// 绑定查询参数
	if jobName := ctx.Query("jobName"); jobName != "" {
		job.JobName = jobName
	}
	if jobGroup := ctx.Query("jobGroup"); jobGroup != "" {
		job.JobGroup = jobGroup
	}
	if status := ctx.Query("status"); status != "" {
		job.Status = status
	}

	// 设置分页
	_ = c.StartPage(ctx) // 忽略返回值，暂未实现分页

	// 查询列表
	list, err := c.jobService.SelectJobList(job)
	if err != nil {
		c.ErrorJSON(ctx, "查询定时任务列表失败: "+err.Error())
		return
	}

	// TODO: 实现分页查询
	total := int64(len(list))

	// 返回分页数据
	tableData := c.GetDataTable(list, total)
	c.SuccessJSON(ctx, tableData)
}

// GetInfo 获取定时任务详细信息
// @Router /monitor/job/{jobId} [get]
func (c *SysJobController) GetInfo(ctx *gin.Context) {
	jobIdStr := ctx.Param("jobId")
	jobId, err := strconv.ParseInt(jobIdStr, 10, 64)
	if err != nil {
		c.ErrorJSON(ctx, "任务ID格式错误")
		return
	}

	job, err := c.jobService.SelectJobById(jobId)
	if err != nil {
		c.ErrorJSON(ctx, "查询定时任务失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, job)
}

// Add 新增定时任务
// @Router /monitor/job [post]
func (c *SysJobController) Add(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.CheckCronExpressionIsValid(job.CronExpression) {
		c.ErrorJSON(ctx, "新增任务'"+job.JobName+"'失败，Cron表达式不正确")
		return
	}

	// 校验目标字符串是否合法
	if err := utils.CheckJobInvokeTarget(job.InvokeTarget); err != nil {
		c.ErrorJSON(ctx, "新增任务'"+job.JobName+"'失败，"+err.Error())
		return
	}

	// 设置创建者
	job.CreateBy = c.GetUsername(ctx)

	// 新增任务
	jobId, err := c.jobService.InsertJob(&job)
	if err != nil {
		c.ErrorJSON(ctx, "新增任务失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(int(jobId)))
}

// Edit 修改定时任务
// @Router /monitor/job [put]
func (c *SysJobController) Edit(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.CheckCronExpressionIsValid(job.CronExpression) {
		c.ErrorJSON(ctx, "修改任务'"+job.JobName+"'失败，Cron表达式不正确")
		return
	}

	// 校验目标字符串是否合法
	if err := utils.CheckJobInvokeTarget(job.InvokeTarget); err != nil {
		c.ErrorJSON(ctx, "修改任务'"+job.JobName+"'失败，"+err.Error())
		return
	}

	// 设置更新者
	job.UpdateBy = c.GetUsername(ctx)

	// 修改任务
	result, err := c.jobService.UpdateJob(&job)
	if err != nil {
		c.ErrorJSON(ctx, "修改任务失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// ChangeStatus 定时任务状态修改
// @Router /monitor/job/changeStatus [put]
func (c *SysJobController) ChangeStatus(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 修改任务状态
	result, err := c.jobService.ChangeStatus(&job)
	if err != nil {
		c.ErrorJSON(ctx, "修改任务状态失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// Run 定时任务立即执行一次
// @Router /monitor/job/run [put]
func (c *SysJobController) Run(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	result, err := c.jobService.Run(&job)
	if err != nil {
		c.ErrorJSON(ctx, "执行任务失败: "+err.Error())
		return
	}

	if !result {
		c.ErrorJSON(ctx, "任务不存在或已过期！")
		return
	}

	c.SuccessJSON(ctx, nil)
}

// Remove 删除定时任务
// @Router /monitor/job/{jobIds} [delete]
func (c *SysJobController) Remove(ctx *gin.Context) {
	jobIdsStr := ctx.Param("jobIds")
	jobIdsArr := strings.Split(jobIdsStr, ",")
	jobIds := make([]int64, 0, len(jobIdsArr))

	// 转换任务ID
	for _, idStr := range jobIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		jobIds = append(jobIds, id)
	}

	// 删除任务
	err := c.jobService.DeleteJobByIds(jobIds)
	if err != nil {
		c.ErrorJSON(ctx, "删除任务失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, nil)
}

// Export 导出定时任务列表
// @Router /monitor/job/export [get]
func (c *SysJobController) Export(ctx *gin.Context) {
	job := &model.SysJob{}

	// 绑定查询参数
	if jobName := ctx.Query("jobName"); jobName != "" {
		job.JobName = jobName
	}
	if jobGroup := ctx.Query("jobGroup"); jobGroup != "" {
		job.JobGroup = jobGroup
	}
	if status := ctx.Query("status"); status != "" {
		job.Status = status
	}

	// 查询列表
	_, err := c.jobService.SelectJobList(job)
	if err != nil {
		c.ErrorJSON(ctx, "查询定时任务列表失败: "+err.Error())
		return
	}

	// TODO: 导出Excel
	c.ErrorJSON(ctx, "导出功能暂未实现")
}
