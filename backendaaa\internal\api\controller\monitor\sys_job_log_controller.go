package monitor

import (
	"backend/internal/api/common"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysJobLogController 调度日志操作处理
type SysJobLogController struct {
	common.BaseController
	jobLogService service.SysJobLogService
}

// NewSysJobLogController 创建调度日志控制器
func NewSysJobLogController(jobLogService service.SysJobLogService) *SysJobLogController {
	return &SysJobLogController{
		jobLogService: jobLogService,
	}
}

// List 查询定时任务调度日志列表
// @Router /monitor/jobLog/list [get]
func (c *SysJobLogController) List(ctx *gin.Context) {
	jobLog := &model.SysJobLog{}

	// 绑定查询参数
	if jobName := ctx.Query("jobName"); jobName != "" {
		jobLog.JobName = jobName
	}
	if jobGroup := ctx.Query("jobGroup"); jobGroup != "" {
		jobLog.JobGroup = jobGroup
	}
	if status := ctx.Query("status"); status != "" {
		jobLog.Status = status
	}

	// 设置分页
	_ = c.StartPage(ctx) // 忽略返回值，暂未实现分页

	// 查询列表
	list, err := c.jobLogService.SelectJobLogList(jobLog)
	if err != nil {
		c.ErrorJSON(ctx, "查询调度日志列表失败: "+err.Error())
		return
	}

	// TODO: 实现分页查询
	total := int64(len(list))

	// 返回分页数据
	tableData := c.GetDataTable(list, total)
	c.SuccessJSON(ctx, tableData)
}

// GetInfo 获取定时任务调度日志详细信息
// @Router /monitor/jobLog/{jobLogId} [get]
func (c *SysJobLogController) GetInfo(ctx *gin.Context) {
	jobLogIdStr := ctx.Param("jobLogId")
	jobLogId, err := strconv.ParseInt(jobLogIdStr, 10, 64)
	if err != nil {
		c.ErrorJSON(ctx, "日志ID格式错误")
		return
	}

	jobLog, err := c.jobLogService.SelectJobLogById(jobLogId)
	if err != nil {
		c.ErrorJSON(ctx, "查询调度日志失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, jobLog)
}

// Remove 删除定时任务调度日志
// @Router /monitor/jobLog/{jobLogIds} [delete]
func (c *SysJobLogController) Remove(ctx *gin.Context) {
	jobLogIdsStr := ctx.Param("jobLogIds")
	jobLogIdsArr := strings.Split(jobLogIdsStr, ",")
	jobLogIds := make([]int64, 0, len(jobLogIdsArr))

	// 转换日志ID
	for _, idStr := range jobLogIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		jobLogIds = append(jobLogIds, id)
	}

	// 删除日志
	_, err := c.jobLogService.DeleteJobLogByIds(jobLogIds)
	if err != nil {
		c.ErrorJSON(ctx, "删除调度日志失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, nil)
}

// Clean 清空定时任务调度日志
// @Router /monitor/jobLog/clean [delete]
func (c *SysJobLogController) Clean(ctx *gin.Context) {
	_, err := c.jobLogService.CleanJobLog()
	if err != nil {
		c.ErrorJSON(ctx, "清空调度日志失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, nil)
}

// Export 导出定时任务调度日志列表
// @Router /monitor/jobLog/export [get]
func (c *SysJobLogController) Export(ctx *gin.Context) {
	jobLog := &model.SysJobLog{}

	// 绑定查询参数
	if jobName := ctx.Query("jobName"); jobName != "" {
		jobLog.JobName = jobName
	}
	if jobGroup := ctx.Query("jobGroup"); jobGroup != "" {
		jobLog.JobGroup = jobGroup
	}
	if status := ctx.Query("status"); status != "" {
		jobLog.Status = status
	}

	// 查询列表
	_, err := c.jobLogService.SelectJobLogList(jobLog)
	if err != nil {
		c.ErrorJSON(ctx, "查询调度日志列表失败: "+err.Error())
		return
	}

	// TODO: 导出Excel
	c.ErrorJSON(ctx, "导出功能暂未实现")
}
