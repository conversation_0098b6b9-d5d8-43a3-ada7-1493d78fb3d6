package monitor

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"encoding/csv"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SysLogininforController 系统访问记录
type SysLogininforController struct {
	controller.BaseController
	logininforService service.SysLogininforService
	passwordService   service.SysPasswordService
}

// NewSysLogininforController 创建登录日志控制器
func NewSysLogininforController(logininforService service.SysLogininforService, passwordService service.SysPasswordService) *SysLogininforController {
	return &SysLogininforController{
		logininforService: logininforService,
		passwordService:   passwordService,
	}
}

// List 查询系统访问记录列表
// @Summary 查询系统访问记录列表
// @Description 查询系统访问记录列表
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param logininfor query model.SysLogininfor false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/list [get]
func (c *SysLogininforController) List(ctx *gin.Context) {
	// 构建查询参数
	logininfor := &model.SysLogininfor{
		UserName: ctx.Query("userName"),
		Ipaddr:   ctx.Query("ipaddr"),
		Status:   ctx.Query("status"),
	}

	// 查询登录日志列表
	list, err := c.logininforService.SelectLogininforList(logininfor)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, list)
}

// Export 导出登录日志
// @Summary 导出登录日志
// @Description 导出登录日志
// @Tags 登录日志
// @Accept json
// @Produce octet-stream
// @Param logininfor query model.SysLogininfor false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/export [get]
func (c *SysLogininforController) Export(ctx *gin.Context) {
	// 构建查询参数
	logininfor := &model.SysLogininfor{
		UserName: ctx.Query("userName"),
		Ipaddr:   ctx.Query("ipaddr"),
		Status:   ctx.Query("status"),
	}

	// 查询登录日志列表
	list, err := c.logininforService.SelectLogininforList(logininfor)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置Excel工具
	headers := []string{"访问编号", "用户名称", "登录地址", "登录地点", "浏览器", "操作系统", "登录状态", "操作信息", "登录时间"}
	fields := []string{"InfoId", "UserName", "Ipaddr", "LoginLocation", "Browser", "OS", "Status", "Msg", "LoginTime"}

	// 创建Excel工具
	excelUtil := utils.NewExcelUtil("登录日志", "登录日志列表", headers, fields)

	// 导出Excel
	filename := "logininfor_" + time.Now().Format("20060102150405") + ".xlsx"
	err = excelUtil.ExportToExcel(ctx, list, filename)
	if err != nil {
		// 如果Excel导出失败，回退到CSV导出
		c.exportToCSV(ctx, list)
	}
}

// exportToCSV 导出为CSV格式（作为Excel导出失败的备选方案）
func (c *SysLogininforController) exportToCSV(ctx *gin.Context, logininfors []*model.SysLogininfor) {
	// 导出数据
	data := make([][]string, 0, len(logininfors)+1)

	// 添加表头
	data = append(data, []string{"访问编号", "用户名称", "登录地址", "登录地点", "浏览器", "操作系统", "登录状态", "操作信息", "登录时间"})

	// 添加数据行
	for _, info := range logininfors {
		var statusStr string
		switch info.Status {
		case "0":
			statusStr = "成功"
		case "1":
			statusStr = "失败"
		default:
			statusStr = info.Status
		}

		loginTime := ""
		if info.LoginTime != nil {
			loginTime = info.LoginTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(info.InfoId, 10),
			info.UserName,
			info.Ipaddr,
			info.LoginLocation,
			info.Browser,
			info.OS,
			statusStr,
			info.Msg,
			loginTime,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=logininfor_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// Remove 删除登录日志
// @Summary 删除登录日志
// @Description 删除登录日志
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param infoIds path string true "登录日志ID，多个以逗号分隔"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/{infoIds} [delete]
func (c *SysLogininforController) Remove(ctx *gin.Context) {
	infoIdsStr := ctx.Param("infoIds")
	infoIdStrs := strings.Split(infoIdsStr, ",")

	var infoIds []int64
	for _, idStr := range infoIdStrs {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.Error(ctx, utils.NewError("参数错误"))
			return
		}
		infoIds = append(infoIds, id)
	}

	rows, err := c.logininforService.DeleteLogininforByIds(infoIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, rows)
}

// Clean 清空登录日志
// @Summary 清空登录日志
// @Description 清空登录日志
// @Tags 登录日志
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/clean [delete]
func (c *SysLogininforController) Clean(ctx *gin.Context) {
	err := c.logininforService.CleanLogininfor()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Unlock 解锁账户
// @Summary 解锁账户
// @Description 解锁账户
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param userName path string true "用户名"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/unlock/{userName} [get]
func (c *SysLogininforController) Unlock(ctx *gin.Context) {
	userName := ctx.Param("userName")
	err := c.passwordService.ClearLoginRecordCache(userName)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}
