package monitor

import (
	"backend/internal/api/common"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"sort"

	"github.com/gin-gonic/gin"
)

// SysUserOnlineController 在线用户监控
type SysUserOnlineController struct {
	common.BaseController
	userOnlineService service.SysUserOnlineService
	redisCache        service.RedisCache
}

// NewSysUserOnlineController 创建在线用户监控控制器
func NewSysUserOnlineController(userOnlineService service.SysUserOnlineService, redisCache service.RedisCache) *SysUserOnlineController {
	return &SysUserOnlineController{
		userOnlineService: userOnlineService,
		redisCache:        redisCache,
	}
}

// List 查询在线用户列表
// @Summary 查询在线用户列表
// @Description 查询在线用户列表
// @Tags 在线用户
// @Accept json
// @Produce json
// @Param ipaddr query string false "登录地址"
// @Param userName query string false "用户名称"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/online/list [get]
func (c *SysUserOnlineController) List(ctx *gin.Context) {
	ipaddr := ctx.Query("ipaddr")
	userName := ctx.Query("userName")

	// 获取所有登录用户的token
	keys, err := c.redisCache.Keys(constants.LOGIN_TOKEN_KEY + "*")
	if err != nil {
		c.ErrorJSON(ctx, "获取在线用户失败: "+err.Error())
		return
	}

	var userOnlineList []*model.SysUserOnline
	for _, key := range keys {
		// 获取登录用户信息
		userObj, err := c.redisCache.GetCacheObject(key)
		if err != nil {
			continue
		}

		user, ok := userObj.(*model.LoginUser)
		if !ok || user == nil {
			continue
		}

		// 根据查询条件过滤
		var userOnline *model.SysUserOnline
		if ipaddr != "" && userName != "" {
			userOnline = c.userOnlineService.SelectOnlineByInfo(ipaddr, userName, user)
		} else if ipaddr != "" {
			userOnline = c.userOnlineService.SelectOnlineByIpaddr(ipaddr, user)
		} else if userName != "" && user.User != nil {
			userOnline = c.userOnlineService.SelectOnlineByUserName(userName, user)
		} else {
			userOnline = c.userOnlineService.LoginUserToUserOnline(user)
		}

		if userOnline != nil {
			userOnlineList = append(userOnlineList, userOnline)
		}
	}

	// 按登录时间倒序排序
	sort.Slice(userOnlineList, func(i, j int) bool {
		return userOnlineList[i].LoginTime > userOnlineList[j].LoginTime
	})

	c.SuccessJSON(ctx, userOnlineList)
}

// ForceLogout 强制退出用户
// @Summary 强制退出用户
// @Description 强制退出用户
// @Tags 在线用户
// @Accept json
// @Produce json
// @Param tokenId path string true "会话编号"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/online/{tokenId} [delete]
func (c *SysUserOnlineController) ForceLogout(ctx *gin.Context) {
	tokenId := ctx.Param("tokenId")
	err := c.redisCache.DeleteObject(constants.LOGIN_TOKEN_KEY + tokenId)
	if err != nil {
		c.ErrorJSON(ctx, "强制退出失败: "+err.Error())
		return
	}
	c.SuccessJSON(ctx, nil)
}
