package system

import (
	"backend/internal/api/controller/common"
	"backend/internal/service"

	"github.com/gin-gonic/gin"
)

// CaptchaController 验证码控制器
type CaptchaController struct {
	commonCaptchaController *common.CaptchaController
}

// NewCaptchaController 创建验证码控制器
func NewCaptchaController(configService service.SysConfigService, redisCache service.RedisCache) *CaptchaController {
	return &CaptchaController{
		commonCaptchaController: common.NewCaptchaController(configService, redisCache),
	}
}

// GetCode 生成验证码
func (c *CaptchaController) GetCode(ctx *gin.Context) {
	c.commonCaptchaController.GetCode(ctx)
}
