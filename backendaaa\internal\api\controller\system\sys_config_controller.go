package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"encoding/csv"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SysConfigController 参数配置控制器
type SysConfigController struct {
	controller.BaseController
	configService service.SysConfigService
}

// NewSysConfigController 创建参数配置控制器
func NewSysConfigController(configService service.SysConfigService) *SysConfigController {
	return &SysConfigController{
		configService: configService,
	}
}

// List 获取参数配置列表
// @Router /system/config/list [get]
func (c *SysConfigController) List(ctx *gin.Context) {
	// 获取查询参数
	var param model.SysConfig
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	configName := ctx.Query("configName")
	configKey := ctx.Query("configKey")
	configType := ctx.Query("configType")

	if configName != "" {
		param.ConfigName = configName
	}
	if configKey != "" {
		param.ConfigKey = configKey
	}
	if configType != "" {
		param.ConfigType = configType
	}

	// 查询参数配置列表
	list, err := c.configService.SelectConfigList(&param)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 根据参数编号获取详细信息
// @Router /system/config/{configId} [get]
func (c *SysConfigController) GetInfo(ctx *gin.Context) {
	configId, err := strconv.ParseInt(ctx.Param("configId"), 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	config, err := c.configService.SelectConfigById(configId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, config)
}

// GetConfigKey 根据参数键名查询参数值
// @Router /system/config/configKey/{configKey} [get]
func (c *SysConfigController) GetConfigKey(ctx *gin.Context) {
	configKey := ctx.Param("configKey")

	configValue, err := c.configService.SelectConfigByKey(configKey)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, configValue)
}

// Add 新增参数配置
// @Router /system/config [post]
func (c *SysConfigController) Add(ctx *gin.Context) {
	var config model.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验参数键名是否唯一
	if !c.configService.CheckConfigKeyUnique(&config) {
		c.Warn(ctx, "新增参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置创建者
	config.CreateBy = c.GetUsername(ctx)

	result, err := c.configService.InsertConfig(&config)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(result), nil)
}

// Edit 修改参数配置
// @Router /system/config [put]
func (c *SysConfigController) Edit(ctx *gin.Context) {
	var config model.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验参数键名是否唯一
	if !c.configService.CheckConfigKeyUnique(&config) {
		c.Warn(ctx, "修改参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置更新者
	config.UpdateBy = c.GetUsername(ctx)

	result, err := c.configService.UpdateConfig(&config)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(result), nil)
}

// Remove 删除参数配置
// @Router /system/config/{configIds} [delete]
func (c *SysConfigController) Remove(ctx *gin.Context) {
	configIdsStr := ctx.Param("configIds")
	configIds := make([]int64, 0)

	for _, idStr := range strings.Split(configIdsStr, ",") {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			configIds = append(configIds, id)
		}
	}

	err := c.configService.DeleteConfigByIds(configIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RefreshCache 刷新参数缓存
// @Router /system/config/refreshCache [delete]
func (c *SysConfigController) RefreshCache(ctx *gin.Context) {
	err := c.configService.ResetConfigCache()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Export 导出参数
// @Summary 导出参数
// @Description 导出参数
// @Tags 参数管理
// @Accept json
// @Produce octet-stream
// @Param config query model.SysConfig false "参数信息"
// @Success 200 {file} 文件流
// @Router /system/config/export [get]
func (c *SysConfigController) Export(ctx *gin.Context) {
	// 获取查询参数
	var param model.SysConfig

	configName := ctx.Query("configName")
	configKey := ctx.Query("configKey")
	configType := ctx.Query("configType")

	if configName != "" {
		param.ConfigName = configName
	}
	if configKey != "" {
		param.ConfigKey = configKey
	}
	if configType != "" {
		param.ConfigType = configType
	}

	// 查询参数配置列表
	list, err := c.configService.SelectConfigList(&param)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置Excel工具
	headers := []string{"参数主键", "参数名称", "参数键名", "参数键值", "系统内置", "备注", "创建者", "创建时间", "更新者", "更新时间"}
	fields := []string{"ConfigID", "ConfigName", "ConfigKey", "ConfigValue", "ConfigType", "Remark", "CreateBy", "CreateTime", "UpdateBy", "UpdateTime"}

	// 创建Excel工具
	excelUtil := utils.NewExcelUtil("参数配置", "参数配置列表", headers, fields)

	// 导出Excel
	filename := "config_" + time.Now().Format("20060102150405") + ".xlsx"
	err = excelUtil.ExportToExcel(ctx, list, filename)
	if err != nil {
		// 如果Excel导出失败，回退到CSV导出
		c.exportToCSV(ctx, list)
	}
}

// exportToCSV 导出为CSV格式（作为Excel导出失败的备选方案）
func (c *SysConfigController) exportToCSV(ctx *gin.Context, configs []*model.SysConfig) {
	// 导出数据
	data := make([][]string, 0, len(configs)+1)

	// 添加表头
	data = append(data, []string{"参数主键", "参数名称", "参数键名", "参数键值", "系统内置", "备注", "创建者", "创建时间", "更新者", "更新时间"})

	// 添加数据行
	for _, config := range configs {
		var configTypeStr string
		switch config.ConfigType {
		case "Y":
			configTypeStr = "是"
		case "N":
			configTypeStr = "否"
		default:
			configTypeStr = config.ConfigType
		}

		createTime := ""
		if config.CreateTime != nil {
			createTime = config.CreateTime.Format("2006-01-02 15:04:05")
		}

		updateTime := ""
		if config.UpdateTime != nil {
			updateTime = config.UpdateTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(config.ConfigID, 10),
			config.ConfigName,
			config.ConfigKey,
			config.ConfigValue,
			configTypeStr,
			config.Remark,
			config.CreateBy,
			createTime,
			config.UpdateBy,
			updateTime,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=config_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// RegisterRoutes 注册路由
func (c *SysConfigController) RegisterRoutes(router *gin.RouterGroup) {
	configRouter := router.Group("/system/config")
	{
		configRouter.GET("/list", c.List)
		configRouter.GET("/:configId", c.GetInfo)
		configRouter.GET("/configKey/:configKey", c.GetConfigKey)
		configRouter.POST("", c.Add)
		configRouter.PUT("", c.Edit)
		configRouter.DELETE("/:configIds", c.Remove)
		configRouter.DELETE("/refreshCache", c.RefreshCache)
		configRouter.GET("/export", c.Export)
	}
}
