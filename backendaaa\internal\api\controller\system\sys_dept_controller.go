package system

import (
	"backend/internal/api/common"
	"backend/internal/common/response"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"bytes"
	"encoding/csv"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysDeptController 部门信息控制器
type SysDeptController struct {
	common.BaseController
	deptService service.SysDeptService
}

// NewSysDeptController 创建部门控制器
func NewSysDeptController(deptService service.SysDeptService) *SysDeptController {
	return &SysDeptController{
		deptService: deptService,
	}
}

// List 获取部门列表
// @Router /system/dept/list [get]
func (c *SysDeptController) List(ctx *gin.Context) {
	// 绑定查询参数
	dept := &model.SysDept{}
	if deptName := ctx.Query("deptName"); deptName != "" {
		dept.DeptName = deptName
	}
	if status := ctx.Query("status"); status != "" {
		dept.Status = status
	}

	// 查询部门列表
	depts, err := c.deptService.SelectDeptList(dept)
	if err != nil {
		c.ErrorJSON(ctx, "查询部门列表失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(depts))
}

// ExcludeChild 查询部门列表（排除节点）
// @Router /system/dept/list/exclude/{deptId} [get]
func (c *SysDeptController) ExcludeChild(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 查询部门列表
	depts, err := c.deptService.SelectDeptList(&model.SysDept{})
	if err != nil {
		c.ErrorJSON(ctx, "查询部门列表失败: "+err.Error())
		return
	}

	// 排除指定节点及其子节点
	filteredDepts := make([]*model.SysDept, 0, len(depts))
	for _, d := range depts {
		// 排除自身
		if d.DeptID == id {
			continue
		}

		// 排除祖先包含当前部门的节点
		if d.Ancestors != "" {
			ancestors := strings.Split(d.Ancestors, ",")
			exclude := false
			for _, a := range ancestors {
				aID, _ := strconv.ParseInt(a, 10, 64)
				if aID == id {
					exclude = true
					break
				}
			}
			if exclude {
				continue
			}
		}

		filteredDepts = append(filteredDepts, d)
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(filteredDepts))
}

// GetInfo 根据部门编号获取详细信息
// @Router /system/dept/{deptId} [get]
func (c *SysDeptController) GetInfo(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 校验部门是否有数据权限
	err := c.deptService.CheckDeptDataScope(id)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 查询部门信息
	dept, err := c.deptService.SelectDeptById(id)
	if err != nil {
		c.ErrorJSON(ctx, "查询部门信息失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(dept))
}

// Add 新增部门
// @Router /system/dept [post]
func (c *SysDeptController) Add(ctx *gin.Context) {
	var dept model.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门名称是否唯一
	unique, err := c.deptService.CheckDeptNameUnique(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门名称失败: "+err.Error())
		return
	}
	if !unique {
		c.ErrorJSON(ctx, "新增部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 设置创建者
	dept.CreateBy = c.GetUsername(ctx)

	// 新增部门
	result, err := c.deptService.InsertDept(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "新增部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}

// Edit 修改部门
// @Router /system/dept [put]
func (c *SysDeptController) Edit(ctx *gin.Context) {
	var dept model.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门是否有数据权限
	err := c.deptService.CheckDeptDataScope(dept.DeptID)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 校验部门名称是否唯一
	unique, err := c.deptService.CheckDeptNameUnique(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门名称失败: "+err.Error())
		return
	}
	if !unique {
		c.ErrorJSON(ctx, "修改部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 校验上级部门不能是自己
	if dept.ParentID == dept.DeptID {
		c.ErrorJSON(ctx, "修改部门'"+dept.DeptName+"'失败，上级部门不能是自己")
		return
	}

	// 校验部门状态
	if dept.Status == constants.DEPT_DISABLE {
		count, err := c.deptService.SelectNormalChildrenDeptById(dept.DeptID)
		if err != nil {
			c.ErrorJSON(ctx, "查询子部门状态失败: "+err.Error())
			return
		}
		if count > 0 {
			c.ErrorJSON(ctx, "该部门包含未停用的子部门！")
			return
		}
	}

	// 设置更新者
	dept.UpdateBy = c.GetUsername(ctx)

	// 修改部门
	result, err := c.deptService.UpdateDept(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "修改部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}

// Remove 删除部门
// @Router /system/dept/{deptId} [delete]
func (c *SysDeptController) Remove(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 校验是否存在子部门
	hasChild, err := c.deptService.HasChildByDeptId(id)
	if err != nil {
		c.ErrorJSON(ctx, "校验子部门失败: "+err.Error())
		return
	}
	if hasChild {
		c.SuccessJSON(ctx, c.WarnMessage("存在下级部门,不允许删除"))
		return
	}

	// 校验是否存在部门用户
	existUser, err := c.deptService.CheckDeptExistUser(id)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门用户失败: "+err.Error())
		return
	}
	if existUser {
		c.SuccessJSON(ctx, c.WarnMessage("部门存在用户,不允许删除"))
		return
	}

	// 校验部门是否有数据权限
	err = c.deptService.CheckDeptDataScope(id)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 删除部门
	result, err := c.deptService.DeleteDeptById(id)
	if err != nil {
		c.ErrorJSON(ctx, "删除部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}

// TreeSelect 获取部门树列表
// @Summary 获取部门树列表
// @Description 获取部门树列表
// @Tags 部门管理
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /system/dept/treeselect [get]
func (c *SysDeptController) TreeSelect(ctx *gin.Context) {
	departments, err := c.deptService.SelectDeptList(&model.SysDept{})
	if err != nil {
		response.Error(ctx, "获取部门树列表失败: "+err.Error())
		return
	}

	data := c.buildDeptTreeSelect(departments)
	response.Success(ctx, data)
}

// buildDeptTreeSelect 构建部门树选择结构
func (c *SysDeptController) buildDeptTreeSelect(depts []*model.SysDept) []map[string]interface{} {
	trees := make([]map[string]interface{}, 0)
	rootDepts := c.findRootDepts(depts)

	for _, dept := range rootDepts {
		tree := c.createDeptTree(dept, depts)
		trees = append(trees, tree)
	}

	return trees
}

// findRootDepts 查找根部门
func (c *SysDeptController) findRootDepts(depts []*model.SysDept) []*model.SysDept {
	roots := make([]*model.SysDept, 0)

	for _, dept := range depts {
		if dept.ParentID == 0 {
			roots = append(roots, dept)
		}
	}

	return roots
}

// createDeptTree 创建部门树
func (c *SysDeptController) createDeptTree(dept *model.SysDept, allDepts []*model.SysDept) map[string]interface{} {
	tree := make(map[string]interface{})
	tree["id"] = dept.DeptID
	tree["label"] = dept.DeptName

	// 查找子部门
	children := make([]map[string]interface{}, 0)
	for _, d := range allDepts {
		if d.ParentID == dept.DeptID {
			child := c.createDeptTree(d, allDepts)
			children = append(children, child)
		}
	}

	if len(children) > 0 {
		tree["children"] = children
	}

	return tree
}

// RoleDeptTreeSelect 根据角色ID查询部门树结构
// @Summary 根据角色ID查询部门树结构
// @Description 根据角色ID查询部门树结构
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param roleId path int true "角色ID"
// @Success 200 {object} response.ResponseData
// @Router /system/dept/roleDeptTreeselect/{roleId} [get]
func (c *SysDeptController) RoleDeptTreeSelect(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Param("roleId"), 10, 64)
	if err != nil {
		response.Error(ctx, "角色ID无效")
		return
	}

	// 查询部门列表
	departments, err := c.deptService.SelectDeptList(&model.SysDept{})
	if err != nil {
		response.Error(ctx, "获取部门列表失败: "+err.Error())
		return
	}

	// 构建部门树
	deptTree := c.buildDeptTreeSelect(departments)

	// 查询角色已分配的部门列表
	checkedKeys, err := c.deptService.SelectDeptListByRoleId(roleId)
	if err != nil {
		response.Error(ctx, "获取角色部门列表失败: "+err.Error())
		return
	}

	result := map[string]interface{}{
		"checkedKeys": checkedKeys,
		"depts":       deptTree,
	}

	response.Success(ctx, result)
}

// Export 导出部门数据
// @Summary 导出部门数据
// @Description 导出部门数据
// @Tags 部门管理
// @Accept json
// @Produce octet-stream
// @Param dept query model.SysDept false "部门信息"
// @Success 200 {file} 文件流
// @Router /system/dept/export [get]
func (c *SysDeptController) Export(ctx *gin.Context) {
	// 构建查询参数
	dept := &model.SysDept{
		DeptName: ctx.Query("deptName"),
		Status:   ctx.Query("status"),
	}

	// 查询列表
	depts, err := c.deptService.SelectDeptList(dept)
	if err != nil {
		response.Error(ctx, "导出部门失败: "+err.Error())
		return
	}

	// 导出数据
	data := make([][]string, 0, len(depts)+1)

	// 添加表头
	data = append(data, []string{"部门ID", "部门名称", "上级部门", "显示顺序", "负责人", "联系电话", "邮箱", "部门状态", "创建时间", "备注"})

	// 添加数据行
	for _, d := range depts {
		// 状态转换
		var statusStr string
		switch d.Status {
		case "0":
			statusStr = "正常"
		case "1":
			statusStr = "停用"
		default:
			statusStr = "未知"
		}

		// 创建时间
		createTime := ""
		if d.CreateTime != nil {
			createTime = d.CreateTime.Format("2006-01-02 15:04:05")
		}

		// 上级部门
		parentName := ""
		if d.ParentName != "" {
			parentName = d.ParentName
		}

		row := []string{
			strconv.FormatInt(d.DeptID, 10),
			d.DeptName,
			parentName,
			strconv.Itoa(d.OrderNum),
			d.Leader,
			d.Phone,
			d.Email,
			statusStr,
			createTime,
			d.Remark,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=dept_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}
