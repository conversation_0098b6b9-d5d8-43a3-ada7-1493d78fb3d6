package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"encoding/csv"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SysDictDataController 字典数据控制器
type SysDictDataController struct {
	controller.BaseController
	dictDataService service.SysDictDataService
	dictTypeService service.SysDictTypeService
}

// NewSysDictDataController 创建字典数据控制器
func NewSysDictDataController(dictDataService service.SysDictDataService, dictTypeService service.SysDictTypeService) *SysDictDataController {
	return &SysDictDataController{
		dictDataService: dictDataService,
		dictTypeService: dictTypeService,
	}
}

// List 获取字典数据列表
// @Router /system/dict/data/list [get]
func (c *SysDictDataController) List(ctx *gin.Context) {
	var dictData model.SysDictData

	// 获取查询参数
	dictLabel := ctx.Query("dictLabel")
	dictData.DictLabel = dictLabel

	dictType := ctx.Query("dictType")
	dictData.DictType = dictType

	status := ctx.Query("status")
	dictData.Status = status

	// 获取分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// 查询字典数据列表
	list, err := c.dictDataService.SelectDictDataList(&dictData)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 查询字典数据详细
// @Router /system/dict/data/{dictCode} [get]
func (c *SysDictDataController) GetInfo(ctx *gin.Context) {
	dictCodeStr := ctx.Param("dictCode")
	dictCode, err := strconv.ParseInt(dictCodeStr, 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	dictData, err := c.dictDataService.SelectDictDataById(dictCode)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, dictData)
}

// DictType 根据字典类型查询字典数据信息
// @Router /system/dict/data/type/{dictType} [get]
func (c *SysDictDataController) DictType(ctx *gin.Context) {
	dictType := ctx.Param("dictType")

	dictDatas, err := c.dictTypeService.SelectDictDataByType(dictType)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if dictDatas == nil {
		dictDatas = []*model.SysDictData{}
	}

	c.Success(ctx, dictDatas)
}

// Add 新增字典数据
// @Router /system/dict/data [post]
func (c *SysDictDataController) Add(ctx *gin.Context) {
	var dict model.SysDictData
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置创建者
	dict.CreateBy = c.GetUsername(ctx)

	dictCode, err := c.dictDataService.InsertDictData(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, dictCode, nil)
}

// Edit 修改保存字典数据
// @Router /system/dict/data [put]
func (c *SysDictDataController) Edit(ctx *gin.Context) {
	var dict model.SysDictData
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置更新者
	dict.UpdateBy = c.GetUsername(ctx)

	rows, err := c.dictDataService.UpdateDictData(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, rows, nil)
}

// Remove 删除字典数据
// @Router /system/dict/data/{dictCodes} [delete]
func (c *SysDictDataController) Remove(ctx *gin.Context) {
	dictCodesStr := ctx.Param("dictCodes")
	dictCodes := make([]int64, 0)

	for _, codeStr := range strings.Split(dictCodesStr, ",") {
		if code, err := strconv.ParseInt(codeStr, 10, 64); err == nil {
			dictCodes = append(dictCodes, code)
		}
	}

	err := c.dictDataService.DeleteDictDataByIds(dictCodes)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Export 导出字典数据
// @Summary 导出字典数据
// @Description 导出字典数据
// @Tags 字典管理
// @Accept json
// @Produce octet-stream
// @Param dictData query model.SysDictData false "字典数据信息"
// @Success 200 {file} 文件流
// @Router /system/dict/data/export [get]
func (c *SysDictDataController) Export(ctx *gin.Context) {
	var dictData model.SysDictData

	// 获取查询参数
	dictLabel := ctx.Query("dictLabel")
	dictData.DictLabel = dictLabel

	dictType := ctx.Query("dictType")
	dictData.DictType = dictType

	status := ctx.Query("status")
	dictData.Status = status

	// 查询字典数据列表
	list, err := c.dictDataService.SelectDictDataList(&dictData)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置Excel工具
	headers := []string{"字典编码", "字典排序", "字典标签", "字典键值", "字典类型", "状态", "CSS类名", "表格回显样式", "是否默认", "创建者", "创建时间", "更新者", "更新时间", "备注"}
	fields := []string{"DictCode", "DictSort", "DictLabel", "DictValue", "DictType", "Status", "CssClass", "ListClass", "IsDefault", "CreateBy", "CreateTime", "UpdateBy", "UpdateTime", "Remark"}

	// 创建Excel工具
	excelUtil := utils.NewExcelUtil("字典数据", "字典数据列表", headers, fields)

	// 导出Excel
	filename := "dict_data_" + time.Now().Format("20060102150405") + ".xlsx"
	err = excelUtil.ExportToExcel(ctx, list, filename)
	if err != nil {
		// 如果Excel导出失败，回退到CSV导出
		c.exportToCSV(ctx, list)
	}
}

// exportToCSV 导出为CSV格式（作为Excel导出失败的备选方案）
func (c *SysDictDataController) exportToCSV(ctx *gin.Context, dictDatas []*model.SysDictData) {
	// 导出数据
	data := make([][]string, 0, len(dictDatas)+1)

	// 添加表头
	data = append(data, []string{"字典编码", "字典排序", "字典标签", "字典键值", "字典类型", "状态", "CSS类名", "表格回显样式", "是否默认", "创建者", "创建时间", "更新者", "更新时间", "备注"})

	// 添加数据行
	for _, dictData := range dictDatas {
		var statusStr string
		switch dictData.Status {
		case "0":
			statusStr = "正常"
		case "1":
			statusStr = "停用"
		default:
			statusStr = "未知"
		}

		var isDefaultStr string
		switch dictData.IsDefault {
		case "Y":
			isDefaultStr = "是"
		case "N":
			isDefaultStr = "否"
		default:
			isDefaultStr = "否"
		}

		createTime := ""
		if dictData.CreateTime != nil {
			createTime = dictData.CreateTime.Format("2006-01-02 15:04:05")
		}

		updateTime := ""
		if dictData.UpdateTime != nil {
			updateTime = dictData.UpdateTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(dictData.DictCode, 10),
			strconv.Itoa(dictData.DictSort),
			dictData.DictLabel,
			dictData.DictValue,
			dictData.DictType,
			statusStr,
			dictData.CssClass,
			dictData.ListClass,
			isDefaultStr,
			dictData.CreateBy,
			createTime,
			dictData.UpdateBy,
			updateTime,
			dictData.Remark,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=dict_data_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// RegisterRoutes 注册路由
func (c *SysDictDataController) RegisterRoutes(router *gin.RouterGroup) {
	dictDataRouter := router.Group("/system/dict/data")
	{
		dictDataRouter.GET("/list", c.List)
		dictDataRouter.GET("/:dictCode", c.GetInfo)
		dictDataRouter.GET("/type/:dictType", c.DictType)
		dictDataRouter.POST("", c.Add)
		dictDataRouter.PUT("", c.Edit)
		dictDataRouter.DELETE("/:dictCodes", c.Remove)
		dictDataRouter.GET("/export", c.Export)
	}
}
