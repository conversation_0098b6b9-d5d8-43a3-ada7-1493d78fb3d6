package system

import (
	"backend/internal/api/controller"
	"backend/internal/common/response"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"encoding/csv"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysRoleController 角色信息
type SysRoleController struct {
	controller.BaseController
	roleService       service.SysRoleService
	userService       service.SysUserService
	deptService       service.SysDeptService
	permissionService service.SysPermissionService
	tokenService      service.TokenService
}

// NewSysRoleController 创建角色控制器
func NewSysRoleController(
	roleService service.SysRoleService,
	userService service.SysUserService,
	deptService service.SysDeptService,
	permissionService service.SysPermissionService,
	tokenService service.TokenService,
) *SysRoleController {
	return &SysRoleController{
		roleService:       roleService,
		userService:       userService,
		deptService:       deptService,
		permissionService: permissionService,
		tokenService:      tokenService,
	}
}

// List 查询角色列表
func (c *SysRoleController) List(ctx *gin.Context) {
	role := &model.SysRole{}
	// 绑定查询参数 - 这里简化处理
	ctx.ShouldBind(role)

	// 设置分页
	pageInfo := c.StartPage(ctx)

	// 查询列表
	list, err := c.roleService.SelectRoleList(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 查询总记录数
	total, err := c.roleService.CountRoleList(role)
	if err != nil {
		c.ErrorJSON(ctx, "查询角色总数失败: "+err.Error())
		return
	}

	// 应用分页
	startIndex := (pageInfo.PageNum - 1) * pageInfo.PageSize
	endIndex := startIndex + pageInfo.PageSize

	// 防止越界
	if startIndex >= len(list) {
		// 返回空列表
		tableData := c.GetDataTable([]interface{}{}, total)
		c.SuccessJSON(ctx, tableData)
		return
	}

	if endIndex > len(list) {
		endIndex = len(list)
	}

	// 对结果进行分页
	pagedRoles := list[startIndex:endIndex]

	// 返回分页数据
	tableData := c.GetDataTable(pagedRoles, total)
	c.SuccessJSON(ctx, tableData)
}

// Export 导出角色数据
// @Summary 导出角色数据
// @Description 导出角色数据
// @Tags 角色管理
// @Accept json
// @Produce octet-stream
// @Param role query model.SysRole false "角色信息"
// @Success 200 {file} 文件流
// @Router /system/role/export [get]
func (c *SysRoleController) Export(ctx *gin.Context) {
	// 构建查询参数
	role := &model.SysRole{
		RoleName: ctx.Query("roleName"),
		RoleKey:  ctx.Query("roleKey"),
		Status:   ctx.Query("status"),
	}

	// 查询列表
	roles, err := c.roleService.SelectRoleList(role)
	if err != nil {
		response.Error(ctx, "导出角色失败: "+err.Error())
		return
	}

	// 导出数据
	data := make([][]string, 0, len(roles)+1)

	// 添加表头
	data = append(data, []string{"角色ID", "角色名称", "角色权限", "角色排序", "数据范围", "状态", "创建时间", "备注"})

	// 添加数据行
	for _, r := range roles {
		// 数据范围转换
		var dataScope string
		switch r.DataScope {
		case "1":
			dataScope = "所有数据权限"
		case "2":
			dataScope = "自定义数据权限"
		case "3":
			dataScope = "本部门数据权限"
		case "4":
			dataScope = "本部门及以下数据权限"
		case "5":
			dataScope = "仅本人数据权限"
		default:
			dataScope = "未知"
		}

		// 状态转换
		var statusStr string
		switch r.Status {
		case "0":
			statusStr = "正常"
		case "1":
			statusStr = "停用"
		default:
			statusStr = "未知"
		}

		// 创建时间
		createTime := ""
		if r.CreateTime != nil {
			createTime = r.CreateTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(r.RoleID, 10),
			r.RoleName,
			r.RoleKey,
			strconv.Itoa(r.RoleSort),
			dataScope,
			statusStr,
			createTime,
			r.Remark,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=role_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// GetInfo 根据角色编号获取详细信息
func (c *SysRoleController) GetInfo(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Param("roleId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色数据权限
	err = c.roleService.CheckRoleDataScope(roleId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取角色信息
	role, err := c.roleService.SelectRoleById(roleId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, role)
}

// Add 新增角色
func (c *SysRoleController) Add(ctx *gin.Context) {
	role := &model.SysRole{}
	if err := ctx.ShouldBindJSON(role); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色名称是否唯一
	if !c.roleService.CheckRoleNameUnique(role) {
		c.Error(ctx, utils.NewError("新增角色'"+role.RoleName+"'失败，角色名称已存在"))
		return
	}

	// 校验角色权限是否唯一
	if !c.roleService.CheckRoleKeyUnique(role) {
		c.Error(ctx, utils.NewError("新增角色'"+role.RoleName+"'失败，角色权限已存在"))
		return
	}

	// 设置创建者
	role.CreateBy = c.GetUsername(ctx)

	// 新增角色
	rows, err := c.roleService.InsertRole(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// Edit 修改保存角色
func (c *SysRoleController) Edit(ctx *gin.Context) {
	role := &model.SysRole{}
	if err := ctx.ShouldBindJSON(role); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色是否允许操作
	err := c.roleService.CheckRoleAllowed(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色数据权限
	err = c.roleService.CheckRoleDataScope(role.RoleID)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色名称是否唯一
	if !c.roleService.CheckRoleNameUnique(role) {
		c.Error(ctx, utils.NewError("修改角色'"+role.RoleName+"'失败，角色名称已存在"))
		return
	}

	// 校验角色权限是否唯一
	if !c.roleService.CheckRoleKeyUnique(role) {
		c.Error(ctx, utils.NewError("修改角色'"+role.RoleName+"'失败，角色权限已存在"))
		return
	}

	// 设置更新者
	role.UpdateBy = c.GetUsername(ctx)

	// 修改角色
	rows, err := c.roleService.UpdateRole(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if rows > 0 {
		// TODO: 更新缓存用户权限
		c.Success(ctx, nil)
		return
	}

	c.Error(ctx, utils.NewError("修改角色'"+role.RoleName+"'失败，请联系管理员"))
}

// DataScope 修改保存数据权限
// @Summary 修改保存数据权限
// @Description 修改保存数据权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param role body model.SysRole true "角色信息"
// @Success 200 {object} response.ResponseData
// @Router /system/role/dataScope [put]
func (c *SysRoleController) DataScope(ctx *gin.Context) {
	role := &model.SysRole{}
	if err := ctx.ShouldBindJSON(role); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色是否允许操作
	err := c.roleService.CheckRoleAllowed(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色数据权限
	err = c.roleService.CheckRoleDataScope(role.RoleID)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 修改数据权限
	rows, err := c.roleService.AuthDataScope(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// ChangeStatus 状态修改
func (c *SysRoleController) ChangeStatus(ctx *gin.Context) {
	role := &model.SysRole{}
	if err := ctx.ShouldBindJSON(role); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色是否允许操作
	err := c.roleService.CheckRoleAllowed(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色数据权限
	err = c.roleService.CheckRoleDataScope(role.RoleID)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置更新者
	role.UpdateBy = c.GetUsername(ctx)

	// 修改角色状态
	rows, err := c.roleService.UpdateRoleStatus(role)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// Remove 删除角色
func (c *SysRoleController) Remove(ctx *gin.Context) {
	roleIds := ctx.Param("roleIds")
	ids := strings.Split(roleIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		roleId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			c.Error(ctx, err)
			return
		}
		idList = append(idList, roleId)
	}

	// 删除角色
	rows, err := c.roleService.DeleteRoleByIds(idList)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// OptionSelect 获取角色选择框列表
func (c *SysRoleController) OptionSelect(ctx *gin.Context) {
	// 查询所有角色
	roles, err := c.roleService.SelectRoleAll()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, roles)
}

// AllocatedList 查询已分配用户角色列表
func (c *SysRoleController) AllocatedList(ctx *gin.Context) {
	user := &model.SysUser{}
	// 绑定查询参数
	ctx.ShouldBind(user)

	// TODO: 添加分页支持

	// 查询列表
	list, err := c.userService.SelectAllocatedList(user)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, list)
}

// UnallocatedList 查询未分配用户角色列表
func (c *SysRoleController) UnallocatedList(ctx *gin.Context) {
	user := &model.SysUser{}
	// 绑定查询参数
	ctx.ShouldBind(user)

	// TODO: 添加分页支持

	// 查询列表
	list, err := c.userService.SelectUnallocatedList(user)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, list)
}

// CancelAuthUser 取消授权用户
func (c *SysRoleController) CancelAuthUser(ctx *gin.Context) {
	userRole := &model.SysUserRole{}
	if err := ctx.ShouldBindJSON(userRole); err != nil {
		c.Error(ctx, err)
		return
	}

	// 取消授权
	rows, err := c.roleService.DeleteAuthUser(userRole)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// CancelAuthUserAll 批量取消授权用户
func (c *SysRoleController) CancelAuthUserAll(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Query("roleId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	userIds := ctx.Query("userIds")
	ids := strings.Split(userIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		userId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			c.Error(ctx, err)
			return
		}
		idList = append(idList, userId)
	}

	// 批量取消授权
	rows, err := c.roleService.DeleteAuthUsers(roleId, idList)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// SelectAuthUserAll 批量选择用户授权
func (c *SysRoleController) SelectAuthUserAll(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Query("roleId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验角色数据权限
	err = c.roleService.CheckRoleDataScope(roleId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	userIds := ctx.Query("userIds")
	ids := strings.Split(userIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		userId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			c.Error(ctx, err)
			return
		}
		idList = append(idList, userId)
	}

	// 批量选择用户授权
	rows, err := c.roleService.InsertAuthUsers(roleId, idList)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(rows), nil)
}

// DeptTree 获取对应角色部门树列表
func (c *SysRoleController) DeptTree(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Param("roleId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 查询角色关联的部门ID
	checkedKeys, err := c.deptService.SelectDeptListByRoleId(roleId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取部门树列表
	depts, err := c.deptService.SelectDeptTreeList(&model.SysDept{})
	if err != nil {
		c.Error(ctx, err)
		return
	}

	result := gin.H{
		"checkedKeys": checkedKeys,
		"depts":       depts,
	}

	c.Success(ctx, result)
}
