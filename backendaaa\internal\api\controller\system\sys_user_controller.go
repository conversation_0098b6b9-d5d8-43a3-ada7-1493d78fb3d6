package system

import (
	"backend/internal/api/common"
	"backend/internal/api/response"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"encoding/csv"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysUserController 用户信息控制器
type SysUserController struct {
	common.BaseController
	userService service.SysUserService
	roleService service.SysRoleService
	deptService service.SysDeptService
	postService service.SysPostService
}

// NewSysUserController 创建用户控制器
func NewSysUserController(
	userService service.SysUserService,
	roleService service.SysRoleService,
	deptService service.SysDeptService,
	postService service.SysPostService,
) *SysUserController {
	return &SysUserController{
		userService: userService,
		roleService: roleService,
		deptService: deptService,
		postService: postService,
	}
}

// List 获取用户列表
// @Router /system/user/list [get]
func (c *SysUserController) List(ctx *gin.Context) {
	// 绑定查询参数
	user := &model.SysUser{}
	if userName := ctx.Query("userName"); userName != "" {
		user.UserName = userName
	}
	if nickName := ctx.Query("nickName"); nickName != "" {
		user.NickName = nickName
	}
	if status := ctx.Query("status"); status != "" {
		user.Status = status
	}
	if deptIdStr := ctx.Query("deptId"); deptIdStr != "" {
		if deptId, err := strconv.ParseInt(deptIdStr, 10, 64); err == nil {
			user.DeptID = deptId
		}
	}
	if phonenumber := ctx.Query("phonenumber"); phonenumber != "" {
		user.PhoneNumber = phonenumber
	}

	// 设置分页
	pageInfo := c.StartPage(ctx)

	// 查询用户列表
	users, err := c.userService.SelectUserList(user)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户列表失败: "+err.Error())
		return
	}

	// 查询总记录数
	total, err := c.userService.CountUserList(user)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户总数失败: "+err.Error())
		return
	}

	// 应用分页
	startIndex := (pageInfo.PageNum - 1) * pageInfo.PageSize
	endIndex := startIndex + pageInfo.PageSize

	// 防止越界
	if startIndex >= len(users) {
		// 返回空列表
		tableData := c.GetDataTable([]interface{}{}, total)
		c.SuccessJSON(ctx, tableData)
		return
	}

	if endIndex > len(users) {
		endIndex = len(users)
	}

	// 对结果进行分页
	pagedUsers := users[startIndex:endIndex]

	// 返回分页数据
	tableData := c.GetDataTable(pagedUsers, total)
	c.SuccessJSON(ctx, tableData)
}

// GetInfo 根据用户编号获取详细信息
// @Router /system/user/{userId} [get]
func (c *SysUserController) GetInfo(ctx *gin.Context) {
	userId := ctx.Param("userId")
	response := common.SuccessResponse()

	if userId != "" && userId != "0" {
		// 检查数据权限
		id, _ := strconv.ParseInt(userId, 10, 64)
		c.userService.CheckUserDataScope(id)

		// 查询用户信息
		user, err := c.userService.SelectUserById(id)
		if err != nil {
			c.ErrorJSON(ctx, "查询用户信息失败: "+err.Error())
			return
		}

		// 查询用户岗位
		// TODO: 实现查询用户岗位
		// postIds := c.postService.SelectPostListByUserId(id)

		// 查询用户角色ID
		// TODO: 实现查询用户角色
		// roleIds := getRoleIds(user.Roles)

		response.Put(common.DataTag, user)
		// response.Put("postIds", postIds)
		// response.Put("roleIds", roleIds)
	}

	// 查询所有角色
	roles, err := c.roleService.SelectRoleAll()
	if err != nil {
		c.ErrorJSON(ctx, "查询角色列表失败: "+err.Error())
		return
	}

	// 管理员显示所有角色，非管理员过滤管理员角色
	id, _ := strconv.ParseInt(userId, 10, 64)
	if !model.IsAdminUser(id) {
		filteredRoles := make([]*model.SysRole, 0, len(roles))
		for _, role := range roles {
			if !role.IsAdmin() {
				filteredRoles = append(filteredRoles, role)
			}
		}
		response.Put("roles", filteredRoles)
	} else {
		response.Put("roles", roles)
	}

	// 查询所有岗位
	posts, err := c.postService.SelectPostAll()
	if err != nil {
		c.ErrorJSON(ctx, "查询岗位列表失败: "+err.Error())
		return
	}
	response.Put("posts", posts)

	c.JSON(ctx, common.Success, response)
}

// Add 新增用户
// @Router /system/user [post]
func (c *SysUserController) Add(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptID)

	// 校验角色数据权限
	c.roleService.CheckRoleDataScopeByIds(user.RoleIDs)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号码唯一性
	if user.PhoneNumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置创建者
	user.CreateBy = c.GetUsername(ctx)

	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 新增用户
	userId, err := c.userService.InsertUser(&user)
	if err != nil {
		c.ErrorJSON(ctx, "新增用户失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(int(userId)))
}

// Edit 修改用户
// @Router /system/user [put]
func (c *SysUserController) Edit(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 参数验证
	if user.UserID == 0 {
		c.ErrorJSON(ctx, "用户ID不能为空")
		return
	}

	if user.UserName == "" {
		c.ErrorJSON(ctx, "用户名不能为空")
		return
	}

	// 校验用户是否允许操作
	if err := c.userService.CheckUserAllowed(&user); err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 校验用户数据权限
	if err := c.userService.CheckUserDataScope(user.UserID); err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 校验部门数据权限
	if user.DeptID != 0 {
		if err := c.deptService.CheckDeptDataScope(user.DeptID); err != nil {
			c.ErrorJSON(ctx, err.Error())
			return
		}
	}

	// 校验角色数据权限
	if len(user.RoleIDs) > 0 {
		if err := c.roleService.CheckRoleDataScopeByIds(user.RoleIDs); err != nil {
			c.ErrorJSON(ctx, err.Error())
			return
		}
	}

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号码唯一性
	if user.PhoneNumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户
	result := c.userService.UpdateUser(&user)

	if result <= 0 {
		c.ErrorJSON(ctx, "修改用户失败")
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// Remove 删除用户
// @Router /system/user/{userIds} [delete]
func (c *SysUserController) Remove(ctx *gin.Context) {
	userIdsStr := ctx.Param("userIds")
	userIdsArr := strings.Split(userIdsStr, ",")
	userIds := make([]int64, 0, len(userIdsArr))

	// 转换用户ID
	for _, idStr := range userIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		userIds = append(userIds, id)
	}

	// 检查当前用户是否在删除的用户中
	currentUserId := c.GetUserId(ctx)
	for _, id := range userIds {
		if id == currentUserId {
			c.ErrorJSON(ctx, "当前用户不能删除")
			return
		}
	}

	// 删除用户
	result := c.userService.DeleteUserByIds(userIds)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// ResetPwd 重置密码
// @Router /system/user/resetPwd [put]
func (c *SysUserController) ResetPwd(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 参数验证
	if user.UserID == 0 {
		c.ErrorJSON(ctx, "用户ID不能为空")
		return
	}

	if user.Password == "" {
		c.ErrorJSON(ctx, "密码不能为空")
		return
	}

	// 校验用户是否允许操作
	if err := c.userService.CheckUserAllowed(&user); err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 校验用户数据权限
	if err := c.userService.CheckUserDataScope(user.UserID); err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 重置密码
	result := c.userService.ResetPwd(&user)

	if result <= 0 {
		c.ErrorJSON(ctx, "重置密码失败")
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// ChangeStatus 状态修改
// @Router /system/user/changeStatus [put]
func (c *SysUserController) ChangeStatus(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验用户是否允许操作
	c.userService.CheckUserAllowed(&user)

	// 校验用户数据权限
	c.userService.CheckUserDataScope(user.UserID)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户状态
	result := c.userService.UpdateUserStatus(&user)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// AuthRole 根据用户编号获取授权角色
// @Router /system/user/authRole/{userId} [get]
func (c *SysUserController) AuthRole(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	userId, _ := strconv.ParseInt(userIdStr, 10, 64)

	response := common.SuccessResponse()

	// 查询用户信息
	user, err := c.userService.SelectUserById(userId)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户信息失败: "+err.Error())
		return
	}

	// 查询用户角色
	roles, err := c.roleService.SelectRolesByUserId(userId)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户角色失败: "+err.Error())
		return
	}

	response.Put("user", user)

	// 管理员显示所有角色，非管理员过滤管理员角色
	if model.IsAdminUser(userId) {
		response.Put("roles", roles)
	} else {
		filteredRoles := make([]*model.SysRole, 0, len(roles))
		for _, role := range roles {
			if !role.IsAdmin() {
				filteredRoles = append(filteredRoles, role)
			}
		}
		response.Put("roles", filteredRoles)
	}

	c.JSON(ctx, common.Success, response)
}

// InsertAuthRole 用户授权角色
// @Router /system/user/authRole [put]
func (c *SysUserController) InsertAuthRole(ctx *gin.Context) {
	userIdStr := ctx.Query("userId")
	roleIdsStr := ctx.Query("roleIds")

	userId, _ := strconv.ParseInt(userIdStr, 10, 64)
	roleIdsArr := strings.Split(roleIdsStr, ",")
	roleIds := make([]int64, 0, len(roleIdsArr))

	// 转换角色ID
	for _, idStr := range roleIdsArr {
		if idStr == "" {
			continue
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		roleIds = append(roleIds, id)
	}

	// 校验用户数据权限
	c.userService.CheckUserDataScope(userId)

	// 校验角色数据权限
	c.roleService.CheckRoleDataScopeByIds(roleIds)

	// 保存用户角色
	err := c.userService.InsertUserAuth(userId, roleIds)
	if err != nil {
		c.ErrorJSON(ctx, "保存用户角色失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.Success())
}

// Export 导出用户
// @Summary 导出用户
// @Description 导出用户
// @Tags 用户管理
// @Accept json
// @Produce octet-stream
// @Param user query model.SysUser false "用户信息"
// @Success 200 {object} response.ResponseData
// @Router /system/user/export [get]
func (c *SysUserController) Export(ctx *gin.Context) {
	// 解析查询参数
	var user model.SysUser
	if err := ctx.ShouldBindQuery(&user); err != nil {
		// 忽略绑定错误，使用默认值
	}

	// 查询用户列表
	users, err := c.userService.SelectUserList(&user)
	if err != nil {
		response.Fail(ctx, "导出用户失败: "+err.Error())
		return
	}

	// 根据Accept头选择导出格式
	accept := ctx.GetHeader("Accept")
	if strings.Contains(accept, "text/csv") {
		c.exportToCSV(ctx, users)
	} else {
		// 默认使用CSV格式
		c.exportToCSV(ctx, users)
	}
}

// exportToCSV 导出为CSV格式（作为Excel导出失败的备选方案）
func (c *SysUserController) exportToCSV(ctx *gin.Context, users []*model.SysUser) {
	// 导出数据
	data := make([][]string, 0, len(users)+1)

	// 添加表头
	data = append(data, []string{"用户ID", "部门", "登录名称", "用户名称", "用户邮箱", "手机号码", "用户性别", "帐号状态", "最后登录IP", "最后登录时间", "创建者", "创建时间", "备注"})

	// 添加数据行
	for _, u := range users {
		var dept string
		if u.Dept != nil {
			dept = u.Dept.DeptName
		}

		var sexStr string
		switch u.Sex {
		case "0":
			sexStr = "男"
		case "1":
			sexStr = "女"
		default:
			sexStr = "未知"
		}

		var statusStr string
		switch u.Status {
		case "0":
			statusStr = "正常"
		case "1":
			statusStr = "停用"
		default:
			statusStr = "未知"
		}

		lastLoginTime := ""
		if u.LoginDate != nil {
			lastLoginTime = u.LoginDate.Format("2006-01-02 15:04:05")
		}

		createTime := ""
		if u.CreateTime != nil {
			createTime = u.CreateTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(u.UserID, 10),
			dept,
			u.UserName,
			u.NickName,
			u.Email,
			u.PhoneNumber,
			sexStr,
			statusStr,
			u.LoginIP,
			lastLoginTime,
			u.CreateBy,
			createTime,
			u.Remark,
		}
		data = append(data, row)
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=user_export.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// ImportTemplate 下载用户导入模板
// @Summary 下载用户导入模板
// @Description 下载用户导入模板
// @Tags 用户管理
// @Accept json
// @Produce octet-stream
// @Success 200 {file} 文件流
// @Router /system/user/importTemplate [get]
func (c *SysUserController) ImportTemplate(ctx *gin.Context) {
	// 设置Excel工具
	headers := []string{"登录名称", "用户名称", "用户邮箱", "手机号码", "用户性别", "帐号状态", "部门编号", "岗位编号", "角色编号", "备注"}
	fields := []string{}

	// 创建Excel工具
	excelUtil := utils.NewExcelUtil("用户数据", "用户数据导入模板", headers, fields)

	// 导出Excel模板
	filename := "user_template.xlsx"
	err := excelUtil.DownloadTemplate(ctx, filename)
	if err != nil {
		// 如果Excel导出失败，回退到CSV导出
		c.downloadCSVTemplate(ctx)
	}
}

// downloadCSVTemplate 下载CSV模板
func (c *SysUserController) downloadCSVTemplate(ctx *gin.Context) {
	// 创建导入模板
	data := [][]string{
		{"登录名称", "用户名称", "用户邮箱", "手机号码", "用户性别", "帐号状态", "部门编号", "岗位编号", "角色编号", "备注"},
		{"admin", "管理员", "<EMAIL>", "13800138000", "0", "0", "100", "1", "1", "备注信息"},
	}

	// 创建CSV文件
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)
	writer.WriteAll(data)
	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=user_template.csv")
	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// ImportData 导入用户数据
// @Summary 导入用户数据
// @Description 导入用户数据
// @Tags 用户管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "导入用户数据文件"
// @Param updateSupport formData string false "是否更新已存在的用户数据"
// @Success 200 {object} response.ResponseData
// @Router /system/user/importData [post]
func (c *SysUserController) ImportData(ctx *gin.Context) {
	// 获取文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.Fail(ctx, "获取文件失败: "+err.Error())
		return
	}

	// 获取是否更新支持
	updateSupport := ctx.DefaultPostForm("updateSupport", "0") == "1"

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.Fail(ctx, "打开文件失败: "+err.Error())
		return
	}
	defer src.Close()

	// 判断文件类型
	var records [][]string
	ext := strings.ToLower(filepath.Ext(file.Filename))

	if ext == ".xlsx" || ext == ".xls" {
		// 读取Excel文件
		records, err = utils.ImportFromExcel(src, 1) // 跳过标题行
		if err != nil {
			response.Fail(ctx, "读取Excel文件失败: "+err.Error())
			return
		}
	} else {
		// 读取CSV文件
		reader := csv.NewReader(src)
		records, err = reader.ReadAll()
		if err != nil {
			response.Fail(ctx, "读取CSV文件失败: "+err.Error())
			return
		}

		// 跳过标题行
		if len(records) > 0 {
			records = records[1:]
		}
	}

	if len(records) == 0 {
		response.Fail(ctx, "导入用户数据不能为空")
		return
	}

	// 获取操作用户
	operName := c.GetUsername(ctx)

	// 数据校验与导入
	successNum := 0
	failureNum := 0
	failureMsg := ""

	// 跳过标题行
	for i := 1; i < len(records); i++ {
		row := records[i]
		if len(row) < 7 {
			failureNum++
			failureMsg += fmt.Sprintf("<br/>第%d行数据格式不正确", i+1)
			continue
		}

		// 提取数据
		userName := row[0]
		nickName := row[1]
		email := row[2]
		phoneNumber := row[3]
		sex := row[4]
		status := row[5]
		deptID, _ := strconv.ParseInt(row[6], 10, 64)

		// 校验必填项
		if userName == "" || nickName == "" {
			failureNum++
			failureMsg += fmt.Sprintf("<br/>第%d行用户名或昵称为空", i+1)
			continue
		}

		// 校验用户名是否唯一
		user := &model.SysUser{UserName: userName}
		exist := !c.userService.CheckUserNameUnique(user)
		if exist && !updateSupport {
			failureNum++
			failureMsg += fmt.Sprintf("<br/>第%d行用户名已存在", i+1)
			continue
		}

		// 构建用户对象
		user = &model.SysUser{
			UserName:    userName,
			NickName:    nickName,
			Email:       email,
			PhoneNumber: phoneNumber,
			Sex:         sex,
			Status:      status,
			DeptID:      deptID,
		}
		// 设置创建者
		user.CreateBy = operName

		// 设置默认密码
		password, err := c.GetDefaultPassword()
		if err != nil {
			password = "123456"
		}
		user.Password = utils.EncryptPassword(password)

		// 保存用户信息
		if exist && updateSupport {
			// 查询旧用户
			oldUser, err := c.userService.SelectUserByUserName(userName)
			if err != nil {
				failureNum++
				failureMsg += fmt.Sprintf("<br/>第%d行更新用户失败: %s", i+1, err.Error())
				continue
			}

			// 保留旧用户的密码和ID
			user.UserID = oldUser.UserID
			user.Password = oldUser.Password
			user.UpdateBy = operName

			// 更新用户
			result := c.userService.UpdateUser(user)
			if result <= 0 {
				failureNum++
				failureMsg += fmt.Sprintf("<br/>第%d行更新用户失败", i+1)
				continue
			}

			// 保存岗位
			if len(row) > 8 && row[7] != "" {
				postIds := strings.Split(row[7], ",")
				user.PostIDs = make([]int64, 0, len(postIds))
				for _, idStr := range postIds {
					id, _ := strconv.ParseInt(idStr, 10, 64)
					user.PostIDs = append(user.PostIDs, id)
				}
				// 更新用户岗位关联
				result = c.userService.UpdateUser(user)
				if result <= 0 {
					failureNum++
					failureMsg += fmt.Sprintf("<br/>第%d行更新用户岗位失败", i+1)
				}
			}

			// 保存角色
			if len(row) > 9 && row[8] != "" {
				roleIds := strings.Split(row[8], ",")
				user.RoleIDs = make([]int64, 0, len(roleIds))
				for _, idStr := range roleIds {
					id, _ := strconv.ParseInt(idStr, 10, 64)
					user.RoleIDs = append(user.RoleIDs, id)
				}
				// 更新用户角色关联
				err = c.userService.InsertUserAuth(user.UserID, user.RoleIDs)
				if err != nil {
					failureNum++
					failureMsg += fmt.Sprintf("<br/>第%d行更新用户角色失败: %s", i+1, err.Error())
				}
			}

			successNum++
		} else {
			// 新增用户
			userId, err := c.userService.InsertUser(user)
			if err != nil {
				failureNum++
				failureMsg += fmt.Sprintf("<br/>第%d行新增用户失败: %s", i+1, err.Error())
				continue
			}
			user.UserID = userId
			successNum++
		}
	}

	// 构建返回信息
	if successNum > 0 {
		successMsg := fmt.Sprintf("成功导入 %d 条用户数据", successNum)
		if failureNum > 0 {
			successMsg += fmt.Sprintf("<br/>失败 %d 条%s", failureNum, failureMsg)
		}
		response.Success(ctx, successMsg)
	} else {
		response.Fail(ctx, fmt.Sprintf("导入失败：%s", failureMsg))
	}
}

// GetDefaultPassword 获取默认密码
func (c *SysUserController) GetDefaultPassword() (string, error) {
	// 这里可以从配置文件或者数据库获取默认密码
	// 暂时返回固定值
	return "123456", nil
}
