package tool

import (
	"backend/internal/common/response"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// BuildController 表单构建控制器
type BuildController struct{}

// NewBuildController 创建表单构建控制器
func NewBuildController() *BuildController {
	return &BuildController{}
}

// Index 表单构建首页
// @Summary 表单构建首页
// @Description 表单构建首页
// @Tags 表单构建
// @Accept json
// @Produce html
// @Success 200 {string} string "表单构建页面"
// @Router /tool/build/index [get]
func (c *BuildController) Index(ctx *gin.Context) {
	// 返回表单构建页面
	filePath := filepath.Join("public", "pages", "tool", "build", "index.html")
	ctx.File(filePath)
}

// Preview 表单构建预览
// @Summary 表单构建预览
// @Description 表单构建预览
// @Tags 表单构建
// @Accept json
// @Produce html
// @Success 200 {string} string "表单预览页面"
// @Router /tool/build/preview [get]
func (c *BuildController) Preview(ctx *gin.Context) {
	// 返回表单预览页面
	filePath := filepath.Join("public", "pages", "tool", "build", "preview.html")
	ctx.File(filePath)
}

// GenerateForm 生成表单代码
// @Summary 生成表单代码
// @Description 根据表单配置生成表单代码
// @Tags 表单构建
// @Accept json
// @Produce json
// @Param formConfig body string true "表单配置"
// @Success 200 {object} response.ResponseData
// @Router /tool/build/generateForm [post]
func (c *BuildController) GenerateForm(ctx *gin.Context) {
	// 获取表单配置
	var formConfig map[string]interface{}
	if err := ctx.ShouldBindJSON(&formConfig); err != nil {
		response.Error(ctx, "无效的表单配置: "+err.Error())
		return
	}

	// 生成表单代码（此处简化实现，实际中需要根据配置生成HTML代码）
	formHTML := c.generateHTMLFromConfig(formConfig)

	// 返回生成的表单代码
	response.Success(ctx, map[string]interface{}{
		"formHTML": formHTML,
	})
}

// generateHTMLFromConfig 根据配置生成HTML代码
func (c *BuildController) generateHTMLFromConfig(config map[string]interface{}) string {
	// 这里简化实现，实际中需要根据配置生成复杂的HTML代码
	// 在真实场景中，这里应该解析config中的各个字段，生成对应的表单元素

	// 返回一个基本的表单结构
	html := `
<div class="form-container">
  <form class="el-form">
    <!-- 此处应根据配置动态生成表单元素 -->
    <div class="el-form-item">
      <label class="el-form-item__label">示例表单</label>
      <div class="el-form-item__content">
        <input type="text" class="el-input__inner" placeholder="请输入">
      </div>
    </div>
    <div class="el-form-item">
      <div class="el-form-item__content">
        <button type="button" class="el-button el-button--primary">
          <span>提交</span>
        </button>
        <button type="button" class="el-button">
          <span>重置</span>
        </button>
      </div>
    </div>
  </form>
</div>
`
	return html
}

// GetList 获取表单构建列表
func (c *BuildController) GetList(ctx *gin.Context) {
	// 模拟返回数据
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": []gin.H{
			{
				"id":          1,
				"formName":    "用户表单",
				"formCode":    "user-form",
				"formContent": "{\"fields\":[{\"type\":\"input\",\"field\":\"username\",\"title\":\"用户名\",\"info\":\"\",\"_fc_drag_tag\":\"input\",\"hidden\":false,\"display\":true,\"validate\":[{\"trigger\":\"change\",\"mode\":\"required\",\"message\":\"请输入用户名\",\"required\":true,\"type\":\"string\"}]}]}",
			},
			{
				"id":          2,
				"formName":    "订单表单",
				"formCode":    "order-form",
				"formContent": "{\"fields\":[{\"type\":\"input\",\"field\":\"orderNo\",\"title\":\"订单号\",\"info\":\"\",\"_fc_drag_tag\":\"input\",\"hidden\":false,\"display\":true,\"validate\":[{\"trigger\":\"change\",\"mode\":\"required\",\"message\":\"请输入订单号\",\"required\":true,\"type\":\"string\"}]}]}",
			},
		},
	})
}

// GetInfo 获取表单构建信息
func (c *BuildController) GetInfo(ctx *gin.Context) {
	id := ctx.Param("id")
	idInt, err := strconv.Atoi(id)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code": -1,
			"msg":  "参数错误",
		})
		return
	}

	// 模拟返回数据
	if idInt == 1 {
		ctx.JSON(http.StatusOK, gin.H{
			"code": 0,
			"msg":  "success",
			"data": gin.H{
				"id":          1,
				"formName":    "用户表单",
				"formCode":    "user-form",
				"formContent": "{\"fields\":[{\"type\":\"input\",\"field\":\"username\",\"title\":\"用户名\",\"info\":\"\",\"_fc_drag_tag\":\"input\",\"hidden\":false,\"display\":true,\"validate\":[{\"trigger\":\"change\",\"mode\":\"required\",\"message\":\"请输入用户名\",\"required\":true,\"type\":\"string\"}]}]}",
			},
		})
	} else {
		ctx.JSON(http.StatusOK, gin.H{
			"code": 0,
			"msg":  "success",
			"data": gin.H{
				"id":          2,
				"formName":    "订单表单",
				"formCode":    "order-form",
				"formContent": "{\"fields\":[{\"type\":\"input\",\"field\":\"orderNo\",\"title\":\"订单号\",\"info\":\"\",\"_fc_drag_tag\":\"input\",\"hidden\":false,\"display\":true,\"validate\":[{\"trigger\":\"change\",\"mode\":\"required\",\"message\":\"请输入订单号\",\"required\":true,\"type\":\"string\"}]}]}",
			},
		})
	}
}

// Create 创建表单构建
func (c *BuildController) Create(ctx *gin.Context) {
	var form struct {
		FormName    string `json:"formName"`
		FormCode    string `json:"formCode"`
		FormContent string `json:"formContent"`
	}
	if err := ctx.ShouldBindJSON(&form); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code": -1,
			"msg":  "参数错误",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "创建成功",
		"data": gin.H{
			"id":          3,
			"formName":    form.FormName,
			"formCode":    form.FormCode,
			"formContent": form.FormContent,
		},
	})
}

// Update 更新表单构建
func (c *BuildController) Update(ctx *gin.Context) {
	var form struct {
		ID          int    `json:"id"`
		FormName    string `json:"formName"`
		FormCode    string `json:"formCode"`
		FormContent string `json:"formContent"`
	}
	if err := ctx.ShouldBindJSON(&form); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code": -1,
			"msg":  "参数错误",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "更新成功",
	})
}

// Delete 删除表单构建
func (c *BuildController) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	idList := strings.Split(ids, ",")

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "删除成功",
		"data": idList,
	})
}
