package tool

import (
	"backend/internal/api/response"
	"backend/internal/service"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

// GenUIController 代码生成器UI控制器
type GenUIController struct {
	genTableService service.GenTableService
}

// NewGenUIController 创建代码生成器UI控制器
func NewGenUIController(genTableService service.GenTableService) *GenUIController {
	return &GenUIController{
		genTableService: genTableService,
	}
}

// Index 代码生成器首页
// @Summary 代码生成器首页
// @Description 代码生成器首页
// @Tags 代码生成器
// @Accept json
// @Produce html
// @Success 200 {string} string "代码生成器页面"
// @Router /tool/gen/index [get]
func (c *GenUIController) Index(ctx *gin.Context) {
	// 返回代码生成器页面
	filePath := filepath.Join("public", "pages", "tool", "gen", "index.html")
	ctx.File(filePath)
}

// ImportTable 导入表结构页面
// @Summary 导入表结构页面
// @Description 导入表结构页面
// @Tags 代码生成器
// @Accept json
// @Produce html
// @Success 200 {string} string "导入表结构页面"
// @Router /tool/gen/importTable [get]
func (c *GenUIController) ImportTable(ctx *gin.Context) {
	// 返回导入表结构页面
	filePath := filepath.Join("public", "pages", "tool", "gen", "importTable.html")
	ctx.File(filePath)
}

// EditTable 修改表配置页面
// @Summary 修改表配置页面
// @Description 修改表配置页面
// @Tags 代码生成器
// @Accept json
// @Produce html
// @Param tableId path int true "表ID"
// @Success 200 {string} string "修改表配置页面"
// @Router /tool/gen/edit/{tableId} [get]
func (c *GenUIController) EditTable(ctx *gin.Context) {
	// 返回修改表配置页面
	filePath := filepath.Join("public", "pages", "tool", "gen", "editTable.html")
	ctx.File(filePath)

	// 这里简化实现，实际应该将tableId传递给前端
	// 例如可以通过重定向附加查询参数的方式：ctx.Redirect(302, "/pages/tool/gen/editTable.html?tableId="+ctx.Param("tableId"))
}

// PreviewCode 预览代码页面
// @Summary 预览代码页面
// @Description 预览代码页面
// @Tags 代码生成器
// @Accept json
// @Produce html
// @Param tableId path int true "表ID"
// @Success 200 {string} string "预览代码页面"
// @Router /tool/gen/preview/{tableId} [get]
func (c *GenUIController) PreviewCode(ctx *gin.Context) {
	// 返回预览代码页面
	filePath := filepath.Join("public", "pages", "tool", "gen", "preview.html")
	ctx.File(filePath)

	// 这里简化实现，实际应该将tableId和生成的代码传递给前端
	// 例如：ctx.Redirect(302, "/pages/tool/gen/preview.html?tableId="+ctx.Param("tableId"))
}

// GetDbTableList 获取数据库表列表
// @Summary 获取数据库表列表
// @Description 获取当前数据库中所有的表信息
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/db/list [get]
func (c *GenUIController) GetDbTableList(ctx *gin.Context) {
	// 调用服务获取数据库表列表
	tables, err := c.genTableService.SelectDbTableList(nil)
	if err != nil {
		response.Fail(ctx, "获取数据库表列表失败: "+err.Error())
		return
	}

	response.Success(ctx, tables)
}

// GetGenTableList 获取已导入的表列表
// @Summary 获取已导入的表列表
// @Description 获取已导入到代码生成器的表列表
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/list [get]
func (c *GenUIController) GetGenTableList(ctx *gin.Context) {
	// 调用服务获取已导入的表列表
	tables, err := c.genTableService.SelectGenTableList(nil)
	if err != nil {
		response.Fail(ctx, "获取代码生成表列表失败: "+err.Error())
		return
	}

	response.Success(ctx, tables)
}

// GetGenTableInfo 获取表信息
// @Summary 获取表信息
// @Description 获取表信息
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Param tableId path int true "表ID"
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/{tableId} [get]
func (c *GenUIController) GetGenTableInfo(ctx *gin.Context) {
	// 获取表ID
	tableId := ctx.Param("tableId")

	// 调用服务获取表信息
	response.Success(ctx, gin.H{"tableId": tableId})
}

// GetTableColumns 获取表字段
// @Summary 获取表字段
// @Description 获取表字段
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Param tableId path int true "表ID"
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/column/{tableId} [get]
func (c *GenUIController) GetTableColumns(ctx *gin.Context) {
	// 获取表ID
	tableId := ctx.Param("tableId")

	// 调用服务获取表字段
	response.Success(ctx, gin.H{"tableId": tableId})
}

// SynchDb 同步数据库
// @Summary 同步数据库
// @Description 同步数据库
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Param tableName path string true "表名"
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/synchDb/{tableName} [get]
func (c *GenUIController) SynchDb(ctx *gin.Context) {
	// 获取表名
	tableName := ctx.Param("tableName")

	// 调用服务同步数据库
	response.Success(ctx, gin.H{"tableName": tableName})
}

// BatchGenCode 批量生成代码
// @Summary 批量生成代码
// @Description 批量生成代码
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/batchGenCode [get]
func (c *GenUIController) BatchGenCode(ctx *gin.Context) {
	// 调用服务批量生成代码
	response.Success(ctx, "批量生成代码成功")
}

// GenCode 生成代码
// @Summary 生成代码
// @Description 生成代码
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Param tableName path string true "表名"
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/genCode/{tableName} [get]
func (c *GenUIController) GenCode(ctx *gin.Context) {
	// 获取表名
	tableName := ctx.Param("tableName")

	// 调用服务生成代码
	response.Success(ctx, gin.H{"tableName": tableName})
}

// UpdateGenTable 更新表信息
// @Summary 更新表信息
// @Description 更新表信息
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /tool/gen [post]
func (c *GenUIController) UpdateGenTable(ctx *gin.Context) {
	// 调用服务更新表信息
	response.Success(ctx, "更新表信息成功")
}

// DeleteGenTable 删除表信息
// @Summary 删除表信息
// @Description 删除表信息
// @Tags 代码生成器
// @Accept json
// @Produce json
// @Param tableIds path string true "表ID列表"
// @Success 200 {object} response.ResponseData
// @Router /tool/gen/{tableIds} [delete]
func (c *GenUIController) DeleteGenTable(ctx *gin.Context) {
	// 获取表ID列表
	tableIds := ctx.Param("tableIds")

	// 调用服务删除表信息
	response.Success(ctx, gin.H{"tableIds": tableIds})
}
