package middleware

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"errors"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Auth 认证中间件
func Auth(tokenService service.TokenService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求头中的token
		token := c.<PERSON>eader(constants.AUTHENTICATION)
		if token == "" {
			// 尝试从URL参数中获取
			token = c.Query(constants.TOKEN_PARAM)
		}

		if token == "" || !strings.HasPrefix(token, constants.TOKEN_PREFIX) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，无效的令牌",
			})
			c.Abort()
			return
		}

		// 去除Bearer前缀
		token = strings.TrimPrefix(token, constants.TOKEN_PREFIX)

		// 验证token
		claims, err := utils.ParseToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，令牌已过期或验证不正确",
			})
			c.Abort()
			return
		}

		// 获取用户信息
		loginUser, err := tokenService.GetLoginUser(token)
		if err != nil || loginUser == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，找不到用户信息",
			})
			c.Abort()
			return
		}

		// 验证token是否过期
		if tokenService.IsTokenExpired(loginUser) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，令牌已过期",
			})
			c.Abort()
			return
		}

		// 刷新token过期时间
		tokenService.VerifyToken(loginUser)

		// 将用户信息保存到上下文
		c.Set(constants.LOGIN_USER_KEY, loginUser)
		c.Set(constants.USER_ID_KEY, claims.UserId)
		c.Set(constants.USERNAME_KEY, claims.Username)

		c.Next()
	}
}

// Permission 权限验证中间件
func Permission(permission string, permissionService service.SysPermissionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userObj, exists := c.Get(constants.LOGIN_USER_KEY)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，无法获取用户信息",
			})
			c.Abort()
			return
		}

		loginUser, ok := userObj.(*model.LoginUser)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，用户信息类型不正确",
			})
			c.Abort()
			return
		}

		// 超级管理员拥有所有权限
		if model.IsAdminUser(loginUser.User.UserID) {
			c.Next()
			return
		}

		// 验证用户是否有权限
		if !permissionService.HasPermission(loginUser.Permissions, permission) {
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "没有权限，请联系管理员",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(tokenService service.TokenService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取token
		token := c.GetHeader("Authorization")
		if token == "" {
			// 尝试从查询参数获取
			token = c.Query("token")
		}

		// 如果还是没有token，则返回未授权错误
		if token == "" {
			respondWithError(c, 401, "请求未授权")
			c.Abort()
			return
		}

		// 如果token格式为Bearer xxx，则提取出xxx部分
		const prefix = "Bearer "
		if strings.HasPrefix(token, prefix) {
			token = token[len(prefix):]
		}

		log.Printf("收到认证请求，Token: %s", token)

		// 验证token并获取登录用户
		loginUser, err := tokenService.GetLoginUser(token)
		if err != nil {
			log.Printf("Token验证失败: %v", err)
			respondWithError(c, 401, "无效的令牌")
			c.Abort()
			return
		}

		// 验证令牌有效期
		if err := tokenService.VerifyToken(loginUser); err != nil {
			log.Printf("Token已过期: %v", err)
			respondWithError(c, 401, "令牌已过期")
			c.Abort()
			return
		}

		// 将登录用户信息存储到上下文中
		c.Set(constants.LOGIN_USER_KEY, loginUser)
		c.Next()
	}
}

// RequirePermissions 权限验证中间件
func RequirePermissions(permissionService service.SysPermissionService, permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取登录用户
		userObj, exists := c.Get(constants.LOGIN_USER_KEY)
		if !exists {
			respondWithError(c, 401, "请求未授权")
			c.Abort()
			return
		}

		// 类型断言
		loginUser, ok := userObj.(*model.LoginUser)
		if !ok {
			respondWithError(c, 500, "用户信息类型错误")
			c.Abort()
			return
		}

		// 如果是管理员，直接放行
		if loginUser.IsAdmin() {
			c.Next()
			return
		}

		// 校验用户是否拥有指定权限
		for _, permission := range permissions {
			hasPermission := false
			for _, userPerm := range loginUser.Permissions {
				if userPerm == permission {
					hasPermission = true
					break
				}
			}
			if !hasPermission {
				respondWithError(c, 403, "权限不足")
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// GetLoginUser 从上下文中获取登录用户
func GetLoginUser(c *gin.Context) (*model.LoginUser, error) {
	loginUserObj, exists := c.Get(constants.LOGIN_USER_KEY)
	if !exists {
		return nil, errors.New("获取登录用户信息失败")
	}

	loginUser, ok := loginUserObj.(*model.LoginUser)
	if !ok {
		return nil, errors.New("用户信息类型错误")
	}

	return loginUser, nil
}

// GetUserId 从上下文中获取用户ID
func GetUserId(c *gin.Context) int64 {
	loginUser, err := GetLoginUser(c)
	if err != nil {
		return 0
	}
	return loginUser.UserID
}

// GetUserName 从上下文中获取用户名
func GetUserName(c *gin.Context) string {
	loginUser, err := GetLoginUser(c)
	if err != nil || loginUser.User == nil {
		return ""
	}
	return loginUser.User.UserName
}

// GetUser 从上下文中获取用户信息
func GetUser(c *gin.Context) *model.SysUser {
	loginUser, err := GetLoginUser(c)
	if err != nil {
		return nil
	}
	return loginUser.User
}

// 响应错误信息
func respondWithError(c *gin.Context, code int, message string) {
	c.JSON(code, gin.H{
		"code": code,
		"msg":  message,
	})
}
