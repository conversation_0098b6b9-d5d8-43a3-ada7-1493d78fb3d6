package middleware

import (
	"backend/internal/common/response"
	"crypto/rand"
	"encoding/base64"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

var (
	// tokenStore 令牌存储
	tokenStore = make(map[string]time.Time)
	// tokenMutex 令牌互斥锁
	tokenMutex sync.RWMutex
	// tokenExpire 令牌过期时间
	tokenExpire = 30 * time.Minute
)

// CSRFProtection CSRF防护中间件
func CSRFProtection() gin.HandlerFunc {
	// 初始化
	go cleanExpiredTokens()

	return func(c *gin.Context) {
		// 跳过不需要CSRF保护的方法
		if c.Request.Method == http.MethodGet || c.Request.Method == http.MethodHead || c.Request.Method == http.MethodOptions {
			// 设置CSRF令牌
			setCSRFToken(c)
			c.Next()
			return
		}

		// 豁免路径列表 - 这些路径不需要CSRF保护
		exemptPaths := []string{
			"/login",        // 登录接口
			"/captchaImage", // 验证码接口
			"/logout",       // 登出接口
			"/register",     // 注册接口
		}

		// 检查当前路径是否在豁免列表中
		currentPath := c.Request.URL.Path
		for _, path := range exemptPaths {
			if strings.HasSuffix(currentPath, path) {
				c.Next()
				return
			}
		}

		// 验证CSRF令牌
		clientToken := c.GetHeader("X-CSRF-Token")
		if clientToken == "" {
			// 从请求参数获取
			clientToken = c.Query("_csrf")
			if clientToken == "" {
				// 从表单获取
				clientToken = c.PostForm("_csrf")
			}
		}

		// 验证令牌
		if !validateCSRFToken(clientToken) {
			response.Forbidden(c, "CSRF令牌无效或已过期")
			c.Abort()
			return
		}

		c.Next()
	}
}

// generateCSRFToken 生成CSRF令牌
func generateCSRFToken() string {
	// 生成随机字节
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return ""
	}

	// 编码为Base64
	token := base64.URLEncoding.EncodeToString(b)

	// 存储令牌
	tokenMutex.Lock()
	tokenStore[token] = time.Now().Add(tokenExpire)
	tokenMutex.Unlock()

	return token
}

// validateCSRFToken 验证CSRF令牌
func validateCSRFToken(token string) bool {
	if token == "" {
		return false
	}

	// 查询令牌
	tokenMutex.RLock()
	expireTime, exists := tokenStore[token]
	tokenMutex.RUnlock()

	// 验证令牌
	if !exists {
		return false
	}

	// 验证过期时间
	if time.Now().After(expireTime) {
		// 删除过期令牌
		tokenMutex.Lock()
		delete(tokenStore, token)
		tokenMutex.Unlock()
		return false
	}

	return true
}

// setCSRFToken 设置CSRF令牌
func setCSRFToken(c *gin.Context) {
	// 生成令牌
	token := generateCSRFToken()

	// 设置令牌到上下文
	c.Set("csrf_token", token)

	// 设置令牌到响应头
	c.Header("X-CSRF-Token", token)

	// 根据请求类型设置响应
	if strings.Contains(c.GetHeader("Accept"), "application/json") {
		// 对于API请求，令牌在响应头中
	} else {
		// 对于页面请求，令牌在响应头和Cookie中
		c.SetCookie("csrf_token", token, int(tokenExpire.Seconds()), "/", "", false, false)
	}
}

// cleanExpiredTokens 清理过期令牌
func cleanExpiredTokens() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now()

		tokenMutex.Lock()
		for token, expireTime := range tokenStore {
			if now.After(expireTime) {
				delete(tokenStore, token)
			}
		}
		tokenMutex.Unlock()
	}
}
