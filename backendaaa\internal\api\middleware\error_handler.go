package middleware

import (
	"backend/internal/api/response"
	"backend/internal/utils"
	"errors"
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 统一错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 打印堆栈信息
				stack := string(debug.Stack())
				log.Printf("Panic recovered: %v\nStack trace: %s", err, stack)

				// 根据错误类型返回不同的错误信息
				var message string
				var statusCode int
				var errorCode int

				switch e := err.(type) {
				case string:
					message = e
					statusCode = http.StatusInternalServerError
					errorCode = 500
				case error:
					// 处理各种自定义错误
					message = e.Error()
					statusCode = http.StatusInternalServerError
					errorCode = 500

					var appErr *utils.AppError
					if errors.As(e, &appErr) {
						message = appErr.Message
						statusCode = appErr.Code
						errorCode = appErr.Code

						// 记录详细错误信息
						log.Printf("AppError: [%s] %s\nOriginal Error: %v\nStack: %s",
							appErr.Type, appErr.Message, appErr.Err, appErr.Stack)
					}
				case response.ErrorResponse:
					message = e.Message
					statusCode = e.StatusCode
					errorCode = e.StatusCode
				default:
					message = "服务器内部错误"
					statusCode = http.StatusInternalServerError
					errorCode = 500
				}

				// 返回JSON格式的错误信息
				c.AbortWithStatusJSON(statusCode, response.ErrorWithCode(errorCode, message))
				return
			}
		}()

		// 继续处理请求
		c.Next()

		// 请求处理完成后检查是否有错误
		if len(c.Errors) > 0 {
			// 获取第一个错误
			err := c.Errors[0].Err

			// 根据错误类型设置状态码和消息
			var statusCode int = http.StatusInternalServerError
			var message string = err.Error()
			var errorCode int = 500

			// 检查是否为自定义应用错误
			var appErr *utils.AppError
			if errors.As(err, &appErr) {
				statusCode = appErr.Code
				message = appErr.Message
				errorCode = appErr.Code

				// 记录详细错误信息
				log.Printf("AppError: [%s] %s\nOriginal Error: %v\nStack: %s",
					appErr.Type, appErr.Message, appErr.Err, appErr.Stack)
			} else {
				// 其他错误类型的处理
				switch {
				case utils.IsErrorType(err, utils.ErrorTypeNotFound):
					statusCode = http.StatusNotFound
					errorCode = 404
				case utils.IsErrorType(err, utils.ErrorTypePermission):
					statusCode = http.StatusForbidden
					errorCode = 403
				case utils.IsErrorType(err, utils.ErrorTypeAuth):
					statusCode = http.StatusUnauthorized
					errorCode = 401
				case utils.IsErrorType(err, utils.ErrorTypeValidation):
					statusCode = http.StatusBadRequest
					errorCode = 400
				case utils.IsErrorType(err, utils.ErrorTypeDuplicate):
					statusCode = http.StatusConflict
					errorCode = 409
				}
			}

			// 如果尚未响应，则返回错误响应
			if !c.Writer.Written() {
				c.AbortWithStatusJSON(statusCode, response.ErrorWithCode(errorCode, message))
			}
		}
	}
}

// NotFound 404处理中间件
func NotFound() gin.HandlerFunc {
	return func(c *gin.Context) {
		err := utils.NewNotFoundError("请求的资源不存在")
		c.JSON(http.StatusNotFound, response.ErrorWithCode(err.Code, err.Message))
		c.Abort()
	}
}

// MethodNotAllowed 405处理中间件
func MethodNotAllowed() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, response.ErrorWithCode(http.StatusMethodNotAllowed, "请求方法不允许"))
		c.Abort()
	}
}

// WithErrorLogging 记录错误日志的中间件
func WithErrorLogging() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理请求后检查是否有错误
		for _, err := range c.Errors {
			log.Printf("Error in request [%s] %s: %v", c.Request.Method, c.Request.URL.Path, err.Err)
		}
	}
}

// CustomError 自定义错误结构
type CustomError struct {
	StatusCode int
	Message    string
}

// Error 实现error接口
func (e *CustomError) Error() string {
	return e.Message
}

// NewError 创建自定义错误
func NewError(statusCode int, message string) *CustomError {
	return &CustomError{
		StatusCode: statusCode,
		Message:    message,
	}
}
