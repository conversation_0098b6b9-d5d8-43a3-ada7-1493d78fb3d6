package middleware

import (
	"backend/internal/common/response"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// IP限流器映射
var (
	ipLimiters = make(map[string]*Limiter)
	ipMutex    sync.RWMutex
)

// Limiter 简单的令牌桶限流器实现
type Limiter struct {
	rate       float64    // 每秒产生的令牌数
	capacity   int        // 令牌桶容量
	tokens     float64    // 当前令牌数
	lastRefill time.Time  // 上次填充时间
	mutex      sync.Mutex // 互斥锁
}

// NewLimiter 创建限流器
func NewLimiter(rate float64, capacity int) *Limiter {
	return &Limiter{
		rate:       rate,
		capacity:   capacity,
		tokens:     float64(capacity),
		lastRefill: time.Now(),
	}
}

// Allow 尝试获取令牌
func (l *Limiter) Allow() bool {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	// 计算需要填充的令牌数
	now := time.Now()
	elapsed := now.Sub(l.lastRefill).Seconds()
	l.lastRefill = now

	// 填充令牌
	l.tokens += elapsed * l.rate
	if l.tokens > float64(l.capacity) {
		l.tokens = float64(l.capacity)
	}

	// 消耗令牌
	if l.tokens < 1 {
		return false
	}

	l.tokens--
	return true
}

// 清理过期的限流器
func init() {
	go cleanupLimiters()
}

// cleanupLimiters 定期清理不活跃的限流器
func cleanupLimiters() {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		ipMutex.Lock()
		for ip := range ipLimiters {
			delete(ipLimiters, ip)
		}
		ipMutex.Unlock()
	}
}

// getRateLimiter 获取IP对应的限流器
func getRateLimiter(ip string) *Limiter {
	ipMutex.RLock()
	limiter, exists := ipLimiters[ip]
	ipMutex.RUnlock()

	if !exists {
		ipMutex.Lock()
		defer ipMutex.Unlock()

		// 再次检查，避免并发创建
		limiter, exists = ipLimiters[ip]
		if !exists {
			// 创建新的限流器，每秒允许5个请求，最多允许10个并发请求
			limiter = NewLimiter(5, 10)
			ipLimiters[ip] = limiter
		}
	}

	return limiter
}

// RateLimit 请求频率限制中间件
// r - 每秒请求数
// b - 令牌桶容量
func RateLimit(r float64, b int) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取客户端IP
		clientIP := c.ClientIP()

		// 获取限流器
		limiter := getRateLimiter(clientIP)

		// 尝试获取令牌
		if !limiter.Allow() {
			response.TooManyRequests(c, "请求过于频繁，请稍后重试")
			c.Abort()
			return
		}

		c.Next()
	}
}

// pathLimiterMap 存储API路径的限流器
var pathLimiterMap = map[string]*Limiter{
	"/login":            NewLimiter(1, 5),  // 登录接口：每秒1个请求，最多5个并发
	"/system/user/list": NewLimiter(2, 10), // 用户列表：每秒2个请求，最多10个并发
	"/captchaImage":     NewLimiter(5, 20), // 验证码：每秒5个请求，最多20个并发
}

// defaultLimiter 默认限流器：每秒10个请求，最多20个并发
var defaultLimiter = NewLimiter(10, 20)

// APIRateLimit API请求频率限制中间件
// 针对特定API路径设置不同的限流规则
func APIRateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求路径
		path := c.FullPath()
		if path == "" {
			path = c.Request.URL.Path
		}

		// 获取限流器
		var limiter *Limiter
		if l, exists := pathLimiterMap[path]; exists {
			limiter = l
		} else {
			limiter = defaultLimiter
		}

		// 尝试获取令牌
		if !limiter.Allow() {
			response.TooManyRequests(c, "请求过于频繁，请稍后重试")
			c.Abort()
			return
		}

		c.Next()
	}
}
