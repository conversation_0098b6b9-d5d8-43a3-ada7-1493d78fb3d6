package middleware

import (
	"backend/internal/common/response"
	"context"
	"time"

	"github.com/gin-gonic/gin"
)

// Timeout 超时中间件，如果请求处理时间超过指定时间，则中断请求
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过不需要超时控制的请求
		if c.Request.Method == "GET" {
			c.Next()
			return
		}

		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// 替换上下文
		c.Request = c.Request.WithContext(ctx)

		// 创建完成通道
		done := make(chan struct{})

		// 开启协程处理请求
		go func() {
			c.Next()
			done <- struct{}{}
		}()

		// 等待处理完成或超时
		select {
		case <-done:
			// 请求正常完成
			return
		case <-ctx.Done():
			// 请求超时
			if ctx.Err() == context.DeadlineExceeded {
				response.Timeout(c, "请求处理超时")
				c.Abort()
				return
			}
		}
	}
}
