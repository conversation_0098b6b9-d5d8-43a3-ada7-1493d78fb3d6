package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// 自定义验证错误消息
var validationMessages = map[string]string{
	"required": "字段不能为空",
	"min":      "最小值为%v",
	"max":      "最大值为%v",
	"email":    "邮箱格式不正确",
	"len":      "长度必须等于%v",
}

// Validator 参数验证中间件，与Java后端的@Validated功能类似
func Validator() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加自定义验证器到binding引擎
		if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
			// 注册自定义验证标签和函数
			_ = v.RegisterValidation("mobile", validateMobile)
			// 可以添加更多自定义验证器
		}
		c.Next()
	}
}

// validateMobile 验证手机号码
func validateMobile(fl validator.FieldLevel) bool {
	mobile := fl.Field().String()
	// 实现手机号码验证逻辑
	if len(mobile) != 11 {
		return false
	}
	// 添加更详细的验证逻辑
	return true
}

// ValidateRequest 验证请求参数
func ValidateRequest(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 根据Content-Type选择不同的绑定方法
		var err error
		contentType := c.ContentType()
		if strings.Contains(contentType, "json") {
			err = c.ShouldBindJSON(obj)
		} else if strings.Contains(contentType, "form") {
			err = c.ShouldBindWith(obj, binding.Form)
		} else {
			err = c.ShouldBind(obj)
		}

		// 如果有验证错误，格式化并返回错误信息
		if err != nil {
			var errMsg string
			if validationErrors, ok := err.(validator.ValidationErrors); ok {
				for _, fieldError := range validationErrors {
					errMsg += formatValidationError(fieldError) + "; "
				}
			} else {
				errMsg = err.Error()
			}

			c.JSON(http.StatusBadRequest, gin.H{
				"code": 400,
				"msg":  "参数验证失败: " + errMsg,
			})
			c.Abort()
			return
		}

		// 继续处理请求
		c.Next()
	}
}

// formatValidationError 格式化验证错误信息
func formatValidationError(err validator.FieldError) string {
	fieldName := err.Field()
	tag := err.Tag()
	param := err.Param()

	// 直接使用字段名称，不尝试通过反射获取json标签
	// 在实际生产环境中，可以使用结构体标签的缓存来改进这一点

	// 获取错误信息模板
	message, exists := validationMessages[tag]
	if !exists {
		message = "验证失败"
	}

	// 格式化错误信息
	if param != "" {
		message = fmt.Sprintf(message, param)
	}

	return fmt.Sprintf("字段 '%s' %s", fieldName, message)
}
