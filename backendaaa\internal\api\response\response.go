package response

import "github.com/gin-gonic/gin"

// ResponseData 响应数据结构
type ResponseData struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	StatusCode int    `json:"-"`
	Code       int    `json:"code"`
	Message    string `json:"msg"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(200, ResponseData{
		Code: 0,
		Msg:  "操作成功",
		Data: data,
	})
}

// Fail 失败响应
func Fail(c *gin.Context, msg string) {
	c.JSON(200, ResponseData{
		Code: -1,
		Msg:  msg,
		Data: nil,
	})
}

// FailWithCode 带状态码的失败响应
func FailWithCode(c *gin.Context, code int, msg string) {
	c.<PERSON>SO<PERSON>(200, ResponseData{
		Code: code,
		Msg:  msg,
		Data: nil,
	})
}

// Custom 自定义响应
func Custom(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(200, ResponseData{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

// Error 创建错误响应
// 已废弃，请使用Fail方法
func Error(message string) *ResponseData {
	return &ResponseData{
		Code: -1,
		Msg:  message,
		Data: nil,
	}
}

// ErrorWithCode 创建带错误码的错误响应
func ErrorWithCode(code int, message string) *ResponseData {
	return &ResponseData{
		Code: code,
		Msg:  message,
		Data: nil,
	}
}

// NewErrorResponse 创建错误响应对象
func NewErrorResponse(statusCode int, code int, message string) ErrorResponse {
	return ErrorResponse{
		StatusCode: statusCode,
		Code:       code,
		Message:    message,
	}
}
