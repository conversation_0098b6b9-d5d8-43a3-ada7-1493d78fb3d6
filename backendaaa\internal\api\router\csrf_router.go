package router

import (
	"crypto/rand"
	"encoding/base64"

	"github.com/gin-gonic/gin"
)

// RegisterCSRFRouter 注册CSRF令牌路由
func RegisterCSRFRouter(r *gin.Engine) {
	// CSRF令牌获取接口
	r.GET("/csrf-token", func(c *gin.Context) {
		// 生成随机字节
		b := make([]byte, 32)
		if _, err := rand.Read(b); err != nil {
			c.String(500, "Failed to generate token")
			return
		}

		// 编码为Base64
		token := base64.StdEncoding.EncodeToString(b)

		// 返回令牌
		c.String(200, token)
	})
}
