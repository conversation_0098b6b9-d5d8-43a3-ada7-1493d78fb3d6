package router

import (
	"backend/internal/api/controller/tool"
	"backend/internal/api/middleware"
	"backend/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterGenRouter 注册代码生成路由
func RegisterGenRouter(r *gin.RouterGroup, db *gorm.DB, tokenService service.TokenService, genTableService service.GenTableService) {
	// 创建控制器
	genUIController := tool.NewGenUIController(genTableService)

	// 代码生成器路由
	genRouter := r.Group("/tool/gen").Use(middleware.Auth(tokenService))
	{
		// UI路由
		genRouter.GET("/index", genUIController.Index)
		genRouter.GET("/importTable", genUIController.ImportTable)
		genRouter.GET("/edit/:tableId", genUIController.EditTable)
		genRouter.GET("/preview/:tableId", genUIController.PreviewCode)

		// API路由
		genRouter.GET("/list", genUIController.GetGenTableList)
		genRouter.GET("/db/list", genUIController.GetDbTableList)
		genRouter.GET("/:tableId", genUIController.GetGenTableInfo)
		genRouter.GET("/column/:tableId", genUIController.GetTableColumns)
		genRouter.GET("/preview/:tableId", genUIController.PreviewCode)
		genRouter.GET("/genCode/:tableName", genUIController.GenCode)
		genRouter.GET("/synchDb/:tableName", genUIController.SynchDb)
		genRouter.GET("/batchGenCode", genUIController.BatchGenCode)
		genRouter.POST("/importTable", genUIController.ImportTable)
		genRouter.POST("", genUIController.UpdateGenTable)
		genRouter.DELETE("/:tableIds", genUIController.DeleteGenTable)
	}
}
