package router

import (
	"backend/internal/job/controller"
	"backend/internal/job/service"
	"backend/internal/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterJobRouter 注册定时任务路由
func RegisterJobRouter(r *gin.RouterGroup, db *gorm.DB) {
	// 创建仓储层
	jobRepo := repository.NewSysJobRepository(db)

	// 创建服务层
	jobService := service.NewSysJobService(jobRepo)

	// 创建控制器
	jobController := controller.NewSysJobController(jobService)

	// 定时任务路由
	job := r.Group("/monitor/job")
	{
		// 定时任务列表
		job.GET("/list", jobController.List)
		// 定时任务详情
		job.GET("/:jobId", jobController.GetInfo)
		// 新增定时任务
		job.POST("", jobController.Add)
		// 修改定时任务
		job.PUT("", jobController.Edit)
		// 删除定时任务
		job.DELETE("/:jobIds", jobController.Remove)
		// 修改定时任务状态
		job.PUT("/changeStatus", jobController.ChangeStatus)
		// 立即执行一次定时任务
		job.PUT("/run", jobController.Run)
		// 校验Cron表达式是否有效
		job.GET("/checkCronExpressionIsValid", jobController.CheckCronExpressionIsValid)
	}
}
