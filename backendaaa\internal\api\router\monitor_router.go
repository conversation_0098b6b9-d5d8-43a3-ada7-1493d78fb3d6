package router

import (
	"backend/internal/api/controller/monitor"
	"backend/internal/api/middleware"
	"backend/internal/database"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/service/impl"

	"github.com/gin-gonic/gin"
)

// RegisterMonitorRouter 注册监控相关路由
func RegisterMonitorRouter(r *gin.Engine, tokenService service.TokenService, permissionService service.SysPermissionService,
	sysOperLogService service.SysOperLogService, sysUserOnlineService service.SysUserOnlineService,
	sysJobService service.SysJobService, sysJobLogService service.SysJobLogService) {

	// 获取数据库连接
	db := database.GetDB()
	if db == nil {
		panic("database connection is not initialized")
	}

	// 创建内存缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 创建TokenService的Redis适配器
	tokenRedisAdapter := impl.NewRedisTokenAdapter(tokenService, redisCache)

	// 创建密码服务
	passwordService := impl.NewSysPasswordService(redisCache, 5, 10) // 最大重试次数5次，锁定10分钟

	// 系统监控
	monitorRouter := r.Group("/monitor")
	monitorRouter.Use(middleware.Auth(tokenService))
	{
		// 登录日志
		sysLogininforRepo := repository.NewSysLogininforRepository(db)
		sysLogininforService := impl.NewSysLogininforService(sysLogininforRepo)
		sysLogininforController := monitor.NewSysLogininforController(sysLogininforService, passwordService)
		logininforRouter := monitorRouter.Group("/logininfor")
		{
			logininforRouter.GET("/list", middleware.Permission("monitor:logininfor:list", permissionService), sysLogininforController.List)
			logininforRouter.DELETE("/:infoIds", middleware.Permission("monitor:logininfor:remove", permissionService), sysLogininforController.Remove)
			logininforRouter.DELETE("/clean", middleware.Permission("monitor:logininfor:remove", permissionService), sysLogininforController.Clean)
			logininforRouter.GET("/export", middleware.Permission("monitor:logininfor:export", permissionService), sysLogininforController.Export)
			logininforRouter.GET("/unlock/:userName", middleware.Permission("monitor:logininfor:unlock", permissionService), sysLogininforController.Unlock)
		}

		// 操作日志记录
		sysOperlogController := monitor.NewSysOperlogController(sysOperLogService)
		operlogRouter := monitorRouter.Group("/operlog")
		{
			operlogRouter.GET("/list", middleware.Permission("monitor:operlog:list", permissionService), sysOperlogController.List)
			operlogRouter.DELETE("/:operIds", middleware.Permission("monitor:operlog:remove", permissionService), sysOperlogController.Remove)
			operlogRouter.DELETE("/clean", middleware.Permission("monitor:operlog:remove", permissionService), sysOperlogController.Clean)
			operlogRouter.GET("/export", middleware.Permission("monitor:operlog:export", permissionService), sysOperlogController.Export)
		}

		// 在线用户查询
		sysUserOnlineController := monitor.NewSysUserOnlineController(sysUserOnlineService, tokenRedisAdapter)
		onlineRouter := monitorRouter.Group("/online")
		{
			onlineRouter.GET("/list", middleware.Permission("monitor:online:list", permissionService), sysUserOnlineController.List)
			onlineRouter.DELETE("/:tokenId", middleware.Permission("monitor:online:forceLogout", permissionService), sysUserOnlineController.ForceLogout)
		}

		// 系统信息
		serverController := monitor.NewServerController()
		serverRouter := monitorRouter.Group("/server")
		{
			serverRouter.GET("", middleware.Permission("monitor:server:list", permissionService), serverController.GetInfo)
		}

		// 缓存监控
		cacheController := monitor.NewCacheController(redisCache)
		cacheRouter := monitorRouter.Group("/cache")
		{
			cacheRouter.GET("", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetInfo)
			cacheRouter.GET("/getNames", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetNames)
			cacheRouter.GET("/getKeys/:cacheName", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetKeys)
			cacheRouter.GET("/getValue/:cacheName/:cacheKey", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetValue)
			cacheRouter.DELETE("/clearCacheKey/:cacheName/:cacheKey", middleware.Permission("monitor:cache:list", permissionService), cacheController.ClearCacheKey)
			cacheRouter.DELETE("/clearCacheAll", middleware.Permission("monitor:cache:list", permissionService), cacheController.ClearCacheAll)
		}

		// 定时任务
		sysJobController := monitor.NewSysJobController(sysJobService)
		jobRouter := monitorRouter.Group("/job")
		{
			jobRouter.GET("/list", middleware.Permission("monitor:job:list", permissionService), sysJobController.List)
			jobRouter.GET("/:jobId", middleware.Permission("monitor:job:query", permissionService), sysJobController.GetInfo)
			jobRouter.POST("", middleware.Permission("monitor:job:add", permissionService), sysJobController.Add)
			jobRouter.PUT("", middleware.Permission("monitor:job:edit", permissionService), sysJobController.Edit)
			jobRouter.DELETE("/:jobIds", middleware.Permission("monitor:job:remove", permissionService), sysJobController.Remove)
			jobRouter.PUT("/changeStatus", middleware.Permission("monitor:job:changeStatus", permissionService), sysJobController.ChangeStatus)
			jobRouter.PUT("/run", middleware.Permission("monitor:job:changeStatus", permissionService), sysJobController.Run)
			jobRouter.GET("/export", middleware.Permission("monitor:job:export", permissionService), sysJobController.Export)
		}

		// 定时任务日志
		sysJobLogController := monitor.NewSysJobLogController(sysJobLogService)
		jobLogRouter := monitorRouter.Group("/jobLog")
		{
			jobLogRouter.GET("/list", middleware.Permission("monitor:job:list", permissionService), sysJobLogController.List)
			jobLogRouter.GET("/:jobLogId", middleware.Permission("monitor:job:query", permissionService), sysJobLogController.GetInfo)
			jobLogRouter.DELETE("/:jobLogIds", middleware.Permission("monitor:job:remove", permissionService), sysJobLogController.Remove)
			jobLogRouter.DELETE("/clean", middleware.Permission("monitor:job:remove", permissionService), sysJobLogController.Clean)
			jobLogRouter.GET("/export", middleware.Permission("monitor:job:export", permissionService), sysJobLogController.Export)
		}
	}
}
