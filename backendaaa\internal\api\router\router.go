package router

import (
	"backend/internal/api/controller/common"
	monitorCtrl "backend/internal/api/controller/monitor"
	"backend/internal/api/controller/system"
	"backend/internal/api/middleware"
	"backend/internal/database"
	"backend/internal/monitor"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/service/impl"

	"time"

	"github.com/gin-gonic/gin"
)

// InitRouter 初始化路由
func InitRouter(r *gin.Engine) {
	// 获取数据库连接
	db := database.GetDB()
	if db == nil {
		panic("database connection is not initialized")
	}

	// 为需要sqlx接口的组件创建sqlx适配器
	sqlxDB := repository.GormToSqlx(db)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(db)
	sysRoleRepo := repository.NewSysRoleRepository(db)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysJobRepo := repository.NewSysJobRepository(db)
	sysJobLogRepo := repository.NewSysJobLogRepository(db)
	sysOperLogRepo := repository.NewSysOperLogRepository(db)

	// 初始化服务层
	sysRoleService := impl.NewSysRoleService(sysRoleRepo, nil, nil, nil)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, nil)
	sysOperLogService := impl.NewSysOperLogService(sysOperLogRepo)
	sysJobService := impl.NewSysJobService(sysJobRepo, sysJobLogRepo)
	sysJobLogService := impl.NewSysJobLogService(sysJobLogRepo)

	// 初始化权限服务
	permissionService := impl.NewSysPermissionService(sysRoleService, sysMenuService, sysUserRepo)

	// 初始化缓存
	redisCache := impl.NewMemoryRedisCache()

	// 初始化用户在线服务
	sysUserOnlineService := impl.NewSysUserOnlineService()

	// 初始化Token服务
	tokenService := impl.NewMemoryTokenService()

	// 注册全局中间件
	r.Use(middleware.Trace())
	r.Use(middleware.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.Cors())
	r.Use(middleware.XSSProtection())
	r.Use(middleware.CSRFProtection())
	r.Use(middleware.APIRateLimit())
	r.Use(middleware.OperLog(sysOperLogService))
	r.Use(middleware.Timeout(time.Second * 30))

	// 注册CSRF令牌路由
	RegisterCSRFRouter(r)

	// 注册基础路由
	registerBaseRouter(r)

	// 注册不需要认证的系统路由
	registerPublicSystemRouter(r)

	// 注册需要认证的系统路由
	registerAuthSystemRouter(r, tokenService, permissionService, redisCache)

	// 注册监控路由
	RegisterMonitorRouter(r, tokenService, permissionService, sysOperLogService,
		sysUserOnlineService, sysJobService, sysJobLogService)

	// 注册通用路由
	registerCommonRouter(r, tokenService)

	// 注册工具路由
	toolGroup := r.Group("")
	RegisterToolRouter(toolGroup, db, tokenService)
}

// registerBaseRouter 注册基本路由
func registerBaseRouter(r *gin.Engine) {
	// 创建系统监控器
	processMonitor := monitor.NewProcessMonitor()
	healthChecker := monitor.NewSystemHealthChecker()

	// 创建健康检查控制器
	healthController := monitorCtrl.NewHealthController(healthChecker, processMonitor)

	// 健康检查路由
	r.GET("/health", healthController.Health)
	r.GET("/health/check", healthController.Check)
	r.GET("/health/server", healthController.ServerInfo)

	// 添加数据库健康检查
	db := database.GetDB()
	if db != nil {
		sqlDB, err := db.DB()
		if err == nil {
			healthChecker.RegisterCheck(monitor.NewDatabaseHealthCheck(sqlDB))
		}
	}

	// 添加自检健康检查
	healthChecker.RegisterCheck(monitor.NewHTTPHealthCheck("http://localhost:8080/health", 5*time.Second))

	// 获取数据库连接
	if db == nil {
		panic("database connection is not initialized")
	}

	// 首页控制器
	sysIndexController := system.NewSysIndexController("RuoYi-Go", "1.0.0")
	r.GET("/", sysIndexController.Index)

	// 初始化配置服务
	sysConfigRepo := repository.NewSysConfigRepository(db)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)

	// 创建缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 验证码控制器
	captchaController := common.NewCaptchaController(sysConfigService, redisCache)
	r.GET("/captchaImage", captchaController.GetCode)

	// 确保OPTIONS请求也能正确响应
	r.OPTIONS("/captchaImage", func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, token")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type, token")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "3600")
		c.AbortWithStatus(200)
	})
}

// registerPublicSystemRouter 注册不需要认证的系统路由
func registerPublicSystemRouter(r *gin.Engine) {
	// 获取数据库连接
	db := database.GetDB()
	if db == nil {
		panic("database connection is not initialized")
	}

	// 为需要sqlx接口的组件创建sqlx适配器
	sqlxDB := repository.GormToSqlx(db)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(db)
	sysRoleRepo := repository.NewSysRoleRepository(db)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysDeptRepo := repository.NewSysDeptRepository(db)
	sysPostRepo := repository.NewSysPostRepository(db)
	sysConfigRepo := repository.NewSysConfigRepository(db)

	// 创建内存缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 创建简易的服务实现
	sysPermissionService := &impl.EmptyPermissionService{}
	sysTokenService := impl.NewEmptyTokenService()

	// 初始化服务层
	sysUserService := impl.NewSysUserService(sysUserRepo, sysRoleRepo, sysPostRepo, nil, nil, sysConfigRepo, sysDeptRepo)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, nil)

	// 创建登录服务
	sysLoginService := impl.NewSysLoginService(
		sysConfigService,
		sysUserService,
		sysTokenService,
		redisCache,
		sysPermissionService,
	)

	// 系统登录控制器
	sysLoginController := system.NewSysLoginController(
		sysLoginService,
		sysMenuService,
		sysPermissionService,
		sysTokenService,
		sysConfigService,
	)

	// 注册登录相关接口
	r.POST("/login", sysLoginController.Login)
	r.GET("/getInfo", middleware.JWTAuthMiddleware(sysTokenService), sysLoginController.GetInfo)
	r.GET("/getRouters", middleware.JWTAuthMiddleware(sysTokenService), sysLoginController.GetRouters)
	r.POST("/logout", middleware.JWTAuthMiddleware(sysTokenService), sysLoginController.Logout)

	// 注册服务
	sysRegisterService := impl.NewSysRegisterService(sysUserService, sysConfigService)
	sysRegisterController := system.NewSysRegisterController(sysRegisterService, sysConfigService)
	r.POST("/register", sysRegisterController.Register)
}

// registerAuthSystemRouter 注册需要认证的系统路由
func registerAuthSystemRouter(r *gin.Engine, tokenService service.TokenService, permissionService service.SysPermissionService, redisCache service.RedisCache) {
	// 系统主路由组（所有路由都需要认证）
	systemGroup := r.Group("/system")
	systemGroup.Use(middleware.JWTAuthMiddleware(tokenService))

	// 获取数据库连接
	db := database.GetDB()
	if db == nil {
		panic("database connection is not initialized")
	}

	// 为需要sqlx接口的组件创建sqlx适配器
	sqlxDB := repository.GormToSqlx(db)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(db)
	sysRoleRepo := repository.NewSysRoleRepository(db)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysDeptRepo := repository.NewSysDeptRepository(db)
	sysPostRepo := repository.NewSysPostRepository(db)
	sysConfigRepo := repository.NewSysConfigRepository(db)
	sysUserRoleRepo := repository.NewSysUserRoleRepository(db)
	sysRoleMenuRepo := repository.NewSysRoleMenuRepository(db)
	sysRoleDeptRepo := repository.NewSysRoleDeptRepository(db)
	sysUserPostRepo := repository.NewSysUserPostRepository(db)
	sysDictTypeRepo := repository.NewSysDictTypeRepository(db)
	sysDictDataRepo := repository.NewSysDictDataRepository(db)
	sysNoticeRepo := repository.NewSysNoticeRepository(db)

	// 初始化服务层
	sysUserService := impl.NewSysUserService(sysUserRepo, sysRoleRepo, sysPostRepo, sysUserRoleRepo, sysUserPostRepo, sysConfigRepo, sysDeptRepo)
	sysRoleService := impl.NewSysRoleService(sysRoleRepo, sysRoleMenuRepo, sysRoleDeptRepo, sysUserRoleRepo)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, sysRoleMenuRepo)
	sysDeptService := impl.NewSysDeptService(sysDeptRepo, sysRoleRepo)
	sysPostService := impl.NewSysPostService(sysPostRepo, sysUserPostRepo)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)
	sysDictTypeService := impl.NewSysDictTypeService(sysDictTypeRepo, sysDictDataRepo)
	sysDictDataService := impl.NewSysDictDataService(sysDictDataRepo)
	sysNoticeService := impl.NewSysNoticeService(sysNoticeRepo)

	// 用户管理
	sysUserController := system.NewSysUserController(sysUserService, sysRoleService, sysDeptService, sysPostService)
	userRouter := systemGroup.Group("/user")
	{
		userRouter.GET("/list", middleware.Permission("system:user:list", permissionService), sysUserController.List)
		userRouter.GET("/:userId", middleware.Permission("system:user:query", permissionService), sysUserController.GetInfo)
		userRouter.POST("", middleware.Permission("system:user:add", permissionService), sysUserController.Add)
		userRouter.PUT("", middleware.Permission("system:user:edit", permissionService), sysUserController.Edit)
		userRouter.DELETE("/:userIds", middleware.Permission("system:user:remove", permissionService), sysUserController.Remove)
		userRouter.PUT("/resetPwd", middleware.Permission("system:user:resetPwd", permissionService), sysUserController.ResetPwd)
		userRouter.PUT("/changeStatus", middleware.Permission("system:user:edit", permissionService), sysUserController.ChangeStatus)
		userRouter.GET("/authRole/:userId", middleware.Permission("system:user:query", permissionService), sysUserController.AuthRole)
		userRouter.PUT("/authRole", middleware.Permission("system:user:edit", permissionService), sysUserController.InsertAuthRole)
		userRouter.POST("/importData", middleware.Permission("system:user:import", permissionService), sysUserController.ImportData)
		userRouter.GET("/importTemplate", middleware.Permission("system:user:import", permissionService), sysUserController.ImportTemplate)
		userRouter.GET("/export", middleware.Permission("system:user:export", permissionService), sysUserController.Export)

		// 用户个人信息
		sysProfileController := system.NewSysProfileController(sysUserService, tokenService, "./uploads/avatar")
		profileRouter := userRouter.Group("/profile")
		{
			profileRouter.GET("", sysProfileController.GetProfile)
			profileRouter.PUT("", sysProfileController.UpdateProfile)
			profileRouter.PUT("/updatePwd", sysProfileController.UpdatePwd)
			profileRouter.POST("/avatar", sysProfileController.UploadAvatar)
		}
	}

	// 部门管理
	sysDeptController := system.NewSysDeptController(sysDeptService)
	deptRouter := systemGroup.Group("/dept")
	{
		deptRouter.GET("/list", middleware.Permission("system:dept:list", permissionService), sysDeptController.List)
		deptRouter.GET("/:deptId", middleware.Permission("system:dept:query", permissionService), sysDeptController.GetInfo)
		deptRouter.POST("", middleware.Permission("system:dept:add", permissionService), sysDeptController.Add)
		deptRouter.PUT("", middleware.Permission("system:dept:edit", permissionService), sysDeptController.Edit)
		deptRouter.DELETE("/:deptId", middleware.Permission("system:dept:remove", permissionService), sysDeptController.Remove)
		deptRouter.GET("/treeselect", sysDeptController.TreeSelect)
		deptRouter.GET("/roleDeptTreeselect/:roleId", sysDeptController.RoleDeptTreeSelect)
		deptRouter.GET("/export", middleware.Permission("system:dept:export", permissionService), sysDeptController.Export)
	}

	// 角色管理 - 注意：实际参数需要与控制器构造函数保持一致
	sysRoleController := system.NewSysRoleController(sysRoleService, sysUserService, sysDeptService, permissionService, tokenService)
	roleRouter := systemGroup.Group("/role")
	{
		roleRouter.GET("/list", middleware.Permission("system:role:list", permissionService), sysRoleController.List)
		roleRouter.GET("/:roleId", middleware.Permission("system:role:query", permissionService), sysRoleController.GetInfo)
		roleRouter.POST("", middleware.Permission("system:role:add", permissionService), sysRoleController.Add)
		roleRouter.PUT("", middleware.Permission("system:role:edit", permissionService), sysRoleController.Edit)
		roleRouter.DELETE("/:roleIds", middleware.Permission("system:role:remove", permissionService), sysRoleController.Remove)
		roleRouter.PUT("/dataScope", middleware.Permission("system:role:edit", permissionService), sysRoleController.DataScope)
		roleRouter.PUT("/changeStatus", middleware.Permission("system:role:edit", permissionService), sysRoleController.ChangeStatus)
		roleRouter.GET("/deptTree/:roleId", sysRoleController.DeptTree)
		roleRouter.GET("/export", middleware.Permission("system:role:export", permissionService), sysRoleController.Export)
	}

	// 菜单管理
	sysMenuController := system.NewSysMenuController(sysMenuService)
	menuRouter := systemGroup.Group("/menu")
	{
		menuRouter.GET("/list", middleware.Permission("system:menu:list", permissionService), sysMenuController.List)
		menuRouter.GET("/:menuId", middleware.Permission("system:menu:query", permissionService), sysMenuController.GetInfo)
		menuRouter.GET("/treeselect", sysMenuController.TreeSelect)
		menuRouter.GET("/roleMenuTreeselect/:roleId", sysMenuController.RoleMenuTreeSelect)
		menuRouter.POST("", middleware.Permission("system:menu:add", permissionService), sysMenuController.Add)
		menuRouter.PUT("", middleware.Permission("system:menu:edit", permissionService), sysMenuController.Edit)
		menuRouter.DELETE("/:menuId", middleware.Permission("system:menu:remove", permissionService), sysMenuController.Remove)
	}

	// 岗位管理
	sysPostController := system.NewSysPostController(sysPostService)
	postRouter := systemGroup.Group("/post")
	{
		postRouter.GET("/list", middleware.Permission("system:post:list", permissionService), sysPostController.List)
		postRouter.GET("/:postId", middleware.Permission("system:post:query", permissionService), sysPostController.GetInfo)
		postRouter.POST("", middleware.Permission("system:post:add", permissionService), sysPostController.Add)
		postRouter.PUT("", middleware.Permission("system:post:edit", permissionService), sysPostController.Edit)
		postRouter.DELETE("/:postIds", middleware.Permission("system:post:remove", permissionService), sysPostController.Remove)
	}

	// 字典类型
	sysDictTypeController := system.NewSysDictTypeController(sysDictTypeService)
	dictTypeRouter := systemGroup.Group("/dict/type")
	{
		dictTypeRouter.GET("/list", middleware.Permission("system:dict:list", permissionService), sysDictTypeController.List)
		dictTypeRouter.GET("/:dictId", middleware.Permission("system:dict:query", permissionService), sysDictTypeController.GetInfo)
		dictTypeRouter.POST("", middleware.Permission("system:dict:add", permissionService), sysDictTypeController.Add)
		dictTypeRouter.PUT("", middleware.Permission("system:dict:edit", permissionService), sysDictTypeController.Edit)
		dictTypeRouter.DELETE("/:dictIds", middleware.Permission("system:dict:remove", permissionService), sysDictTypeController.Remove)
		dictTypeRouter.GET("/optionselect", sysDictTypeController.OptionSelect)
	}

	// 字典数据
	sysDictDataController := system.NewSysDictDataController(sysDictDataService, sysDictTypeService)
	dictDataRouter := systemGroup.Group("/dict/data")
	{
		dictDataRouter.GET("/list", middleware.Permission("system:dict:list", permissionService), sysDictDataController.List)
		dictDataRouter.GET("/type/:dictType", sysDictDataController.DictType)
		dictDataRouter.GET("/:dictCode", middleware.Permission("system:dict:query", permissionService), sysDictDataController.GetInfo)
		dictDataRouter.POST("", middleware.Permission("system:dict:add", permissionService), sysDictDataController.Add)
		dictDataRouter.PUT("", middleware.Permission("system:dict:edit", permissionService), sysDictDataController.Edit)
		dictDataRouter.DELETE("/:dictCodes", middleware.Permission("system:dict:remove", permissionService), sysDictDataController.Remove)
	}

	// 参数设置
	sysConfigController := system.NewSysConfigController(sysConfigService)
	configRouter := systemGroup.Group("/config")
	{
		configRouter.GET("/list", middleware.Permission("system:config:list", permissionService), sysConfigController.List)
		configRouter.GET("/:configId", middleware.Permission("system:config:query", permissionService), sysConfigController.GetInfo)
		configRouter.GET("/configKey/:configKey", sysConfigController.GetConfigKey)
		configRouter.POST("", middleware.Permission("system:config:add", permissionService), sysConfigController.Add)
		configRouter.PUT("", middleware.Permission("system:config:edit", permissionService), sysConfigController.Edit)
		configRouter.DELETE("/:configIds", middleware.Permission("system:config:remove", permissionService), sysConfigController.Remove)
	}

	// 通知公告
	sysNoticeController := system.NewSysNoticeController(sysNoticeService)
	noticeRouter := systemGroup.Group("/notice")
	{
		noticeRouter.GET("/list", middleware.Permission("system:notice:list", permissionService), sysNoticeController.List)
		noticeRouter.GET("/:noticeId", middleware.Permission("system:notice:query", permissionService), sysNoticeController.GetInfo)
		noticeRouter.POST("", middleware.Permission("system:notice:add", permissionService), sysNoticeController.Add)
		noticeRouter.PUT("", middleware.Permission("system:notice:edit", permissionService), sysNoticeController.Edit)
		noticeRouter.DELETE("/:noticeIds", middleware.Permission("system:notice:remove", permissionService), sysNoticeController.Remove)
	}
}

// registerCommonRouter 注册通用路由
func registerCommonRouter(r *gin.Engine, tokenService service.TokenService) {
	// 通用模块 - 需要认证
	commonGroup := r.Group("/common")
	commonGroup.Use(middleware.JWTAuthMiddleware(tokenService))
	{
		// 通用上传下载控制器
		commonController := common.NewCommonController("./uploads", "./downloads", "./resources", "http://localhost:8080")
		commonGroup.GET("/download", commonController.FileDownload)
		commonGroup.POST("/upload", commonController.UploadFile)
		commonGroup.POST("/uploads", commonController.UploadFiles)
		commonGroup.GET("/download/resource", commonController.ResourceDownload)
	}
}

// 已移除重复的registerToolRouter函数，使用tool_router.go中的RegisterToolRouter函数
