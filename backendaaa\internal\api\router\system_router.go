package router

import (
	"backend/internal/api/controller/system"
	"backend/internal/api/middleware"
	"backend/internal/database"
	"backend/internal/repository"
	"backend/internal/service/impl"

	"github.com/gin-gonic/gin"
)

// InitSystemRouter 初始化系统路由
func InitSystemRouter(r *gin.Engine) {
	// 获取数据库连接
	db := database.GetDB()
	if db == nil {
		panic("database connection is not initialized")
	}

	// 创建sqlx适配器
	sqlxDB := repository.GormToSqlx(db)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(db)
	sysRoleRepo := repository.NewSysRoleRepository(db)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysDeptRepo := repository.NewSysDeptRepository(db)
	sysPostRepo := repository.NewSysPostRepository(db)
	sysConfigRepo := repository.NewSysConfigRepository(db)
	sysUserRoleRepo := repository.NewSysUserRoleRepository(db)
	sysRoleMenuRepo := repository.NewSysRoleMenuRepository(db)
	sysRoleDeptRepo := repository.NewSysRoleDeptRepository(db)
	sysUserPostRepo := repository.NewSysUserPostRepository(db)
	sysDictTypeRepo := repository.NewSysDictTypeRepository(db)
	sysDictDataRepo := repository.NewSysDictDataRepository(db)
	sysNoticeRepo := repository.NewSysNoticeRepository(db)

	// 创建缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 初始化服务层
	sysUserService := impl.NewSysUserService(sysUserRepo, sysRoleRepo, sysPostRepo, sysUserRoleRepo, sysUserPostRepo, sysConfigRepo, sysDeptRepo)
	sysRoleService := impl.NewSysRoleService(sysRoleRepo, sysRoleMenuRepo, sysRoleDeptRepo, sysUserRoleRepo)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, sysRoleMenuRepo)
	sysDeptService := impl.NewSysDeptService(sysDeptRepo, sysRoleRepo)
	sysPostService := impl.NewSysPostService(sysPostRepo, sysUserPostRepo)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)
	sysDictTypeService := impl.NewSysDictTypeService(sysDictTypeRepo, sysDictDataRepo)
	sysDictDataService := impl.NewSysDictDataService(sysDictDataRepo)
	sysNoticeService := impl.NewSysNoticeService(sysNoticeRepo)
	sysPermissionService := impl.NewSysPermissionService(sysRoleService, sysMenuService, sysUserRepo)
	sysTokenService := impl.NewMemoryTokenService()

	// 首页控制器
	sysIndexController := system.NewSysIndexController("RuoYi-Go", "1.0.0")
	r.GET("/", sysIndexController.Index)

	// 创建登录服务
	sysLoginService := impl.NewSysLoginService(
		sysConfigService,
		sysUserService,
		sysTokenService,
		redisCache,
		sysPermissionService,
	)

	// 系统登录控制器
	sysLoginController := system.NewSysLoginController(
		sysLoginService,
		sysMenuService,
		sysPermissionService,
		sysTokenService,
		sysConfigService,
	)

	// 验证码控制器
	captchaController := system.NewCaptchaController(sysConfigService, redisCache)
	r.GET("/captchaImage", captchaController.GetCode)

	// 注册登录相关接口
	r.POST("/login", sysLoginController.Login)
	r.GET("/getInfo", middleware.Auth(sysTokenService), sysLoginController.GetInfo)
	r.GET("/getRouters", middleware.Auth(sysTokenService), sysLoginController.GetRouters)

	// 注册服务
	sysRegisterService := impl.NewSysRegisterService(sysUserService, sysConfigService)
	sysRegisterController := system.NewSysRegisterController(sysRegisterService, sysConfigService)
	r.POST("/register", sysRegisterController.Register)

	// 系统控制器
	sysUserController := system.NewSysUserController(sysUserService, sysRoleService, sysDeptService, sysPostService)
	sysDeptController := system.NewSysDeptController(sysDeptService)
	sysRoleController := system.NewSysRoleController(sysRoleService, sysUserService, sysDeptService, sysPermissionService, sysTokenService)
	sysMenuController := system.NewSysMenuController(sysMenuService)
	sysPostController := system.NewSysPostController(sysPostService)
	sysConfigController := system.NewSysConfigController(sysConfigService)
	sysDictTypeController := system.NewSysDictTypeController(sysDictTypeService)
	sysDictDataController := system.NewSysDictDataController(sysDictDataService, sysDictTypeService)
	sysNoticeController := system.NewSysNoticeController(sysNoticeService)
	sysProfileController := system.NewSysProfileController(sysUserService, sysTokenService, "./uploads/avatar")

	// 系统路由组
	systemRouter := r.Group("/system")
	systemRouter.Use(middleware.Auth(sysTokenService))
	{
		// 用户管理
		userRouter := systemRouter.Group("/user")
		{
			userRouter.GET("/list", middleware.Permission("system:user:list", sysPermissionService), sysUserController.List)
			userRouter.GET("/:userId", middleware.Permission("system:user:query", sysPermissionService), sysUserController.GetInfo)
			userRouter.POST("", middleware.Permission("system:user:add", sysPermissionService), sysUserController.Add)
			userRouter.PUT("", middleware.Permission("system:user:edit", sysPermissionService), sysUserController.Edit)
			userRouter.DELETE("/:userIds", middleware.Permission("system:user:remove", sysPermissionService), sysUserController.Remove)
			userRouter.PUT("/resetPwd", middleware.Permission("system:user:resetPwd", sysPermissionService), sysUserController.ResetPwd)
			userRouter.PUT("/changeStatus", middleware.Permission("system:user:edit", sysPermissionService), sysUserController.ChangeStatus)
			userRouter.GET("/authRole/:userId", middleware.Permission("system:user:query", sysPermissionService), sysUserController.AuthRole)
			userRouter.PUT("/authRole", middleware.Permission("system:user:edit", sysPermissionService), sysUserController.InsertAuthRole)
			// 导入导出
			userRouter.POST("/importData", middleware.Permission("system:user:import", sysPermissionService), sysUserController.ImportData)
			userRouter.GET("/importTemplate", middleware.Permission("system:user:import", sysPermissionService), sysUserController.ImportTemplate)
			userRouter.GET("/export", middleware.Permission("system:user:export", sysPermissionService), sysUserController.Export)

			// 用户个人信息
			profileRouter := userRouter.Group("/profile")
			{
				profileRouter.GET("", sysProfileController.GetProfile)
				profileRouter.PUT("", sysProfileController.UpdateProfile)
				profileRouter.PUT("/updatePwd", sysProfileController.UpdatePwd)
				profileRouter.POST("/avatar", sysProfileController.UploadAvatar)
			}
		}

		// 部门管理
		deptRouter := systemRouter.Group("/dept")
		{
			deptRouter.GET("/list", middleware.Permission("system:dept:list", sysPermissionService), sysDeptController.List)
			deptRouter.GET("/:deptId", middleware.Permission("system:dept:query", sysPermissionService), sysDeptController.GetInfo)
			deptRouter.POST("", middleware.Permission("system:dept:add", sysPermissionService), sysDeptController.Add)
			deptRouter.PUT("", middleware.Permission("system:dept:edit", sysPermissionService), sysDeptController.Edit)
			deptRouter.DELETE("/:deptId", middleware.Permission("system:dept:remove", sysPermissionService), sysDeptController.Remove)
			deptRouter.GET("/treeselect", sysDeptController.TreeSelect)
			deptRouter.GET("/roleDeptTreeselect/:roleId", sysDeptController.RoleDeptTreeSelect)
			deptRouter.GET("/export", middleware.Permission("system:dept:export", sysPermissionService), sysDeptController.Export)
		}

		// 菜单管理
		menuRouter := systemRouter.Group("/menu")
		{
			menuRouter.GET("/list", middleware.Permission("system:menu:list", sysPermissionService), sysMenuController.List)
			menuRouter.GET("/:menuId", middleware.Permission("system:menu:query", sysPermissionService), sysMenuController.GetInfo)
			menuRouter.GET("/treeselect", sysMenuController.TreeSelect)
			menuRouter.GET("/roleMenuTreeselect/:roleId", sysMenuController.RoleMenuTreeSelect)
			menuRouter.POST("", middleware.Permission("system:menu:add", sysPermissionService), sysMenuController.Add)
			menuRouter.PUT("", middleware.Permission("system:menu:edit", sysPermissionService), sysMenuController.Edit)
			menuRouter.DELETE("/:menuId", middleware.Permission("system:menu:remove", sysPermissionService), sysMenuController.Remove)
			menuRouter.GET("/export", middleware.Permission("system:menu:export", sysPermissionService), sysMenuController.Export)
		}

		// 角色管理
		roleRouter := systemRouter.Group("/role")
		{
			roleRouter.GET("/list", middleware.Permission("system:role:list", sysPermissionService), sysRoleController.List)
			roleRouter.GET("/:roleId", middleware.Permission("system:role:query", sysPermissionService), sysRoleController.GetInfo)
			roleRouter.POST("", middleware.Permission("system:role:add", sysPermissionService), sysRoleController.Add)
			roleRouter.PUT("", middleware.Permission("system:role:edit", sysPermissionService), sysRoleController.Edit)
			roleRouter.DELETE("/:roleIds", middleware.Permission("system:role:remove", sysPermissionService), sysRoleController.Remove)
			roleRouter.PUT("/dataScope", middleware.Permission("system:role:edit", sysPermissionService), sysRoleController.DataScope)
			roleRouter.PUT("/changeStatus", middleware.Permission("system:role:edit", sysPermissionService), sysRoleController.ChangeStatus)
			roleRouter.GET("/deptTree/:roleId", sysRoleController.DeptTree)
			roleRouter.GET("/export", middleware.Permission("system:role:export", sysPermissionService), sysRoleController.Export)
		}

		// 岗位管理
		postRouter := systemRouter.Group("/post")
		{
			postRouter.GET("/list", middleware.Permission("system:post:list", sysPermissionService), sysPostController.List)
			postRouter.GET("/:postId", middleware.Permission("system:post:query", sysPermissionService), sysPostController.GetInfo)
			postRouter.POST("", middleware.Permission("system:post:add", sysPermissionService), sysPostController.Add)
			postRouter.PUT("", middleware.Permission("system:post:edit", sysPermissionService), sysPostController.Edit)
			postRouter.DELETE("/:postIds", middleware.Permission("system:post:remove", sysPermissionService), sysPostController.Remove)
		}

		// 字典类型
		dictTypeRouter := systemRouter.Group("/dict/type")
		{
			dictTypeRouter.GET("/list", middleware.Permission("system:dict:list", sysPermissionService), sysDictTypeController.List)
			dictTypeRouter.GET("/:dictId", middleware.Permission("system:dict:query", sysPermissionService), sysDictTypeController.GetInfo)
			dictTypeRouter.POST("", middleware.Permission("system:dict:add", sysPermissionService), sysDictTypeController.Add)
			dictTypeRouter.PUT("", middleware.Permission("system:dict:edit", sysPermissionService), sysDictTypeController.Edit)
			dictTypeRouter.DELETE("/:dictIds", middleware.Permission("system:dict:remove", sysPermissionService), sysDictTypeController.Remove)
			dictTypeRouter.GET("/optionselect", sysDictTypeController.OptionSelect)
			dictTypeRouter.DELETE("/refreshCache", sysDictTypeController.RefreshCache)
			dictTypeRouter.GET("/export", middleware.Permission("system:dict:export", sysPermissionService), sysDictTypeController.Export)
		}

		// 字典数据
		dictDataRouter := systemRouter.Group("/dict/data")
		{
			dictDataRouter.GET("/list", middleware.Permission("system:dict:list", sysPermissionService), sysDictDataController.List)
			dictDataRouter.GET("/type/:dictType", sysDictDataController.DictType)
			dictDataRouter.GET("/:dictCode", middleware.Permission("system:dict:query", sysPermissionService), sysDictDataController.GetInfo)
			dictDataRouter.POST("", middleware.Permission("system:dict:add", sysPermissionService), sysDictDataController.Add)
			dictDataRouter.PUT("", middleware.Permission("system:dict:edit", sysPermissionService), sysDictDataController.Edit)
			dictDataRouter.DELETE("/:dictCodes", middleware.Permission("system:dict:remove", sysPermissionService), sysDictDataController.Remove)
			dictDataRouter.GET("/export", middleware.Permission("system:dict:export", sysPermissionService), sysDictDataController.Export)
		}

		// 参数设置
		configRouter := systemRouter.Group("/config")
		{
			configRouter.GET("/list", middleware.Permission("system:config:list", sysPermissionService), sysConfigController.List)
			configRouter.GET("/:configId", middleware.Permission("system:config:query", sysPermissionService), sysConfigController.GetInfo)
			configRouter.GET("/configKey/:configKey", sysConfigController.GetConfigKey)
			configRouter.POST("", middleware.Permission("system:config:add", sysPermissionService), sysConfigController.Add)
			configRouter.PUT("", middleware.Permission("system:config:edit", sysPermissionService), sysConfigController.Edit)
			configRouter.DELETE("/:configIds", middleware.Permission("system:config:remove", sysPermissionService), sysConfigController.Remove)
			configRouter.DELETE("/refreshCache", middleware.Permission("system:config:remove", sysPermissionService), sysConfigController.RefreshCache)
			configRouter.GET("/export", middleware.Permission("system:config:export", sysPermissionService), sysConfigController.Export)
		}

		// 通知公告
		noticeRouter := systemRouter.Group("/notice")
		{
			noticeRouter.GET("/list", middleware.Permission("system:notice:list", sysPermissionService), sysNoticeController.List)
			noticeRouter.GET("/:noticeId", middleware.Permission("system:notice:query", sysPermissionService), sysNoticeController.GetInfo)
			noticeRouter.POST("", middleware.Permission("system:notice:add", sysPermissionService), sysNoticeController.Add)
			noticeRouter.PUT("", middleware.Permission("system:notice:edit", sysPermissionService), sysNoticeController.Edit)
			noticeRouter.DELETE("/:noticeIds", middleware.Permission("system:notice:remove", sysPermissionService), sysNoticeController.Remove)
		}
	}
}
