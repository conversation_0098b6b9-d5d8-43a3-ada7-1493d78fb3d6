package router

import (
	"backend/internal/api/controller/tool"
	"backend/internal/api/middleware"
	"backend/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterToolRouter 注册工具相关路由
func RegisterToolRouter(r *gin.RouterGroup, db *gorm.DB, tokenService service.TokenService) {
	// 创建控制器
	swaggerController := tool.NewSwaggerController()
	buildController := tool.NewBuildController()

	// 注册Swagger路由
	r.GET("/tool/swagger", swaggerController.Redirect)

	// 表单构建器路由
	buildGroup := r.Group("/tool/build").Use(middleware.Auth(tokenService))
	{
		buildGroup.GET("/index", buildController.Index)
		buildGroup.GET("/preview", buildController.Preview)
		buildGroup.POST("/generateForm", buildController.GenerateForm)
	}

	// 代码生成器路由已在gen_router.go中注册
}
