package bootstrap

import (
	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/utils"
	"backend/internal/utils/logger"
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// Setup 初始化系统
func Setup() error {
	// 初始化配置
	config.Init("")

	// 初始化日志
	logger.Setup()

	// 初始化数据库
	if err := database.Setup(); err != nil {
		return err
	}

	// 启动性能监控
	startPerformanceMonitoring()

	return nil
}

// Shutdown 系统关闭
func Shutdown() {
	// 关闭数据库连接
	if err := database.Close(); err != nil {
		log.Printf("关闭数据库连接出错: %v", err)
	}

	// 清理资源
	cleanup()
}

// HandleShutdown 处理系统关闭信号
func HandleShutdown(onShutdown func()) {
	// 创建一个用于接收系统信号的通道
	c := make(chan os.Signal, 1)
	// 监听系统中断信号和终止信号
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// 等待信号
	go func() {
		<-c
		log.Println("系统正在关闭...")

		// 执行自定义关闭函数
		if onShutdown != nil {
			onShutdown()
		}

		// 执行系统关闭
		Shutdown()

		log.Println("系统已安全关闭")
		os.Exit(0)
	}()
}

// 启动性能监控
func startPerformanceMonitoring() {
	// 获取全局性能指标实例
	metrics := utils.GetGlobalMetrics()

	// 每分钟收集一次指标
	metrics.StartMonitoring(time.Minute)

	// 定期输出性能指标
	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			metrics.PrintMetrics()
		}
	}()

	// 监控内存使用情况，当超过阈值时进行优化
	go monitorMemory()

	log.Println("性能监控已启动")
}

// 监控内存使用情况
func monitorMemory() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		metrics := utils.GetGlobalMetrics()
		allocated, _ := metrics.GetMemoryUsage()

		// 如果内存分配超过1GB，触发内存优化
		if allocated > 1024*1024*1024 {
			log.Println("检测到内存使用过高，执行内存优化...")
			utils.OptimizeMemory()
		}
	}
}

// cleanup 清理资源
func cleanup() {
	// 清理临时文件
	// 关闭连接
	// 释放资源
	log.Println("系统资源已清理")
}

// WaitForSignal 等待信号
func WaitForSignal(ctx context.Context) {
	// 创建一个用于接收系统信号的通道
	c := make(chan os.Signal, 1)
	// 监听系统中断信号和终止信号
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	select {
	case <-c:
		log.Println("收到系统信号，正在关闭...")
	case <-ctx.Done():
		log.Println("上下文已取消，正在关闭...")
	}

	// 执行系统关闭
	Shutdown()
}
