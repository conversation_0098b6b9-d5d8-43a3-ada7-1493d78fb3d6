package response

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ResponseData 通用响应结构
type ResponseData struct {
	Code      int         `json:"code"`      // 响应码
	Msg       string      `json:"msg"`       // 响应消息
	Data      interface{} `json:"data"`      // 响应数据
	Rows      interface{} `json:"rows"`      // 数据行
	Total     int64       `json:"total"`     // 总数
	Timestamp int64       `json:"timestamp"` // 时间戳
}

// Response 统一响应结构体
type Response struct {
	Code      int         `json:"code"`
	Msg       string      `json:"msg"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"` // 时间戳
}

// TableResponse 表格响应结构体
type TableResponse struct {
	Code      int         `json:"code"`
	Msg       string      `json:"msg"`
	Rows      interface{} `json:"rows"`
	Total     int64       `json:"total"`
	Timestamp int64       `json:"timestamp"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.J<PERSON>N(http.StatusOK, Response{
		Code:      200,
		Msg:       "操作成功",
		Data:      data,
		Timestamp: time.Now().Unix(),
	})
}

// SuccessData 成功响应带数据
func SuccessData(c *gin.Context, data interface{}) {
	var total int64
	if rows, ok := data.([]interface{}); ok {
		total = int64(len(rows))
	}

	c.JSON(http.StatusOK, TableResponse{
		Code:      200,
		Msg:       "操作成功",
		Rows:      data,
		Total:     total,
		Timestamp: time.Now().Unix(),
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:      http.StatusOK,
		Msg:       message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	})
}

// SuccessWithTable 表格数据响应
func SuccessWithTable(c *gin.Context, rows interface{}, total int64) {
	c.JSON(http.StatusOK, TableResponse{
		Code:      http.StatusOK,
		Msg:       "查询成功",
		Rows:      rows,
		Total:     total,
		Timestamp: time.Now().Unix(),
	})
}

// Error 错误响应
func Error(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      500,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// ErrorWithCode 错误响应带状态码
func ErrorWithCode(c *gin.Context, code int, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      code,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// ErrorWithData 错误响应带数据
func ErrorWithData(c *gin.Context, code int, msg string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:      code,
		Msg:       msg,
		Data:      data,
		Timestamp: time.Now().Unix(),
	})
}

// ParameterError 参数错误响应
func ParameterError(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      400,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      401,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      403,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// NotFound 未找到响应
func NotFound(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, Response{
		Code:      404,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// Timeout 超时响应
func Timeout(c *gin.Context, msg string) {
	if msg == "" {
		msg = "请求超时，请稍后重试"
	}
	c.JSON(http.StatusOK, Response{
		Code:      408,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// TooManyRequests 请求过多响应
func TooManyRequests(c *gin.Context, msg string) {
	if msg == "" {
		msg = "请求过于频繁，请稍后重试"
	}
	c.JSON(http.StatusOK, Response{
		Code:      429,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}

// ServerError 服务器错误响应
func ServerError(c *gin.Context, msg string) {
	if msg == "" {
		msg = "服务器内部错误，请联系管理员"
	}
	c.JSON(http.StatusOK, Response{
		Code:      500,
		Msg:       msg,
		Timestamp: time.Now().Unix(),
	})
}
