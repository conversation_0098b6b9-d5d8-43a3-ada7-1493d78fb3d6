package utils

import (
	"regexp"
	"strings"
)

// 危险字符正则表达式
var sqlInjectionPattern = regexp.MustCompile(`(?i)('|"|--|\/\*|\*\/|;|\b(select|update|delete|insert|drop|alter|exec|execute|truncate|declare|create|into)\b)`)

// EscapeSQLString 转义SQL字符串
func EscapeSQLString(value string) string {
	// 替换单引号
	value = strings.ReplaceAll(value, "'", "''")
	// 替换反斜杠
	value = strings.ReplaceAll(value, "\\", "\\\\")
	return value
}

// SanitizeSQLInput 净化SQL输入
func SanitizeSQLInput(value string) string {
	// 转义特殊字符
	return EscapeSQLString(value)
}

// CheckSQLInjection 检查SQL注入
func CheckSQLInjection(value string) bool {
	// 检查是否包含SQL注入模式
	return sqlInjectionPattern.MatchString(value)
}

// BuildSafeSQLLike 构建安全的SQL LIKE条件
func BuildSafeSQLLike(column, value string) string {
	// 转义LIKE特殊字符
	value = strings.ReplaceAll(value, "%", "\\%")
	value = strings.ReplaceAll(value, "_", "\\_")
	value = EscapeSQLString(value)
	return column + " LIKE '%" + value + "%'"
}

// SafeSQLOrderBy 安全的SQL排序
func SafeSQLOrderBy(column, direction string) string {
	// 验证排序方向
	direction = strings.ToUpper(direction)
	if direction != "ASC" && direction != "DESC" {
		direction = "ASC"
	}

	// 验证列名（只允许字母、数字和下划线）
	if !regexp.MustCompile(`^[a-zA-Z0-9_\.]+$`).MatchString(column) {
		// 默认排序列
		column = "id"
	}

	return column + " " + direction
}
