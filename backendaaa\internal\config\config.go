package config

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// 全局配置变量
var Global *Configuration

// Configuration 配置结构体
type Configuration struct {
	System   SystemConfig   `yaml:"system"`
	Server   ServerConfig   `yaml:"server"`
	Log      LogConfig      `yaml:"log"`
	User     UserConfig     `yaml:"user"`
	Upload   UploadConfig   `yaml:"upload"`
	Redis    RedisConfig    `yaml:"redis"`
	Token    TokenConfig    `yaml:"token"`
	Database DatabaseConfig `yaml:"database"`
	Swagger  SwaggerConfig  `yaml:"swagger"`
	Xss      XssConfig      `yaml:"xss"`
	Gen      GenConfig      `yaml:"gen"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	Name           string `yaml:"name"`
	Version        string `yaml:"version"`
	CopyrightYear  string `yaml:"copyrightYear"`
	Profile        string `yaml:"profile"`
	AddressEnabled bool   `yaml:"addressEnabled"`
	CaptchaType    string `yaml:"captchaType"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port        int    `yaml:"port"`
	ContextPath string `yaml:"contextPath"`
	RunMode     string `yaml:"runMode"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	Path       string `yaml:"path"`
	Filename   string `yaml:"filename"`
	MaxSize    int    `yaml:"maxSize"`
	MaxBackups int    `yaml:"maxBackups"`
	MaxAge     int    `yaml:"maxAge"`
	Compress   bool   `yaml:"compress"`
}

// UserConfig 用户配置
type UserConfig struct {
	Password PasswordConfig `yaml:"password"`
}

// PasswordConfig 密码配置
type PasswordConfig struct {
	MaxRetryCount int `yaml:"maxRetryCount"`
	LockTime      int `yaml:"lockTime"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxFileSize    string `yaml:"maxFileSize"`
	MaxRequestSize string `yaml:"maxRequestSize"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string     `yaml:"host"`
	Port     int        `yaml:"port"`
	Database int        `yaml:"database"`
	Password string     `yaml:"password"`
	Timeout  int        `yaml:"timeout"`
	Pool     PoolConfig `yaml:"pool"`
}

// PoolConfig Redis连接池配置
type PoolConfig struct {
	MinIdle   int `yaml:"minIdle"`
	MaxIdle   int `yaml:"maxIdle"`
	MaxActive int `yaml:"maxActive"`
	MaxWait   int `yaml:"maxWait"`
}

// TokenConfig 令牌配置
type TokenConfig struct {
	Header     string `yaml:"header"`
	Secret     string `yaml:"secret"`
	ExpireTime int    `yaml:"expireTime"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type            string `yaml:"type"`
	DriverName      string `yaml:"driverName"`
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Database        string `yaml:"database"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	Params          string `yaml:"params"`
	MaxIdleConns    int    `yaml:"maxIdleConns"`
	MaxOpenConns    int    `yaml:"maxOpenConns"`
	ConnMaxLifetime string `yaml:"connMaxLifetime"`
}

// SwaggerConfig Swagger配置
type SwaggerConfig struct {
	Enabled     bool   `yaml:"enabled"`
	PathMapping string `yaml:"pathMapping"`
}

// XssConfig XSS配置
type XssConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Excludes    string `yaml:"excludes"`
	URLPatterns string `yaml:"urlPatterns"`
}

// GenConfig 代码生成配置
type GenConfig struct {
	Author        string `yaml:"author"`
	PackageName   string `yaml:"packageName"`
	AutoRemovePre bool   `yaml:"autoRemovePre"`
	TablePrefix   string `yaml:"tablePrefix"`
}

// GetDSN 获取数据库连接字符串
func (db *DatabaseConfig) GetDSN() string {
	switch db.Type {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
			db.Username, db.Password, db.Host, db.Port, db.Database, db.Params)
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			db.Host, db.Port, db.Username, db.Password, db.Database)
	default:
		return ""
	}
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Configuration, error) {
	// 如果未指定配置文件路径，则使用默认路径
	if configPath == "" {
		dir, err := os.Getwd()
		if err != nil {
			return nil, err
		}
		configPath = filepath.Join(dir, "config", "config.yaml")
	}

	// 读取配置文件
	file, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	// 解析配置文件
	config := &Configuration{}
	err = yaml.Unmarshal(file, config)
	if err != nil {
		return nil, err
	}

	// 设置全局配置
	Global = config

	return config, nil
}

// Init 初始化配置
func Init(configPath string) {
	config, err := LoadConfig(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}
	Global = config
	log.Printf("Configuration loaded successfully. System: %s, Version: %s", config.System.Name, config.System.Version)
}

// GetGlobalConfig 获取全局配置
func GetGlobalConfig() *Configuration {
	return Global
}
