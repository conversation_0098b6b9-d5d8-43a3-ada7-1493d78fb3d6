package constants

// 缓存键常量
const (
	// 登录用户 redis key
	LOGIN_TOKEN_KEY = "login_tokens:"

	// 验证码 redis key
	CAPTCHA_CODE_KEY = "captcha_codes:"

	// 参数管理 cache key
	SYS_CONFIG_KEY = "sys_config:"

	// 字典管理 cache key
	SYS_DICT_KEY = "sys_dict:"

	// 在线用户 cache key
	ONLINE_TOKEN_KEY = "online_tokens:"

	// 密码错误次数 cache key
	PWD_ERR_CNT_KEY = "pwd_err_cnt:"

	// REPEAT_SUBMIT_KEY 防重提交 redis key
	REPEAT_SUBMIT_KEY = "repeat_submit:"

	// RATE_LIMIT_KEY 限流 redis key
	RATE_LIMIT_KEY = "rate_limit:"
)
