package database

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"backend/internal/config"
)

var (
	DB *gorm.DB
)

// 事务隔离级别
type IsolationLevel string

const (
	// 默认隔离级别
	DefaultIsolation IsolationLevel = ""
	// 读未提交
	ReadUncommitted IsolationLevel = "READ UNCOMMITTED"
	// 读已提交
	ReadCommitted IsolationLevel = "READ COMMITTED"
	// 可重复读
	RepeatableRead IsolationLevel = "REPEATABLE READ"
	// 串行化
	Serializable IsolationLevel = "SERIALIZABLE"
)

// Setup 初始化数据库连接
func Setup() error {
	var err error
	var dialector gorm.Dialector

	// 获取数据库配置
	dbConfig := config.Global.Database

	// 根据数据库类型创建对应的方言
	switch dbConfig.Type {
	case "mysql":
		dsn := dbConfig.GetDSN()
		dialector = mysql.Open(dsn)
	case "postgres":
		dsn := dbConfig.GetDSN()
		dialector = postgres.Open(dsn)
	case "sqlite":
		dsn := dbConfig.Database
		dialector = sqlite.Open(dsn)
	case "sqlserver":
		dsn := fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s&encrypt=disable",
			dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.Database)
		dialector = sqlserver.Open(dsn)
	default:
		return fmt.Errorf("unsupported database type: %s", dbConfig.Type)
	}

	// 配置日志
	newLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢SQL阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略记录未找到错误
			Colorful:                  true,        // 彩色输出
		},
	)

	// 创建GORM连接
	DB, err = gorm.Open(dialector, &gorm.Config{
		Logger: newLogger,
		// 开启预编译语句缓存，提高性能
		PrepareStmt: true,
		// 禁用默认事务，提高性能
		SkipDefaultTransaction: true,
		// 命名策略
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 获取通用数据库对象，然后使用其提供的功能
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConns)
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConns)

	// 解析连接最大生命周期
	connMaxLifetime, err := time.ParseDuration(dbConfig.ConnMaxLifetime)
	if err != nil {
		// 如果解析失败，使用默认值30分钟
		log.Printf("无法解析连接最大生命周期 %s: %v，使用默认值30分钟", dbConfig.ConnMaxLifetime, err)
		connMaxLifetime = 30 * time.Minute
	}
	sqlDB.SetConnMaxLifetime(connMaxLifetime)

	// 设置连接最大空闲时间
	sqlDB.SetConnMaxIdleTime(time.Minute * 5)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// 初始化sqlx连接
	sqlxDB := sqlx.NewDb(sqlDB, dbConfig.Type)
	if err := sqlxDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping sqlx database: %v", err)
	}

	// 设置sqlx连接
	SetupSqlx(sqlxDB)

	log.Println("Database connected successfully")
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Transaction 事务处理，支持自定义隔离级别
// fn 为事务内的处理函数
// isolationLevel 为事务隔离级别，如果为空则使用数据库默认隔离级别
func Transaction(fn func(tx *gorm.DB) error, isolationLevel ...IsolationLevel) error {
	var level IsolationLevel
	if len(isolationLevel) > 0 {
		level = isolationLevel[0]
	}

	options := &sql.TxOptions{}

	// 根据传入的隔离级别设置事务选项
	switch level {
	case ReadUncommitted:
		options.Isolation = sql.LevelReadUncommitted
	case ReadCommitted:
		options.Isolation = sql.LevelReadCommitted
	case RepeatableRead:
		options.Isolation = sql.LevelRepeatableRead
	case Serializable:
		options.Isolation = sql.LevelSerializable
	default:
		options = nil // 使用数据库默认隔离级别
	}

	// 开始事务
	tx := DB.Begin(options)
	if tx.Error != nil {
		return tx.Error
	}

	// 设置事务上下文，用于在嵌套事务中传递
	ctx := context.WithValue(context.Background(), "tx", tx)
	tx = tx.WithContext(ctx)

	defer func() {
		// 发生panic时回滚事务
		if r := recover(); r != nil {
			tx.Rollback()
			// 重新抛出panic
			panic(r)
		}
	}()

	// 执行事务函数
	if err := fn(tx); err != nil {
		// 发生错误时回滚事务
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// TransactionWithPropagation 支持事务传播特性的事务处理
// 模拟Spring的事务传播行为
func TransactionWithPropagation(ctx context.Context, fn func(tx *gorm.DB) error) error {
	// 检查上下文中是否已存在事务
	existingTx, exists := ctx.Value("tx").(*gorm.DB)
	if exists && existingTx != nil {
		// 如果已存在事务，直接使用现有事务
		return fn(existingTx)
	}

	// 如果不存在事务，创建新事务
	return Transaction(fn)
}
