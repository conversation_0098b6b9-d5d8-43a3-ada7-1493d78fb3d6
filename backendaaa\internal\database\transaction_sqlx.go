package database

import (
	"errors"
	"log"

	"github.com/jmoiron/sqlx"
)

var (
	SqlxDB *sqlx.DB
	// ErrNoDB 数据库连接不存在错误
	ErrNoDB = errors.New("sqlx database connection not established")
)

// SetupSqlx 初始化 sqlx 连接
func SetupSqlx(db *sqlx.DB) {
	SqlxDB = db
}

// GetSqlxDB 获取 sqlx 数据库连接
func GetSqlxDB() *sqlx.DB {
	return SqlxDB
}

// SQLxTransaction 执行 sqlx 事务
func SQLxTransaction(fn func(tx *sqlx.Tx) error) error {
	if SqlxDB == nil {
		return ErrNoDB
	}

	// 开始事务
	tx, err := SqlxDB.Beginx()
	if err != nil {
		log.Printf("开始事务失败: %v", err)
		return err
	}

	// 设置 defer 用于回滚事务
	defer func() {
		// 发生 panic 时回滚事务
		if r := recover(); r != nil {
			tx.Rollback()
			// 重新抛出 panic
			panic(r)
		}
	}()

	// 执行事务函数
	if err := fn(tx); err != nil {
		// 发生错误时回滚事务
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit()
}
