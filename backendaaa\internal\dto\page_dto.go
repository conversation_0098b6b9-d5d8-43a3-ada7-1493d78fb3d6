package dto

// PageQuery 分页查询参数
type PageQuery struct {
	// 页码
	PageNum int `json:"pageNum" form:"pageNum"`
	// 每页记录数
	PageSize int `json:"pageSize" form:"pageSize"`
	// 排序列
	OrderByColumn string `json:"orderByColumn" form:"orderByColumn"`
	// 排序的方向desc或者asc
	IsAsc string `json:"isAsc" form:"isAsc"`
}

// TableDataInfo 表格分页数据对象
type TableDataInfo struct {
	// 总记录数
	Total int64 `json:"total"`
	// 列表数据
	Rows interface{} `json:"rows"`
	// 消息状态码
	Code int `json:"code"`
	// 消息内容
	Msg string `json:"msg"`
}

// NewTableDataInfo 创建分页数据对象
func NewTableDataInfo(rows interface{}, total int64) *TableDataInfo {
	return &TableDataInfo{
		Total: total,
		Rows:  rows,
		Code:  200,
		Msg:   "查询成功",
	}
}
