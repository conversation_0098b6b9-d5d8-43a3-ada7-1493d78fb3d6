package dto

// ResponseDTO 通用响应信息
type ResponseDTO struct {
	// 状态码
	Code int `json:"code"`
	// 返回内容
	Msg string `json:"msg"`
	// 数据对象
	Data interface{} `json:"data"`
}

// SuccessDTO 返回成功消息
func SuccessDTO(msg string) *ResponseDTO {
	return &ResponseDTO{
		Code: 200,
		Msg:  msg,
		Data: nil,
	}
}

// SuccessWithDataDTO 返回成功数据
func SuccessWithDataDTO(data interface{}) *ResponseDTO {
	return &ResponseDTO{
		Code: 200,
		Msg:  "操作成功",
		Data: data,
	}
}

// ErrorDTO 返回错误消息
func ErrorDTO(msg string) *ResponseDTO {
	return &ResponseDTO{
		Code: 500,
		Msg:  msg,
		Data: nil,
	}
}

// CustomResponseDTO 自定义返回
func CustomResponseDTO(code int, msg string, data interface{}) *ResponseDTO {
	return &ResponseDTO{
		Code: code,
		Msg:  msg,
		Data: data,
	}
}
