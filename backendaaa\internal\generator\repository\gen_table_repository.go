package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// GenTableRepository 代码生成表数据访问接口
type GenTableRepository interface {
	// SelectGenTableList 查询业务列表
	SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error)

	// SelectGenTableById 查询业务信息
	SelectGenTableById(id int64) (*model.GenTable, error)

	// SelectGenTableByName 查询表名称业务信息
	SelectGenTableByName(tableName string) (*model.GenTable, error)

	// SelectGenTableAll 查询所有表信息
	SelectGenTableAll() ([]*model.GenTable, error)

	// InsertGenTable 新增业务
	InsertGenTable(genTable *model.GenTable) error

	// UpdateGenTable 修改业务
	UpdateGenTable(genTable *model.GenTable) error

	// DeleteGenTableByIds 批量删除业务
	DeleteGenTableByIds(ids []int64) error

	// SelectDbTableList 查询据库列表
	SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error)

	// SelectDbTableListByNames 查询据库列表
	SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error)

	// CreateTable 创建表
	CreateTable(sql string) error
}

// genTableRepository 代码生成表数据访问实现
type genTableRepository struct {
	db *gorm.DB
}

// NewGenTableRepository 创建代码生成表数据访问实现
func NewGenTableRepository(db *gorm.DB) GenTableRepository {
	return &genTableRepository{
		db: db,
	}
}

// SelectGenTableList 查询业务列表
func (r *genTableRepository) SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	var list []*model.GenTable
	query := r.db.Model(&model.GenTable{})

	if genTable.TableName != "" {
		query = query.Where("table_name like ?", "%"+genTable.TableName+"%")
	}

	if genTable.TableComment != "" {
		query = query.Where("table_comment like ?", "%"+genTable.TableComment+"%")
	}

	if genTable.CreateBy != "" {
		query = query.Where("create_by = ?", genTable.CreateBy)
	}

	err := query.Find(&list).Error
	return list, err
}

// SelectGenTableById 查询业务信息
func (r *genTableRepository) SelectGenTableById(id int64) (*model.GenTable, error) {
	var genTable model.GenTable
	err := r.db.Where("table_id = ?", id).First(&genTable).Error
	if err != nil {
		return nil, err
	}
	return &genTable, nil
}

// SelectGenTableByName 查询表名称业务信息
func (r *genTableRepository) SelectGenTableByName(tableName string) (*model.GenTable, error) {
	var genTable model.GenTable
	err := r.db.Where("table_name = ?", tableName).First(&genTable).Error
	if err != nil {
		return nil, err
	}
	return &genTable, nil
}

// SelectGenTableAll 查询所有表信息
func (r *genTableRepository) SelectGenTableAll() ([]*model.GenTable, error) {
	var list []*model.GenTable
	err := r.db.Find(&list).Error
	return list, err
}

// InsertGenTable 新增业务
func (r *genTableRepository) InsertGenTable(genTable *model.GenTable) error {
	return r.db.Create(genTable).Error
}

// UpdateGenTable 修改业务
func (r *genTableRepository) UpdateGenTable(genTable *model.GenTable) error {
	return r.db.Save(genTable).Error
}

// DeleteGenTableByIds 批量删除业务
func (r *genTableRepository) DeleteGenTableByIds(ids []int64) error {
	return r.db.Where("table_id in ?", ids).Delete(&model.GenTable{}).Error
}

// SelectDbTableList 查询数据库表列表
func (r *genTableRepository) SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	// 这里需要根据不同数据库类型实现不同的查询逻辑
	// 以SQL Server为例
	var tables []*model.GenTable

	// 构建查询SQL
	query := `SELECT
		obj.name AS table_name,
		ISNULL(ep.[value], '') AS table_comment
	FROM
		sys.objects obj
	LEFT JOIN sys.extended_properties ep ON ep.major_id = obj.object_id AND ep.minor_id = 0 AND ep.class = 1
	WHERE
		obj.type = 'U'
		AND obj.is_ms_shipped = 0
		AND obj.name NOT LIKE 'sys%'
		AND obj.name NOT IN (SELECT table_name FROM gen_table)`

	// 添加查询条件
	params := []interface{}{}
	if genTable.TableName != "" {
		query += " AND obj.name LIKE ?"
		params = append(params, "%"+genTable.TableName+"%")
	}
	if genTable.TableComment != "" {
		query += " AND ISNULL(ep.[value], '') LIKE ?"
		params = append(params, "%"+genTable.TableComment+"%")
	}

	// 执行查询
	rows, err := r.db.Raw(query, params...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	for rows.Next() {
		var tableName, tableComment string
		if err := rows.Scan(&tableName, &tableComment); err != nil {
			return nil, err
		}
		tables = append(tables, &model.GenTable{
			TableName:    tableName,
			TableComment: tableComment,
		})
	}

	return tables, nil
}

// SelectDbTableListByNames 根据表名查询数据库表列表
func (r *genTableRepository) SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error) {
	// 这里需要根据不同数据库类型实现不同的查询逻辑
	// 以SQL Server为例
	var tables []*model.GenTable

	if len(tableNames) == 0 {
		return tables, nil
	}

	// 构建查询SQL
	query := `SELECT
		obj.name AS table_name,
		ISNULL(ep.[value], '') AS table_comment
	FROM
		sys.objects obj
	LEFT JOIN sys.extended_properties ep ON ep.major_id = obj.object_id AND ep.minor_id = 0 AND ep.class = 1
	WHERE
		obj.type = 'U'
		AND obj.is_ms_shipped = 0
		AND obj.name NOT LIKE 'sys%'
		AND obj.name IN (?)`

	// 执行查询
	rows, err := r.db.Raw(query, tableNames).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	for rows.Next() {
		var tableName, tableComment string
		if err := rows.Scan(&tableName, &tableComment); err != nil {
			return nil, err
		}
		tables = append(tables, &model.GenTable{
			TableName:    tableName,
			TableComment: tableComment,
		})
	}

	return tables, nil
}

// CreateTable 创建表
func (r *genTableRepository) CreateTable(sql string) error {
	return r.db.Exec(sql).Error
}
