package controller

import (
	"backend/internal/common/response"
	"backend/internal/constants"
	"backend/internal/job/service"
	"backend/internal/model"
	"bytes"
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SysJobController 定时任务控制器
type SysJobController struct {
	jobService service.SysJobService
}

// NewSysJobController 创建定时任务控制器
func NewSysJobController(jobService service.SysJobService) *SysJobController {
	return &SysJobController{
		jobService: jobService,
	}
}

// List 获取定时任务列表
// @Summary 获取定时任务列表
// @Description 获取定时任务列表
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobName query string false "任务名称"
// @Param jobGroup query string false "任务组名"
// @Param status query string false "状态"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/list [get]
func (c *SysJobController) List(ctx *gin.Context) {
	job := &model.SysJob{
		JobName:  ctx.Query("jobName"),
		JobGroup: ctx.Query("jobGroup"),
		Status:   ctx.Query("status"),
	}

	list, err := c.jobService.SelectJobList(job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, list)
}

// GetInfo 获取定时任务详细信息
// @Summary 获取定时任务详细信息
// @Description 获取定时任务详细信息
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobId path int true "任务ID"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/{jobId} [get]
func (c *SysJobController) GetInfo(ctx *gin.Context) {
	jobId, err := strconv.ParseInt(ctx.Param("jobId"), 10, 64)
	if err != nil {
		response.Error(ctx, "任务ID无效")
		return
	}

	job, err := c.jobService.SelectJobById(jobId)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, job)
}

// Add 新增定时任务
// @Summary 新增定时任务
// @Description 新增定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job [post]
func (c *SysJobController) Add(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.ValidateCronExpression(job.CronExpression) {
		response.Error(ctx, "Cron表达式无效")
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		response.Error(ctx, fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName))
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		response.Error(ctx, fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName))
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.CreateBy = username.(string)
	}

	err := c.jobService.InsertJob(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Edit 修改定时任务
// @Summary 修改定时任务
// @Description 修改定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job [put]
func (c *SysJobController) Edit(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.ValidateCronExpression(job.CronExpression) {
		response.Error(ctx, "Cron表达式无效")
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		response.Error(ctx, fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName))
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		response.Error(ctx, fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName))
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.UpdateBy = username.(string)
	}

	err := c.jobService.UpdateJob(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Remove 删除定时任务
// @Summary 删除定时任务
// @Description 删除定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobIds path string true "任务ID，多个以逗号分隔"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/{jobIds} [delete]
func (c *SysJobController) Remove(ctx *gin.Context) {
	jobIds := ctx.Param("jobIds")
	ids := strings.Split(jobIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		jobId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			continue
		}
		idList = append(idList, jobId)
	}

	err := c.jobService.DeleteJobByIds(idList)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ChangeStatus 修改定时任务状态
// @Summary 修改定时任务状态
// @Description 修改定时任务状态
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/changeStatus [put]
func (c *SysJobController) ChangeStatus(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	err := c.jobService.ChangeStatus(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Run 立即执行一次定时任务
// @Summary 立即执行一次定时任务
// @Description 立即执行一次定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/run [put]
func (c *SysJobController) Run(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	err := c.jobService.Run(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// CheckCronExpressionIsValid 校验Cron表达式
// @Summary 校验Cron表达式
// @Description 校验Cron表达式
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param expression query string true "Cron表达式"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/checkCronExpressionIsValid [get]
func (c *SysJobController) CheckCronExpressionIsValid(ctx *gin.Context) {
	expression := ctx.Query("expression")
	valid := c.jobService.ValidateCronExpression(expression)
	response.Success(ctx, valid)
}

// Export 导出任务
// @Summary 导出任务
// @Description 导出任务
// @Tags 定时任务
// @Accept json
// @Produce octet-stream
// @Param jobName query string false "任务名称"
// @Param jobGroup query string false "任务组名"
// @Param status query string false "状态"
// @Success 200 {octet-stream} []byte "成功"
// @Router /monitor/job/export [post]
func (c *SysJobController) Export(ctx *gin.Context) {
	job := &model.SysJob{
		JobName:  ctx.Query("jobName"),
		JobGroup: ctx.Query("jobGroup"),
		Status:   ctx.Query("status"),
	}

	list, err := c.jobService.SelectJobList(job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 创建CSV文件
	buffer := &bytes.Buffer{}
	writer := csv.NewWriter(buffer)

	// 写入CSV表头
	header := []string{"任务ID", "任务名称", "任务组名", "调用目标字符串", "Cron执行表达式", "状态", "创建者", "创建时间", "更新者", "更新时间", "备注"}
	if err := writer.Write(header); err != nil {
		response.Error(ctx, fmt.Sprintf("写入CSV头失败: %v", err))
		return
	}

	// 写入数据
	for _, job := range list {
		statusText := "正常"
		if job.Status == "1" {
			statusText = "暂停"
		}

		createTime := ""
		if job.CreateTime != nil {
			createTime = job.CreateTime.Format("2006-01-02 15:04:05")
		}

		updateTime := ""
		if job.UpdateTime != nil {
			updateTime = job.UpdateTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(job.JobID, 10),
			job.JobName,
			job.JobGroup,
			job.InvokeTarget,
			job.CronExpression,
			statusText,
			job.CreateBy,
			createTime,
			job.UpdateBy,
			updateTime,
			job.Remark,
		}
		if err := writer.Write(row); err != nil {
			response.Error(ctx, fmt.Sprintf("写入CSV数据失败: %v", err))
			return
		}
	}

	writer.Flush()

	// 设置响应头
	filename := "job_" + time.Now().Format("20060102150405") + ".csv"
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	ctx.Data(http.StatusOK, "text/csv", buffer.Bytes())
}
