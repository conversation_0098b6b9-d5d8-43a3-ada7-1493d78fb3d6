package controller

import (
	"backend/internal/common/response"
	"backend/internal/job/service"
	"backend/internal/model"
	"bytes"
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SysJobLogController 定时任务日志控制器
type SysJobLogController struct {
	jobLogService service.SysJobLogService
}

// NewSysJobLogController 创建定时任务日志控制器
func NewSysJobLogController(jobLogService service.SysJobLogService) *SysJobLogController {
	return &SysJobLogController{
		jobLogService: jobLogService,
	}
}

// List 获取定时任务调度日志列表
// @Summary 获取定时任务调度日志列表
// @Description 获取定时任务调度日志列表
// @Tags 定时任务日志
// @Accept json
// @Produce json
// @Param jobName query string false "任务名称"
// @Param jobGroup query string false "任务组名"
// @Param status query string false "状态"
// @Success 200 {object} response.ResponseData
// @Router /monitor/jobLog/list [get]
func (c *SysJobLogController) List(ctx *gin.Context) {
	jobLog := &model.SysJobLog{
		JobName:  ctx.Query("jobName"),
		JobGroup: ctx.Query("jobGroup"),
		Status:   ctx.Query("status"),
	}

	list, err := c.jobLogService.SelectJobLogList(jobLog)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, list)
}

// GetInfo 获取定时任务日志详细信息
// @Summary 获取定时任务日志详细信息
// @Description 获取定时任务日志详细信息
// @Tags 定时任务日志
// @Accept json
// @Produce json
// @Param jobLogId path int true "调度日志ID"
// @Success 200 {object} response.ResponseData
// @Router /monitor/jobLog/{jobLogId} [get]
func (c *SysJobLogController) GetInfo(ctx *gin.Context) {
	jobLogId, err := strconv.ParseInt(ctx.Param("jobLogId"), 10, 64)
	if err != nil {
		response.Error(ctx, "调度日志ID无效")
		return
	}

	info, err := c.jobLogService.SelectJobLogById(jobLogId)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, info)
}

// Remove 删除定时任务调度日志
// @Summary 删除定时任务调度日志
// @Description 删除定时任务调度日志
// @Tags 定时任务日志
// @Accept json
// @Produce json
// @Param jobLogIds path string true "调度日志ID，多个以逗号分隔"
// @Success 200 {object} response.ResponseData
// @Router /monitor/jobLog/{jobLogIds} [delete]
func (c *SysJobLogController) Remove(ctx *gin.Context) {
	jobLogIds := ctx.Param("jobLogIds")
	ids := strings.Split(jobLogIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		jobLogId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			continue
		}
		idList = append(idList, jobLogId)
	}

	count, err := c.jobLogService.DeleteJobLogByIds(idList)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, count)
}

// Clean 清空定时任务调度日志
// @Summary 清空定时任务调度日志
// @Description 清空定时任务调度日志
// @Tags 定时任务日志
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseData
// @Router /monitor/jobLog/clean [delete]
func (c *SysJobLogController) Clean(ctx *gin.Context) {
	err := c.jobLogService.CleanJobLog()
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Export 导出任务日志
// @Summary 导出任务日志
// @Description 导出任务日志
// @Tags 定时任务日志
// @Accept json
// @Produce octet-stream
// @Param jobName query string false "任务名称"
// @Param jobGroup query string false "任务组名"
// @Param status query string false "状态"
// @Param beginTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Success 200 {octet-stream} []byte "成功"
// @Router /monitor/jobLog/export [post]
func (c *SysJobLogController) Export(ctx *gin.Context) {
	jobLog := &model.SysJobLog{
		JobName:  ctx.Query("jobName"),
		JobGroup: ctx.Query("jobGroup"),
		Status:   ctx.Query("status"),
	}

	list, err := c.jobLogService.SelectJobLogList(jobLog)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 创建CSV文件
	buffer := &bytes.Buffer{}
	writer := csv.NewWriter(buffer)

	// 写入CSV表头
	header := []string{"日志序号", "任务名称", "任务组名", "调用目标字符串", "日志信息", "执行状态", "异常信息", "开始时间", "停止时间"}
	if err := writer.Write(header); err != nil {
		response.Error(ctx, fmt.Sprintf("写入CSV头失败: %v", err))
		return
	}

	// 写入数据
	for _, log := range list {
		statusText := "成功"
		if log.Status == "1" {
			statusText = "失败"
		}

		startTime := ""
		if log.StartTime != nil {
			startTime = log.StartTime.Format("2006-01-02 15:04:05")
		}

		stopTime := ""
		if log.StopTime != nil {
			stopTime = log.StopTime.Format("2006-01-02 15:04:05")
		}

		row := []string{
			strconv.FormatInt(log.JobLogID, 10),
			log.JobName,
			log.JobGroup,
			log.InvokeTarget,
			log.JobMessage,
			statusText,
			log.ExceptionInfo,
			startTime,
			stopTime,
		}
		if err := writer.Write(row); err != nil {
			response.Error(ctx, fmt.Sprintf("写入CSV数据失败: %v", err))
			return
		}
	}

	writer.Flush()

	// 设置响应头
	filename := "job_log_" + time.Now().Format("20060102150405") + ".csv"
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	ctx.Data(http.StatusOK, "text/csv", buffer.Bytes())
}
