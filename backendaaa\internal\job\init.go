package job

import (
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils/logger"
	"context"
	"sync"
)

var (
	scheduler     *JobScheduler
	once          sync.Once
	globalContext context.Context
	cancelFunc    context.CancelFunc
)

// Setup 初始化任务调度器
func Setup(jobService service.SysJobService, jobLogService service.SysJobLogService) {
	once.Do(func() {
		globalContext, cancelFunc = context.WithCancel(context.Background())

		// 创建调度器
		scheduler = NewJobScheduler(jobService, jobLogService, logger.Logger)

		// 初始化GetJobScheduler函数
		model.GetJobScheduler = func() model.JobScheduler {
			return scheduler
		}

		// 启动调度器
		if err := scheduler.Start(globalContext); err != nil {
			logger.Error("启动任务调度器失败: " + err.Error())
		} else {
			logger.Info("任务调度器已启动")
		}
	})
}

// Shutdown 关闭任务调度器
func Shutdown() {
	if cancelFunc != nil {
		cancelFunc()
		logger.Info("任务调度器已关闭")
	}
}

// GetScheduler 获取任务调度器实例
func GetScheduler() *JobScheduler {
	return scheduler
}
