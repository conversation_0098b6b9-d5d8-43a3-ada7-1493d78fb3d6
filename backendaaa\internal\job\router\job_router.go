package router

import (
	"backend/internal/api/middleware"
	"backend/internal/job/controller"
	"backend/internal/job/service"
	"backend/internal/job/service/impl"
	"backend/internal/repository"
	sysService "backend/internal/service/impl"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

// RegisterJobRouter 注册定时任务路由
func RegisterJobRouter(r *gin.RouterGroup, db *gorm.DB) {
	// 创建空的服务实现
	tokenService := sysService.NewEmptyTokenService()
	permissionService := &sysService.EmptyPermissionService{}

	// 创建仓库
	jobRepo := repository.NewSysJobRepository(db)
	jobLogRepo := repository.NewSysJobLogRepository(db)

	// 创建任务服务
	jobLogService := impl.NewSysJobLogService(jobLogRepo)
	jobService := service.NewSysJobService(jobRepo)

	// 创建控制器
	jobController := controller.NewSysJobController(jobService)
	jobLogController := controller.NewSysJobLogController(jobLogService)

	// 任务调度
	jobGroup := r.Group("/monitor/job").Use(middleware.Auth(tokenService))
	{
		// 任务管理
		jobGroup.GET("/list", middleware.Permission("monitor:job:list", permissionService), jobController.List)
		jobGroup.GET("/:jobId", middleware.Permission("monitor:job:query", permissionService), jobController.GetInfo)
		jobGroup.POST("", middleware.Permission("monitor:job:add", permissionService), jobController.Add)
		jobGroup.PUT("", middleware.Permission("monitor:job:edit", permissionService), jobController.Edit)
		jobGroup.DELETE("/:jobIds", middleware.Permission("monitor:job:remove", permissionService), jobController.Remove)
		jobGroup.PUT("/changeStatus", middleware.Permission("monitor:job:changeStatus", permissionService), jobController.ChangeStatus)
		jobGroup.POST("/run", middleware.Permission("monitor:job:changeStatus", permissionService), jobController.Run)
		jobGroup.GET("/checkCronExpressionIsValid", jobController.CheckCronExpressionIsValid)
	}

	// 任务调度日志
	jobLogRouter := r.Group("/monitor/jobLog").Use(middleware.Auth(tokenService))
	{
		jobLogRouter.GET("/list", middleware.Permission("monitor:job:list", permissionService), jobLogController.List)
		jobLogRouter.GET("/:jobLogId", middleware.Permission("monitor:job:query", permissionService), jobLogController.GetInfo)
		jobLogRouter.DELETE("/:jobLogIds", middleware.Permission("monitor:job:remove", permissionService), jobLogController.Remove)
		jobLogRouter.POST("/clean", middleware.Permission("monitor:job:remove", permissionService), jobLogController.Clean)
		jobLogRouter.GET("/export", middleware.Permission("monitor:job:export", permissionService), jobLogController.Export)
	}
}
