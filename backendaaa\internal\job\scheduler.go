package job

import (
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// JobScheduler 定时任务调度器
type JobScheduler struct {
	cron          *cron.Cron
	jobEntryMap   map[int64]cron.EntryID
	jobService    service.SysJobService
	jobLogService service.SysJobLogService
	mutex         sync.RWMutex
	logger        *zap.Logger
}

// NewJobScheduler 创建定时任务调度器
func NewJobScheduler(jobService service.SysJobService, jobLogService service.SysJobLogService, logger *zap.Logger) *JobScheduler {
	// 创建带秒级精度的cron调度器
	cronScheduler := cron.New(cron.WithSeconds())

	return &JobScheduler{
		cron:          cronScheduler,
		jobEntryMap:   make(map[int64]cron.EntryID),
		jobService:    jobService,
		jobLogService: jobLogService,
		logger:        logger,
	}
}

// Start 启动任务调度器
func (js *JobScheduler) Start(ctx context.Context) error {
	// 加载所有启用的任务
	jobs, err := js.jobService.SelectJobList(&model.SysJob{Status: "0"})
	if err != nil {
		return fmt.Errorf("加载定时任务失败: %v", err)
	}

	// 添加所有任务到调度器
	for _, job := range jobs {
		if err := js.AddJob(job); err != nil {
			js.logger.Error("添加定时任务失败",
				zap.Int64("jobId", job.JobID),
				zap.String("jobName", job.JobName),
				zap.Error(err))
		}
	}

	// 启动调度器
	js.cron.Start()
	js.logger.Info("任务调度器已启动")

	// 监听上下文取消信号
	go func() {
		<-ctx.Done()
		js.Stop()
	}()

	return nil
}

// Stop 停止任务调度器
func (js *JobScheduler) Stop() {
	js.logger.Info("正在停止任务调度器...")
	js.cron.Stop()
	js.logger.Info("任务调度器已停止")
}

// AddJob 添加定时任务
func (js *JobScheduler) AddJob(job *model.SysJob) error {
	if job == nil {
		return fmt.Errorf("任务不能为空")
	}

	// 校验Cron表达式
	if !utils.ValidCronExpression(job.CronExpression) {
		return fmt.Errorf("无效的Cron表达式: %s", job.CronExpression)
	}

	// 检查任务是否已存在
	js.mutex.RLock()
	_, exists := js.jobEntryMap[job.JobID]
	js.mutex.RUnlock()
	if exists {
		// 先移除现有的任务
		js.RemoveJob(job.JobID)
	}

	// 创建任务执行函数
	jobFunc := func() {
		js.executeJob(job)
	}

	// 添加到cron调度器
	entryID, err := js.cron.AddFunc(job.CronExpression, jobFunc)
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %v", err)
	}

	// 记录任务ID和EntryID的映射关系
	js.mutex.Lock()
	js.jobEntryMap[job.JobID] = entryID
	js.mutex.Unlock()

	js.logger.Info("定时任务已添加",
		zap.Int64("jobId", job.JobID),
		zap.String("jobName", job.JobName),
		zap.String("cronExpression", job.CronExpression))

	return nil
}

// RemoveJob 移除定时任务
func (js *JobScheduler) RemoveJob(jobID int64) {
	js.mutex.RLock()
	entryID, exists := js.jobEntryMap[jobID]
	js.mutex.RUnlock()

	if exists {
		js.cron.Remove(entryID)
		js.mutex.Lock()
		delete(js.jobEntryMap, jobID)
		js.mutex.Unlock()
		js.logger.Info("定时任务已移除", zap.Int64("jobId", jobID))
	}
}

// RunJob 立即执行一次任务
func (js *JobScheduler) RunJob(job *model.SysJob) error {
	if job == nil {
		return fmt.Errorf("任务不能为空")
	}

	go js.executeJob(job)
	return nil
}

// executeJob 执行定时任务
func (js *JobScheduler) executeJob(job *model.SysJob) {
	// 记录开始时间
	startTime := time.Now()

	// 创建任务日志
	jobLog := &model.SysJobLog{
		JobName:      job.JobName,
		JobGroup:     job.JobGroup,
		InvokeTarget: job.InvokeTarget,
		JobMessage:   "任务开始执行",
		Status:       "0", // 默认成功
		StartTime:    &startTime,
	}

	// 执行任务
	js.logger.Info("开始执行定时任务",
		zap.Int64("jobId", job.JobID),
		zap.String("jobName", job.JobName))

	result := utils.InvokeMethod(job.InvokeTarget)

	// 计算执行耗时
	elapsed := time.Since(startTime)
	stopTime := time.Now()
	jobLog.StopTime = &stopTime
	jobLog.JobMessage = fmt.Sprintf("任务执行完成，耗时: %v", elapsed)

	// 处理执行结果
	if !result.Success {
		jobLog.Status = "1" // 失败
		jobLog.ExceptionInfo = "执行失败: "
		if result.Error != nil {
			jobLog.ExceptionInfo += result.Error.Error()
		} else {
			jobLog.ExceptionInfo += result.Message
		}
		js.logger.Error("定时任务执行失败",
			zap.Int64("jobId", job.JobID),
			zap.String("jobName", job.JobName),
			zap.Duration("elapsed", elapsed),
			zap.String("error", jobLog.ExceptionInfo))
	} else {
		js.logger.Info("定时任务执行成功",
			zap.Int64("jobId", job.JobID),
			zap.String("jobName", job.JobName),
			zap.Duration("elapsed", elapsed))
	}

	// 保存任务日志
	_, err := js.jobLogService.InsertJobLog(jobLog)
	if err != nil {
		js.logger.Error("保存任务日志失败",
			zap.Int64("jobId", job.JobID),
			zap.String("jobName", job.JobName),
			zap.Error(err))
	}
}
