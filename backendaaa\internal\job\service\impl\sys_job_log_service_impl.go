package impl

import (
	"backend/internal/job/service"
	"backend/internal/model"
	"backend/internal/repository"
)

// SysJobLogServiceImpl 定时任务日志服务实现
type SysJobLogServiceImpl struct {
	jobLogRepo repository.SysJobLogRepository
}

// NewSysJobLogService 创建定时任务日志服务
func NewSysJobLogService(jobLogRepo repository.SysJobLogRepository) service.SysJobLogService {
	return &SysJobLogServiceImpl{
		jobLogRepo: jobLogRepo,
	}
}

// SelectJobLogList 获取quartz调度器日志的计划任务
func (s *SysJobLogServiceImpl) SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error) {
	return s.jobLogRepo.SelectJobLogList(jobLog)
}

// SelectJobLogById 通过调度任务日志ID查询调度信息
func (s *SysJobLogServiceImpl) SelectJobLogById(jobLogId int64) (*model.SysJobLog, error) {
	return s.jobLogRepo.SelectJobLogById(jobLogId)
}

// AddJobLog 新增任务日志
func (s *SysJobLogServiceImpl) AddJobLog(jobLog *model.SysJobLog) error {
	_, err := s.jobLogRepo.InsertJobLog(jobLog)
	return err
}

// DeleteJobLogByIds 批量删除调度日志信息
func (s *SysJobLogServiceImpl) DeleteJobLogByIds(logIds []int64) (int, error) {
	return s.jobLogRepo.DeleteJobLogByIds(logIds)
}

// DeleteJobLogById 删除任务日志
func (s *SysJobLogServiceImpl) DeleteJobLogById(jobId int64) (int, error) {
	err := s.jobLogRepo.DeleteJobLogById(jobId)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// CleanJobLog 清空任务日志
func (s *SysJobLogServiceImpl) CleanJobLog() error {
	_, err := s.jobLogRepo.CleanJobLog()
	return err
}

// GetRepository 获取仓库实例
func (s *SysJobLogServiceImpl) GetRepository() interface{} {
	return s.jobLogRepo
}
