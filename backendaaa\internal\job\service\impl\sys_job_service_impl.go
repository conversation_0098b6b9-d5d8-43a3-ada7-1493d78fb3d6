package impl

import (
	"backend/internal/common/constants"
	"backend/internal/job/service"
	"backend/internal/job/util"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/utils"
	"fmt"
)

// SysJobServiceImpl 定时任务服务实现
type SysJobServiceImpl struct {
	jobRepo       repository.SysJobRepository
	jobLogRepo    repository.SysJobLogRepository
	jobLogService service.SysJobLogService
	jobScheduler  *util.JobScheduler
}

// NewSysJobService 创建定时任务服务
func NewSysJobService(jobRepo repository.SysJobRepository, jobLogService service.SysJobLogService) service.SysJobService {
	jobLogRepo, ok := jobLogService.(*SysJobLogServiceImpl).GetRepository().(repository.SysJobLogRepository)
	if !ok {
		panic("无法获取JobLogRepository")
	}

	scheduler := util.NewJobScheduler(jobRepo, jobLogRepo)
	scheduler.Start()

	// 初始化任务
	err := scheduler.InitJobs()
	if err != nil {
		fmt.Printf("初始化任务失败: %v\n", err)
	}

	return &SysJobServiceImpl{
		jobRepo:       jobRepo,
		jobLogRepo:    jobLogRepo,
		jobLogService: jobLogService,
		jobScheduler:  scheduler,
	}
}

// SelectJobList 获取任务列表
func (s *SysJobServiceImpl) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	return s.jobRepo.SelectJobList(job)
}

// SelectJobById 根据ID获取任务
func (s *SysJobServiceImpl) SelectJobById(jobId int64) (*model.SysJob, error) {
	return s.jobRepo.SelectJobById(jobId)
}

// PauseJob 暂停任务
func (s *SysJobServiceImpl) PauseJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_PAUSE
	err := s.jobRepo.UpdateJobStatus(job)
	if err != nil {
		return err
	}
	s.jobScheduler.PauseJob(job)
	return nil
}

// ResumeJob 恢复任务
func (s *SysJobServiceImpl) ResumeJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_NORMAL
	err := s.jobRepo.UpdateJobStatus(job)
	if err != nil {
		return err
	}
	return s.jobScheduler.ResumeJob(job)
}

// DeleteJob 删除任务
func (s *SysJobServiceImpl) DeleteJob(job *model.SysJob) error {
	s.jobScheduler.DeleteJob(job)
	return s.jobRepo.DeleteJobById(job.JobID)
}

// DeleteJobByIds 批量删除任务
func (s *SysJobServiceImpl) DeleteJobByIds(jobIds []int64) error {
	for _, jobId := range jobIds {
		job, err := s.jobRepo.SelectJobById(jobId)
		if err == nil && job != nil {
			s.jobScheduler.DeleteJob(job)
		}
	}
	return s.jobRepo.DeleteJobByIds(jobIds)
}

// ChangeStatus 更改任务状态
func (s *SysJobServiceImpl) ChangeStatus(job *model.SysJob) error {
	if job.Status == constants.JOB_STATUS_NORMAL {
		return s.PauseJob(job)
	} else {
		return s.ResumeJob(job)
	}
}

// Run 立即执行任务
func (s *SysJobServiceImpl) Run(job *model.SysJob) error {
	if !s.ValidateCronExpression(job.CronExpression) {
		return fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}

	jobCopy, err := s.jobRepo.SelectJobById(job.JobID)
	if err != nil {
		return err
	}
	if jobCopy == nil {
		return fmt.Errorf("任务不存在")
	}

	s.jobScheduler.RunOnce(jobCopy)
	return nil
}

// InsertJob 新增任务
func (s *SysJobServiceImpl) InsertJob(job *model.SysJob) error {
	if !s.ValidateCronExpression(job.CronExpression) {
		return fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}

	jobId, err := s.jobRepo.InsertJob(job)
	if err != nil {
		return err
	}

	job.JobID = jobId
	if job.Status == constants.JOB_STATUS_NORMAL {
		err = s.jobScheduler.CreateJob(job)
		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateJob 更新任务
func (s *SysJobServiceImpl) UpdateJob(job *model.SysJob) error {
	if !s.ValidateCronExpression(job.CronExpression) {
		return fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}

	err := s.jobRepo.UpdateJob(job)
	if err != nil {
		return err
	}

	return s.jobScheduler.UpdateJob(job)
}

// ValidateCronExpression 校验Cron表达式
func (s *SysJobServiceImpl) ValidateCronExpression(cronExpression string) bool {
	return utils.ValidCronExpression(cronExpression)
}
