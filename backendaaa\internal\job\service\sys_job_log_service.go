package service

import (
	"backend/internal/model"
)

// SysJobLogService 定时任务调度日志信息服务接口
type SysJobLogService interface {
	// SelectJobLogList 获取quartz调度器日志的计划任务
	SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error)

	// SelectJobLogById 通过调度任务日志ID查询调度信息
	SelectJobLogById(jobLogId int64) (*model.SysJobLog, error)

	// AddJobLog 新增任务日志
	AddJobLog(jobLog *model.SysJobLog) error

	// DeleteJobLogByIds 批量删除调度日志信息
	DeleteJobLogByIds(logIds []int64) (int, error)

	// DeleteJobLogById 删除任务日志
	DeleteJobLogById(jobId int64) (int, error)

	// CleanJobLog 清空任务日志
	CleanJobLog() error
}
