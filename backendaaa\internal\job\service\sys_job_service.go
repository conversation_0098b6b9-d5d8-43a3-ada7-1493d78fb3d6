package service

import (
	"backend/internal/common/constants"
	"backend/internal/database"
	"backend/internal/job/util"
	"backend/internal/model"
	"backend/internal/repository"
	"fmt"
)

// SysJobService 定时任务服务接口
type SysJobService interface {
	// SelectJobList 查询任务列表
	SelectJobList(job *model.SysJob) ([]*model.SysJob, error)

	// SelectJobById 通过ID查询任务
	SelectJobById(jobId int64) (*model.SysJob, error)

	// PauseJob 暂停任务
	PauseJob(job *model.SysJob) error

	// ResumeJob 恢复任务
	ResumeJob(job *model.SysJob) error

	// DeleteJob 删除任务
	DeleteJob(job *model.SysJob) error

	// DeleteJobByIds 批量删除任务
	DeleteJobByIds(jobIds []int64) error

	// ChangeStatus 更改任务状态
	ChangeStatus(job *model.SysJob) error

	// Run 立即运行任务
	Run(job *model.SysJob) error

	// InsertJob 新增任务
	InsertJob(job *model.SysJob) error

	// UpdateJob 修改任务
	UpdateJob(job *model.SysJob) error

	// ValidateCronExpression 校验Cron表达式
	ValidateCronExpression(cronExpression string) bool
}

// SysJobServiceImpl 定时任务服务实现
type SysJobServiceImpl struct {
	jobRepo      repository.SysJobRepository
	jobLogRepo   repository.SysJobLogRepository
	jobScheduler *util.JobScheduler
}

// NewSysJobService 创建定时任务服务
func NewSysJobService(jobRepo repository.SysJobRepository) SysJobService {
	// 创建日志仓库
	jobLogRepo := repository.NewSysJobLogRepository(database.GetDB())

	// 创建调度器
	scheduler := util.NewJobScheduler(jobRepo, jobLogRepo)
	scheduler.Start()

	// 初始化任务
	err := scheduler.InitJobs()
	if err != nil {
		fmt.Printf("初始化任务失败: %v\n", err)
	}

	return &SysJobServiceImpl{
		jobRepo:      jobRepo,
		jobLogRepo:   jobLogRepo,
		jobScheduler: scheduler,
	}
}

// SelectJobList 查询任务列表
func (s *SysJobServiceImpl) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	return s.jobRepo.SelectJobList(job)
}

// SelectJobById 通过ID查询任务
func (s *SysJobServiceImpl) SelectJobById(jobId int64) (*model.SysJob, error) {
	return s.jobRepo.SelectJobById(jobId)
}

// PauseJob 暂停任务
func (s *SysJobServiceImpl) PauseJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_PAUSE
	err := s.jobRepo.UpdateJobStatus(job)
	if err != nil {
		return err
	}

	s.jobScheduler.PauseJob(job)
	return nil
}

// ResumeJob 恢复任务
func (s *SysJobServiceImpl) ResumeJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_NORMAL
	err := s.jobRepo.UpdateJobStatus(job)
	if err != nil {
		return err
	}

	return s.jobScheduler.ResumeJob(job)
}

// DeleteJob 删除任务
func (s *SysJobServiceImpl) DeleteJob(job *model.SysJob) error {
	s.jobScheduler.DeleteJob(job)
	return s.jobRepo.DeleteJobById(job.JobID)
}

// DeleteJobByIds 批量删除任务
func (s *SysJobServiceImpl) DeleteJobByIds(jobIds []int64) error {
	for _, jobId := range jobIds {
		job, err := s.jobRepo.SelectJobById(jobId)
		if err == nil && job != nil {
			s.jobScheduler.DeleteJob(job)
		}
	}
	return s.jobRepo.DeleteJobByIds(jobIds)
}

// ChangeStatus 更改任务状态
func (s *SysJobServiceImpl) ChangeStatus(job *model.SysJob) error {
	if job.Status == constants.JOB_STATUS_NORMAL {
		return s.ResumeJob(job)
	} else {
		return s.PauseJob(job)
	}
}

// Run 立即运行任务
func (s *SysJobServiceImpl) Run(job *model.SysJob) error {
	jobCopy, err := s.jobRepo.SelectJobById(job.JobID)
	if err != nil {
		return err
	}

	s.jobScheduler.RunOnce(jobCopy)
	return nil
}

// InsertJob 新增任务
func (s *SysJobServiceImpl) InsertJob(job *model.SysJob) error {
	jobId, err := s.jobRepo.InsertJob(job)
	if err != nil {
		return err
	}

	job.JobID = jobId
	return s.jobScheduler.CreateJob(job)
}

// UpdateJob 修改任务
func (s *SysJobServiceImpl) UpdateJob(job *model.SysJob) error {
	err := s.jobRepo.UpdateJob(job)
	if err != nil {
		return err
	}

	return s.jobScheduler.UpdateJob(job)
}

// ValidateCronExpression 校验Cron表达式
func (s *SysJobServiceImpl) ValidateCronExpression(cronExpression string) bool {
	return util.IsValidCronExpression(cronExpression)
}
