package util

import (
	"fmt"
	"time"

	"github.com/robfig/cron/v3"
)

// IsValidCronExpression 验证Cron表达式是否有效
func IsValidCronExpression(cronExpression string) bool {
	if cronExpression == "" {
		return false
	}

	// 尝试解析Cron表达式
	_, err := cron.ParseStandard(cronExpression)
	if err != nil {
		fmt.Printf("Cron表达式验证失败 '%s': %v\n", cronExpression, err)
		return false
	}

	return true
}

// GetNextExecution 获取下一次执行时间
func GetNextExecution(cronExpression string) (*time.Time, error) {
	schedule, err := cron.ParseStandard(cronExpression)
	if err != nil {
		return nil, err
	}

	next := schedule.Next(time.Now())
	return &next, nil
}
