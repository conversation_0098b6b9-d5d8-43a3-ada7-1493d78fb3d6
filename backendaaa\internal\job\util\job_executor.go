package util

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/utils"
	"fmt"
	"time"
)

// JobExecutor 任务执行器
type JobExecutor struct {
	jobLogRepo repository.SysJobLogRepository
}

// NewJobExecutor 创建任务执行器
func NewJobExecutor(jobLogRepo repository.SysJobLogRepository) *JobExecutor {
	return &JobExecutor{
		jobLogRepo: jobLogRepo,
	}
}

// Execute 执行任务
func (e *JobExecutor) Execute(job *model.SysJob) {
	if job == nil {
		return
	}

	// 任务开始时间
	startTime := time.Now()

	// 创建任务日志对象
	jobLog := &model.SysJobLog{
		JobName:      job.JobName,
		JobGroup:     job.JobGroup,
		InvokeTarget: job.InvokeTarget,
		StartTime:    &startTime,
		Status:       "0", // 成功状态
	}

	// 执行任务
	fmt.Printf("开始执行任务: [%d] %s\n", job.JobID, job.JobName)
	var result *utils.InvokeResult

	// 并发控制
	if job.Concurrent == "0" {
		// 允许并发执行
		result = e.invokeTarget(job.InvokeTarget)
	} else {
		// 禁止并发执行，使用任务组名+任务名作为锁标识
		lockKey := job.JobGroup + job.JobName
		if utils.TryLock(lockKey) {
			defer utils.ReleaseLock(lockKey)
			result = e.invokeTarget(job.InvokeTarget)
		} else {
			// 任务被占用，记录日志并返回
			jobLog.Status = "1" // 失败状态
			jobLog.ExceptionInfo = "任务正在执行中，不允许并发执行"
			stopTime := time.Now()
			jobLog.StopTime = &stopTime

			e.jobLogRepo.InsertJobLog(jobLog)
			return
		}
	}

	// 记录执行结束时间
	stopTime := time.Now()
	jobLog.StopTime = &stopTime

	// 计算执行耗时
	elapsed := stopTime.Sub(startTime)
	jobLog.JobMessage = fmt.Sprintf("执行时间：%v毫秒", elapsed.Milliseconds())

	// 处理执行结果
	if !result.Success {
		jobLog.Status = "1" // 失败状态
		if result.Error != nil {
			jobLog.ExceptionInfo = result.Error.Error()
		} else {
			jobLog.ExceptionInfo = result.Message
		}
		fmt.Printf("任务执行失败: [%d] %s, 错误: %s\n", job.JobID, job.JobName, jobLog.ExceptionInfo)
	} else {
		fmt.Printf("任务执行成功: [%d] %s, 耗时: %v毫秒\n", job.JobID, job.JobName, elapsed.Milliseconds())
	}

	// 保存日志
	e.jobLogRepo.InsertJobLog(jobLog)
}

// invokeTarget 调用目标方法
func (e *JobExecutor) invokeTarget(invokeTarget string) *utils.InvokeResult {
	// 使用工具类执行目标方法
	return utils.InvokeMethod(invokeTarget)
}
