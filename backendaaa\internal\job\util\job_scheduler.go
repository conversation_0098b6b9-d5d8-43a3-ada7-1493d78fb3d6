package util

import (
	"backend/internal/model"
	"backend/internal/repository"
	"fmt"
	"sync"

	"github.com/robfig/cron/v3"
)

// JobScheduler 定时任务调度器
type JobScheduler struct {
	cron        *cron.Cron
	jobEntryMap map[int64]cron.EntryID
	jobRepo     repository.SysJobRepository
	jobLogRepo  repository.SysJobLogRepository
	jobExecutor *JobExecutor
	mutex       sync.RWMutex
}

// NewJobScheduler 创建定时任务调度器
func NewJobScheduler(jobRepo repository.SysJobRepository, jobLogRepo repository.SysJobLogRepository) *JobScheduler {
	// 创建带秒级精度的cron调度器
	cronScheduler := cron.New(cron.WithSeconds())

	// 创建任务执行器
	jobExecutor := NewJobExecutor(jobLogRepo)

	return &JobScheduler{
		cron:        cronScheduler,
		jobEntryMap: make(map[int64]cron.EntryID),
		jobRepo:     jobRepo,
		jobLogRepo:  jobLogRepo,
		jobExecutor: jobExecutor,
	}
}

// Start 启动任务调度器
func (js *JobScheduler) Start() {
	js.cron.Start()
	fmt.Println("任务调度器已启动")
}

// Stop 停止任务调度器
func (js *JobScheduler) Stop() {
	js.cron.Stop()
	fmt.Println("任务调度器已停止")
}

// InitJobs 初始化任务
func (js *JobScheduler) InitJobs() error {
	// 加载所有启用的任务
	jobs, err := js.jobRepo.SelectJobList(&model.SysJob{Status: "0"})
	if err != nil {
		return fmt.Errorf("加载定时任务失败: %v", err)
	}

	// 添加所有任务到调度器
	for _, job := range jobs {
		if err := js.CreateJob(job); err != nil {
			fmt.Printf("添加定时任务失败: %v\n", err)
		}
	}

	return nil
}

// CreateJob 创建定时任务
func (js *JobScheduler) CreateJob(job *model.SysJob) error {
	if job == nil {
		return fmt.Errorf("任务不能为空")
	}

	// 校验Cron表达式
	if !IsValidCronExpression(job.CronExpression) {
		return fmt.Errorf("无效的Cron表达式: %s", job.CronExpression)
	}

	// 检查任务是否已存在
	js.mutex.RLock()
	_, exists := js.jobEntryMap[job.JobID]
	js.mutex.RUnlock()
	if exists {
		// 先移除现有的任务
		js.DeleteJob(job)
	}

	// 创建任务执行函数
	jobFunc := func() {
		js.executeJob(job)
	}

	// 添加到cron调度器
	entryID, err := js.cron.AddFunc(job.CronExpression, jobFunc)
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %v", err)
	}

	// 记录任务ID和EntryID的映射关系
	js.mutex.Lock()
	js.jobEntryMap[job.JobID] = entryID
	js.mutex.Unlock()

	fmt.Printf("定时任务已添加: [%d] %s (%s)\n", job.JobID, job.JobName, job.CronExpression)

	return nil
}

// UpdateJob 更新任务
func (js *JobScheduler) UpdateJob(job *model.SysJob) error {
	// 删除旧任务
	js.DeleteJob(job)

	// 如果任务状态为正常，则重新创建
	if job.Status == "0" {
		return js.CreateJob(job)
	}

	return nil
}

// DeleteJob 删除定时任务
func (js *JobScheduler) DeleteJob(job *model.SysJob) {
	js.mutex.RLock()
	entryID, exists := js.jobEntryMap[job.JobID]
	js.mutex.RUnlock()

	if exists {
		js.cron.Remove(entryID)
		js.mutex.Lock()
		delete(js.jobEntryMap, job.JobID)
		js.mutex.Unlock()
		fmt.Printf("定时任务已移除: [%d] %s\n", job.JobID, job.JobName)
	}
}

// PauseJob 暂停任务
func (js *JobScheduler) PauseJob(job *model.SysJob) {
	js.DeleteJob(job)
}

// ResumeJob 恢复任务
func (js *JobScheduler) ResumeJob(job *model.SysJob) error {
	return js.CreateJob(job)
}

// RunOnce 立即执行一次任务
func (js *JobScheduler) RunOnce(job *model.SysJob) {
	go js.executeJob(job)
}

// executeJob 执行定时任务
func (js *JobScheduler) executeJob(job *model.SysJob) {
	fmt.Printf("调度执行任务: [%d] %s\n", job.JobID, job.JobName)
	// 使用jobExecutor执行任务
	js.jobExecutor.Execute(job)
}
