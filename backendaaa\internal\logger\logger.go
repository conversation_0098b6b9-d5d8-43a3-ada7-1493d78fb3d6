package logger

import (
	"backend/internal/config"
	"fmt"
	"io"
	"log"
	"os"
	"path"
	"runtime"
	"strings"
	"time"
)

// Level 日志级别
type Level int

const (
	// DEBUG 调试级别
	DEBUG Level = iota
	// INFO 信息级别
	INFO
	// WARN 警告级别
	WARN
	// ERROR 错误级别
	ERROR
	// FATAL 致命错误级别
	FATAL
)

var levelNames = []string{
	"DEBUG",
	"INFO",
	"WARN",
	"ERROR",
	"FATAL",
}

// Logger 日志记录器
type Logger struct {
	level      Level
	logger     *log.Logger
	callerSkip int
}

// 全局默认Logger
var defaultLogger *Logger

// Setup 初始化日志系统
func Setup() error {
	logConfig := config.Global.Log
	level := parseLevel(logConfig.Level)

	var writer io.Writer = os.Stdout

	// 如果配置了日志文件，则使用文件作为日志输出
	if logConfig.Filename != "" {
		dir := path.Dir(logConfig.Filename)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %v", err)
		}

		f, err := os.OpenFile(logConfig.Filename, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return fmt.Errorf("打开日志文件失败: %v", err)
		}

		// 同时输出到控制台和文件
		writer = io.MultiWriter(os.Stdout, f)
	}

	logger := log.New(writer, "", log.LstdFlags)
	defaultLogger = &Logger{
		level:      level,
		logger:     logger,
		callerSkip: 2,
	}

	Info("日志系统初始化完成")
	return nil
}

// parseLevel 解析日志级别字符串
func parseLevel(levelStr string) Level {
	levelStr = strings.ToUpper(levelStr)
	for i, name := range levelNames {
		if name == levelStr {
			return Level(i)
		}
	}
	return INFO // 默认使用INFO级别
}

// 获取调用信息
func (l *Logger) caller() string {
	_, file, line, ok := runtime.Caller(l.callerSkip)
	if !ok {
		file = "???"
		line = 0
	}

	// 提取文件的短名称
	short := file
	for i := len(file) - 1; i > 0; i-- {
		if file[i] == '/' {
			short = file[i+1:]
			break
		}
	}

	return fmt.Sprintf("%s:%d", short, line)
}

// Debug 输出调试信息
func (l *Logger) Debug(format string, v ...interface{}) {
	if l.level <= DEBUG {
		l.output(DEBUG, format, v...)
	}
}

// Info 输出普通信息
func (l *Logger) Info(format string, v ...interface{}) {
	if l.level <= INFO {
		l.output(INFO, format, v...)
	}
}

// Warn 输出警告信息
func (l *Logger) Warn(format string, v ...interface{}) {
	if l.level <= WARN {
		l.output(WARN, format, v...)
	}
}

// Error 输出错误信息
func (l *Logger) Error(format string, v ...interface{}) {
	if l.level <= ERROR {
		l.output(ERROR, format, v...)
	}
}

// Fatal 输出致命错误信息并退出程序
func (l *Logger) Fatal(format string, v ...interface{}) {
	if l.level <= FATAL {
		l.output(FATAL, format, v...)
	}
	os.Exit(1)
}

// output 输出日志
func (l *Logger) output(level Level, format string, v ...interface{}) {
	now := time.Now().Format("2006-01-02 15:04:05")
	msg := fmt.Sprintf(format, v...)
	caller := l.caller()
	l.logger.Printf("[%s] [%s] %s - %s", now, levelNames[level], caller, msg)
}

// Debug 输出调试信息
func Debug(format string, v ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Debug(format, v...)
	}
}

// Info 输出普通信息
func Info(format string, v ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Info(format, v...)
	}
}

// Warn 输出警告信息
func Warn(format string, v ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Warn(format, v...)
	}
}

// Error 输出错误信息
func Error(format string, v ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Error(format, v...)
	}
}

// Fatal 输出致命错误信息并退出程序
func Fatal(format string, v ...interface{}) {
	if defaultLogger != nil {
		defaultLogger.Fatal(format, v...)
	}
	os.Exit(1)
}

// GetLogger 获取默认日志记录器
func GetLogger() *Logger {
	return defaultLogger
}

// NewLogger 创建新的日志记录器
func NewLogger(level Level, writer io.Writer) *Logger {
	return &Logger{
		level:      level,
		logger:     log.New(writer, "", log.LstdFlags),
		callerSkip: 2,
	}
}
