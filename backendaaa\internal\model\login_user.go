package model

import "time"

// LoginUser 登录用户身份权限
type LoginUser struct {
	// 用户ID
	UserID int64 `json:"userId"`
	// 部门ID
	DeptID int64 `json:"deptId"`
	// 用户唯一标识
	Token string `json:"token"`
	// 登录时间
	LoginTime time.Time `json:"loginTime"`
	// 过期时间
	ExpireTime time.Time `json:"expireTime"`
	// 令牌刷新时间
	TokenRefreshTime time.Time `json:"tokenRefreshTime"`
	// 登录IP地址
	IpAddr string `json:"ipaddr"`
	// 登录地点
	LoginLocation string `json:"loginLocation"`
	// 浏览器类型
	Browser string `json:"browser"`
	// 操作系统
	OS string `json:"os"`
	// 权限列表
	Permissions []string `json:"permissions"`
	// 用户信息
	User *SysUser `json:"user"`
}

// IsAdmin 判断是否为管理员
func (u *LoginUser) IsAdmin() bool {
	return IsAdminUser(u.UserID)
}
