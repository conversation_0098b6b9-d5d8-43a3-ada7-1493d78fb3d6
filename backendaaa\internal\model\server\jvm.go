package server

import (
	"backend/internal/utils"
	"time"
)

// Jvm JVM相关信息
type Jvm struct {
	// 当前JVM占用的内存总数(B)
	Total int64 `json:"total"`

	// JVM最大可用内存总数(B)
	Max int64 `json:"max"`

	// JVM空闲内存(B)
	Free int64 `json:"free"`

	// JDK版本
	Version string `json:"version"`

	// JDK路径
	Home string `json:"home"`

	// JVM启动时间
	StartTime time.Time `json:"startTime"`
}

// GetTotal 获取JVM占用的内存总数(MB)
func (j *Jvm) GetTotal() float64 {
	return utils.Arith.Div(float64(j.Total), float64(1024*1024), 2)
}

// GetMax 获取JVM最大可用内存(MB)
func (j *Jvm) GetMax() float64 {
	return utils.Arith.Div(float64(j.<PERSON>), float64(1024*1024), 2)
}

// GetFree 获取JVM空闲内存(MB)
func (j *Jvm) GetFree() float64 {
	return utils.Arith.Div(float64(j.Free), float64(1024*1024), 2)
}

// GetUsed 获取JVM已用内存(MB)
func (j *Jvm) GetUsed() float64 {
	return utils.Arith.Div(float64(j.Total-j.Free), float64(1024*1024), 2)
}

// GetUsage 获取JVM内存使用率
func (j *Jvm) GetUsage() float64 {
	if j.Total == 0 {
		return 0
	}
	return utils.Arith.Mul(utils.Arith.Div(float64(j.Total-j.Free), float64(j.Total), 4), 100)
}

// GetName 获取JDK名称
func (j *Jvm) GetName() string {
	return "Go Runtime"
}

// GetRunTime 获取JVM运行时间
func (j *Jvm) GetRunTime() string {
	duration := time.Since(j.StartTime)
	days := int(duration.Hours() / 24)
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60
	seconds := int(duration.Seconds()) % 60

	if days > 0 {
		return utils.FormatDuration(days, hours, minutes, seconds)
	} else {
		return utils.FormatTimeHMS(hours, minutes, seconds)
	}
}
