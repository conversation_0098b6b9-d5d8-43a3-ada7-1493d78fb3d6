package model

// SysConfig 参数配置表 sys_config
type SysConfig struct {
	BaseModel
	// 参数主键
	ConfigID int64 `json:"configId" gorm:"column:config_id;primary_key;auto_increment;comment:参数主键" validate:""`
	// 参数名称
	ConfigName string `json:"configName" gorm:"column:config_name;comment:参数名称" validate:"required,max=100"`
	// 参数键名
	ConfigKey string `json:"configKey" gorm:"column:config_key;comment:参数键名" validate:"required,max=100"`
	// 参数键值
	ConfigValue string `json:"configValue" gorm:"column:config_value;comment:参数键值" validate:"required,max=500"`
	// 系统内置（Y是 N否）
	ConfigType string `json:"configType" gorm:"column:config_type;default:N;comment:系统内置（Y是 N否）" validate:""`
	// 备注
	Remark string `json:"remark" gorm:"column:remark;comment:备注" validate:"max=500"`
}

// TableName 设置表名
func (SysConfig) TableName() string {
	return "sys_config"
}

// ToString 返回结构体的字符串表示，类似Java中的toString方法
func (c *SysConfig) ToString() string {
	return ""
}
