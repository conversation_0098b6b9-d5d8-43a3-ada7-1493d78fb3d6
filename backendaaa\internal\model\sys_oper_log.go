package model

import (
	"time"
)

// SysOperLog 操作日志记录表
type SysOperLog struct {
	BaseModel
	// 日志主键
	OperId int64 `json:"operId" gorm:"column:oper_id;primary_key;auto_increment;comment:日志主键"`
	// 操作模块
	Title string `json:"title" gorm:"column:title;comment:操作模块"`
	// 业务类型（0其它 1新增 2修改 3删除）
	BusinessType int `json:"businessType" gorm:"column:business_type;comment:业务类型（0其它 1新增 2修改 3删除）"`
	// 请求方法
	Method string `json:"method" gorm:"column:method;comment:请求方法"`
	// 请求方式
	RequestMethod string `json:"requestMethod" gorm:"column:request_method;comment:请求方式"`
	// 操作类别（0其它 1后台用户 2手机端用户）
	OperatorType int `json:"operatorType" gorm:"column:operator_type;comment:操作类别（0其它 1后台用户 2手机端用户）"`
	// 操作人员
	OperName string `json:"operName" gorm:"column:oper_name;comment:操作人员"`
	// 部门名称
	DeptName string `json:"deptName" gorm:"column:dept_name;comment:部门名称"`
	// 请求URL
	OperUrl string `json:"operUrl" gorm:"column:oper_url;comment:请求URL"`
	// 操作地址
	OperIp string `json:"operIp" gorm:"column:oper_ip;comment:操作地址"`
	// 操作地点
	OperLocation string `json:"operLocation" gorm:"column:oper_location;comment:操作地点"`
	// 请求参数
	OperParam string `json:"operParam" gorm:"column:oper_param;type:text;comment:请求参数"`
	// 返回参数
	JsonResult string `json:"jsonResult" gorm:"column:json_result;type:text;comment:返回参数"`
	// 操作状态（0正常 1异常）
	Status int `json:"status" gorm:"column:status;comment:操作状态（0正常 1异常）"`
	// 错误消息
	ErrorMsg string `json:"errorMsg" gorm:"column:error_msg;type:text;comment:错误消息"`
	// 操作时间
	OperTime *time.Time `json:"operTime" gorm:"column:oper_time;comment:操作时间"`
	// 消耗时间
	CostTime int64 `json:"costTime" gorm:"column:cost_time;comment:消耗时间"`
	// 业务类型数组 - 非数据库字段
	BusinessTypes []int `json:"businessTypes" gorm:"-"`
}

// TableName 设置表名
func (SysOperLog) TableName() string {
	return "sys_oper_log"
}
