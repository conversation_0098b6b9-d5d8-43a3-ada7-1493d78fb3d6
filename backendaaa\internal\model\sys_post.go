package model

// SysPost 岗位信息表 sys_post
type SysPost struct {
	BaseModel
	// 岗位ID
	PostID int64 `json:"postId" gorm:"column:post_id;primary_key;auto_increment;comment:岗位ID"`
	// 岗位编码
	PostCode string `json:"postCode" gorm:"column:post_code;not null;comment:岗位编码"`
	// 岗位名称
	PostName string `json:"postName" gorm:"column:post_name;not null;comment:岗位名称"`
	// 显示顺序
	PostSort int `json:"postSort" gorm:"column:post_sort;not null;comment:显示顺序"`
	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;not null;comment:状态（0正常 1停用）"`
}

// TableName 设置表名
func (SysPost) TableName() string {
	return "sys_post"
}
