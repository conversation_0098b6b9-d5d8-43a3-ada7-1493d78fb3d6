package model

import (
	"time"
)

// SysUser 用户对象 sys_user，对应Java中的SysUser
type SysUser struct {
	BaseModel
	// 用户ID
	UserID int64 `json:"userId" gorm:"column:user_id;primary_key;auto_increment;comment:用户ID"`
	// 部门ID
	DeptID int64 `json:"deptId" gorm:"column:dept_id;comment:部门ID"`
	// 用户账号
	UserName string `json:"userName" gorm:"column:user_name;not null;comment:用户账号" validate:"required,max=30,xss"`
	// 用户昵称
	NickName string `json:"nickName" gorm:"column:nick_name;not null;comment:用户昵称" validate:"max=30,xss"`
	// 用户类型（00系统用户）
	UserType string `json:"userType" gorm:"column:user_type;default:00;comment:用户类型（00系统用户）"`
	// 用户邮箱
	Email string `json:"email" gorm:"column:email;default:'';comment:用户邮箱" validate:"email,max=50"`
	// 手机号码
	PhoneNumber string `json:"phonenumber" gorm:"column:phonenumber;default:'';comment:手机号码" validate:"max=11"`
	// 用户性别（0男 1女 2未知）
	Sex string `json:"sex" gorm:"column:sex;default:0;comment:用户性别（0男 1女 2未知）"`
	// 头像地址
	Avatar string `json:"avatar" gorm:"column:avatar;default:'';comment:头像地址"`
	// 密码
	Password string `json:"password" gorm:"column:password;default:'';comment:密码"`
	// 帐号状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;default:0;comment:帐号状态（0正常 1停用）"`
	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag;default:0;comment:删除标志（0代表存在 2代表删除）"`
	// 最后登录IP
	LoginIP string `json:"loginIp" gorm:"column:login_ip;default:'';comment:最后登录IP"`
	// 最后登录时间
	LoginDate *time.Time `json:"loginDate" gorm:"column:login_date;comment:最后登录时间"`
	// 密码最后更新时间
	PwdUpdateDate *time.Time `json:"pwdUpdateDate" gorm:"column:pwd_update_date;comment:密码最后更新时间"`

	// 部门对象 - 非数据库字段
	Dept *SysDept `json:"dept" gorm:"-"`
	// 角色对象 - 非数据库字段
	Roles []*SysRole `json:"roles" gorm:"-"`
	// 角色组 - 非数据库字段
	RoleIDs []int64 `json:"roleIds" gorm:"-"`
	// 岗位组 - 非数据库字段
	PostIDs []int64 `json:"postIds" gorm:"-"`
	// 角色ID - 非数据库字段
	RoleID int64 `json:"roleId" gorm:"-"`
}

// TableName 设置表名
func (SysUser) TableName() string {
	return "sys_user"
}

// IsAdmin 判断是否是管理员
func (u *SysUser) IsAdmin() bool {
	return IsAdminUser(u.UserID)
}

// ToString 返回结构体的字符串表示，类似Java中的toString方法
func (u *SysUser) ToString() string {
	return ""
}
