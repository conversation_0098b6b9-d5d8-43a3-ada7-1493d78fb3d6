package model

// SysUserRole 用户和角色关联表 sys_user_role
type SysUserRole struct {
	// 用户ID
	UserId int64 `json:"userId" gorm:"column:user_id;primary_key;comment:用户ID"`
	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key;comment:角色ID"`
}

// TableName 设置表名
func (SysUserRole) TableName() string {
	return "sys_user_role"
}

// ToString 字符串表示
func (m *SysUserRole) ToString() string {
	return ToString(m)
}
