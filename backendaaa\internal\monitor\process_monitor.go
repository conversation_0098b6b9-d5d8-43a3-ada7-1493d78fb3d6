package monitor

import (
	"encoding/json"
	"os"
	"runtime"
	"sync"
	"time"
)

// ProcessInfo 进程信息
type ProcessInfo struct {
	PID          int       `json:"pid"`          // 进程ID
	Hostname     string    `json:"hostname"`     // 主机名
	NumCPU       int       `json:"numCPU"`       // CPU核心数
	NumGoroutine int       `json:"numGoroutine"` // 协程数
	GOOS         string    `json:"goos"`         // 操作系统
	GOARCH       string    `json:"goarch"`       // 架构
	GoVersion    string    `json:"goVersion"`    // Go版本
	StartTime    time.Time `json:"startTime"`    // 启动时间
	Uptime       string    `json:"uptime"`       // 运行时间
}

// MemoryInfo 内存信息
type MemoryInfo struct {
	Alloc         uint64  `json:"alloc"`         // 已分配内存
	TotalAlloc    uint64  `json:"totalAlloc"`    // 总分配内存
	Sys           uint64  `json:"sys"`           // 系统内存
	NumGC         uint32  `json:"numGC"`         // GC次数
	GCCPUFraction float64 `json:"gcCPUFraction"` // GC CPU占用
	HeapObjects   uint64  `json:"heapObjects"`   // 堆对象数
	HeapAlloc     uint64  `json:"heapAlloc"`     // 堆已分配
	HeapSys       uint64  `json:"heapSys"`       // 堆系统内存
}

// ProcessMonitor 进程监控器
type ProcessMonitor struct {
	processInfo  ProcessInfo
	memoryInfo   MemoryInfo
	mutex        sync.RWMutex
	updateTicker *time.Ticker
}

// NewProcessMonitor 创建进程监控器
func NewProcessMonitor() *ProcessMonitor {
	hostname, _ := os.Hostname()

	monitor := &ProcessMonitor{
		processInfo: ProcessInfo{
			PID:       os.Getpid(),
			Hostname:  hostname,
			NumCPU:    runtime.NumCPU(),
			GOOS:      runtime.GOOS,
			GOARCH:    runtime.GOARCH,
			GoVersion: runtime.Version(),
			StartTime: time.Now(),
		},
		updateTicker: time.NewTicker(5 * time.Second),
	}

	// 初始更新一次
	monitor.update()

	// 启动定时更新
	go monitor.startUpdate()

	return monitor
}

// startUpdate 开始定时更新
func (m *ProcessMonitor) startUpdate() {
	for range m.updateTicker.C {
		m.update()
	}
}

// update 更新监控数据
func (m *ProcessMonitor) update() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 更新进程信息
	m.processInfo.NumGoroutine = runtime.NumGoroutine()
	m.processInfo.Uptime = time.Since(m.processInfo.StartTime).String()

	// 更新内存信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	m.memoryInfo = MemoryInfo{
		Alloc:         memStats.Alloc,
		TotalAlloc:    memStats.TotalAlloc,
		Sys:           memStats.Sys,
		NumGC:         memStats.NumGC,
		GCCPUFraction: memStats.GCCPUFraction,
		HeapObjects:   memStats.HeapObjects,
		HeapAlloc:     memStats.HeapAlloc,
		HeapSys:       memStats.HeapSys,
	}
}

// Stop 停止监控
func (m *ProcessMonitor) Stop() {
	if m.updateTicker != nil {
		m.updateTicker.Stop()
	}
}

// GetProcessInfo 获取进程信息
func (m *ProcessMonitor) GetProcessInfo() ProcessInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.processInfo
}

// GetMemoryInfo 获取内存信息
func (m *ProcessMonitor) GetMemoryInfo() MemoryInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.memoryInfo
}

// ToJSON 转换为JSON
func (m *ProcessMonitor) ToJSON() (string, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	data := map[string]interface{}{
		"process": m.processInfo,
		"memory":  m.memoryInfo,
	}

	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	return string(jsonBytes), nil
}
