package repository

import (
	"backend/internal/model"
)

// DBTableRepository 数据库表数据访问接口
type DBTableRepository interface {
	// SelectDbTableList 查询数据库表列表
	SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error)
	// SelectDbTableListByNames 根据表名称查询数据库表列表
	SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error)
	// SelectDbTableColumnsByName 根据表名称查询列信息
	SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error)
}

// DBTableRepositoryImpl 数据库表数据访问实现
type DBTableRepositoryImpl struct {
	*BaseRepository
}

// NewDBTableRepository 创建数据库表数据访问对象
func NewDBTableRepository(repo *BaseRepository) DBTableRepository {
	return &DBTableRepositoryImpl{
		BaseRepository: repo,
	}
}

// SelectDbTableList 查询数据库表列表
func (r *DBTableRepositoryImpl) SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	var tables []*model.GenTable

	// 构建SQL查询
	sql := `SELECT table_name, table_comment, create_time, update_time 
			FROM information_schema.tables 
			WHERE table_schema = (SELECT DATABASE()) 
			AND table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
			AND table_name NOT IN (SELECT table_name FROM gen_table)`

	params := []interface{}{}

	if genTable != nil && genTable.TableName != "" {
		sql += " AND table_name LIKE ?"
		params = append(params, "%"+genTable.TableName+"%")
	}
	if genTable != nil && genTable.TableComment != "" {
		sql += " AND table_comment LIKE ?"
		params = append(params, "%"+genTable.TableComment+"%")
	}

	sql += " ORDER BY create_time DESC"

	err := r.DB.Raw(sql, params...).Scan(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// SelectDbTableListByNames 根据表名称查询数据库表列表
func (r *DBTableRepositoryImpl) SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error) {
	if len(tableNames) == 0 {
		return nil, nil
	}

	var tables []*model.GenTable

	// 构建SQL查询
	sql := `SELECT table_name, table_comment, create_time, update_time 
			FROM information_schema.tables 
			WHERE table_schema = (SELECT DATABASE()) 
			AND table_name IN (?)`

	err := r.DB.Raw(sql, tableNames).Scan(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// SelectDbTableColumnsByName 根据表名称查询列信息
func (r *DBTableRepositoryImpl) SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error) {
	if tableName == "" {
		return nil, nil
	}

	var columns []*model.GenTableColumn

	// 构建SQL查询
	sql := `SELECT 
				column_name,
				(CASE WHEN column_key = 'PRI' THEN '1' ELSE '0' END) AS is_pk,
				(CASE WHEN extra = 'auto_increment' THEN '1' ELSE '0' END) AS is_increment,
				(CASE WHEN is_nullable = 'NO' THEN '1' ELSE '0' END) AS is_required,
				(CASE WHEN column_key = 'UNI' THEN '1' ELSE '0' END) AS is_unique,
				column_type,
				column_comment,
				ordinal_position AS sort
			FROM information_schema.columns
			WHERE table_schema = (SELECT DATABASE())
			AND table_name = ?
			ORDER BY ordinal_position`

	err := r.DB.Raw(sql, tableName).Scan(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}
