package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// GenTableColumnRepository 代码生成表字段数据访问接口
type GenTableColumnRepository interface {
	// SelectGenTableColumnListByTableId 根据表ID查询列信息
	SelectGenTableColumnListByTableId(tableId int64) ([]*model.GenTableColumn, error)
	// InsertGenTableColumn 新增业务字段
	InsertGenTableColumn(genTableColumn *model.GenTableColumn) error
	// UpdateGenTableColumn 修改业务字段
	UpdateGenTableColumn(genTableColumn *model.GenTableColumn) error
	// DeleteGenTableColumnByIds 批量删除业务字段
	DeleteGenTableColumnByIds(ids []int64) error
	// DeleteGenTableColumnByTableId 根据表ID删除列信息
	DeleteGenTableColumnByTableId(tableId int64) error
	// InsertGenTableColumns 批量插入业务表字段
	InsertGenTableColumns(columns []*model.GenTableColumn) error

	// 事务相关方法
	// InsertGenTableColumnTx 事务中新增业务字段
	InsertGenTableColumnTx(tx *gorm.DB, genTableColumn *model.GenTableColumn) error
	// UpdateGenTableColumnTx 事务中修改业务字段
	UpdateGenTableColumnTx(tx *gorm.DB, genTableColumn *model.GenTableColumn) error
	// DeleteGenTableColumnByIdsTx 事务中批量删除业务字段
	DeleteGenTableColumnByIdsTx(tx *gorm.DB, ids []int64) error
	// DeleteGenTableColumnByTableIdTx 事务中根据表ID删除列信息
	DeleteGenTableColumnByTableIdTx(tx *gorm.DB, tableId int64) error
	// InsertGenTableColumnsTx 事务中批量插入业务表字段
	InsertGenTableColumnsTx(tx *gorm.DB, columns []*model.GenTableColumn) error

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// GenTableColumnRepositoryImpl 代码生成表字段数据访问实现
type GenTableColumnRepositoryImpl struct {
	db *gorm.DB
}

// NewGenTableColumnRepository 创建代码生成表字段数据访问实现
func NewGenTableColumnRepository(db *gorm.DB) GenTableColumnRepository {
	return &GenTableColumnRepositoryImpl{
		db: db,
	}
}

// GetDB 获取数据库连接
func (r *GenTableColumnRepositoryImpl) GetDB() *gorm.DB {
	return r.db
}

// SelectGenTableColumnListByTableId 根据表ID查询列信息
func (r *GenTableColumnRepositoryImpl) SelectGenTableColumnListByTableId(tableId int64) ([]*model.GenTableColumn, error) {
	var columns []*model.GenTableColumn
	err := r.db.Table(model.GenTableColumn{}.GetTableName()).
		Where("table_id = ?", tableId).
		Order("sort").
		Find(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}

// InsertGenTableColumn 新增业务字段
func (r *GenTableColumnRepositoryImpl) InsertGenTableColumn(genTableColumn *model.GenTableColumn) error {
	return r.db.Table(genTableColumn.GetTableName()).Create(genTableColumn).Error
}

// UpdateGenTableColumn 修改业务字段
func (r *GenTableColumnRepositoryImpl) UpdateGenTableColumn(genTableColumn *model.GenTableColumn) error {
	return r.db.Table(genTableColumn.GetTableName()).
		Where("column_id = ?", genTableColumn.ColumnID).
		Updates(genTableColumn).Error
}

// DeleteGenTableColumnByIds 批量删除业务字段
func (r *GenTableColumnRepositoryImpl) DeleteGenTableColumnByIds(ids []int64) error {
	return r.db.Table(model.GenTableColumn{}.GetTableName()).
		Where("column_id IN ?", ids).
		Delete(&model.GenTableColumn{}).Error
}

// DeleteGenTableColumnByTableId 根据表ID删除列信息
func (r *GenTableColumnRepositoryImpl) DeleteGenTableColumnByTableId(tableId int64) error {
	return r.db.Table(model.GenTableColumn{}.GetTableName()).
		Where("table_id = ?", tableId).
		Delete(&model.GenTableColumn{}).Error
}

// InsertGenTableColumns 批量插入业务表字段
func (r *GenTableColumnRepositoryImpl) InsertGenTableColumns(columns []*model.GenTableColumn) error {
	return r.db.Table(model.GenTableColumn{}.GetTableName()).Create(columns).Error
}

// InsertGenTableColumnTx 事务中新增业务字段
func (r *GenTableColumnRepositoryImpl) InsertGenTableColumnTx(tx *gorm.DB, genTableColumn *model.GenTableColumn) error {
	return tx.Table(genTableColumn.GetTableName()).Create(genTableColumn).Error
}

// UpdateGenTableColumnTx 事务中修改业务字段
func (r *GenTableColumnRepositoryImpl) UpdateGenTableColumnTx(tx *gorm.DB, genTableColumn *model.GenTableColumn) error {
	return tx.Table(genTableColumn.GetTableName()).
		Where("column_id = ?", genTableColumn.ColumnID).
		Updates(genTableColumn).Error
}

// DeleteGenTableColumnByIdsTx 事务中批量删除业务字段
func (r *GenTableColumnRepositoryImpl) DeleteGenTableColumnByIdsTx(tx *gorm.DB, ids []int64) error {
	return tx.Table(model.GenTableColumn{}.GetTableName()).
		Where("column_id IN ?", ids).
		Delete(&model.GenTableColumn{}).Error
}

// DeleteGenTableColumnByTableIdTx 事务中根据表ID删除列信息
func (r *GenTableColumnRepositoryImpl) DeleteGenTableColumnByTableIdTx(tx *gorm.DB, tableId int64) error {
	return tx.Table(model.GenTableColumn{}.GetTableName()).
		Where("table_id = ?", tableId).
		Delete(&model.GenTableColumn{}).Error
}

// InsertGenTableColumnsTx 事务中批量插入业务表字段
func (r *GenTableColumnRepositoryImpl) InsertGenTableColumnsTx(tx *gorm.DB, columns []*model.GenTableColumn) error {
	return tx.Table(model.GenTableColumn{}.GetTableName()).Create(columns).Error
}
