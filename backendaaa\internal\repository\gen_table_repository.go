package repository

import (
	"backend/internal/model"
	"errors"

	"gorm.io/gorm"
)

// GenTableRepository 代码生成表数据访问接口
type GenTableRepository interface {
	// SelectGenTableList 查询业务列表
	SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error)
	// SelectDbTableList 查询数据库表列表
	SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error)
	// SelectDbTableListByNames 根据表名称查询数据库表列表
	SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error)
	// SelectGenTableById 查询业务表详细信息
	SelectGenTableById(id int64) (*model.GenTable, error)
	// SelectGenTableAll 查询所有表信息
	SelectGenTableAll() ([]*model.GenTable, error)
	// InsertGenTable 插入业务表
	InsertGenTable(genTable *model.GenTable) (int64, error)
	// UpdateGenTable 修改业务表
	UpdateGenTable(genTable *model.GenTable) error
	// DeleteGenTableByIds 批量删除业务表
	DeleteGenTableByIds(ids []int64) error
	// CreateTable 创建表
	CreateTable(sql string) error
	// SelectGenTableByName 根据表名查询表
	SelectGenTableByName(tableName string) ([]*model.GenTable, error)
	// DeleteGenTableById 删除表信息
	DeleteGenTableById(tableId int64) error

	// 事务相关方法
	// InsertGenTableTx 事务中插入业务表
	InsertGenTableTx(tx *gorm.DB, genTable *model.GenTable) (int64, error)
	// UpdateGenTableTx 事务中修改业务表
	UpdateGenTableTx(tx *gorm.DB, genTable *model.GenTable) error
	// DeleteGenTableByIdTx 事务中删除表信息
	DeleteGenTableByIdTx(tx *gorm.DB, tableId int64) error
	// DeleteGenTableByIdsTx 事务中批量删除业务表
	DeleteGenTableByIdsTx(tx *gorm.DB, ids []int64) error
	// CreateTableTx 事务中创建表
	CreateTableTx(tx *gorm.DB, sql string) error

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// GenTableRepositoryImpl 代码生成表数据访问实现
type GenTableRepositoryImpl struct {
	db *gorm.DB
}

// NewGenTableRepository 创建代码生成表数据访问实现
func NewGenTableRepository(db *gorm.DB) GenTableRepository {
	return &GenTableRepositoryImpl{
		db: db,
	}
}

// GetDB 获取数据库连接
func (r *GenTableRepositoryImpl) GetDB() *gorm.DB {
	return r.db
}

// SelectGenTableList 查询业务列表
func (r *GenTableRepositoryImpl) SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	var tables []*model.GenTable
	db := r.db.Table(genTable.GetTableName())

	if genTable.TableName != "" {
		db = db.Where("table_name like ?", "%"+genTable.TableName+"%")
	}
	if genTable.TableComment != "" {
		db = db.Where("table_comment like ?", "%"+genTable.TableComment+"%")
	}

	err := db.Find(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// SelectDbTableList 查询数据库表列表
func (r *GenTableRepositoryImpl) SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	var tables []*model.GenTable

	// 构建SQL查询
	sql := `SELECT table_name, table_comment, create_time, update_time 
			FROM information_schema.tables 
			WHERE table_schema = (SELECT DATABASE()) 
			AND table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
			AND table_name NOT IN (SELECT table_name FROM gen_table)`

	params := []interface{}{}

	if genTable.TableName != "" {
		sql += " AND table_name LIKE ?"
		params = append(params, "%"+genTable.TableName+"%")
	}
	if genTable.TableComment != "" {
		sql += " AND table_comment LIKE ?"
		params = append(params, "%"+genTable.TableComment+"%")
	}

	sql += " ORDER BY create_time DESC"

	err := r.db.Raw(sql, params...).Scan(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// SelectDbTableListByNames 根据表名称查询数据库表列表
func (r *GenTableRepositoryImpl) SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error) {
	if len(tableNames) == 0 {
		return nil, errors.New("表名不能为空")
	}

	var tables []*model.GenTable

	// 构建SQL查询
	sql := `SELECT table_name, table_comment, create_time, update_time 
			FROM information_schema.tables 
			WHERE table_schema = (SELECT DATABASE()) 
			AND table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
			AND table_name IN (?)`

	err := r.db.Raw(sql, tableNames).Scan(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// SelectGenTableById 查询业务表详细信息
func (r *GenTableRepositoryImpl) SelectGenTableById(id int64) (*model.GenTable, error) {
	var genTable *model.GenTable
	err := r.db.Table(model.GenTable{}.GetTableName()).Where("table_id = ?", id).First(&genTable).Error
	if err != nil {
		return nil, err
	}
	return genTable, nil
}

// SelectGenTableAll 查询所有表信息
func (r *GenTableRepositoryImpl) SelectGenTableAll() ([]*model.GenTable, error) {
	var tables []*model.GenTable
	err := r.db.Table(model.GenTable{}.GetTableName()).Find(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// InsertGenTable 插入业务表
func (r *GenTableRepositoryImpl) InsertGenTable(genTable *model.GenTable) (int64, error) {
	err := r.db.Table(model.GenTable{}.GetTableName()).Create(genTable).Error
	if err != nil {
		return 0, err
	}
	return genTable.TableID, nil
}

// UpdateGenTable 修改业务表
func (r *GenTableRepositoryImpl) UpdateGenTable(genTable *model.GenTable) error {
	return r.db.Table(genTable.GetTableName()).Where("table_id = ?", genTable.TableID).Updates(genTable).Error
}

// DeleteGenTableByIds 批量删除业务表
func (r *GenTableRepositoryImpl) DeleteGenTableByIds(ids []int64) error {
	return r.db.Table(model.GenTable{}.GetTableName()).Where("table_id IN ?", ids).Delete(&model.GenTable{}).Error
}

// CreateTable 创建表
func (r *GenTableRepositoryImpl) CreateTable(sql string) error {
	return r.db.Exec(sql).Error
}

// SelectGenTableByName 根据表名查询表
func (r *GenTableRepositoryImpl) SelectGenTableByName(tableName string) ([]*model.GenTable, error) {
	var tables []*model.GenTable
	err := r.db.Table(model.GenTable{}.GetTableName()).Where("table_name = ?", tableName).Find(&tables).Error
	if err != nil {
		return nil, err
	}
	return tables, nil
}

// DeleteGenTableById 删除表信息
func (r *GenTableRepositoryImpl) DeleteGenTableById(tableId int64) error {
	return r.db.Table(model.GenTable{}.GetTableName()).Where("table_id = ?", tableId).Delete(&model.GenTable{}).Error
}

// InsertGenTableTx 事务中插入业务表
func (r *GenTableRepositoryImpl) InsertGenTableTx(tx *gorm.DB, genTable *model.GenTable) (int64, error) {
	err := tx.Table(model.GenTable{}.GetTableName()).Create(genTable).Error
	if err != nil {
		return 0, err
	}
	return genTable.TableID, nil
}

// UpdateGenTableTx 事务中修改业务表
func (r *GenTableRepositoryImpl) UpdateGenTableTx(tx *gorm.DB, genTable *model.GenTable) error {
	return tx.Table(genTable.GetTableName()).Where("table_id = ?", genTable.TableID).Updates(genTable).Error
}

// DeleteGenTableByIdTx 事务中删除表信息
func (r *GenTableRepositoryImpl) DeleteGenTableByIdTx(tx *gorm.DB, tableId int64) error {
	return tx.Table(model.GenTable{}.GetTableName()).Where("table_id = ?", tableId).Delete(&model.GenTable{}).Error
}

// DeleteGenTableByIdsTx 事务中批量删除业务表
func (r *GenTableRepositoryImpl) DeleteGenTableByIdsTx(tx *gorm.DB, ids []int64) error {
	return tx.Table(model.GenTable{}.GetTableName()).Where("table_id IN ?", ids).Delete(&model.GenTable{}).Error
}

// CreateTableTx 事务中创建表
func (r *GenTableRepositoryImpl) CreateTableTx(tx *gorm.DB, sql string) error {
	return tx.Exec(sql).Error
}
