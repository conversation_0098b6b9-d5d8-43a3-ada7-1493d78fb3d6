package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysDictTypeRepository 字典类型仓库接口
type SysDictTypeRepository interface {
	// SelectDictTypeList 查询字典类型列表
	SelectDictTypeList(dictType *model.SysDictType) ([]*model.SysDictType, error)

	// SelectDictTypeAll 查询所有字典类型
	SelectDictTypeAll() ([]*model.SysDictType, error)

	// SelectDictTypeById 根据字典类型ID查询信息
	SelectDictTypeById(dictId int64) (*model.SysDictType, error)

	// SelectDictTypeByType 根据字典类型查询信息
	SelectDictTypeByType(dictType string) (*model.SysDictType, error)

	// DeleteDictTypeById 删除字典类型
	DeleteDictTypeById(dictId int64) error

	// DeleteDictTypeByIds 批量删除字典类型
	DeleteDictTypeByIds(dictIds []int64) error

	// InsertDictType 新增字典类型
	InsertDictType(dictType *model.SysDictType) (int64, error)

	// UpdateDictType 修改字典类型
	UpdateDictType(dictType *model.SysDictType) (int64, error)

	// CheckDictTypeUnique 校验字典类型是否唯一
	CheckDictTypeUnique(dictType string, dictId int64) (*model.SysDictType, error)

	// 事务相关方法
	// DeleteDictTypeByIdTx 事务中删除字典类型
	DeleteDictTypeByIdTx(tx *gorm.DB, dictId int64) error

	// DeleteDictTypeByIdsTx 事务中批量删除字典类型
	DeleteDictTypeByIdsTx(tx *gorm.DB, dictIds []int64) error

	// InsertDictTypeTx 事务中新增字典类型
	InsertDictTypeTx(tx *gorm.DB, dictType *model.SysDictType) (int64, error)

	// UpdateDictTypeTx 事务中修改字典类型
	UpdateDictTypeTx(tx *gorm.DB, dictType *model.SysDictType) (int64, error)

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// SysDictTypeRepositoryImpl 字典类型仓库实现
type SysDictTypeRepositoryImpl struct {
	*BaseRepository
}

// NewSysDictTypeRepository 创建字典类型仓库
func NewSysDictTypeRepository(db *gorm.DB) SysDictTypeRepository {
	return &SysDictTypeRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// GetDB 获取数据库连接
func (r *SysDictTypeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

// SelectDictTypeList 查询字典类型列表
func (r *SysDictTypeRepositoryImpl) SelectDictTypeList(dictType *model.SysDictType) ([]*model.SysDictType, error) {
	var list []*model.SysDictType
	db := r.DB.Model(&model.SysDictType{})

	if dictType.DictName != "" {
		db = db.Where("dict_name LIKE ?", "%"+dictType.DictName+"%")
	}
	if dictType.Status != "" {
		db = db.Where("status = ?", dictType.Status)
	}
	if dictType.DictType != "" {
		db = db.Where("dict_type LIKE ?", "%"+dictType.DictType+"%")
	}

	err := db.Order("dict_sort").Find(&list).Error
	return list, err
}

// SelectDictTypeAll 查询所有字典类型
func (r *SysDictTypeRepositoryImpl) SelectDictTypeAll() ([]*model.SysDictType, error) {
	var list []*model.SysDictType
	err := r.DB.Find(&list).Error
	return list, err
}

// SelectDictTypeById 根据字典类型ID查询信息
func (r *SysDictTypeRepositoryImpl) SelectDictTypeById(dictId int64) (*model.SysDictType, error) {
	var dictType model.SysDictType
	err := r.DB.Where("dict_id = ?", dictId).First(&dictType).Error
	if err != nil {
		return nil, err
	}
	return &dictType, nil
}

// SelectDictTypeByType 根据字典类型查询信息
func (r *SysDictTypeRepositoryImpl) SelectDictTypeByType(dictType string) (*model.SysDictType, error) {
	var dict model.SysDictType
	err := r.DB.Where("dict_type = ?", dictType).First(&dict).Error
	if err != nil {
		return nil, err
	}
	return &dict, nil
}

// DeleteDictTypeById 删除字典类型
func (r *SysDictTypeRepositoryImpl) DeleteDictTypeById(dictId int64) error {
	return r.DB.Delete(&model.SysDictType{}, dictId).Error
}

// DeleteDictTypeByIds 批量删除字典类型
func (r *SysDictTypeRepositoryImpl) DeleteDictTypeByIds(dictIds []int64) error {
	return r.DB.Delete(&model.SysDictType{}, "dict_id IN ?", dictIds).Error
}

// InsertDictType 新增字典类型
func (r *SysDictTypeRepositoryImpl) InsertDictType(dictType *model.SysDictType) (int64, error) {
	err := r.DB.Create(dictType).Error
	if err != nil {
		return 0, err
	}
	return dictType.DictID, nil
}

// UpdateDictType 修改字典类型
func (r *SysDictTypeRepositoryImpl) UpdateDictType(dictType *model.SysDictType) (int64, error) {
	err := r.DB.Updates(dictType).Error
	if err != nil {
		return 0, err
	}
	return int64(r.DB.RowsAffected), nil
}

// CheckDictTypeUnique 校验字典类型是否唯一
func (r *SysDictTypeRepositoryImpl) CheckDictTypeUnique(dictType string, dictId int64) (*model.SysDictType, error) {
	var dict model.SysDictType
	err := r.DB.Where("dict_type = ?", dictType).First(&dict).Error
	if err != nil {
		return nil, err
	}
	if dict.DictID != dictId {
		return &dict, nil
	}
	return nil, nil
}

// DeleteDictTypeByIdTx 事务中删除字典类型
func (r *SysDictTypeRepositoryImpl) DeleteDictTypeByIdTx(tx *gorm.DB, dictId int64) error {
	return tx.Delete(&model.SysDictType{}, dictId).Error
}

// DeleteDictTypeByIdsTx 事务中批量删除字典类型
func (r *SysDictTypeRepositoryImpl) DeleteDictTypeByIdsTx(tx *gorm.DB, dictIds []int64) error {
	return tx.Delete(&model.SysDictType{}, "dict_id IN ?", dictIds).Error
}

// InsertDictTypeTx 事务中新增字典类型
func (r *SysDictTypeRepositoryImpl) InsertDictTypeTx(tx *gorm.DB, dictType *model.SysDictType) (int64, error) {
	err := tx.Create(dictType).Error
	if err != nil {
		return 0, err
	}
	return dictType.DictID, nil
}

// UpdateDictTypeTx 事务中修改字典类型
func (r *SysDictTypeRepositoryImpl) UpdateDictTypeTx(tx *gorm.DB, dictType *model.SysDictType) (int64, error) {
	err := tx.Updates(dictType).Error
	if err != nil {
		return 0, err
	}
	return int64(tx.RowsAffected), nil
}
