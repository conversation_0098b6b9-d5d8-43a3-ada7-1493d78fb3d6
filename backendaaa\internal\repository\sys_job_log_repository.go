package repository

import (
	"backend/internal/model"
	"errors"

	"gorm.io/gorm"
)

// SysJobLogRepository 定时任务调度日志信息数据访问接口
type SysJobLogRepository interface {
	Repository
	// SelectJobLogList 获取定时任务调度日志集合
	SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error)
	// SelectJobLogById 查询定时任务调度日志信息
	SelectJobLogById(jobLogId int64) (*model.SysJobLog, error)
	// InsertJobLog 新增定时任务调度日志信息
	InsertJobLog(jobLog *model.SysJobLog) (int64, error)
	// DeleteJobLogById 删除定时任务调度日志信息
	DeleteJobLogById(jobLogId int64) error
	// DeleteJobLogByIds 批量删除定时任务调度日志信息
	DeleteJobLogByIds(jobLogIds []int64) (int, error)
	// CleanJobLog 清空定时任务调度日志
	CleanJobLog() (int, error)

	// 事务相关方法
	// InsertJobLogTx 事务中新增定时任务调度日志信息
	InsertJobLogTx(tx *gorm.DB, jobLog *model.SysJobLog) (int64, error)
	// DeleteJobLogByIdTx 事务中删除定时任务调度日志信息
	DeleteJobLogByIdTx(tx *gorm.DB, jobLogId int64) error
	// DeleteJobLogByIdsTx 事务中批量删除定时任务调度日志信息
	DeleteJobLogByIdsTx(tx *gorm.DB, jobLogIds []int64) (int, error)
	// CleanJobLogTx 事务中清空定时任务调度日志
	CleanJobLogTx(tx *gorm.DB) (int, error)

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// SysJobLogRepositoryImpl 定时任务调度日志信息数据访问实现
type SysJobLogRepositoryImpl struct {
	*BaseRepository
}

// NewSysJobLogRepository 创建定时任务调度日志数据访问对象
func NewSysJobLogRepository(db *gorm.DB) SysJobLogRepository {
	return &SysJobLogRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// GetDB 获取数据库连接
func (r *SysJobLogRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

// SelectJobLogList 获取定时任务调度日志集合
func (r *SysJobLogRepositoryImpl) SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error) {
	var jobLogs []*model.SysJobLog
	query := r.DB.Model(&model.SysJobLog{})

	if jobLog.JobName != "" {
		query = query.Where("job_name like ?", "%"+jobLog.JobName+"%")
	}
	if jobLog.JobGroup != "" {
		query = query.Where("job_group = ?", jobLog.JobGroup)
	}
	if jobLog.Status != "" {
		query = query.Where("status = ?", jobLog.Status)
	}

	err := query.Find(&jobLogs).Error
	if err != nil {
		return nil, err
	}
	return jobLogs, nil
}

// SelectJobLogById 查询定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) SelectJobLogById(jobLogId int64) (*model.SysJobLog, error) {
	var jobLog model.SysJobLog
	err := r.DB.Where("job_log_id = ?", jobLogId).First(&jobLog).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &jobLog, nil
}

// InsertJobLog 新增定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) InsertJobLog(jobLog *model.SysJobLog) (int64, error) {
	err := r.DB.Create(jobLog).Error
	if err != nil {
		return 0, err
	}
	return jobLog.JobLogID, nil
}

// DeleteJobLogById 删除定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) DeleteJobLogById(jobLogId int64) error {
	return r.DB.Where("job_log_id = ?", jobLogId).Delete(&model.SysJobLog{}).Error
}

// DeleteJobLogByIds 批量删除定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) DeleteJobLogByIds(jobLogIds []int64) (int, error) {
	result := r.DB.Where("job_log_id IN ?", jobLogIds).Delete(&model.SysJobLog{})
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

// CleanJobLog 清空定时任务调度日志
func (r *SysJobLogRepositoryImpl) CleanJobLog() (int, error) {
	result := r.DB.Exec("TRUNCATE TABLE sys_job_log")
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

// InsertJobLogTx 事务中新增定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) InsertJobLogTx(tx *gorm.DB, jobLog *model.SysJobLog) (int64, error) {
	err := tx.Create(jobLog).Error
	if err != nil {
		return 0, err
	}
	return jobLog.JobLogID, nil
}

// DeleteJobLogByIdTx 事务中删除定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) DeleteJobLogByIdTx(tx *gorm.DB, jobLogId int64) error {
	return tx.Where("job_log_id = ?", jobLogId).Delete(&model.SysJobLog{}).Error
}

// DeleteJobLogByIdsTx 事务中批量删除定时任务调度日志信息
func (r *SysJobLogRepositoryImpl) DeleteJobLogByIdsTx(tx *gorm.DB, jobLogIds []int64) (int, error) {
	result := tx.Where("job_log_id IN ?", jobLogIds).Delete(&model.SysJobLog{})
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

// CleanJobLogTx 事务中清空定时任务调度日志
func (r *SysJobLogRepositoryImpl) CleanJobLogTx(tx *gorm.DB) (int, error) {
	result := tx.Exec("TRUNCATE TABLE sys_job_log")
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}
