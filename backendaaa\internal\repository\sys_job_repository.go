package repository

import (
	"backend/internal/model"
	"errors"

	"gorm.io/gorm"
)

// SysJobRepository 定时任务信息数据访问接口
type SysJobRepository interface {
	Repository
	// SelectJobList 查询定时任务列表
	SelectJobList(job *model.SysJob) ([]*model.SysJob, error)
	// SelectJobById 通过ID查询定时任务信息
	SelectJobById(jobId int64) (*model.SysJob, error)
	// InsertJob 新增定时任务信息
	InsertJob(job *model.SysJob) (int64, error)
	// UpdateJob 修改定时任务信息
	UpdateJob(job *model.SysJob) error
	// DeleteJobById 通过ID删除定时任务信息
	DeleteJobById(jobId int64) error
	// DeleteJobByIds 批量删除定时任务信息
	DeleteJobByIds(jobIds []int64) error
	// UpdateJobStatus 修改定时任务状态
	UpdateJobStatus(job *model.SysJob) error
	// SelectJobAll 查询所有调度任务
	SelectJobAll() ([]*model.SysJob, error)
	// CheckJobNameUnique 校验任务名称是否唯一
	CheckJobNameUnique(job *model.SysJob) (bool, error)

	// 事务相关方法
	// InsertJobTx 事务中新增定时任务信息
	InsertJobTx(tx *gorm.DB, job *model.SysJob) (int64, error)
	// UpdateJobTx 事务中修改定时任务信息
	UpdateJobTx(tx *gorm.DB, job *model.SysJob) error
	// DeleteJobByIdTx 事务中通过ID删除定时任务信息
	DeleteJobByIdTx(tx *gorm.DB, jobId int64) error
	// DeleteJobByIdsTx 事务中批量删除定时任务信息
	DeleteJobByIdsTx(tx *gorm.DB, jobIds []int64) error
	// UpdateJobStatusTx 事务中修改定时任务状态
	UpdateJobStatusTx(tx *gorm.DB, job *model.SysJob) error

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// SysJobRepositoryImpl 定时任务信息数据访问实现
type SysJobRepositoryImpl struct {
	*BaseRepository
}

// NewSysJobRepository 创建定时任务数据访问对象
func NewSysJobRepository(db *gorm.DB) SysJobRepository {
	return &SysJobRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// GetDB 获取数据库连接
func (r *SysJobRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

// SelectJobList 查询定时任务列表
func (r *SysJobRepositoryImpl) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	var jobs []*model.SysJob
	query := r.DB.Model(&model.SysJob{})

	if job.JobName != "" {
		query = query.Where("job_name like ?", "%"+job.JobName+"%")
	}
	if job.JobGroup != "" {
		query = query.Where("job_group = ?", job.JobGroup)
	}
	if job.Status != "" {
		query = query.Where("status = ?", job.Status)
	}

	err := query.Find(&jobs).Error
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

// SelectJobById 通过ID查询定时任务信息
func (r *SysJobRepositoryImpl) SelectJobById(jobId int64) (*model.SysJob, error) {
	var job model.SysJob
	err := r.DB.Where("job_id = ?", jobId).First(&job).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &job, nil
}

// InsertJob 新增定时任务信息
func (r *SysJobRepositoryImpl) InsertJob(job *model.SysJob) (int64, error) {
	err := r.DB.Create(job).Error
	if err != nil {
		return 0, err
	}
	return job.JobID, nil
}

// UpdateJob 修改定时任务信息
func (r *SysJobRepositoryImpl) UpdateJob(job *model.SysJob) error {
	return r.DB.Updates(job).Error
}

// DeleteJobById 通过ID删除定时任务信息
func (r *SysJobRepositoryImpl) DeleteJobById(jobId int64) error {
	return r.DB.Where("job_id = ?", jobId).Delete(&model.SysJob{}).Error
}

// DeleteJobByIds 批量删除定时任务信息
func (r *SysJobRepositoryImpl) DeleteJobByIds(jobIds []int64) error {
	return r.DB.Where("job_id IN ?", jobIds).Delete(&model.SysJob{}).Error
}

// UpdateJobStatus 修改定时任务状态
func (r *SysJobRepositoryImpl) UpdateJobStatus(job *model.SysJob) error {
	return r.DB.Model(&model.SysJob{}).
		Where("job_id = ?", job.JobID).
		Update("status", job.Status).Error
}

// SelectJobAll 查询所有调度任务
func (r *SysJobRepositoryImpl) SelectJobAll() ([]*model.SysJob, error) {
	var jobs []*model.SysJob
	err := r.DB.Find(&jobs).Error
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

// CheckJobNameUnique 校验任务名称是否唯一
func (r *SysJobRepositoryImpl) CheckJobNameUnique(job *model.SysJob) (bool, error) {
	var count int64
	query := r.DB.Model(&model.SysJob{}).
		Where("job_name = ?", job.JobName).
		Where("job_group = ?", job.JobGroup)

	if job.JobID != 0 {
		query = query.Where("job_id <> ?", job.JobID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// InsertJobTx 事务中新增定时任务信息
func (r *SysJobRepositoryImpl) InsertJobTx(tx *gorm.DB, job *model.SysJob) (int64, error) {
	err := tx.Create(job).Error
	if err != nil {
		return 0, err
	}
	return job.JobID, nil
}

// UpdateJobTx 事务中修改定时任务信息
func (r *SysJobRepositoryImpl) UpdateJobTx(tx *gorm.DB, job *model.SysJob) error {
	return tx.Updates(job).Error
}

// DeleteJobByIdTx 事务中通过ID删除定时任务信息
func (r *SysJobRepositoryImpl) DeleteJobByIdTx(tx *gorm.DB, jobId int64) error {
	return tx.Where("job_id = ?", jobId).Delete(&model.SysJob{}).Error
}

// DeleteJobByIdsTx 事务中批量删除定时任务信息
func (r *SysJobRepositoryImpl) DeleteJobByIdsTx(tx *gorm.DB, jobIds []int64) error {
	return tx.Where("job_id IN ?", jobIds).Delete(&model.SysJob{}).Error
}

// UpdateJobStatusTx 事务中修改定时任务状态
func (r *SysJobRepositoryImpl) UpdateJobStatusTx(tx *gorm.DB, job *model.SysJob) error {
	return tx.Model(&model.SysJob{}).
		Where("job_id = ?", job.JobID).
		Update("status", job.Status).Error
}
