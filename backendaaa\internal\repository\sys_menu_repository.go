package repository

import (
	"database/sql"

	"github.com/jmoiron/sqlx"

	"backend/internal/model"
)

// SysMenuRepository interface defines methods for SysMenu data access
type SysMenuRepository interface {
	// SelectMenuList returns all menus based on the filter
	SelectMenuList(menu *model.SysMenu) ([]*model.SysMenu, error)

	// SelectMenuPerms returns all permissions
	SelectMenuPerms() ([]string, error)

	// SelectMenuListByUserId returns menus for a specific user
	SelectMenuListByUserId(menu *model.SysMenu, userId int64) ([]*model.SysMenu, error)

	// SelectMenuPermsByRoleId returns permissions for a specific role
	SelectMenuPermsByRoleId(roleId int64) ([]string, error)

	// SelectMenuPermsByUserId returns permissions for a specific user
	SelectMenuPermsByUserId(userId int64) ([]string, error)

	// SelectMenuTreeAll returns the full menu tree
	SelectMenuTreeAll() ([]*model.SysMenu, error)

	// SelectMenuTreeByUserId returns the menu tree for a specific user
	SelectMenuTreeByUserId(userId int64) ([]*model.SysMenu, error)

	// SelectMenuListByRoleId returns menus associated with a role
	SelectMenuListByRoleId(roleId int64, menuCheckStrictly bool) ([]int64, error)

	// SelectMenuById returns a specific menu by ID
	SelectMenuById(menuId int64) (*model.SysMenu, error)

	// HasChildByMenuId checks if a menu has child items
	HasChildByMenuId(menuId int64) (int, error)

	// InsertMenu creates a new menu
	InsertMenu(menu *model.SysMenu) (int64, error)

	// UpdateMenu updates an existing menu
	UpdateMenu(menu *model.SysMenu) (int64, error)

	// DeleteMenuById deletes a menu
	DeleteMenuById(menuId int64) (int64, error)

	// CheckMenuNameUnique checks if a menu name is unique
	CheckMenuNameUnique(menuName string, parentId int64) (*model.SysMenu, error)

	// 事务相关方法
	// InsertMenuTx 在事务中创建菜单
	InsertMenuTx(tx *sqlx.Tx, menu *model.SysMenu) (int64, error)

	// UpdateMenuTx 在事务中更新菜单
	UpdateMenuTx(tx *sqlx.Tx, menu *model.SysMenu) (int64, error)

	// DeleteMenuByIdTx 在事务中删除菜单
	DeleteMenuByIdTx(tx *sqlx.Tx, menuId int64) (int64, error)
}

// sysMenuRepository implements SysMenuRepository interface
type sysMenuRepository struct {
	db *sqlx.DB
}

// NewSysMenuRepository creates a new SysMenuRepository
func NewSysMenuRepository(db *sqlx.DB) SysMenuRepository {
	return &sysMenuRepository{db: db}
}

// SelectMenuList implements SysMenuRepository.SelectMenuList
func (r *sysMenuRepository) SelectMenuList(menu *model.SysMenu) ([]*model.SysMenu, error) {
	query := `
		SELECT 
			menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, 
			visible, status, perms, icon, create_by, create_time, update_by, update_time, remark 
		FROM sys_menu
		WHERE 1=1
	`
	args := []interface{}{}

	if menu.MenuName != "" {
		query += " AND menu_name like ?"
		args = append(args, "%"+menu.MenuName+"%")
	}

	if menu.Visible != "" {
		query += " AND visible = ?"
		args = append(args, menu.Visible)
	}

	if menu.Status != "" {
		query += " AND status = ?"
		args = append(args, menu.Status)
	}

	query += " ORDER BY parent_id, order_num"

	var menus []*model.SysMenu
	err := r.db.Select(&menus, query, args...)
	return menus, err
}

// SelectMenuPerms implements SysMenuRepository.SelectMenuPerms
func (r *sysMenuRepository) SelectMenuPerms() ([]string, error) {
	query := `
		SELECT 
			DISTINCT perms
		FROM sys_menu
		WHERE perms IS NOT NULL AND perms != ''
	`

	var perms []string
	err := r.db.Select(&perms, query)
	return perms, err
}

// SelectMenuListByUserId implements SysMenuRepository.SelectMenuListByUserId
func (r *sysMenuRepository) SelectMenuListByUserId(menu *model.SysMenu, userId int64) ([]*model.SysMenu, error) {
	query := `
		SELECT DISTINCT
			m.menu_id, m.menu_name, m.parent_id, m.order_num, m.path, m.component, m.is_frame, m.is_cache, 
			m.menu_type, m.visible, m.status, m.perms, m.icon, m.create_by, m.create_time, m.update_by, 
			m.update_time, m.remark
		FROM sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
		LEFT JOIN sys_role ro ON ur.role_id = ro.role_id
		WHERE ur.user_id = ?
	`
	args := []interface{}{userId}

	if menu.MenuName != "" {
		query += " AND m.menu_name like ?"
		args = append(args, "%"+menu.MenuName+"%")
	}

	if menu.Visible != "" {
		query += " AND m.visible = ?"
		args = append(args, menu.Visible)
	}

	if menu.Status != "" {
		query += " AND m.status = ?"
		args = append(args, menu.Status)
	}

	query += " ORDER BY m.parent_id, m.order_num"

	var menus []*model.SysMenu
	err := r.db.Select(&menus, query, args...)
	return menus, err
}

// SelectMenuPermsByRoleId implements SysMenuRepository.SelectMenuPermsByRoleId
func (r *sysMenuRepository) SelectMenuPermsByRoleId(roleId int64) ([]string, error) {
	query := `
		SELECT 
			DISTINCT m.perms
		FROM sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		WHERE rm.role_id = ? AND m.perms IS NOT NULL AND m.perms != ''
	`

	var perms []string
	err := r.db.Select(&perms, query, roleId)
	return perms, err
}

// SelectMenuPermsByUserId implements SysMenuRepository.SelectMenuPermsByUserId
func (r *sysMenuRepository) SelectMenuPermsByUserId(userId int64) ([]string, error) {
	query := `
		SELECT
			DISTINCT m.perms
		FROM sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
		LEFT JOIN sys_role r ON r.role_id = ur.role_id
		WHERE m.perms IS NOT NULL AND m.perms != ''
		AND ur.user_id = ?
	`

	var perms []string
	err := r.db.Select(&perms, query, userId)
	return perms, err
}

// SelectMenuTreeAll implements SysMenuRepository.SelectMenuTreeAll
func (r *sysMenuRepository) SelectMenuTreeAll() ([]*model.SysMenu, error) {
	query := `
		SELECT
			m.menu_id, m.menu_name, m.parent_id, m.order_num, m.path, m.component, m.is_frame, m.is_cache, 
			m.menu_type, m.visible, m.status, m.perms, m.icon, m.create_by, m.create_time, m.update_by, 
			m.update_time, m.remark
		FROM sys_menu m
		WHERE m.menu_type IN ('M', 'C') AND m.status = '0'
		ORDER BY m.parent_id, m.order_num
	`

	var menus []*model.SysMenu
	err := r.db.Select(&menus, query)
	return menus, err
}

// SelectMenuTreeByUserId implements SysMenuRepository.SelectMenuTreeByUserId
func (r *sysMenuRepository) SelectMenuTreeByUserId(userId int64) ([]*model.SysMenu, error) {
	query := `
		SELECT DISTINCT
			m.menu_id, m.menu_name, m.parent_id, m.order_num, m.path, m.component, m.is_frame, m.is_cache, 
			m.menu_type, m.visible, m.status, m.perms, m.icon, m.create_by, m.create_time, m.update_by, 
			m.update_time, m.remark
		FROM sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
		LEFT JOIN sys_role ro ON ur.role_id = ro.role_id
		WHERE ur.user_id = ? AND m.menu_type IN ('M', 'C') AND m.status = '0'
		ORDER BY m.parent_id, m.order_num
	`

	var menus []*model.SysMenu
	err := r.db.Select(&menus, query, userId)
	return menus, err
}

// SelectMenuListByRoleId implements SysMenuRepository.SelectMenuListByRoleId
func (r *sysMenuRepository) SelectMenuListByRoleId(roleId int64, menuCheckStrictly bool) ([]int64, error) {
	var query string

	if menuCheckStrictly {
		query = `
			SELECT
				m.menu_id
			FROM sys_menu m
			LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
			WHERE rm.role_id = ?
		`
	} else {
		query = `
			SELECT
				m.menu_id
			FROM sys_menu m
			LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
			WHERE rm.role_id = ?
			AND m.menu_id NOT IN (
				SELECT m.parent_id
				FROM sys_menu m
				INNER JOIN sys_role_menu rm ON m.menu_id = rm.menu_id AND rm.role_id = ?
			)
		`
	}

	var menuIds []int64
	var err error

	if menuCheckStrictly {
		err = r.db.Select(&menuIds, query, roleId)
	} else {
		err = r.db.Select(&menuIds, query, roleId, roleId)
	}

	return menuIds, err
}

// SelectMenuById implements SysMenuRepository.SelectMenuById
func (r *sysMenuRepository) SelectMenuById(menuId int64) (*model.SysMenu, error) {
	query := `
		SELECT 
			menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, 
			visible, status, perms, icon, create_by, create_time, update_by, update_time, remark 
		FROM sys_menu
		WHERE menu_id = ?
	`

	var menu model.SysMenu
	err := r.db.Get(&menu, query, menuId)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &menu, err
}

// HasChildByMenuId implements SysMenuRepository.HasChildByMenuId
func (r *sysMenuRepository) HasChildByMenuId(menuId int64) (int, error) {
	query := `
		SELECT COUNT(1) FROM sys_menu WHERE parent_id = ?
	`

	var count int
	err := r.db.Get(&count, query, menuId)
	return count, err
}

// InsertMenu implements SysMenuRepository.InsertMenu
func (r *sysMenuRepository) InsertMenu(menu *model.SysMenu) (int64, error) {
	query := `
		INSERT INTO sys_menu (
			menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, 
			visible, status, perms, icon, create_by, create_time, update_by, update_time, remark
		) VALUES (
			?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
		)
	`

	res, err := r.db.Exec(query,
		menu.MenuName, menu.ParentID, menu.OrderNum, menu.Path, menu.Component, menu.IsFrame,
		menu.IsCache, menu.MenuType, menu.Visible, menu.Status, menu.Perms, menu.Icon,
		menu.CreateBy, menu.CreateTime, menu.UpdateBy, menu.UpdateTime, menu.Remark,
	)

	if err != nil {
		return 0, err
	}

	return res.LastInsertId()
}

// UpdateMenu implements SysMenuRepository.UpdateMenu
func (r *sysMenuRepository) UpdateMenu(menu *model.SysMenu) (int64, error) {
	query := `
		UPDATE sys_menu
		SET 
			menu_name = ?, parent_id = ?, order_num = ?, path = ?, component = ?, is_frame = ?,
			is_cache = ?, menu_type = ?, visible = ?, status = ?, perms = ?, icon = ?,
			update_by = ?, update_time = ?, remark = ?
		WHERE menu_id = ?
	`

	res, err := r.db.Exec(query,
		menu.MenuName, menu.ParentID, menu.OrderNum, menu.Path, menu.Component, menu.IsFrame,
		menu.IsCache, menu.MenuType, menu.Visible, menu.Status, menu.Perms, menu.Icon,
		menu.UpdateBy, menu.UpdateTime, menu.Remark, menu.MenuID,
	)

	if err != nil {
		return 0, err
	}

	return res.RowsAffected()
}

// DeleteMenuById implements SysMenuRepository.DeleteMenuById
func (r *sysMenuRepository) DeleteMenuById(menuId int64) (int64, error) {
	query := `DELETE FROM sys_menu WHERE menu_id = ?`

	res, err := r.db.Exec(query, menuId)

	if err != nil {
		return 0, err
	}

	return res.RowsAffected()
}

// CheckMenuNameUnique implements SysMenuRepository.CheckMenuNameUnique
func (r *sysMenuRepository) CheckMenuNameUnique(menuName string, parentId int64) (*model.SysMenu, error) {
	query := `
		SELECT 
			menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, 
			visible, status, perms, icon, create_by, create_time, update_by, update_time, remark 
		FROM sys_menu
		WHERE menu_name = ? AND parent_id = ?
		LIMIT 1
	`

	var menu model.SysMenu
	err := r.db.Get(&menu, query, menuName, parentId)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &menu, err
}

// InsertMenuTx 在事务中创建菜单
func (r *sysMenuRepository) InsertMenuTx(tx *sqlx.Tx, menu *model.SysMenu) (int64, error) {
	query := `
		INSERT INTO sys_menu (
			menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, 
			visible, status, perms, icon, create_by, create_time, update_by, update_time, remark
		) VALUES (
			?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
		)
	`

	res, err := tx.Exec(query,
		menu.MenuName, menu.ParentID, menu.OrderNum, menu.Path, menu.Component, menu.IsFrame,
		menu.IsCache, menu.MenuType, menu.Visible, menu.Status, menu.Perms, menu.Icon,
		menu.CreateBy, menu.CreateTime, menu.UpdateBy, menu.UpdateTime, menu.Remark,
	)

	if err != nil {
		return 0, err
	}

	return res.LastInsertId()
}

// UpdateMenuTx 在事务中更新菜单
func (r *sysMenuRepository) UpdateMenuTx(tx *sqlx.Tx, menu *model.SysMenu) (int64, error) {
	query := `
		UPDATE sys_menu
		SET 
			menu_name = ?, parent_id = ?, order_num = ?, path = ?, component = ?, is_frame = ?,
			is_cache = ?, menu_type = ?, visible = ?, status = ?, perms = ?, icon = ?,
			update_by = ?, update_time = ?, remark = ?
		WHERE menu_id = ?
	`

	res, err := tx.Exec(query,
		menu.MenuName, menu.ParentID, menu.OrderNum, menu.Path, menu.Component, menu.IsFrame,
		menu.IsCache, menu.MenuType, menu.Visible, menu.Status, menu.Perms, menu.Icon,
		menu.UpdateBy, menu.UpdateTime, menu.Remark, menu.MenuID,
	)

	if err != nil {
		return 0, err
	}

	return res.RowsAffected()
}

// DeleteMenuByIdTx 在事务中删除菜单
func (r *sysMenuRepository) DeleteMenuByIdTx(tx *sqlx.Tx, menuId int64) (int64, error) {
	query := `DELETE FROM sys_menu WHERE menu_id = ?`

	res, err := tx.Exec(query, menuId)

	if err != nil {
		return 0, err
	}

	return res.RowsAffected()
}
