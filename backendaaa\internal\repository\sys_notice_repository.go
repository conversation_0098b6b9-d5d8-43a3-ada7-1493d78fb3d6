package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysNoticeRepository 通知公告仓库接口
type SysNoticeRepository interface {
	// SelectNoticeById 查询公告信息
	SelectNoticeById(noticeId int64) (*model.SysNotice, error)

	// SelectNoticeList 查询公告列表
	SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error)

	// InsertNotice 新增公告
	InsertNotice(notice *model.SysNotice) (int64, error)

	// UpdateNotice 修改公告
	UpdateNotice(notice *model.SysNotice) (int64, error)

	// DeleteNoticeById 删除公告信息
	DeleteNoticeById(noticeId int64) error

	// DeleteNoticeByIds 批量删除公告信息
	DeleteNoticeByIds(noticeIds []int64) error

	// 事务相关方法
	// InsertNoticeTx 事务中新增公告
	InsertNoticeTx(tx *gorm.DB, notice *model.SysNotice) (int64, error)

	// UpdateNoticeTx 事务中修改公告
	UpdateNoticeTx(tx *gorm.DB, notice *model.SysNotice) (int64, error)

	// DeleteNoticeByIdTx 事务中删除公告信息
	DeleteNoticeByIdTx(tx *gorm.DB, noticeId int64) error

	// DeleteNoticeByIdsTx 事务中批量删除公告信息
	DeleteNoticeByIdsTx(tx *gorm.DB, noticeIds []int64) error

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// SysNoticeRepositoryImpl 通知公告仓库实现
type SysNoticeRepositoryImpl struct {
	*BaseRepository
}

// NewSysNoticeRepository 创建通知公告仓库
func NewSysNoticeRepository(db *gorm.DB) SysNoticeRepository {
	return &SysNoticeRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// GetDB 获取数据库连接
func (r *SysNoticeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

// SelectNoticeById 查询公告信息
func (r *SysNoticeRepositoryImpl) SelectNoticeById(noticeId int64) (*model.SysNotice, error) {
	var notice model.SysNotice
	err := r.DB.Where("notice_id = ?", noticeId).First(&notice).Error
	if err != nil {
		return nil, err
	}
	return &notice, nil
}

// SelectNoticeList 查询公告列表
func (r *SysNoticeRepositoryImpl) SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error) {
	var list []*model.SysNotice
	db := r.DB.Model(&model.SysNotice{})

	if notice.NoticeTitle != "" {
		db = db.Where("notice_title LIKE ?", "%"+notice.NoticeTitle+"%")
	}
	if notice.NoticeType != "" {
		db = db.Where("notice_type = ?", notice.NoticeType)
	}
	if notice.CreateBy != "" {
		db = db.Where("create_by = ?", notice.CreateBy)
	}
	if notice.Status != "" {
		db = db.Where("status = ?", notice.Status)
	}

	err := db.Order("create_time DESC").Find(&list).Error
	return list, err
}

// InsertNotice 新增公告
func (r *SysNoticeRepositoryImpl) InsertNotice(notice *model.SysNotice) (int64, error) {
	err := r.DB.Create(notice).Error
	if err != nil {
		return 0, err
	}
	return notice.NoticeID, nil
}

// UpdateNotice 修改公告
func (r *SysNoticeRepositoryImpl) UpdateNotice(notice *model.SysNotice) (int64, error) {
	err := r.DB.Model(&model.SysNotice{}).Where("notice_id = ?", notice.NoticeID).Updates(notice).Error
	if err != nil {
		return 0, err
	}
	return int64(r.DB.RowsAffected), nil
}

// DeleteNoticeById 删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeById(noticeId int64) error {
	return r.DB.Delete(&model.SysNotice{}, noticeId).Error
}

// DeleteNoticeByIds 批量删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeByIds(noticeIds []int64) error {
	return r.DB.Delete(&model.SysNotice{}, "notice_id IN ?", noticeIds).Error
}

// InsertNoticeTx 事务中新增公告
func (r *SysNoticeRepositoryImpl) InsertNoticeTx(tx *gorm.DB, notice *model.SysNotice) (int64, error) {
	err := tx.Create(notice).Error
	if err != nil {
		return 0, err
	}
	return notice.NoticeID, nil
}

// UpdateNoticeTx 事务中修改公告
func (r *SysNoticeRepositoryImpl) UpdateNoticeTx(tx *gorm.DB, notice *model.SysNotice) (int64, error) {
	err := tx.Model(&model.SysNotice{}).Where("notice_id = ?", notice.NoticeID).Updates(notice).Error
	if err != nil {
		return 0, err
	}
	return int64(tx.RowsAffected), nil
}

// DeleteNoticeByIdTx 事务中删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeByIdTx(tx *gorm.DB, noticeId int64) error {
	return tx.Delete(&model.SysNotice{}, noticeId).Error
}

// DeleteNoticeByIdsTx 事务中批量删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeByIdsTx(tx *gorm.DB, noticeIds []int64) error {
	return tx.Delete(&model.SysNotice{}, "notice_id IN ?", noticeIds).Error
}
