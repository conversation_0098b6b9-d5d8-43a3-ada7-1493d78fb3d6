package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysOperLogRepository 操作日志数据访问接口
type SysOperLogRepository interface {
	// Insert 插入操作日志
	Insert(operLog *model.SysOperLog) error

	// SelectList 查询操作日志列表
	SelectList(operLog *model.SysOperLog) ([]*model.SysOperLog, error)

	// SelectById 根据ID查询操作日志
	SelectById(operId int64) (*model.SysOperLog, error)

	// DeleteByIds 批量删除操作日志
	DeleteByIds(operIds []int64) (int64, error)

	// DeleteAll 清空操作日志
	DeleteAll() error

	// 事务相关方法
	// InsertTx 事务中插入操作日志
	InsertTx(tx *gorm.DB, operLog *model.SysOperLog) error

	// DeleteByIdsTx 事务中批量删除操作日志
	DeleteByIdsTx(tx *gorm.DB, operIds []int64) (int64, error)

	// DeleteAllTx 事务中清空操作日志
	DeleteAllTx(tx *gorm.DB) error

	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// GormSysOperLogRepository 基于Gorm的操作日志数据访问实现
type GormSysOperLogRepository struct {
	db *gorm.DB
}

// NewSysOperLogRepository 创建操作日志数据访问实例
func NewSysOperLogRepository(db *gorm.DB) SysOperLogRepository {
	return &GormSysOperLogRepository{db: db}
}

// GetDB 获取数据库连接
func (r *GormSysOperLogRepository) GetDB() *gorm.DB {
	return r.db
}

// Insert 插入操作日志
func (r *GormSysOperLogRepository) Insert(operLog *model.SysOperLog) error {
	return r.db.Create(operLog).Error
}

// SelectList 查询操作日志列表
func (r *GormSysOperLogRepository) SelectList(operLog *model.SysOperLog) ([]*model.SysOperLog, error) {
	var logs []*model.SysOperLog
	db := r.db.Model(&model.SysOperLog{})

	// 构建查询条件
	if operLog != nil {
		if operLog.Title != "" {
			db = db.Where("title LIKE ?", "%"+operLog.Title+"%")
		}
		if operLog.BusinessType != 0 {
			db = db.Where("business_type = ?", operLog.BusinessType)
		}
		if operLog.Status != 0 {
			db = db.Where("status = ?", operLog.Status)
		}
		if operLog.OperName != "" {
			db = db.Where("oper_name LIKE ?", "%"+operLog.OperName+"%")
		}
		if operLog.OperIp != "" {
			db = db.Where("oper_ip LIKE ?", "%"+operLog.OperIp+"%")
		}
		if operLog.OperTime != nil {
			db = db.Where("oper_time >= ?", operLog.OperTime)
		}
	}

	// 按操作时间倒序排序
	db = db.Order("oper_time DESC")

	err := db.Find(&logs).Error
	return logs, err
}

// SelectById 根据ID查询操作日志
func (r *GormSysOperLogRepository) SelectById(operId int64) (*model.SysOperLog, error) {
	var operLog model.SysOperLog
	err := r.db.Where("oper_id = ?", operId).First(&operLog).Error
	if err != nil {
		return nil, err
	}
	return &operLog, nil
}

// DeleteByIds 批量删除操作日志
func (r *GormSysOperLogRepository) DeleteByIds(operIds []int64) (int64, error) {
	result := r.db.Where("oper_id IN ?", operIds).Delete(&model.SysOperLog{})
	return result.RowsAffected, result.Error
}

// DeleteAll 清空操作日志
func (r *GormSysOperLogRepository) DeleteAll() error {
	return r.db.Exec("DELETE FROM sys_oper_log").Error
}

// InsertTx 事务中插入操作日志
func (r *GormSysOperLogRepository) InsertTx(tx *gorm.DB, operLog *model.SysOperLog) error {
	return tx.Create(operLog).Error
}

// DeleteByIdsTx 事务中批量删除操作日志
func (r *GormSysOperLogRepository) DeleteByIdsTx(tx *gorm.DB, operIds []int64) (int64, error) {
	result := tx.Where("oper_id IN ?", operIds).Delete(&model.SysOperLog{})
	return result.RowsAffected, result.Error
}

// DeleteAllTx 事务中清空操作日志
func (r *GormSysOperLogRepository) DeleteAllTx(tx *gorm.DB) error {
	return tx.Exec("DELETE FROM sys_oper_log").Error
}
