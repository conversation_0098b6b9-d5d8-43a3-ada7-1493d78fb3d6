package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysPostRepository 岗位Repository接口
type SysPostRepository interface {
	Repository
	// SelectPostList 查询岗位列表
	SelectPostList(post *model.SysPost) ([]*model.SysPost, error)
	// SelectPostById 根据岗位ID查询岗位
	SelectPostById(postId int64) (*model.SysPost, error)
	// SelectPostsByUserName 根据用户名查询岗位
	SelectPostsByUserName(userName string) ([]*model.SysPost, error)
	// SelectPostsByUserId 根据用户ID查询岗位
	SelectPostsByUserId(userId int64) ([]*model.SysPost, error)
	// InsertPost 新增岗位信息
	InsertPost(post *model.SysPost) (int64, error)
	// UpdatePost 修改岗位信息
	UpdatePost(post *model.SysPost) error
	// DeletePostById 通过岗位ID删除岗位
	DeletePostById(postId int64) error
	// DeletePostByIds 批量删除岗位信息
	DeletePostByIds(postIds []int64) error
	// CheckPostNameUnique 校验岗位名称是否唯一
	CheckPostNameUnique(post *model.SysPost) (bool, error)
	// CheckPostCodeUnique 校验岗位编码是否唯一
	CheckPostCodeUnique(post *model.SysPost) (bool, error)

	// 事务相关方法
	// InsertPostTx 事务中新增岗位信息
	InsertPostTx(tx *gorm.DB, post *model.SysPost) (int64, error)
	// UpdatePostTx 事务中修改岗位信息
	UpdatePostTx(tx *gorm.DB, post *model.SysPost) error
	// DeletePostByIdTx 事务中通过岗位ID删除岗位
	DeletePostByIdTx(tx *gorm.DB, postId int64) error
	// DeletePostByIdsTx 事务中批量删除岗位信息
	DeletePostByIdsTx(tx *gorm.DB, postIds []int64) error
}

// SysPostRepositoryImpl 岗位Repository实现
type SysPostRepositoryImpl struct {
	*BaseRepository
}

// NewSysPostRepository 创建岗位Repository
func NewSysPostRepository(db *gorm.DB) SysPostRepository {
	return &SysPostRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectPostList 查询岗位列表
func (r *SysPostRepositoryImpl) SelectPostList(post *model.SysPost) ([]*model.SysPost, error) {
	var posts []*model.SysPost
	db := r.DB.Model(&model.SysPost{})

	if post.PostID != 0 {
		db = db.Where("post_id = ?", post.PostID)
	}
	if post.PostName != "" {
		db = db.Where("post_name LIKE ?", "%"+post.PostName+"%")
	}
	if post.PostCode != "" {
		db = db.Where("post_code LIKE ?", "%"+post.PostCode+"%")
	}
	if post.Status != "" {
		db = db.Where("status = ?", post.Status)
	}

	err := db.Find(&posts).Error
	return posts, err
}

// SelectPostById 根据岗位ID查询岗位
func (r *SysPostRepositoryImpl) SelectPostById(postId int64) (*model.SysPost, error) {
	var post model.SysPost
	err := r.DB.Where("post_id = ?", postId).First(&post).Error
	if err != nil {
		return nil, err
	}
	return &post, nil
}

// SelectPostsByUserName 根据用户名查询岗位
func (r *SysPostRepositoryImpl) SelectPostsByUserName(userName string) ([]*model.SysPost, error) {
	var posts []*model.SysPost
	err := r.DB.Model(&model.SysPost{}).
		Joins("INNER JOIN sys_user_post up ON up.post_id = sys_post.post_id").
		Joins("INNER JOIN sys_user u ON u.user_id = up.user_id").
		Where("u.user_name = ?", userName).
		Find(&posts).Error
	return posts, err
}

// SelectPostsByUserId 根据用户ID查询岗位
func (r *SysPostRepositoryImpl) SelectPostsByUserId(userId int64) ([]*model.SysPost, error) {
	var posts []*model.SysPost
	err := r.DB.Model(&model.SysPost{}).
		Joins("INNER JOIN sys_user_post up ON up.post_id = sys_post.post_id").
		Where("up.user_id = ?", userId).
		Find(&posts).Error
	return posts, err
}

// InsertPost 新增岗位信息
func (r *SysPostRepositoryImpl) InsertPost(post *model.SysPost) (int64, error) {
	err := r.DB.Create(post).Error
	if err != nil {
		return 0, err
	}
	return post.PostID, nil
}

// UpdatePost 修改岗位信息
func (r *SysPostRepositoryImpl) UpdatePost(post *model.SysPost) error {
	return r.DB.Updates(post).Error
}

// DeletePostById 通过岗位ID删除岗位
func (r *SysPostRepositoryImpl) DeletePostById(postId int64) error {
	return r.DB.Delete(&model.SysPost{}, postId).Error
}

// DeletePostByIds 批量删除岗位信息
func (r *SysPostRepositoryImpl) DeletePostByIds(postIds []int64) error {
	return r.DB.Delete(&model.SysPost{}, "post_id IN ?", postIds).Error
}

// CheckPostNameUnique 校验岗位名称是否唯一
func (r *SysPostRepositoryImpl) CheckPostNameUnique(post *model.SysPost) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysPost{}).Where("post_name = ?", post.PostName)
	if post.PostID != 0 {
		db = db.Where("post_id <> ?", post.PostID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// CheckPostCodeUnique 校验岗位编码是否唯一
func (r *SysPostRepositoryImpl) CheckPostCodeUnique(post *model.SysPost) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysPost{}).Where("post_code = ?", post.PostCode)
	if post.PostID != 0 {
		db = db.Where("post_id <> ?", post.PostID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// InsertPostTx 事务中新增岗位信息
func (r *SysPostRepositoryImpl) InsertPostTx(tx *gorm.DB, post *model.SysPost) (int64, error) {
	err := tx.Create(post).Error
	if err != nil {
		return 0, err
	}
	return post.PostID, nil
}

// UpdatePostTx 事务中修改岗位信息
func (r *SysPostRepositoryImpl) UpdatePostTx(tx *gorm.DB, post *model.SysPost) error {
	return tx.Updates(post).Error
}

// DeletePostByIdTx 事务中通过岗位ID删除岗位
func (r *SysPostRepositoryImpl) DeletePostByIdTx(tx *gorm.DB, postId int64) error {
	return tx.Delete(&model.SysPost{}, postId).Error
}

// DeletePostByIdsTx 事务中批量删除岗位信息
func (r *SysPostRepositoryImpl) DeletePostByIdsTx(tx *gorm.DB, postIds []int64) error {
	return tx.Delete(&model.SysPost{}, "post_id IN ?", postIds).Error
}
