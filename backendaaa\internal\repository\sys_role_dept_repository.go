package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysRoleDeptRepository 角色与部门关联表Repository接口
type SysRoleDeptRepository interface {
	Repository
	// DeleteRoleDeptByRoleId 通过角色ID删除角色和部门关联
	DeleteRoleDeptByRoleId(roleId int64) error
	// DeleteRoleDeptByRoleIdTx 事务中通过角色ID删除角色和部门关联
	DeleteRoleDeptByRoleIdTx(tx *gorm.DB, roleId int64) error
	// DeleteRoleDept 批量删除角色部门关联信息
	DeleteRoleDept(roleIds []int64) error
	// BatchRoleDept 批量新增角色部门信息
	BatchRoleDept(roleDeptList []*model.SysRoleDept) error
	// BatchRoleDeptTx 事务中批量新增角色部门信息
	BatchRoleDeptTx(tx *gorm.DB, roleDeptList []*model.SysRoleDept) error
	// SelectCountRoleDeptByDeptId 查询部门使用数量
	SelectCountRoleDeptByDeptId(deptId int64) (int64, error)
}

// SysRoleDeptRepositoryImpl 角色与部门关联表Repository实现
type SysRoleDeptRepositoryImpl struct {
	*BaseRepository
}

// NewSysRoleDeptRepository 创建角色与部门关联表Repository
func NewSysRoleDeptRepository(db *gorm.DB) SysRoleDeptRepository {
	return &SysRoleDeptRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// DeleteRoleDeptByRoleId 通过角色ID删除角色和部门关联
func (r *SysRoleDeptRepositoryImpl) DeleteRoleDeptByRoleId(roleId int64) error {
	return r.DB.Where("role_id = ?", roleId).Delete(&model.SysRoleDept{}).Error
}

// DeleteRoleDeptByRoleIdTx 事务中通过角色ID删除角色和部门关联
func (r *SysRoleDeptRepositoryImpl) DeleteRoleDeptByRoleIdTx(tx *gorm.DB, roleId int64) error {
	return tx.Where("role_id = ?", roleId).Delete(&model.SysRoleDept{}).Error
}

// DeleteRoleDept 批量删除角色部门关联信息
func (r *SysRoleDeptRepositoryImpl) DeleteRoleDept(roleIds []int64) error {
	return r.DB.Where("role_id IN ?", roleIds).Delete(&model.SysRoleDept{}).Error
}

// BatchRoleDept 批量新增角色部门信息
func (r *SysRoleDeptRepositoryImpl) BatchRoleDept(roleDeptList []*model.SysRoleDept) error {
	if len(roleDeptList) == 0 {
		return nil
	}
	return r.DB.Create(&roleDeptList).Error
}

// BatchRoleDeptTx 事务中批量新增角色部门信息
func (r *SysRoleDeptRepositoryImpl) BatchRoleDeptTx(tx *gorm.DB, roleDeptList []*model.SysRoleDept) error {
	if len(roleDeptList) == 0 {
		return nil
	}
	return tx.Create(&roleDeptList).Error
}

// SelectCountRoleDeptByDeptId 查询部门使用数量
func (r *SysRoleDeptRepositoryImpl) SelectCountRoleDeptByDeptId(deptId int64) (int64, error) {
	var count int64
	err := r.DB.Model(&model.SysRoleDept{}).Where("dept_id = ?", deptId).Count(&count).Error
	return count, err
}
