package router

import (
	"backend/internal/api/router"
	"backend/internal/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// InitRouter 初始化路由
func InitRouter(db *gorm.DB) *gin.Engine {
	r := gin.New()
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RecoveryMiddleware())
	r.Use(middleware.CORSMiddleware())
	// 添加参数验证中间件，实现与Java后端类似的@Validated功能
	r.Use(middleware.Validator())

	// 初始化所有路由，使用api/router包，确保与Java后端保持一致
	router.InitRouter(r)

	return r
}
