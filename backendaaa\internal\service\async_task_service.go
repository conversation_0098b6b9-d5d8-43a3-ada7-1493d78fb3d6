package service

import (
	"context"
	"time"
)

// Task 任务接口
type Task interface {
	// Execute 执行任务
	Execute(ctx context.Context) error

	// GetID 获取任务ID
	GetID() string

	// GetName 获取任务名称
	GetName() string
}

// AsyncTaskService 异步任务服务接口
type AsyncTaskService interface {
	// Submit 提交任务
	Submit(task Task) error

	// SubmitWithDelay 延迟提交任务
	SubmitWithDelay(task Task, delay time.Duration) error

	// Cancel 取消任务
	Cancel(taskID string) error

	// GetTaskStatus 获取任务状态
	GetTaskStatus(taskID string) (string, error)
}
