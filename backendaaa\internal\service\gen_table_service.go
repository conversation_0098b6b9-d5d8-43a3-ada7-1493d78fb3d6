package service

import (
	"backend/internal/model"
	"io"
)

// GenTableService 代码生成表业务接口
type GenTableService interface {
	// SelectGenTableList 查询业务列表
	SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error)
	// SelectDbTableList 查询数据库表列表
	SelectDbTableList(genTable *model.GenTable) ([]*model.GenTable, error)
	// SelectDbTableListByNames 根据表名查询数据库表列表
	SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error)
	// SelectGenTableById 查询业务信息
	SelectGenTableById(id int64) (*model.GenTable, error)
	// SelectGenTableAll 查询所有表信息
	SelectGenTableAll() ([]*model.GenTable, error)
	// ImportGenTable 导入表结构
	ImportGenTable(tableList []*model.GenTable, operName string) error
	// UpdateGenTable 修改业务
	UpdateGenTable(genTable *model.GenTable) error
	// DeleteGenTableByIds 删除业务信息
	DeleteGenTableByIds(tableIds []int64) error
	// ValidateEdit 校验编辑表单数据
	ValidateEdit(genTable *model.GenTable) error
	// PreviewCode 预览代码
	PreviewCode(tableId int64) (map[string]string, error)
	// GenerateCode 生成代码（自定义路径）
	GenerateCode(tableName string) (map[string][]byte, error)
	// SynchDb 同步数据库
	SynchDb(tableName string) error
	// BatchGenerateCode 批量生成代码
	BatchGenerateCode(tableNames []string) (io.ReadCloser, error)
	// CreateTable 创建表
	CreateTable(sql string) (bool, error)
	// SelectDbTableColumnsByName 查询表列信息
	SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error)
	// GetDbTableColumns 获取数据库表列信息
	GetDbTableColumns(tableName string) ([]*model.GenTableColumn, error)
	// GenCode 生成代码
	GenCode(tableName string) (map[string]string, error)
	// BatchGenCode 批量生成代码
	BatchGenCode(tables []string) (string, error)
}
