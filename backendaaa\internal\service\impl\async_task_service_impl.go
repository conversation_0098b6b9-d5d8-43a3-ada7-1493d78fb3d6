package impl

import (
	"backend/internal/service"
	"context"
	"errors"
	"log"
	"sync"
	"time"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	// StatusPending 待处理
	StatusPending TaskStatus = "pending"
	// StatusRunning 运行中
	StatusRunning TaskStatus = "running"
	// StatusCompleted 已完成
	StatusCompleted TaskStatus = "completed"
	// StatusFailed 失败
	StatusFailed TaskStatus = "failed"
	// StatusCancelled 已取消
	StatusCancelled TaskStatus = "cancelled"
)

// TaskInfo 任务信息
type TaskInfo struct {
	Task   service.Task // 任务
	Status TaskStatus   // 状态
	Error  error        // 错误信息
}

// AsyncTaskServiceImpl 异步任务服务实现
type AsyncTaskServiceImpl struct {
	tasks      map[string]*TaskInfo   // 任务映射
	delayQueue map[string]*time.Timer // 延迟队列
	mutex      sync.RWMutex           // 互斥锁
	workerPool chan struct{}          // 工作池
}

// NewAsyncTaskService 创建异步任务服务
func NewAsyncTaskService(workerCount int) service.AsyncTaskService {
	if workerCount <= 0 {
		workerCount = 10 // 默认工作协程数
	}

	return &AsyncTaskServiceImpl{
		tasks:      make(map[string]*TaskInfo),
		delayQueue: make(map[string]*time.Timer),
		workerPool: make(chan struct{}, workerCount),
	}
}

// Submit 提交任务
func (s *AsyncTaskServiceImpl) Submit(task service.Task) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	taskID := task.GetID()
	if _, exists := s.tasks[taskID]; exists {
		return errors.New("任务已存在")
	}

	taskInfo := &TaskInfo{
		Task:   task,
		Status: StatusPending,
	}

	s.tasks[taskID] = taskInfo

	// 异步执行任务
	go s.executeTask(taskInfo)

	return nil
}

// SubmitWithDelay 延迟提交任务
func (s *AsyncTaskServiceImpl) SubmitWithDelay(task service.Task, delay time.Duration) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	taskID := task.GetID()
	if _, exists := s.tasks[taskID]; exists {
		return errors.New("任务已存在")
	}

	taskInfo := &TaskInfo{
		Task:   task,
		Status: StatusPending,
	}

	s.tasks[taskID] = taskInfo

	// 创建延迟任务
	timer := time.AfterFunc(delay, func() {
		// 从延迟队列中移除
		s.mutex.Lock()
		delete(s.delayQueue, taskID)
		s.mutex.Unlock()

		// 执行任务
		go s.executeTask(taskInfo)
	})

	// 添加到延迟队列
	s.delayQueue[taskID] = timer

	return nil
}

// Cancel 取消任务
func (s *AsyncTaskServiceImpl) Cancel(taskID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查任务是否存在
	taskInfo, exists := s.tasks[taskID]
	if !exists {
		return errors.New("任务不存在")
	}

	// 检查是否为延迟任务
	if timer, exists := s.delayQueue[taskID]; exists {
		// 停止定时器
		timer.Stop()
		delete(s.delayQueue, taskID)
	}

	// 如果任务未开始或已完成，则直接取消
	if taskInfo.Status == StatusPending || taskInfo.Status == StatusCompleted || taskInfo.Status == StatusFailed {
		taskInfo.Status = StatusCancelled
		return nil
	}

	// 如果任务正在运行，则无法取消
	if taskInfo.Status == StatusRunning {
		return errors.New("任务正在运行，无法取消")
	}

	return nil
}

// GetTaskStatus 获取任务状态
func (s *AsyncTaskServiceImpl) GetTaskStatus(taskID string) (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 检查任务是否存在
	taskInfo, exists := s.tasks[taskID]
	if !exists {
		return "", errors.New("任务不存在")
	}

	return string(taskInfo.Status), nil
}

// executeTask 执行任务
func (s *AsyncTaskServiceImpl) executeTask(taskInfo *TaskInfo) {
	// 获取工作协程
	s.workerPool <- struct{}{}
	defer func() {
		// 释放工作协程
		<-s.workerPool
	}()

	// 更新任务状态
	s.mutex.Lock()
	taskInfo.Status = StatusRunning
	s.mutex.Unlock()

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 执行任务
	err := taskInfo.Task.Execute(ctx)

	// 更新任务状态
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if err != nil {
		taskInfo.Status = StatusFailed
		taskInfo.Error = err
		log.Printf("任务执行失败: %s, 错误: %v", taskInfo.Task.GetName(), err)
	} else {
		taskInfo.Status = StatusCompleted
		log.Printf("任务执行成功: %s", taskInfo.Task.GetName())
	}
}
