package impl

import (
	"backend/internal/service"
	"encoding/json"
	"fmt"
	"time"
)

// CacheQueryServiceImpl 缓存查询服务实现
type CacheQueryServiceImpl struct {
	redisCache service.RedisCache
	prefix     string
}

// NewCacheQueryService 创建缓存查询服务
func NewCacheQueryService(redisCache service.RedisCache, prefix string) service.CacheQueryService {
	return &CacheQueryServiceImpl{
		redisCache: redisCache,
		prefix:     prefix,
	}
}

// GetCache 获取缓存数据
func (s *CacheQueryServiceImpl) GetCache(key string, value interface{}) error {
	cacheKey := s.getCacheKey(key)

	// 获取缓存
	cacheData, err := s.redisCache.GetCacheObject(cacheKey)
	if err != nil {
		return err
	}

	// 反序列化数据
	cacheStr, ok := cacheData.(string)
	if !ok {
		return fmt.Errorf("缓存数据类型错误")
	}

	if err := json.Unmarshal([]byte(cacheStr), value); err != nil {
		return err
	}

	return nil
}

// SetCache 设置缓存数据
func (s *CacheQueryServiceImpl) SetCache(key string, value interface{}, ttl time.Duration) error {
	cacheKey := s.getCacheKey(key)

	// 序列化数据
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	// 存入缓存
	return s.redisCache.SetCacheObject(cacheKey, string(data), ttl)
}

// DeleteCache 删除缓存数据
func (s *CacheQueryServiceImpl) DeleteCache(key string) error {
	cacheKey := s.getCacheKey(key)
	return s.redisCache.DeleteObject(cacheKey)
}

// ClearCache 清空缓存
func (s *CacheQueryServiceImpl) ClearCache(prefix string) error {
	if prefix == "" {
		prefix = s.prefix
	} else {
		prefix = s.prefix + ":" + prefix
	}

	// 获取所有匹配的键
	pattern := prefix + "*"
	keys, err := s.redisCache.Keys(pattern)
	if err != nil {
		return err
	}

	// 如果有匹配的键，删除它们
	if len(keys) > 0 {
		return s.redisCache.DeleteObjects(keys)
	}

	return nil
}

// getCacheKey 获取缓存键
func (s *CacheQueryServiceImpl) getCacheKey(key string) string {
	return fmt.Sprintf("%s:%s", s.prefix, key)
}
