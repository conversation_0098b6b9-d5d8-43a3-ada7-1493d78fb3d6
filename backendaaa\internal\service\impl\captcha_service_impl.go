package impl

import (
	"log"
	"strings"
	"time"

	"backend/internal/constants"
	"backend/internal/service"
	"backend/internal/utils"
)

// CaptchaService 验证码服务实现
type CaptchaService struct {
	redisCache service.RedisCache
}

// NewCaptchaService 创建验证码服务
func NewCaptchaService(redisCache service.RedisCache) *CaptchaService {
	return &CaptchaService{
		redisCache: redisCache,
	}
}

// CreateCaptcha 创建验证码
func (c *CaptchaService) CreateCaptcha() (uuid string, img string, err error) {
	// 生成UUID
	uuid = utils.SimpleUUID()
	verifyKey := constants.CAPTCHA_CODE_KEY + uuid
	log.Printf("生成新验证码UUID: %s, 完整key: %s", uuid, verifyKey)

	// 使用数学验证码驱动
	driver := utils.NewCustomMathDriver()

	// 生成验证码
	id, question, answer := driver.GenerateIdQuestionAnswer()
	item, err := driver.DrawCaptcha(question)
	if err != nil {
		log.Printf("生成验证码错误: %v", err)
		return "", "", err
	}

	// 获取base64编码
	b64s := item.EncodeB64string()

	// 确保base64编码不包含任何前缀，前端会添加完整的前缀
	b64s = strings.TrimPrefix(b64s, "data:image/png;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/jpeg;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/jpg;base64,")
	b64s = strings.TrimPrefix(b64s, "data:image/gif;base64,")

	log.Printf("验证码生成成功，ID: %s", id)
	log.Printf("验证码问题: %s, 答案: %s", question, answer)
	log.Printf("验证码base64编码长度: %d", len(b64s))

	// 检查base64编码是否为空
	if b64s == "" {
		log.Printf("警告: 生成的验证码base64编码为空!")
		// 使用一个简单的1x1像素透明PNG图片作为默认值
		b64s = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII="
	}

	// 延长验证码有效期到5分钟，与常量保持一致
	err = c.redisCache.SetCacheObject(verifyKey, answer, time.Duration(constants.CAPTCHA_EXPIRATION)*time.Minute)
	if err != nil {
		log.Printf("存储验证码到Redis错误: %v", err)
		return "", "", err
	}

	// 检查是否成功存储到Redis
	storedValue, getErr := c.redisCache.GetCacheObject(verifyKey)
	if getErr != nil || storedValue == nil {
		log.Printf("验证存储到Redis失败，无法获取: %v", getErr)
	} else {
		log.Printf("验证码已成功存储到Redis，key=%s, 值=%v，类型=%T", verifyKey, storedValue, storedValue)
	}

	return uuid, b64s, nil
}
