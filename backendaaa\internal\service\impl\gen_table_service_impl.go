package impl

import (
	"archive/zip"
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
)

// GenTableServiceImpl 代码生成业务实现
type GenTableServiceImpl struct {
	genTableRepo       repository.GenTableRepository
	genTableColumnRepo repository.GenTableColumnRepository
	dbTableRepository  repository.DBTableRepository
}

// NewGenTableService 创建代码生成业务实现
func NewGenTableService(genTableRepo repository.GenTableRepository, genTableColumnRepo repository.GenTableColumnRepository, dbTableRepo repository.DBTableRepository) service.GenTableService {
	return &GenTableServiceImpl{
		genTableRepo:       genTableRepo,
		genTableColumnRepo: genTableColumnRepo,
		dbTableRepository:  dbTableRepo,
	}
}

// SelectGenTableById 查询业务信息
func (s *GenTableServiceImpl) SelectGenTableById(tableId int64) (*model.GenTable, error) {
	table, err := s.genTableRepo.SelectGenTableById(tableId)
	if err != nil {
		return nil, err
	}

	// 查询列信息
	columns, err := s.genTableColumnRepo.SelectGenTableColumnListByTableId(tableId)
	if err != nil {
		return nil, err
	}

	table.Columns = columns

	return table, nil
}

// SelectGenTableList 查询业务列表
func (s *GenTableServiceImpl) SelectGenTableList(genTable *model.GenTable) ([]*model.GenTable, error) {
	return s.genTableRepo.SelectGenTableList(genTable)
}

// SelectDbTableList 查询据库表列表
func (s *GenTableServiceImpl) SelectDbTableList(dbTable *model.GenTable) ([]*model.GenTable, error) {
	return s.dbTableRepository.SelectDbTableList(dbTable)
}

// SelectDbTableListByNames 根据表名称查询数据库表列表
func (s *GenTableServiceImpl) SelectDbTableListByNames(tableNames []string) ([]*model.GenTable, error) {
	return s.dbTableRepository.SelectDbTableListByNames(tableNames)
}

// UpdateGenTable 修改业务
func (s *GenTableServiceImpl) UpdateGenTable(genTable *model.GenTable) error {
	if genTable.TableID == 0 {
		return errors.New("表ID不能为空")
	}

	if genTable.TableName == "" {
		return errors.New("表名称不能为空")
	}

	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新表信息
	err := s.genTableRepo.UpdateGenTableTx(tx, genTable)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新列信息
	if len(genTable.Columns) > 0 {
		for _, column := range genTable.Columns {
			err = s.genTableColumnRepo.UpdateGenTableColumnTx(tx, column)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// DeleteGenTableByIds 批量删除业务
func (s *GenTableServiceImpl) DeleteGenTableByIds(tableIds []int64) error {
	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, tableID := range tableIds {
		// 删除表信息
		err := s.genTableRepo.DeleteGenTableByIdTx(tx, tableID)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 删除列信息
		err = s.genTableColumnRepo.DeleteGenTableColumnByTableIdTx(tx, tableID)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// ImportGenTable 导入表结构
func (s *GenTableServiceImpl) ImportGenTable(tableList []*model.GenTable, operName string) error {
	if len(tableList) == 0 {
		return errors.New("表不能为空")
	}

	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 遍历表
	for _, table := range tableList {
		// 初始化表信息
		table.ClassName = utils.ConvertToCamelCase(table.TableName)
		table.PackageName = "main"
		table.ModuleName = "module"
		table.BusinessName = utils.ToCamelCase(table.TableName)
		table.FunctionName = "功能名"
		table.FunctionAuthor = "author"
		table.TplCategory = "crud"
		table.CreateBy = operName
		createTime := time.Now()
		table.CreateTime = &createTime

		// 插入表信息
		tableId, err := s.genTableRepo.InsertGenTableTx(tx, table)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 获取列信息
		columns, err := s.dbTableRepository.SelectDbTableColumnsByName(table.TableName)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 初始化列信息
		for _, column := range columns {
			column.TableID = tableId
			column.CreateBy = operName
			colCreateTime := time.Now()
			column.CreateTime = &colCreateTime

			// 设置Java字段名
			column.JavaField = utils.ToCamelCase(column.ColumnName)

			// 设置默认显示类型
			if column.IsIncrement == "1" || column.IsPk == "1" {
				column.IsInsert = "0"
				column.IsEdit = "0"
				column.IsList = "0"
				column.IsQuery = "0"
			} else {
				column.IsInsert = "1"
				column.IsEdit = "1"
				column.IsList = "1"
				column.IsQuery = "1"
			}

			// 根据列类型设置查询方式
			if strings.Contains(column.ColumnType, "char") || strings.Contains(column.ColumnType, "text") {
				column.QueryType = "LIKE"
			} else {
				column.QueryType = "EQ"
			}

			// 根据列类型设置Java类型
			if strings.Contains(column.ColumnType, "int") {
				column.JavaType = "Long"
				column.HtmlType = "input"
			} else if strings.Contains(column.ColumnType, "datetime") || strings.Contains(column.ColumnType, "timestamp") {
				column.JavaType = "Date"
				column.HtmlType = "datetime"
			} else if strings.Contains(column.ColumnType, "decimal") || strings.Contains(column.ColumnType, "float") || strings.Contains(column.ColumnType, "double") {
				column.JavaType = "BigDecimal"
				column.HtmlType = "input"
			} else {
				column.JavaType = "String"
				column.HtmlType = "input"
			}
		}

		// 保存列信息
		for _, column := range columns {
			err = s.genTableColumnRepo.InsertGenTableColumnTx(tx, column)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// SynchDb 同步数据库
func (s *GenTableServiceImpl) SynchDb(tableName string) error {
	return s.HandleSynchDb(tableName, "")
}

// HandleSynchDb 处理同步数据库
func (s *GenTableServiceImpl) HandleSynchDb(tableName string, operName string) error {
	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	tables, err := s.genTableRepo.SelectGenTableByName(tableName)
	if err != nil {
		tx.Rollback()
		return err
	}

	if len(tables) == 0 {
		tx.Rollback()
		return errors.New("同步数据失败，原表结构不存在")
	}

	table := tables[0]
	tableId := table.TableID

	// 查询列信息
	oldColumns, err := s.genTableColumnRepo.SelectGenTableColumnListByTableId(tableId)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 新的列信息
	dbColumns, err := s.dbTableRepository.SelectDbTableColumnsByName(tableName)
	if err != nil {
		tx.Rollback()
		return err
	}

	if len(dbColumns) == 0 {
		tx.Rollback()
		return errors.New("同步数据失败，原表结构不存在")
	}

	// 处理字段删除
	for _, oColumn := range oldColumns {
		exists := false
		for _, dbColumn := range dbColumns {
			if oColumn.ColumnName == dbColumn.ColumnName {
				exists = true
				break
			}
		}
		if !exists {
			err = s.genTableColumnRepo.DeleteGenTableColumnByIdsTx(tx, []int64{oColumn.ColumnID})
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 处理新增和更新
	for _, dbColumn := range dbColumns {
		exists := false
		for _, oColumn := range oldColumns {
			if dbColumn.ColumnName == oColumn.ColumnName {
				exists = true
				dbColumn.ColumnID = oColumn.ColumnID
				dbColumn.IsEdit = oColumn.IsEdit
				dbColumn.IsList = oColumn.IsList
				dbColumn.IsQuery = oColumn.IsQuery
				dbColumn.IsInsert = oColumn.IsInsert
				dbColumn.JavaField = oColumn.JavaField
				dbColumn.JavaType = oColumn.JavaType
				dbColumn.QueryType = oColumn.QueryType
				dbColumn.HtmlType = oColumn.HtmlType
				dbColumn.DictType = oColumn.DictType

				if operName != "" {
					dbColumn.UpdateBy = operName
				}
				updateTime := time.Now()
				dbColumn.UpdateTime = &updateTime

				err = s.genTableColumnRepo.UpdateGenTableColumnTx(tx, dbColumn)
				if err != nil {
					tx.Rollback()
					return err
				}
				break
			}
		}
		if !exists {
			dbColumn.TableID = tableId
			if operName != "" {
				dbColumn.CreateBy = operName
			}
			createTime := time.Now()
			dbColumn.CreateTime = &createTime

			// 设置默认值
			dbColumn.JavaField = utils.ToCamelCase(dbColumn.ColumnName)

			// 设置默认显示类型
			if dbColumn.IsIncrement == "1" || dbColumn.IsPk == "1" {
				dbColumn.IsInsert = "0"
				dbColumn.IsEdit = "0"
				dbColumn.IsList = "0"
				dbColumn.IsQuery = "0"
			} else {
				dbColumn.IsInsert = "1"
				dbColumn.IsEdit = "1"
				dbColumn.IsList = "1"
				dbColumn.IsQuery = "1"
			}

			// 根据列类型设置查询方式
			if strings.Contains(dbColumn.ColumnType, "char") || strings.Contains(dbColumn.ColumnType, "text") {
				dbColumn.QueryType = "LIKE"
			} else {
				dbColumn.QueryType = "EQ"
			}

			// 根据列类型设置Java类型
			if strings.Contains(dbColumn.ColumnType, "int") {
				dbColumn.JavaType = "Long"
				dbColumn.HtmlType = "input"
			} else if strings.Contains(dbColumn.ColumnType, "datetime") || strings.Contains(dbColumn.ColumnType, "timestamp") {
				dbColumn.JavaType = "Date"
				dbColumn.HtmlType = "datetime"
			} else if strings.Contains(dbColumn.ColumnType, "decimal") || strings.Contains(dbColumn.ColumnType, "float") || strings.Contains(dbColumn.ColumnType, "double") {
				dbColumn.JavaType = "BigDecimal"
				dbColumn.HtmlType = "input"
			} else {
				dbColumn.JavaType = "String"
				dbColumn.HtmlType = "input"
			}

			err = s.genTableColumnRepo.InsertGenTableColumnTx(tx, dbColumn)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// ValidateGenCode 验证代码生成参数
func (s *GenTableServiceImpl) ValidateGenCode(tableName string) error {
	if tableName == "" {
		return errors.New("表名称不能为空")
	}

	// 查询表信息
	tables, err := s.genTableRepo.SelectGenTableByName(tableName)
	if err != nil {
		return err
	}

	if len(tables) == 0 {
		return fmt.Errorf("表[%s]不存在", tableName)
	}

	return nil
}

// GenCode 生成代码
func (s *GenTableServiceImpl) GenCode(tableName string) (map[string]string, error) {
	// 校验表
	err := s.ValidateGenCode(tableName)
	if err != nil {
		return nil, err
	}

	// 查询表信息
	tables, err := s.genTableRepo.SelectGenTableByName(tableName)
	if err != nil {
		return nil, err
	}

	// 查询列信息
	columns, err := s.genTableColumnRepo.SelectGenTableColumnListByTableId(tables[0].TableID)
	if err != nil {
		return nil, err
	}

	// 设置列信息
	tables[0].Columns = columns

	// 生成代码
	codeMap, err := s.generateCode(tables[0])
	if err != nil {
		return nil, err
	}

	return codeMap, nil
}

// BatchGenCode 批量生成代码
func (s *GenTableServiceImpl) BatchGenCode(tables []string) (string, error) {
	var codeZipBuffer bytes.Buffer
	zipWriter := zip.NewWriter(&codeZipBuffer)

	for _, tableName := range tables {
		// 查询表信息
		tablesList, err := s.genTableRepo.SelectGenTableByName(tableName)
		if err != nil || len(tablesList) == 0 {
			continue
		}

		// 查询列信息
		columns, err := s.genTableColumnRepo.SelectGenTableColumnListByTableId(tablesList[0].TableID)
		if err != nil {
			continue
		}

		// 设置列信息
		tablesList[0].Columns = columns

		// 生成代码
		codeMap, err := s.generateCode(tablesList[0])
		if err != nil {
			continue
		}

		// 写入zip
		for fileName, data := range codeMap {
			fw, err := zipWriter.Create(fileName)
			if err != nil {
				continue
			}
			_, err = fw.Write([]byte(data))
			if err != nil {
				continue
			}
		}
	}

	err := zipWriter.Close()
	if err != nil {
		return "", err
	}

	// 将生成的zip内容转换为base64字符串
	base64Data := base64.StdEncoding.EncodeToString(codeZipBuffer.Bytes())
	return base64Data, nil
}

// PreviewCode 预览代码
func (s *GenTableServiceImpl) PreviewCode(tableId int64) (map[string]string, error) {
	// 查询表信息
	table, err := s.SelectGenTableById(tableId)
	if err != nil {
		return nil, err
	}

	// 生成代码
	codeMap, err := s.generateCode(table)
	if err != nil {
		return nil, err
	}

	return codeMap, nil
}

// generateCode 生成代码
func (s *GenTableServiceImpl) generateCode(table *model.GenTable) (map[string]string, error) {
	// 这里是生成代码的具体实现
	// 由于未实现生成器引擎，返回一个简单的代码示例
	codeMap := make(map[string]string)

	// 简单示例，实际应该根据模板生成代码
	codeMap["entity.go"] = "package entity\n\n// " + table.ClassName + " Entity\ntype " + table.ClassName + " struct {\n\t// TODO: 根据列信息生成字段\n}"
	codeMap["controller.go"] = "package controller\n\n// " + table.ClassName + "Controller Controller\ntype " + table.ClassName + "Controller struct {\n\t// TODO: 实现控制器逻辑\n}"
	codeMap["service.go"] = "package service\n\n// " + table.ClassName + "Service Service\ntype " + table.ClassName + "Service struct {\n\t// TODO: 实现服务逻辑\n}"
	codeMap["repository.go"] = "package repository\n\n// " + table.ClassName + "Repository Repository\ntype " + table.ClassName + "Repository struct {\n\t// TODO: 实现仓库逻辑\n}"

	return codeMap, nil
}

// DeleteGenTableByName 通过表名删除表
func (s *GenTableServiceImpl) DeleteGenTableByName(tableName string) error {
	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询表信息
	tables, err := s.genTableRepo.SelectGenTableByName(tableName)
	if err != nil {
		tx.Rollback()
		return err
	}

	if len(tables) == 0 {
		tx.Rollback()
		return errors.New("该表不存在")
	}

	table := tables[0]
	tableId := table.TableID

	// 删除列信息
	err = s.genTableColumnRepo.DeleteGenTableColumnByTableIdTx(tx, tableId)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 删除表信息
	err = s.genTableRepo.DeleteGenTableByIdTx(tx, tableId)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// GetDbTableColumns 查询表列表
func (s *GenTableServiceImpl) GetDbTableColumns(tableName string) ([]*model.GenTableColumn, error) {
	return s.dbTableRepository.SelectDbTableColumnsByName(tableName)
}

// BatchGenerateCode 批量生成代码（下载方式）
func (s *GenTableServiceImpl) BatchGenerateCode(tableNames []string) (io.ReadCloser, error) {
	// 简单实现，将每个表的代码生成结果汇总并返回
	zipBuf := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuf)

	for _, tableName := range tableNames {
		// 生成代码
		codeMap, err := s.GenCode(tableName)
		if err != nil {
			return nil, err
		}

		// 将代码写入zip文件
		for fileName, content := range codeMap {
			fileWriter, err := zipWriter.Create(fileName)
			if err != nil {
				return nil, err
			}

			_, err = fileWriter.Write([]byte(content))
			if err != nil {
				return nil, err
			}
		}
	}

	// 关闭zip写入器
	err := zipWriter.Close()
	if err != nil {
		return nil, err
	}

	// 创建一个ReadCloser接口返回
	return io.NopCloser(bytes.NewReader(zipBuf.Bytes())), nil
}

// CreateTable 创建表
func (s *GenTableServiceImpl) CreateTable(sql string) (bool, error) {
	// 开启事务
	db := s.genTableRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return false, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := s.genTableRepo.CreateTableTx(tx, sql)
	if err != nil {
		tx.Rollback()
		log.Printf("创建表失败：%v", err)
		return false, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return false, err
	}

	return true, nil
}

// GenerateCode 生成代码（自定义路径）
func (s *GenTableServiceImpl) GenerateCode(tableName string) (map[string][]byte, error) {
	// 获取表信息
	table, err := s.genTableRepo.SelectGenTableByName(tableName)
	if err != nil {
		return nil, err
	}

	if len(table) == 0 {
		return nil, fmt.Errorf("表 %s 不存在", tableName)
	}

	// 获取列信息
	columns, err := s.genTableColumnRepo.SelectGenTableColumnListByTableId(table[0].TableID)
	if err != nil {
		return nil, err
	}

	table[0].Columns = columns

	// 生成代码
	codeMap, err := s.generateCode(table[0])
	if err != nil {
		return nil, err
	}

	// 转换为字节数组
	result := make(map[string][]byte)
	for fileName, content := range codeMap {
		result[fileName] = []byte(content)
	}

	return result, nil
}

// SelectDbTableColumnsByName 根据表名称查询列信息
func (s *GenTableServiceImpl) SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error) {
	return s.dbTableRepository.SelectDbTableColumnsByName(tableName)
}

// SelectGenTableAll 查询所有业务
func (s *GenTableServiceImpl) SelectGenTableAll() ([]*model.GenTable, error) {
	return s.genTableRepo.SelectGenTableAll()
}

// ValidateEdit 验证编辑
func (s *GenTableServiceImpl) ValidateEdit(genTable *model.GenTable) error {
	if genTable.TableName == "" {
		return errors.New("表名称不能为空")
	}
	if genTable.TableComment == "" {
		return errors.New("表注释不能为空")
	}
	if genTable.ClassName == "" {
		return errors.New("实体类名称不能为空")
	}
	if genTable.FunctionAuthor == "" {
		return errors.New("作者不能为空")
	}
	if genTable.PackageName == "" {
		return errors.New("生成包路径不能为空")
	}
	if genTable.ModuleName == "" {
		return errors.New("生成模块名不能为空")
	}
	if genTable.BusinessName == "" {
		return errors.New("生成业务名不能为空")
	}
	if genTable.FunctionName == "" {
		return errors.New("生成功能名不能为空")
	}
	return nil
}
