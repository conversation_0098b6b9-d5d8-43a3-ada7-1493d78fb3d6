package impl

import (
	"backend/internal/constants"
	"backend/internal/service"
	"backend/internal/utils"
	"errors"
	"fmt"
	"log"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"
)

// 缓存项结构
type cacheItem struct {
	value      interface{}
	expireTime *time.Time // 过期时间，nil表示永不过期
}

// MemoryRedisCache 内存实现的Redis缓存（用于开发和测试）
type MemoryRedisCache struct {
	cache       map[string]cacheItem
	mutex       sync.RWMutex
	startTime   time.Time
	commandHits map[string]int64
}

// NewMemoryRedisCache 创建内存Redis缓存实例
func NewMemoryRedisCache() service.RedisCache {
	cache := &MemoryRedisCache{
		cache:       make(map[string]cacheItem),
		startTime:   time.Now(),
		commandHits: make(map[string]int64),
	}

	// 启动一个goroutine定期清理过期的缓存项
	go cache.cleanExpiredItems()

	return cache
}

// cleanExpiredItems 定期清理过期的缓存项
func (c *MemoryRedisCache) cleanExpiredItems() {
	ticker := time.NewTicker(10 * time.Minute) // 增加清理间隔，减少过早清理的可能性
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()

		var expiredCount int
		var captchaCount int
		var totalCount int

		// 先统计
		for key, _ := range c.cache {
			totalCount++
			if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
				captchaCount++
			}
		}

		// 再清理
		for key, item := range c.cache {
			// 验证码特殊处理，给予更长的宽限期
			if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
				// 只有超过过期时间30秒后才清理验证码，给予更长的宽限期
				if item.expireTime != nil && now.After(item.expireTime.Add(30*time.Second)) {
					delete(c.cache, key)
					expiredCount++
					log.Printf("缓存清理: 删除过期验证码: %s, 原过期时间: %v", key, *item.expireTime)
				}
			} else if item.expireTime != nil && now.After(*item.expireTime) {
				delete(c.cache, key)
				expiredCount++
			}
		}

		// 记录清理日志
		log.Printf("缓存清理: 总项数=%d, 验证码数量=%d, 清理过期项=%d", totalCount, captchaCount, expiredCount)

		c.mutex.Unlock()
	}
}

// SetCacheObject 设置缓存对象
func (c *MemoryRedisCache) SetCacheObject(key string, value interface{}, timeout ...time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 调试跟踪
	if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
		utils.TraceCacheOperation("SetCacheObject", key, value)
	}

	// 处理可选的过期时间
	var expireTime *time.Time
	if len(timeout) > 0 && timeout[0] > 0 {
		t := time.Now().Add(timeout[0])
		expireTime = &t
	}

	// 如果是验证码类型，先检查旧验证码数量，如果太多就清理一些
	if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
		var captchaCount int
		for existingKey := range c.cache {
			if strings.HasPrefix(existingKey, constants.CAPTCHA_CODE_KEY) {
				captchaCount++
			}
		}

		// 如果验证码数量超过20个，清理一半最旧的验证码
		if captchaCount > 20 {
			log.Printf("验证码数量过多(%d)，清理旧验证码", captchaCount)
			var captchaKeys []string
			var captchaTimes []time.Time

			for existingKey, item := range c.cache {
				if strings.HasPrefix(existingKey, constants.CAPTCHA_CODE_KEY) {
					if item.expireTime != nil {
						captchaKeys = append(captchaKeys, existingKey)
						captchaTimes = append(captchaTimes, *item.expireTime)
					}
				}
			}

			// 按照过期时间排序，清理最早过期的一半验证码
			if len(captchaKeys) > 10 {
				type keyTimeItem struct {
					key  string
					time time.Time
				}

				items := make([]keyTimeItem, len(captchaKeys))
				for i := range captchaKeys {
					items[i] = keyTimeItem{captchaKeys[i], captchaTimes[i]}
				}

				// 按过期时间排序
				sort.Slice(items, func(i, j int) bool {
					return items[i].time.Before(items[j].time)
				})

				// 清理最早过期的一半
				toDelete := len(items) / 2
				for i := 0; i < toDelete; i++ {
					delete(c.cache, items[i].key)
					log.Printf("主动清理旧验证码: %s, 过期时间: %v", items[i].key, items[i].time)
				}
			}
		}
	}

	c.cache[key] = cacheItem{value: value, expireTime: expireTime}
	c.commandHits["set"]++

	// 日志记录
	if expireTime != nil {
		log.Printf("SetCacheObject: key=%s, value=%v, 过期时间=%v", key, value, *expireTime)
	} else {
		log.Printf("SetCacheObject: key=%s, value=%v, 永不过期", key, value)
	}

	return nil
}

// GetCacheObject 获取缓存对象
func (c *MemoryRedisCache) GetCacheObject(key string) (interface{}, error) {
	// 使用写锁而不是读锁，确保与Keys方法一致的视图
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 调试跟踪
	if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
		utils.TraceCacheOperation("GetCacheObject", key, nil)
	}

	item, exists := c.cache[key]
	if !exists {
		c.commandHits["miss"]++
		// 返回nil,nil与Java的RedisCache.getCacheObject保持一致，Java版本返回null不会抛异常
		return nil, nil
	}

	// 检查是否过期
	if item.expireTime != nil && time.Now().After(*item.expireTime) {
		// 立即删除过期项而不是异步删除，避免并发问题
		delete(c.cache, key)
		c.commandHits["miss"]++
		// 返回nil,nil与Java版本保持一致，Java版本返回null不会抛异常
		log.Printf("GetCacheObject: key=%s 已过期，过期时间=%v", key, *item.expireTime)
		return nil, nil
	}

	c.commandHits["get"]++

	// 记录访问
	if strings.HasPrefix(key, constants.CAPTCHA_CODE_KEY) {
		log.Printf("成功获取验证码: key=%s, 值=%v, 类型=%T", key, item.value, item.value)
		if item.expireTime != nil {
			now := time.Now()
			remaining := item.expireTime.Sub(now)
			log.Printf("验证码有效期还剩: %.2f秒", remaining.Seconds())
		}
	}

	return item.value, nil
}

// DeleteObject 删除缓存对象
func (c *MemoryRedisCache) DeleteObject(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	delete(c.cache, key)
	c.commandHits["del"]++
	return nil
}

// DeleteObjects 删除多个缓存对象
func (c *MemoryRedisCache) DeleteObjects(keys []string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	for _, key := range keys {
		delete(c.cache, key)
	}
	c.commandHits["del"] += int64(len(keys))
	return nil
}

// HasKey 判断缓存对象是否存在
func (c *MemoryRedisCache) HasKey(key string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.cache[key]
	if !exists {
		return false
	}

	// 检查是否过期
	if item.expireTime != nil && time.Now().After(*item.expireTime) {
		// 异步删除过期项
		go func() {
			c.mutex.Lock()
			delete(c.cache, key)
			c.mutex.Unlock()
		}()
		return false
	}

	return true
}

// Keys 获取符合pattern的所有key
func (c *MemoryRedisCache) Keys(pattern string) ([]string, error) {
	// 使用写锁而不是读锁，确保完整一致的视图
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 简单实现，仅支持"prefix*"格式的pattern
	var keys []string
	var now = time.Now()

	if pattern == "*" {
		// 获取所有key
		for key, item := range c.cache {
			// 跳过过期的key
			if item.expireTime != nil && now.After(*item.expireTime) {
				log.Printf("Keys: 跳过过期key: %s, 过期时间: %v", key, *item.expireTime)
				continue
			}
			keys = append(keys, key)
		}
	} else if strings.HasSuffix(pattern, "*") {
		// 获取指定前缀的key
		prefix := pattern[:len(pattern)-1]
		for key, item := range c.cache {
			// 跳过过期的key
			if item.expireTime != nil && now.After(*item.expireTime) {
				log.Printf("Keys: 跳过过期key: %s, 过期时间: %v", key, *item.expireTime)
				continue
			}
			if strings.HasPrefix(key, prefix) {
				keys = append(keys, key)
			}
		}
	} else {
		// 精确匹配
		if item, exists := c.cache[pattern]; exists {
			// 跳过过期的key
			if item.expireTime == nil || !now.After(*item.expireTime) {
				keys = append(keys, pattern)
			} else {
				log.Printf("Keys: 跳过过期key: %s, 过期时间: %v", pattern, *item.expireTime)
			}
		}
	}

	log.Printf("Keys查询: pattern=%s, 找到key数量=%d", pattern, len(keys))
	if len(keys) > 0 {
		log.Printf("找到的keys: %v", keys)
	}

	c.commandHits["keys"]++
	return keys, nil
}

// GetKeys 获取符合pattern的所有key（别名，为了兼容缓存监控）
func (c *MemoryRedisCache) GetKeys(pattern string) ([]string, error) {
	return c.Keys(pattern)
}

// GetInfo 获取缓存信息
func (c *MemoryRedisCache) GetInfo() (map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 构建缓存信息
	info := make(map[string]string)
	info["redis_version"] = "memory-cache"
	info["uptime_in_seconds"] = fmt.Sprintf("%d", int(time.Since(c.startTime).Seconds()))
	info["uptime_in_days"] = fmt.Sprintf("%d", int(time.Since(c.startTime).Hours()/24))
	info["connected_clients"] = "1"
	info["used_memory"] = fmt.Sprintf("%d", memStats.Alloc)
	info["used_memory_human"] = fmt.Sprintf("%.2fM", float64(memStats.Alloc)/(1024*1024))
	info["total_system_memory"] = fmt.Sprintf("%d", memStats.Sys)
	info["total_system_memory_human"] = fmt.Sprintf("%.2fM", float64(memStats.Sys)/(1024*1024))
	info["os"] = runtime.GOOS
	info["arch_bits"] = fmt.Sprintf("%d", 8*runtime.NumCPU())
	info["cpu_cores"] = fmt.Sprintf("%d", runtime.NumCPU())
	info["process_id"] = fmt.Sprintf("%d", 0)

	return info, nil
}

// GetCommandStats 获取缓存命令统计
func (c *MemoryRedisCache) GetCommandStats() ([]map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 构建命令统计
	var stats []map[string]string

	// 如果没有命令记录，添加一些模拟数据
	if len(c.commandHits) == 0 {
		c.commandHits["get"] = 10
		c.commandHits["set"] = 5
		c.commandHits["del"] = 2
		c.commandHits["keys"] = 3
	}

	// 遍历命令统计
	for cmd, hits := range c.commandHits {
		stat := make(map[string]string)
		stat["name"] = cmd
		stat["value"] = fmt.Sprintf("%d", hits)
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetDBSize 获取缓存大小
func (c *MemoryRedisCache) GetDBSize() (int64, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 计算有效的缓存项数量（排除过期的）
	count := 0
	now := time.Now()
	for _, item := range c.cache {
		if item.expireTime == nil || !now.After(*item.expireTime) {
			count++
		}
	}

	return int64(count), nil
}

// Expire 设置过期时间
func (c *MemoryRedisCache) Expire(key string, timeout time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	item, exists := c.cache[key]
	if !exists {
		return errors.New("key not found")
	}

	expireTime := time.Now().Add(timeout)
	c.cache[key] = cacheItem{value: item.value, expireTime: &expireTime}
	return nil
}

// Set 设置缓存
func (c *MemoryRedisCache) Set(key string, value interface{}, timeout time.Duration) error {
	return c.SetCacheObject(key, value, timeout)
}

// Get 获取缓存
func (c *MemoryRedisCache) Get(key string, val interface{}) error {
	data, err := c.GetCacheObject(key)
	if err != nil {
		return err
	}

	// 简单处理，直接类型转换
	// 实际应用中应当使用JSON序列化/反序列化
	if ptr, ok := val.(*interface{}); ok {
		*ptr = data
		return nil
	}

	// 类型不匹配
	return errors.New("类型不匹配")
}

// Delete 删除缓存
func (c *MemoryRedisCache) Delete(key string) error {
	return c.DeleteObject(key)
}

// Has 判断缓存是否存在
func (c *MemoryRedisCache) Has(key string) bool {
	return c.HasKey(key)
}

// Clear 清空缓存
func (c *MemoryRedisCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache = make(map[string]cacheItem)
	return nil
}
