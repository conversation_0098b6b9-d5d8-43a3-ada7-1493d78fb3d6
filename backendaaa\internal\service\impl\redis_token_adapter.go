package impl

import (
	"backend/internal/constants"
	"backend/internal/service"
	"strings"
	"time"
)

// RedisTokenAdapter 令牌服务的Redis缓存适配器
type RedisTokenAdapter struct {
	tokenService service.TokenService
	redisCache   service.RedisCache
}

// NewRedisTokenAdapter 创建一个Redis Token适配器
func NewRedisTokenAdapter(tokenService service.TokenService, redisCache service.RedisCache) service.RedisCache {
	return &RedisTokenAdapter{
		tokenService: tokenService,
		redisCache:   redisCache,
	}
}

// SetCacheObject 设置缓存对象
func (a *RedisTokenAdapter) SetCacheObject(key string, value interface{}, timeout ...time.Duration) error {
	return a.redisCache.SetCacheObject(key, value, timeout...)
}

// GetCacheObject 获取缓存对象
func (a *RedisTokenAdapter) GetCacheObject(key string) (interface{}, error) {
	// 如果是获取登录令牌，使用token服务处理
	if strings.HasPrefix(key, constants.LOGIN_TOKEN_KEY) {
		tokenId := strings.TrimPrefix(key, constants.LOGIN_TOKEN_KEY)
		loginUser, err := a.tokenService.GetLoginUser(tokenId)
		if err != nil {
			return nil, err
		}
		return loginUser, nil
	}

	// 其他情况使用redisCache处理
	return a.redisCache.GetCacheObject(key)
}

// DeleteObject 删除缓存对象
func (a *RedisTokenAdapter) DeleteObject(key string) error {
	// 如果是删除登录令牌，使用token服务处理
	if strings.HasPrefix(key, constants.LOGIN_TOKEN_KEY) {
		tokenId := strings.TrimPrefix(key, constants.LOGIN_TOKEN_KEY)
		return a.tokenService.DeleteLoginUser(tokenId)
	}

	// 其他情况使用redisCache处理
	return a.redisCache.DeleteObject(key)
}

// DeleteObjects 删除多个缓存对象
func (a *RedisTokenAdapter) DeleteObjects(keys []string) error {
	// 遍历处理每个key
	for _, key := range keys {
		if err := a.DeleteObject(key); err != nil {
			return err
		}
	}
	return nil
}

// HasKey 判断缓存对象是否存在
func (a *RedisTokenAdapter) HasKey(key string) bool {
	// 如果是检查登录令牌，使用token服务处理
	if strings.HasPrefix(key, constants.LOGIN_TOKEN_KEY) {
		tokenId := strings.TrimPrefix(key, constants.LOGIN_TOKEN_KEY)
		_, err := a.tokenService.GetLoginUser(tokenId)
		return err == nil
	}

	// 其他情况使用redisCache处理
	return a.redisCache.HasKey(key)
}

// Keys 获取符合pattern的所有key
func (a *RedisTokenAdapter) Keys(pattern string) ([]string, error) {
	// 如果是查询登录令牌
	if pattern == constants.LOGIN_TOKEN_KEY+"*" {
		// 这里是一个简单实现，实际上可能需要从数据库或其他存储中获取所有的token
		// 为简化处理，这里返回一个假的token列表
		// 在实际项目中，这里应当实现真实的逻辑
		return []string{constants.LOGIN_TOKEN_KEY + "dummy-token"}, nil
	}

	// 其他情况使用redisCache处理
	return a.redisCache.Keys(pattern)
}

// GetKeys 获取符合pattern的所有key（别名，为了兼容缓存监控）
func (a *RedisTokenAdapter) GetKeys(pattern string) ([]string, error) {
	return a.Keys(pattern)
}

// GetInfo 获取缓存信息
func (a *RedisTokenAdapter) GetInfo() (map[string]string, error) {
	return a.redisCache.GetInfo()
}

// GetCommandStats 获取缓存命令统计
func (a *RedisTokenAdapter) GetCommandStats() ([]map[string]string, error) {
	return a.redisCache.GetCommandStats()
}

// GetDBSize 获取缓存大小
func (a *RedisTokenAdapter) GetDBSize() (int64, error) {
	return a.redisCache.GetDBSize()
}

// Expire 设置过期时间
func (a *RedisTokenAdapter) Expire(key string, timeout time.Duration) error {
	return a.redisCache.Expire(key, timeout)
}
