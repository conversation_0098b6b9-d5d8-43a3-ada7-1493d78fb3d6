package impl

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// RedisTokenService 基于Redis的Token服务实现
type RedisTokenService struct {
	redisCache service.RedisCache
}

// NewRedisTokenService 创建Redis Token服务
func NewRedisTokenService(redisCache service.RedisCache) service.TokenService {
	return &RedisTokenService{
		redisCache: redisCache,
	}
}

// CreateToken 创建令牌
func (s *RedisTokenService) CreateToken(loginUser *model.LoginUser) (string, error) {
	if loginUser == nil || loginUser.User == nil {
		return "", errors.New("登录用户为空")
	}

	// 生成JWT Token
	token, err := utils.GenerateToken(loginUser.User.UserID, loginUser.User.UserName)
	if err != nil {
		return "", err
	}

	// 设置登录用户信息
	loginUser.Token = token
	loginUser.LoginTime = time.Now()
	loginUser.ExpireTime = time.Now().Add(24 * time.Hour)       // Token有效期24小时
	loginUser.TokenRefreshTime = time.Now().Add(12 * time.Hour) // 12小时后刷新

	// 将登录用户信息存入Redis，有效期设置为30小时（比Token有效期长一些）
	err = s.SetLoginUser(loginUser)
	if err != nil {
		return "", err
	}

	return token, nil
}

// GetLoginUser 获取登录用户
func (s *RedisTokenService) GetLoginUser(token string) (*model.LoginUser, error) {
	if token == "" {
		return nil, errors.New("令牌为空")
	}

	// 解析JWT Token以验证格式
	_, err := utils.ParseToken(token)
	if err != nil {
		return nil, err
	}

	// 从Redis中获取用户信息
	userKey := fmt.Sprintf("%s%s", constants.LOGIN_TOKEN_KEY, token)
	loginUserObj, err := s.redisCache.GetCacheObject(userKey)
	if err != nil {
		return nil, err
	}

	if loginUserObj == nil {
		return nil, errors.New("登录状态已过期")
	}

	// 将接口类型转换为LoginUser
	var loginUser *model.LoginUser
	if data, ok := loginUserObj.([]byte); ok {
		// 如果是字节数组，则进行JSON反序列化
		err := json.Unmarshal(data, &loginUser)
		if err != nil {
			return nil, err
		}
	} else if jsonStr, ok := loginUserObj.(string); ok {
		// 如果是字符串，则进行JSON反序列化
		err := json.Unmarshal([]byte(jsonStr), &loginUser)
		if err != nil {
			return nil, err
		}
	} else {
		// 尝试直接类型断言
		loginUser, ok = loginUserObj.(*model.LoginUser)
		if !ok {
			return nil, errors.New("无效的登录用户类型")
		}
	}

	return loginUser, nil
}

// DeleteLoginUser 删除登录用户
func (s *RedisTokenService) DeleteLoginUser(token string) error {
	if token == "" {
		return errors.New("令牌为空")
	}

	// 从Redis中删除用户信息
	userKey := fmt.Sprintf("%s%s", constants.LOGIN_TOKEN_KEY, token)
	return s.redisCache.DeleteObject(userKey)
}

// RefreshToken 刷新令牌
func (s *RedisTokenService) RefreshToken(loginUser *model.LoginUser) error {
	if loginUser == nil {
		return errors.New("登录用户为空")
	}

	// 更新过期时间
	loginUser.ExpireTime = time.Now().Add(24 * time.Hour)
	loginUser.TokenRefreshTime = time.Now().Add(12 * time.Hour)

	// 更新Redis中的用户信息
	return s.SetLoginUser(loginUser)
}

// IsTokenExpired 判断令牌是否过期
func (s *RedisTokenService) IsTokenExpired(loginUser *model.LoginUser) bool {
	if loginUser == nil {
		return true
	}
	return time.Now().After(loginUser.ExpireTime)
}

// VerifyToken 验证令牌有效期，相差不足12小时，自动刷新缓存
func (s *RedisTokenService) VerifyToken(loginUser *model.LoginUser) error {
	if loginUser == nil {
		return errors.New("登录用户为空")
	}

	// 检查是否已过期
	if s.IsTokenExpired(loginUser) {
		return errors.New("令牌已过期")
	}

	// 判断是否需要刷新
	if time.Now().After(loginUser.TokenRefreshTime) {
		return s.RefreshToken(loginUser)
	}

	return nil
}

// SetLoginUser 设置登录用户
func (s *RedisTokenService) SetLoginUser(loginUser *model.LoginUser) error {
	if loginUser == nil || loginUser.Token == "" {
		return errors.New("登录用户为空")
	}

	userKey := fmt.Sprintf("%s%s", constants.LOGIN_TOKEN_KEY, loginUser.Token)
	// 设置到Redis，有效期为30小时
	return s.redisCache.SetCacheObject(userKey, loginUser, 30*time.Hour)
}
