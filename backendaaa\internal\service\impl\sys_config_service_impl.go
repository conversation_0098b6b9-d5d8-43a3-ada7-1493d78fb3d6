package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"
	"sync"

	"gorm.io/gorm"
)

var configCache sync.Map

// SysConfigServiceImpl 系统配置服务实现
type SysConfigServiceImpl struct {
	configRepo repository.SysConfigRepository
}

// NewSysConfigService 创建系统配置服务
func NewSysConfigService(configRepo repository.SysConfigRepository) service.SysConfigService {
	return &SysConfigServiceImpl{
		configRepo: configRepo,
	}
}

// SelectConfigById 查询参数配置信息
func (s *SysConfigServiceImpl) SelectConfigById(configId int64) (*model.SysConfig, error) {
	return s.configRepo.SelectConfigById(configId)
}

// SelectConfigByKey 根据键名查询参数配置信息
func (s *SysConfigServiceImpl) SelectConfigByKey(configKey string) (string, error) {
	// 先从缓存中获取
	if val, ok := configCache.Load(configKey); ok {
		return val.(string), nil
	}

	// 缓存中没有，从数据库获取
	config, err := s.configRepo.SelectConfigByKey(configKey)
	if err != nil {
		return "", err
	}
	if config != nil {
		configCache.Store(configKey, config.ConfigValue)
		return config.ConfigValue, nil
	}
	return "", nil
}

// SelectCaptchaEnabled 获取验证码开关
func (s *SysConfigServiceImpl) SelectCaptchaEnabled() bool {
	val, err := s.SelectConfigByKey("sys.account.captchaEnabled")
	if err != nil {
		return true
	}
	return val == "true"
}

// SelectConfigList 查询参数配置列表
func (s *SysConfigServiceImpl) SelectConfigList(config *model.SysConfig) ([]*model.SysConfig, error) {
	return s.configRepo.SelectConfigList(config)
}

// InsertConfig 新增参数配置
func (s *SysConfigServiceImpl) InsertConfig(config *model.SysConfig) (int, error) {
	// 使用事务处理
	var result int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		result, err = s.configRepo.InsertConfigTx(tx, config)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("新增参数配置失败: %v", err)
		return 0, err
	}

	// 新增成功后，更新缓存
	configCache.Store(config.ConfigKey, config.ConfigValue)
	return int(result), nil
}

// UpdateConfig 修改参数配置
func (s *SysConfigServiceImpl) UpdateConfig(config *model.SysConfig) (int, error) {
	// 使用事务处理
	var result int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		result, err = s.configRepo.UpdateConfigTx(tx, config)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("修改参数配置失败: %v", err)
		return 0, err
	}

	// 更新缓存
	configCache.Store(config.ConfigKey, config.ConfigValue)
	return int(result), nil
}

// DeleteConfigByIds 批量删除参数信息
func (s *SysConfigServiceImpl) DeleteConfigByIds(configIds []int64) error {
	// 先获取这些配置，用于删除缓存
	configKeys := make([]string, 0, len(configIds))
	for _, configId := range configIds {
		config, err := s.SelectConfigById(configId)
		if err == nil && config != nil {
			configKeys = append(configKeys, config.ConfigKey)
		}
	}

	// 使用事务处理删除操作
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.configRepo.DeleteConfigByIdsTx(tx, configIds)
	})

	if err != nil {
		log.Printf("批量删除参数配置失败: %v", err)
		return err
	}

	// 删除缓存
	for _, key := range configKeys {
		configCache.Delete(key)
	}

	return nil
}

// LoadingConfigCache 加载参数缓存数据
func (s *SysConfigServiceImpl) LoadingConfigCache() error {
	configs, err := s.configRepo.SelectConfigList(&model.SysConfig{})
	if err != nil {
		return err
	}

	// 清空现有缓存
	configCache = sync.Map{}

	// 加载所有配置到缓存
	for _, config := range configs {
		configCache.Store(config.ConfigKey, config.ConfigValue)
	}
	return nil
}

// ClearConfigCache 清空参数缓存数据
func (s *SysConfigServiceImpl) ClearConfigCache() error {
	configCache = sync.Map{}
	return nil
}

// ResetConfigCache 重置参数缓存数据
func (s *SysConfigServiceImpl) ResetConfigCache() error {
	return s.LoadingConfigCache()
}

// CheckConfigKeyUnique 校验参数键名是否唯一
func (s *SysConfigServiceImpl) CheckConfigKeyUnique(config *model.SysConfig) bool {
	// 查询是否存在相同键名的配置
	existConfig, err := s.configRepo.CheckConfigKeyUnique(config.ConfigKey, config.ConfigID)
	if err != nil {
		return false
	}

	// 没有找到相同键名的配置，表示键名唯一
	return existConfig == nil
}
