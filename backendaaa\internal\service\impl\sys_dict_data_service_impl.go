package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"

	"gorm.io/gorm"
)

// SysDictDataServiceImpl 字典数据服务实现
type SysDictDataServiceImpl struct {
	dictDataRepo repository.SysDictDataRepository
}

// NewSysDictDataService 创建字典数据服务
func NewSysDictDataService(dictDataRepo repository.SysDictDataRepository) service.SysDictDataService {
	return &SysDictDataServiceImpl{
		dictDataRepo: dictDataRepo,
	}
}

// SelectDictDataList 查询字典数据列表
func (s *SysDictDataServiceImpl) SelectDictDataList(dictData *model.SysDictData) ([]*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataList(dictData)
}

// SelectDictDataById 根据字典数据ID查询信息
func (s *SysDictDataServiceImpl) SelectDictDataById(dictCode int64) (*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataById(dictCode)
}

// SelectDictLabel 根据字典类型和字典值获取字典标签
func (s *SysDictDataServiceImpl) SelectDictLabel(dictType, dictValue string) (string, error) {
	return s.dictDataRepo.SelectDictLabel(dictType, dictValue)
}

// SelectDictDataByType 根据字典类型查询字典数据
func (s *SysDictDataServiceImpl) SelectDictDataByType(dictType string) ([]*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataByType(dictType)
}

// DeleteDictDataById 删除字典数据
func (s *SysDictDataServiceImpl) DeleteDictDataById(dictCode int64) error {
	// 查询要删除的字典数据
	dictData, err := s.SelectDictDataById(dictCode)
	if err != nil {
		return err
	}

	// 使用统一的事务管理
	err = database.Transaction(func(tx *gorm.DB) error {
		// 删除字典数据
		return s.dictDataRepo.DeleteDictDataByIdTx(tx, dictCode)
	})

	if err != nil {
		log.Printf("删除字典数据失败: %v", err)
		return err
	}

	// 删除缓存
	dictCache.Delete(dictData.DictType)

	return nil
}

// DeleteDictDataByIds 批量删除字典数据
func (s *SysDictDataServiceImpl) DeleteDictDataByIds(dictCodes []int64) error {
	// 需要删除缓存的字典类型
	dictTypesToDelete := make(map[string]bool)

	// 查询所有要删除的字典数据，以获取其类型
	for _, dictCode := range dictCodes {
		dictData, err := s.SelectDictDataById(dictCode)
		if err == nil && dictData != nil {
			dictTypesToDelete[dictData.DictType] = true
		}
	}

	// 使用统一的事务管理
	err := database.Transaction(func(tx *gorm.DB) error {
		// 批量删除字典数据
		return s.dictDataRepo.DeleteDictDataByIdsTx(tx, dictCodes)
	})

	if err != nil {
		log.Printf("批量删除字典数据失败: %v", err)
		return err
	}

	// 删除缓存
	for dictType := range dictTypesToDelete {
		dictCache.Delete(dictType)
	}

	return nil
}

// InsertDictData 新增字典数据
func (s *SysDictDataServiceImpl) InsertDictData(dictData *model.SysDictData) (int64, error) {
	var dictCode int64

	// 使用统一的事务管理
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		// 插入字典数据
		dictCode, err = s.dictDataRepo.InsertDictDataTx(tx, dictData)
		return err
	})

	if err != nil {
		log.Printf("新增字典数据失败: %v", err)
		return 0, err
	}

	// 清除缓存
	dictCache.Delete(dictData.DictType)

	return dictCode, nil
}

// UpdateDictData 修改字典数据
func (s *SysDictDataServiceImpl) UpdateDictData(dictData *model.SysDictData) (int64, error) {
	// 查询旧数据
	oldDict, err := s.dictDataRepo.SelectDictDataById(dictData.DictCode)
	if err != nil {
		return 0, err
	}

	var rows int64

	// 使用统一的事务管理
	err = database.Transaction(func(tx *gorm.DB) error {
		var err error
		// 更新数据
		rows, err = s.dictDataRepo.UpdateDictDataTx(tx, dictData)
		return err
	})

	if err != nil {
		log.Printf("修改字典数据失败: %v", err)
		return 0, err
	}

	// 清除缓存
	dictCache.Delete(oldDict.DictType)
	if oldDict.DictType != dictData.DictType {
		dictCache.Delete(dictData.DictType)
	}

	return rows, nil
}
