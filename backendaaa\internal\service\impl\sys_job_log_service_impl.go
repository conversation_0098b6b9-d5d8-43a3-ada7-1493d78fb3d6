package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"
)

// SysJobLogServiceImpl 定时任务调度日志信息业务层处理
type SysJobLogServiceImpl struct {
	jobLogRepo repository.SysJobLogRepository
}

// NewSysJobLogService 创建定时任务日志服务实例
func NewSysJobLogService(jobLogRepo repository.SysJobLogRepository) service.SysJobLogService {
	return &SysJobLogServiceImpl{
		jobLogRepo: jobLogRepo,
	}
}

// SelectJobLogList 获取定时任务调度日志列表
func (s *SysJobLogServiceImpl) SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error) {
	return s.jobLogRepo.SelectJobLogList(jobLog)
}

// SelectJobLogById 通过调度任务日志ID查询调度信息
func (s *SysJobLogServiceImpl) SelectJobLogById(jobLogId int64) (*model.SysJobLog, error) {
	return s.jobLogRepo.SelectJobLogById(jobLogId)
}

// InsertJobLog 新增任务日志
func (s *SysJobLogServiceImpl) InsertJobLog(jobLog *model.SysJobLog) (int64, error) {
	return s.jobLogRepo.InsertJobLog(jobLog)
}

// DeleteJobLogById 删除任务日志
func (s *SysJobLogServiceImpl) DeleteJobLogById(jobLogId int64) (int, error) {
	// 开启事务
	db := s.jobLogRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := s.jobLogRepo.DeleteJobLogByIdTx(tx, jobLogId)
	if err != nil {
		tx.Rollback()
		return 0, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return 0, err
	}

	return 1, nil
}

// DeleteJobLogByIds 批量删除调度日志信息
func (s *SysJobLogServiceImpl) DeleteJobLogByIds(jobLogIds []int64) (int, error) {
	// 开启事务
	db := s.jobLogRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	count, err := s.jobLogRepo.DeleteJobLogByIdsTx(tx, jobLogIds)
	if err != nil {
		tx.Rollback()
		return 0, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return 0, err
	}

	return count, nil
}

// CleanJobLog 清空任务日志
func (s *SysJobLogServiceImpl) CleanJobLog() (int, error) {
	// 开启事务
	db := s.jobLogRepo.GetDB()
	tx := db.Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	count, err := s.jobLogRepo.CleanJobLogTx(tx)
	if err != nil {
		tx.Rollback()
		log.Printf("清空任务日志失败：%v", err)
		return 0, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return 0, err
	}

	return count, nil
}
