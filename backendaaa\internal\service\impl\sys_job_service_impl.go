package impl

import (
	"backend/internal/constants"
	"backend/internal/database"
	"backend/internal/job/util"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"fmt"
	"log"

	"gorm.io/gorm"
)

// SysJobServiceImpl 定时任务调度信息业务层处理
type SysJobServiceImpl struct {
	jobRepo      repository.SysJobRepository
	jobLogRepo   repository.SysJobLogRepository
	jobScheduler *util.JobScheduler
}

// NewSysJobService 创建定时任务服务实例
func NewSysJobService(jobRepo repository.SysJobRepository, jobLogRepo repository.SysJobLogRepository) service.SysJobService {
	scheduler := util.NewJobScheduler(jobRepo, jobLogRepo)
	scheduler.Start()

	// 初始化任务
	err := scheduler.InitJobs()
	if err != nil {
		fmt.Printf("初始化任务失败: %v\n", err)
	}

	return &SysJobServiceImpl{
		jobRepo:      jobRepo,
		jobLogRepo:   jobLogRepo,
		jobScheduler: scheduler,
	}
}

// SelectJobList 查询定时任务列表
func (s *SysJobServiceImpl) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	return s.jobRepo.SelectJobList(job)
}

// SelectJobById 通过调度编号获取调度信息
func (s *SysJobServiceImpl) SelectJobById(jobId int64) (*model.SysJob, error) {
	return s.jobRepo.SelectJobById(jobId)
}

// PauseJob 暂停任务
func (s *SysJobServiceImpl) PauseJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_PAUSE

	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.jobRepo.UpdateJobStatusTx(tx, job)
	})

	if err != nil {
		log.Printf("暂停任务失败: %v", err)
		return err
	}

	s.jobScheduler.PauseJob(job)
	return nil
}

// ResumeJob 恢复任务
func (s *SysJobServiceImpl) ResumeJob(job *model.SysJob) error {
	job.Status = constants.JOB_STATUS_NORMAL

	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.jobRepo.UpdateJobStatusTx(tx, job)
	})

	if err != nil {
		log.Printf("恢复任务失败: %v", err)
		return err
	}

	return s.jobScheduler.ResumeJob(job)
}

// DeleteJob 删除任务
func (s *SysJobServiceImpl) DeleteJob(job *model.SysJob) error {
	s.jobScheduler.DeleteJob(job)

	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.jobRepo.DeleteJobByIdTx(tx, job.JobID)
	})

	if err != nil {
		log.Printf("删除任务失败: %v", err)
	}

	return err
}

// DeleteJobByIds 批量删除调度信息
func (s *SysJobServiceImpl) DeleteJobByIds(jobIds []int64) error {
	for _, jobId := range jobIds {
		job, err := s.jobRepo.SelectJobById(jobId)
		if err == nil && job != nil {
			s.jobScheduler.DeleteJob(job)
		}
	}

	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.jobRepo.DeleteJobByIdsTx(tx, jobIds)
	})

	if err != nil {
		log.Printf("批量删除任务失败: %v", err)
	}

	return err
}

// ChangeStatus 修改任务状态
func (s *SysJobServiceImpl) ChangeStatus(job *model.SysJob) (int, error) {
	var err error
	if job.Status == constants.JOB_STATUS_NORMAL {
		err = s.ResumeJob(job)
	} else {
		err = s.PauseJob(job)
	}
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// Run 立即运行任务
func (s *SysJobServiceImpl) Run(job *model.SysJob) (bool, error) {
	jobCopy, err := s.jobRepo.SelectJobById(job.JobID)
	if err != nil {
		return false, err
	}
	if jobCopy == nil {
		return false, fmt.Errorf("任务不存在")
	}

	s.jobScheduler.RunOnce(jobCopy)
	return true, nil
}

// InsertJob 新增任务
func (s *SysJobServiceImpl) InsertJob(job *model.SysJob) (int64, error) {
	// 使用事务处理
	var jobId int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		jobId, err = s.jobRepo.InsertJobTx(tx, job)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("新增任务失败: %v", err)
		return 0, err
	}

	job.JobID = jobId
	err = s.jobScheduler.CreateJob(job)
	return jobId, err
}

// UpdateJob 更新任务
func (s *SysJobServiceImpl) UpdateJob(job *model.SysJob) (int, error) {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.jobRepo.UpdateJobTx(tx, job)
	})

	if err != nil {
		log.Printf("更新任务失败: %v", err)
		return 0, err
	}

	err = s.jobScheduler.UpdateJob(job)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// CheckCronExpressionIsValid 校验cron表达式是否有效
func (s *SysJobServiceImpl) CheckCronExpressionIsValid(cronExpression string) bool {
	return util.IsValidCronExpression(cronExpression)
}
