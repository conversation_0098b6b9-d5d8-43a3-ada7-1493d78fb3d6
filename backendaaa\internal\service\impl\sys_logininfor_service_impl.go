package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"

	"gorm.io/gorm"
)

// SysLogininforServiceImpl 登录日志服务实现
type SysLogininforServiceImpl struct {
	logininforRepository repository.SysLogininforRepository
}

// NewSysLogininforService 创建登录日志服务实例
func NewSysLogininforService(logininforRepository repository.SysLogininforRepository) service.SysLogininforService {
	return &SysLogininforServiceImpl{
		logininforRepository: logininforRepository,
	}
}

// InsertLogininfor 新增系统登录日志
func (s *SysLogininforServiceImpl) InsertLogininfor(logininfor *model.SysLogininfor) error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.logininforRepository.InsertTx(tx, logininfor)
	})

	if err != nil {
		log.Printf("新增登录日志失败: %v", err)
	}

	return err
}

// SelectLogininforList 查询系统登录日志集合
func (s *SysLogininforServiceImpl) SelectLogininforList(logininfor *model.SysLogininfor) ([]*model.SysLogininfor, error) {
	return s.logininforRepository.SelectList(logininfor)
}

// DeleteLogininforByIds 批量删除系统登录日志
func (s *SysLogininforServiceImpl) DeleteLogininforByIds(infoIds []int64) (int64, error) {
	// 使用事务处理
	var rowsAffected int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		rowsAffected, err = s.logininforRepository.DeleteByIdsTx(tx, infoIds)
		return err
	})

	if err != nil {
		log.Printf("批量删除登录日志失败: %v", err)
		return 0, err
	}

	return rowsAffected, nil
}

// CleanLogininfor 清空系统登录日志
func (s *SysLogininforServiceImpl) CleanLogininfor() error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.logininforRepository.DeleteAllTx(tx)
	})

	if err != nil {
		log.Printf("清空登录日志失败: %v", err)
	}

	return err
}
