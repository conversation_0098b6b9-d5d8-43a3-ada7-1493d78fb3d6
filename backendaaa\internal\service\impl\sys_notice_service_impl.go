package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"

	"gorm.io/gorm"
)

// SysNoticeServiceImpl 通知公告服务实现
type SysNoticeServiceImpl struct {
	noticeRepo repository.SysNoticeRepository
}

// NewSysNoticeService 创建通知公告服务
func NewSysNoticeService(noticeRepo repository.SysNoticeRepository) service.SysNoticeService {
	return &SysNoticeServiceImpl{
		noticeRepo: noticeRepo,
	}
}

// SelectNoticeById 查询公告信息
func (s *SysNoticeServiceImpl) SelectNoticeById(noticeId int64) (*model.SysNotice, error) {
	return s.noticeRepo.SelectNoticeById(noticeId)
}

// SelectNoticeList 查询公告列表
func (s *SysNoticeServiceImpl) SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error) {
	return s.noticeRepo.SelectNoticeList(notice)
}

// InsertNotice 新增公告
func (s *SysNoticeServiceImpl) InsertNotice(notice *model.SysNotice) (int64, error) {
	// 使用事务处理
	var noticeId int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		noticeId, err = s.noticeRepo.InsertNoticeTx(tx, notice)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("新增通知公告失败: %v", err)
		return 0, err
	}

	return noticeId, nil
}

// UpdateNotice 修改公告
func (s *SysNoticeServiceImpl) UpdateNotice(notice *model.SysNotice) (int64, error) {
	// 使用事务处理
	var rows int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		rows, err = s.noticeRepo.UpdateNoticeTx(tx, notice)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("修改通知公告失败: %v", err)
		return 0, err
	}

	return rows, nil
}

// DeleteNoticeById 删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeById(noticeId int64) error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.noticeRepo.DeleteNoticeByIdTx(tx, noticeId)
	})

	if err != nil {
		log.Printf("删除通知公告失败: %v", err)
	}

	return err
}

// DeleteNoticeByIds 批量删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeByIds(noticeIds []int64) error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.noticeRepo.DeleteNoticeByIdsTx(tx, noticeIds)
	})

	if err != nil {
		log.Printf("批量删除通知公告失败: %v", err)
	}

	return err
}
