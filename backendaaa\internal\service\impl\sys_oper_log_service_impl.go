package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"

	"gorm.io/gorm"
)

// SysOperLogServiceImpl 操作日志服务实现
type SysOperLogServiceImpl struct {
	operLogRepository repository.SysOperLogRepository
}

// NewSysOperLogService 创建操作日志服务实例
func NewSysOperLogService(operLogRepository repository.SysOperLogRepository) service.SysOperLogService {
	return &SysOperLogServiceImpl{
		operLogRepository: operLogRepository,
	}
}

// InsertOperLog 新增操作日志
func (s *SysOperLogServiceImpl) InsertOperLog(operLog *model.SysOperLog) error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.operLogRepository.InsertTx(tx, operLog)
	})

	if err != nil {
		log.Printf("新增操作日志失败: %v", err)
	}

	return err
}

// SelectOperLogList 查询系统操作日志集合
func (s *SysOperLogServiceImpl) SelectOperLogList(operLog *model.SysOperLog) ([]*model.SysOperLog, error) {
	return s.operLogRepository.SelectList(operLog)
}

// DeleteOperLogByIds 批量删除系统操作日志
func (s *SysOperLogServiceImpl) DeleteOperLogByIds(operIds []int64) (int64, error) {
	// 使用事务处理
	var rowsAffected int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		rowsAffected, err = s.operLogRepository.DeleteByIdsTx(tx, operIds)
		return err
	})

	if err != nil {
		log.Printf("批量删除操作日志失败: %v", err)
		return 0, err
	}

	return rowsAffected, nil
}

// SelectOperLogById 查询操作日志详细
func (s *SysOperLogServiceImpl) SelectOperLogById(operId int64) (*model.SysOperLog, error) {
	return s.operLogRepository.SelectById(operId)
}

// CleanOperLog 清空操作日志
func (s *SysOperLogServiceImpl) CleanOperLog() error {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.operLogRepository.DeleteAllTx(tx)
	})

	if err != nil {
		log.Printf("清空操作日志失败: %v", err)
	}

	return err
}
