package impl

import (
	"backend/internal/constants"
	"backend/internal/service"
	"backend/internal/utils"
	"fmt"
)

// SysPasswordServiceImpl 密码服务实现
type SysPasswordServiceImpl struct {
	redisCache    service.RedisCache
	maxRetryCount int
	lockTime      int
}

// NewSysPasswordService 创建密码服务实例
func NewSysPasswordService(redisCache service.RedisCache, maxRetryCount, lockTime int) service.SysPasswordService {
	return &SysPasswordServiceImpl{
		redisCache:    redisCache,
		maxRetryCount: maxRetryCount,
		lockTime:      lockTime,
	}
}

// getCacheKey 获取缓存键
func (s *SysPasswordServiceImpl) getCacheKey(username string) string {
	return constants.PWD_ERR_CNT_KEY + username
}

// Validate 验证密码
func (s *SysPasswordServiceImpl) Validate(user interface{}, password string) error {
	// 获取用户名
	username := ""
	userPassword := ""

	// 根据用户类型获取用户名和密码
	switch u := user.(type) {
	case map[string]interface{}:
		if name, ok := u["userName"].(string); ok {
			username = name
		}
		if pwd, ok := u["password"].(string); ok {
			userPassword = pwd
		}
	default:
		return fmt.Errorf("不支持的用户类型")
	}

	// 获取用户密码错误次数
	retryCount, err := s.redisCache.GetCacheObject(s.getCacheKey(username))
	if err != nil {
		retryCount = 0
	}

	count, ok := retryCount.(int)
	if !ok {
		count = 0
	}

	// 超过最大重试次数，锁定账户
	if count >= s.maxRetryCount {
		return fmt.Errorf("密码输入错误%d次，帐户锁定%d分钟", s.maxRetryCount, s.lockTime)
	}

	// 验证密码
	if !utils.MatchesPassword(password, userPassword) {
		// 密码错误，增加错误次数
		count++
		s.redisCache.SetCacheObject(s.getCacheKey(username), count)
		return fmt.Errorf("密码不正确")
	}

	// 密码正确，清除错误记录
	s.ClearLoginRecordCache(username)
	return nil
}

// ClearLoginRecordCache 清除登录记录缓存
func (s *SysPasswordServiceImpl) ClearLoginRecordCache(username string) error {
	if username == "" {
		return nil
	}

	// 删除密码错误次数缓存
	return s.redisCache.DeleteObject(s.getCacheKey(username))
}
