package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"log"

	"gorm.io/gorm"
)

// SysPostServiceImpl 岗位服务实现
type SysPostServiceImpl struct {
	postRepo     repository.SysPostRepository
	userPostRepo repository.SysUserPostRepository
}

// NewSysPostService 创建岗位服务
func NewSysPostService(
	postRepo repository.SysPostRepository,
	userPostRepo repository.SysUserPostRepository,
) service.SysPostService {
	return &SysPostServiceImpl{
		postRepo:     postRepo,
		userPostRepo: userPostRepo,
	}
}

// SelectPostList 查询岗位列表
func (s *SysPostServiceImpl) SelectPostList(post *model.SysPost) ([]*model.SysPost, error) {
	return s.postRepo.SelectPostList(post)
}

// SelectPostById 通过岗位ID查询岗位信息
func (s *SysPostServiceImpl) SelectPostById(postId int64) (*model.SysPost, error) {
	return s.postRepo.SelectPostById(postId)
}

// SelectPostsByUserId 根据用户ID获取岗位选择框列表
func (s *SysPostServiceImpl) SelectPostsByUserId(userId int64) ([]*model.SysPost, error) {
	return s.postRepo.SelectPostsByUserId(userId)
}

// SelectPostAll 查询所有岗位
func (s *SysPostServiceImpl) SelectPostAll() ([]*model.SysPost, error) {
	return s.postRepo.SelectPostList(&model.SysPost{})
}

// SelectPostListByUserId 根据用户ID查询岗位ID列表
func (s *SysPostServiceImpl) SelectPostListByUserId(userId int64) ([]int64, error) {
	posts, err := s.postRepo.SelectPostsByUserId(userId)
	if err != nil {
		return nil, err
	}

	var postIds []int64
	for _, post := range posts {
		postIds = append(postIds, post.PostID)
	}
	return postIds, nil
}

// InsertPost 新增保存岗位信息
func (s *SysPostServiceImpl) InsertPost(post *model.SysPost) int {
	// 使用事务处理
	var postId int64
	err := database.Transaction(func(tx *gorm.DB) error {
		var err error
		postId, err = s.postRepo.InsertPostTx(tx, post)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Printf("新增岗位失败: %v", err)
		return 0
	}
	return int(postId)
}

// UpdatePost 修改保存岗位信息
func (s *SysPostServiceImpl) UpdatePost(post *model.SysPost) int {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		return s.postRepo.UpdatePostTx(tx, post)
	})

	if err != nil {
		log.Printf("修改岗位失败: %v", err)
		return 0
	}
	return 1
}

// DeletePostByIds 批量删除岗位信息
func (s *SysPostServiceImpl) DeletePostByIds(postIds []int64) int {
	// 使用事务处理
	err := database.Transaction(func(tx *gorm.DB) error {
		for _, postId := range postIds {
			// 查询岗位是否已分配
			count, err := s.CountUserPostById(postId)
			if err != nil {
				return err
			}
			if count > 0 {
				return service.NewError("岗位已分配，不能删除")
			}
		}

		return s.postRepo.DeletePostByIdsTx(tx, postIds)
	})

	if err != nil {
		log.Printf("删除岗位失败: %v", err)
		return 0
	}
	return 1
}

// CheckPostNameUnique 校验岗位名称是否唯一
func (s *SysPostServiceImpl) CheckPostNameUnique(post *model.SysPost) bool {
	isUnique, err := s.postRepo.CheckPostNameUnique(post)
	if err != nil {
		return false
	}
	return isUnique
}

// CheckPostCodeUnique 校验岗位编码是否唯一
func (s *SysPostServiceImpl) CheckPostCodeUnique(post *model.SysPost) bool {
	isUnique, err := s.postRepo.CheckPostCodeUnique(post)
	if err != nil {
		return false
	}
	return isUnique
}

// CountUserPostById 通过岗位ID查询岗位使用数量
func (s *SysPostServiceImpl) CountUserPostById(postId int64) (int64, error) {
	return s.userPostRepo.CountUserPostByPostId(postId)
}
