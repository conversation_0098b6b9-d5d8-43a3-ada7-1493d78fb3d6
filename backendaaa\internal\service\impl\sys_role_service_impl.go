package impl

import (
	"backend/internal/database"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
	"fmt"

	"gorm.io/gorm"
)

// SysRoleServiceImpl 角色服务实现
type SysRoleServiceImpl struct {
	roleRepository     repository.SysRoleRepository
	roleMenuRepository repository.SysRoleMenuRepository
	roleDeptRepository repository.SysRoleDeptRepository
	userRoleRepository repository.SysUserRoleRepository
}

// NewSysRoleService 创建角色服务实例
func NewSysRoleService(
	roleRepository repository.SysRoleRepository,
	roleMenuRepository repository.SysRoleMenuRepository,
	roleDeptRepository repository.SysRoleDeptRepository,
	userRoleRepository repository.SysUserRoleRepository,
) service.SysRoleService {
	return &SysRoleServiceImpl{
		roleRepository:     roleRepository,
		roleMenuRepository: roleMenuRepository,
		roleDeptRepository: roleDeptRepository,
		userRoleRepository: userRoleRepository,
	}
}

// SelectRoleList 根据条件分页查询角色数据
func (s *SysRoleServiceImpl) SelectRoleList(role *model.SysRole) ([]*model.SysRole, error) {
	return s.roleRepository.SelectRoleList(role)
}

// CountRoleList 根据条件统计角色总数
func (s *SysRoleServiceImpl) CountRoleList(role *model.SysRole) (int64, error) {
	return s.roleRepository.CountRoleList(role)
}

// SelectRoleById 通过角色ID查询角色
func (s *SysRoleServiceImpl) SelectRoleById(roleId int64) (*model.SysRole, error) {
	return s.roleRepository.SelectRoleById(roleId)
}

// SelectRoleAll 查询所有角色
func (s *SysRoleServiceImpl) SelectRoleAll() ([]*model.SysRole, error) {
	return s.SelectRoleList(&model.SysRole{})
}

// SelectRolesByUserId 根据用户ID查询角色
func (s *SysRoleServiceImpl) SelectRolesByUserId(userId int64) ([]*model.SysRole, error) {
	// 查询用户拥有的角色
	userRoles, err := s.roleRepository.SelectRolesByUserId(userId)
	if err != nil {
		return nil, err
	}

	// 查询所有角色
	roles, err := s.SelectRoleAll()
	if err != nil {
		return nil, err
	}

	// 标记用户拥有的角色
	for _, role := range roles {
		for _, userRole := range userRoles {
			if role.RoleID == userRole.RoleID {
				role.Flag = true
				break
			}
		}
	}

	return roles, nil
}

// InsertRole 新增角色
func (s *SysRoleServiceImpl) InsertRole(role *model.SysRole) (int, error) {
	// 使用事务处理，确保数据一致性
	err := database.Transaction(func(tx *gorm.DB) error {
		// 新增角色信息
		roleId, err := s.roleRepository.InsertRoleTx(tx, role)
		if err != nil {
			return err
		}
		role.RoleID = roleId

		// 新增角色菜单关联
		return s.insertRoleMenuTx(tx, role)
	})

	if err != nil {
		return 0, err
	}
	return 1, nil
}

// UpdateRole 修改角色
func (s *SysRoleServiceImpl) UpdateRole(role *model.SysRole) (int, error) {
	// 使用事务处理，确保数据一致性
	err := database.Transaction(func(tx *gorm.DB) error {
		// 修改角色信息
		err := s.roleRepository.UpdateRoleTx(tx, role)
		if err != nil {
			return err
		}

		// 删除角色与菜单关联
		err = s.roleMenuRepository.DeleteRoleMenuByRoleIdTx(tx, role.RoleID)
		if err != nil {
			return err
		}

		// 新增角色菜单关联
		return s.insertRoleMenuTx(tx, role)
	})

	if err != nil {
		return 0, err
	}
	return 1, nil
}

// AuthDataScope 修改数据权限
func (s *SysRoleServiceImpl) AuthDataScope(role *model.SysRole) (int, error) {
	// 使用事务处理，确保数据一致性
	err := database.Transaction(func(tx *gorm.DB) error {
		// 修改角色信息
		err := s.roleRepository.UpdateRoleTx(tx, role)
		if err != nil {
			return err
		}

		// 删除角色与部门关联
		err = s.roleDeptRepository.DeleteRoleDeptByRoleIdTx(tx, role.RoleID)
		if err != nil {
			return err
		}

		// 新增角色部门信息
		return s.insertRoleDeptTx(tx, role)
	})

	if err != nil {
		return 0, err
	}
	return 1, nil
}

// UpdateRoleStatus 修改角色状态
func (s *SysRoleServiceImpl) UpdateRoleStatus(role *model.SysRole) (int, error) {
	err := s.roleRepository.UpdateRole(role)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// DeleteRoleById 删除角色
func (s *SysRoleServiceImpl) DeleteRoleById(roleId int64) (int, error) {
	// 使用事务处理，确保数据一致性
	err := database.Transaction(func(tx *gorm.DB) error {
		// 删除角色与菜单关联
		err := s.roleMenuRepository.DeleteRoleMenuByRoleIdTx(tx, roleId)
		if err != nil {
			return err
		}

		// 删除角色与部门关联
		err = s.roleDeptRepository.DeleteRoleDeptByRoleIdTx(tx, roleId)
		if err != nil {
			return err
		}

		// 删除角色
		return s.roleRepository.DeleteRoleByIdTx(tx, roleId)
	})

	if err != nil {
		return 0, err
	}
	return 1, nil
}

// DeleteRoleByIds 批量删除角色
func (s *SysRoleServiceImpl) DeleteRoleByIds(roleIds []int64) (int, error) {
	for _, roleId := range roleIds {
		role := &model.SysRole{RoleID: roleId}
		err := s.CheckRoleAllowed(role)
		if err != nil {
			return 0, err
		}

		err = s.CheckRoleDataScope(roleId)
		if err != nil {
			return 0, err
		}

		// 查询角色信息
		role, err = s.SelectRoleById(roleId)
		if err != nil {
			return 0, err
		}

		// 检查是否已分配用户
		count, err := s.userRoleRepository.CountUserRoleByRoleId(roleId)
		if err != nil {
			return 0, err
		}
		if count > 0 {
			return 0, utils.NewError(fmt.Sprintf("%s已分配,不能删除", role.RoleName))
		}
	}

	// 使用事务处理批量删除
	err := database.Transaction(func(tx *gorm.DB) error {
		for _, roleId := range roleIds {
			// 删除角色与菜单关联
			err := s.roleMenuRepository.DeleteRoleMenuByRoleIdTx(tx, roleId)
			if err != nil {
				return err
			}

			// 删除角色与部门关联
			err = s.roleDeptRepository.DeleteRoleDeptByRoleIdTx(tx, roleId)
			if err != nil {
				return err
			}

			// 删除角色
			err = s.roleRepository.DeleteRoleByIdTx(tx, roleId)
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return 0, err
	}
	return len(roleIds), nil
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (s *SysRoleServiceImpl) CheckRoleNameUnique(role *model.SysRole) bool {
	return s.roleRepository.CheckRoleNameUnique(role.RoleName, role.RoleID)
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (s *SysRoleServiceImpl) CheckRoleKeyUnique(role *model.SysRole) bool {
	return s.roleRepository.CheckRoleKeyUnique(role.RoleKey, role.RoleID)
}

// CheckRoleAllowed 校验角色是否允许操作
func (s *SysRoleServiceImpl) CheckRoleAllowed(role *model.SysRole) error {
	if role.IsAdmin() {
		return utils.NewError("不允许操作超级管理员角色")
	}
	return nil
}

// CheckRoleDataScope 校验角色是否有数据权限
func (s *SysRoleServiceImpl) CheckRoleDataScope(roleId int64) error {
	// 暂时不实现数据权限检查
	return nil
}

// SelectUserRoleByUserId 获取用户所属角色列表
func (s *SysRoleServiceImpl) SelectUserRoleByUserId(userId int64) ([]int64, error) {
	userRoles, err := s.userRoleRepository.SelectUserRoleByUserId(userId)
	if err != nil {
		return nil, err
	}

	roleIds := make([]int64, 0, len(userRoles))
	for _, userRole := range userRoles {
		roleIds = append(roleIds, userRole.RoleId)
	}
	return roleIds, nil
}

// CheckRoleDataScopeByIds 批量校验角色是否有数据权限
func (s *SysRoleServiceImpl) CheckRoleDataScopeByIds(roleIds []int64) error {
	// 暂时不实现数据权限检查
	return nil
}

// DeleteAuthUser 取消用户授权
func (s *SysRoleServiceImpl) DeleteAuthUser(userRole *model.SysUserRole) (int, error) {
	err := s.userRoleRepository.DeleteUserRoleInfo(userRole)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// DeleteAuthUsers 批量取消用户授权
func (s *SysRoleServiceImpl) DeleteAuthUsers(roleId int64, userIds []int64) (int, error) {
	return s.userRoleRepository.DeleteUserRoleInfos(roleId, userIds)
}

// InsertAuthUsers 批量选择用户授权
func (s *SysRoleServiceImpl) InsertAuthUsers(roleId int64, userIds []int64) (int, error) {
	// 使用事务处理批量授权
	err := database.Transaction(func(tx *gorm.DB) error {
		// 新增用户角色信息
		userRoles := make([]*model.SysUserRole, 0, len(userIds))
		for _, userId := range userIds {
			userRoles = append(userRoles, &model.SysUserRole{
				UserId: userId,
				RoleId: roleId,
			})
		}
		return s.userRoleRepository.BatchUserRoleTx(tx, userRoles)
	})

	if err != nil {
		return 0, err
	}
	return len(userIds), nil
}

// insertRoleMenu 新增角色菜单信息
func (s *SysRoleServiceImpl) insertRoleMenu(role *model.SysRole) error {
	// 非事务方法，直接调用仓库层方法
	if len(role.MenuIDs) > 0 {
		// 先构建菜单数组
		list := make([]*model.SysRoleMenu, 0, len(role.MenuIDs))
		for _, menuId := range role.MenuIDs {
			rm := &model.SysRoleMenu{
				RoleId: role.RoleID,
				MenuId: menuId,
			}
			list = append(list, rm)
		}
		return s.roleMenuRepository.BatchRoleMenu(list)
	}
	return nil
}

// insertRoleMenuTx 新增角色菜单信息(事务)
func (s *SysRoleServiceImpl) insertRoleMenuTx(tx *gorm.DB, role *model.SysRole) error {
	// 事务方法，在事务中执行
	if len(role.MenuIDs) > 0 {
		// 先构建菜单数组
		list := make([]*model.SysRoleMenu, 0, len(role.MenuIDs))
		for _, menuId := range role.MenuIDs {
			rm := &model.SysRoleMenu{
				RoleId: role.RoleID,
				MenuId: menuId,
			}
			list = append(list, rm)
		}
		return s.roleMenuRepository.BatchRoleMenuTx(tx, list)
	}
	return nil
}

// insertRoleDept 新增角色部门信息
func (s *SysRoleServiceImpl) insertRoleDept(role *model.SysRole) error {
	// 非事务方法，直接调用仓库层方法
	if len(role.DeptIDs) > 0 {
		// 先构建部门数组
		list := make([]*model.SysRoleDept, 0, len(role.DeptIDs))
		for _, deptId := range role.DeptIDs {
			rd := &model.SysRoleDept{
				RoleId: role.RoleID,
				DeptId: deptId,
			}
			list = append(list, rd)
		}
		return s.roleDeptRepository.BatchRoleDept(list)
	}
	return nil
}

// insertRoleDeptTx 新增角色部门信息(事务)
func (s *SysRoleServiceImpl) insertRoleDeptTx(tx *gorm.DB, role *model.SysRole) error {
	// 事务方法，在事务中执行
	if len(role.DeptIDs) > 0 {
		// 先构建部门数组
		list := make([]*model.SysRoleDept, 0, len(role.DeptIDs))
		for _, deptId := range role.DeptIDs {
			rd := &model.SysRoleDept{
				RoleId: role.RoleID,
				DeptId: deptId,
			}
			list = append(list, rd)
		}
		return s.roleDeptRepository.BatchRoleDeptTx(tx, list)
	}
	return nil
}
