package impl

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"errors"
	"fmt"
	"time"
)

// EmptyTokenService 空Token服务实现
type EmptyTokenService struct {
}

// NewEmptyTokenService 创建一个空的Token服务
func NewEmptyTokenService() service.TokenService {
	return &EmptyTokenService{}
}

// CreateToken 创建Token
func (s *EmptyTokenService) CreateToken(loginUser *model.LoginUser) (string, error) {
	return "dummy-token", nil
}

// GetLoginUser 获取登录用户
func (s *EmptyTokenService) GetLoginUser(token string) (*model.LoginUser, error) {
	if token == "dummy-token" {
		return &model.LoginUser{
			User: &model.SysUser{
				UserID: 1,
			},
		}, nil
	}
	return nil, errors.New("invalid token")
}

// DeleteLoginUser 删除登录用户
func (s *EmptyTokenService) DeleteLoginUser(token string) error {
	return nil
}

// RefreshToken 刷新令牌
func (s *EmptyTokenService) RefreshToken(loginUser *model.LoginUser) error {
	return nil
}

// IsTokenExpired 判断令牌是否过期
func (s *EmptyTokenService) IsTokenExpired(loginUser *model.LoginUser) bool {
	return false
}

// VerifyToken 验证令牌有效期
func (s *EmptyTokenService) VerifyToken(loginUser *model.LoginUser) error {
	return nil
}

// SetLoginUser 设置登录用户
func (s *EmptyTokenService) SetLoginUser(loginUser *model.LoginUser) error {
	return nil
}

// MemoryTokenService 基于内存的Token服务实现
type MemoryTokenService struct {
	// 用户缓存
	userCache map[string]*model.LoginUser
}

// NewMemoryTokenService 创建一个内存Token服务
func NewMemoryTokenService() service.TokenService {
	return &MemoryTokenService{
		userCache: make(map[string]*model.LoginUser),
	}
}

// CreateToken 创建令牌
func (s *MemoryTokenService) CreateToken(loginUser *model.LoginUser) (string, error) {
	// 生成Token
	token, err := utils.GenerateToken(loginUser.User.UserID, loginUser.User.UserName)
	if err != nil {
		return "", err
	}

	// 设置用户信息
	loginUser.Token = token
	loginUser.LoginTime = time.Now()
	loginUser.ExpireTime = time.Now().Add(2 * time.Hour)
	loginUser.TokenRefreshTime = time.Now().Add(30 * time.Minute)

	// 存入缓存
	s.userCache[token] = loginUser

	return token, nil
}

// GetLoginUser 获取登录用户
func (s *MemoryTokenService) GetLoginUser(token string) (*model.LoginUser, error) {
	if loginUser, ok := s.userCache[token]; ok {
		return loginUser, nil
	}
	return nil, errors.New("用户未登录")
}

// DeleteLoginUser 删除登录用户
func (s *MemoryTokenService) DeleteLoginUser(token string) error {
	delete(s.userCache, token)
	return nil
}

// RefreshToken 刷新令牌
func (s *MemoryTokenService) RefreshToken(loginUser *model.LoginUser) error {
	if loginUser == nil {
		return errors.New("登录用户为空")
	}

	// 更新过期时间
	loginUser.ExpireTime = time.Now().Add(2 * time.Hour)
	loginUser.TokenRefreshTime = time.Now().Add(30 * time.Minute)

	// 更新缓存
	s.userCache[loginUser.Token] = loginUser

	return nil
}

// IsTokenExpired 判断令牌是否过期
func (s *MemoryTokenService) IsTokenExpired(loginUser *model.LoginUser) bool {
	if loginUser == nil {
		return true
	}

	return time.Now().After(loginUser.ExpireTime)
}

// VerifyToken 验证令牌有效期
func (s *MemoryTokenService) VerifyToken(loginUser *model.LoginUser) error {
	if loginUser == nil {
		return errors.New("登录用户为空")
	}

	// 判断是否需要刷新
	if time.Now().After(loginUser.TokenRefreshTime) {
		return s.RefreshToken(loginUser)
	}

	return nil
}

// SetLoginUser 设置登录用户
func (s *MemoryTokenService) SetLoginUser(loginUser *model.LoginUser) error {
	if loginUser == nil || loginUser.Token == "" {
		return errors.New("登录用户为空")
	}

	s.userCache[loginUser.Token] = loginUser
	return nil
}

// getUserKey 获取用户键
func getUserKey(token string) string {
	return fmt.Sprintf("%s%s", constants.LOGIN_TOKEN_KEY, token)
}
