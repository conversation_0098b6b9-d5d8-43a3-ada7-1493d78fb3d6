package service

import "time"

// SimplifiedRedisCache Redis缓存服务接口（简化版）
type SimplifiedRedisCache interface {
	// Set 设置缓存
	Set(key string, value interface{}, timeout time.Duration) error

	// Get 获取缓存
	Get(key string, val interface{}) error

	// Delete 删除缓存
	Delete(key string) error

	// Has 判断缓存是否存在
	Has(key string) bool

	// Clear 清空缓存
	Clear() error

	// Keys 获取匹配的所有key
	Keys(pattern string) ([]string, error)
}
