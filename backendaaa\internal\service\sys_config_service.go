package service

import "backend/internal/model"

// 工具函数
func ConvertToInt(str string) int {
	var result int
	// 实际应用中需要实现字符串转整数的逻辑
	return result
}

// SysConfigService 参数配置服务接口
type SysConfigService interface {
	// SelectConfigById 查询参数配置信息
	SelectConfigById(configId int64) (*model.SysConfig, error)

	// SelectConfigByKey 根据键名查询参数配置信息
	SelectConfigByKey(configKey string) (string, error)

	// SelectCaptchaEnabled 获取验证码开关
	SelectCaptchaEnabled() bool

	// SelectConfigList 查询参数配置列表
	SelectConfigList(config *model.SysConfig) ([]*model.SysConfig, error)

	// InsertConfig 新增参数配置
	InsertConfig(config *model.SysConfig) (int, error)

	// UpdateConfig 修改参数配置
	UpdateConfig(config *model.SysConfig) (int, error)

	// DeleteConfigByIds 批量删除参数信息
	DeleteConfigByIds(configIds []int64) error

	// LoadingConfigCache 加载参数缓存数据
	LoadingConfigCache() error

	// ClearConfigCache 清空参数缓存数据
	ClearConfigCache() error

	// ResetConfigCache 重置参数缓存数据
	ResetConfigCache() error

	// CheckConfigKeyUnique 校验参数键名是否唯一
	CheckConfigKeyUnique(config *model.SysConfig) bool
}
