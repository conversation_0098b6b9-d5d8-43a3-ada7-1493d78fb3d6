package service

import "backend/internal/model"

// SysDictDataService 字典数据服务接口
type SysDictDataService interface {
	// SelectDictDataList 查询字典数据列表
	SelectDictDataList(dictData *model.SysDictData) ([]*model.SysDictData, error)

	// SelectDictDataById 根据字典数据ID查询信息
	SelectDictDataById(dictCode int64) (*model.SysDictData, error)

	// SelectDictLabel 根据字典类型和字典值获取字典标签
	SelectDictLabel(dictType, dictValue string) (string, error)

	// SelectDictDataByType 根据字典类型查询字典数据
	SelectDictDataByType(dictType string) ([]*model.SysDictData, error)

	// DeleteDictDataById 删除字典数据
	DeleteDictDataById(dictCode int64) error

	// DeleteDictDataByIds 批量删除字典数据
	DeleteDictDataByIds(dictCodes []int64) error

	// InsertDictData 新增字典数据
	InsertDictData(dictData *model.SysDictData) (int64, error)

	// UpdateDictData 修改字典数据
	UpdateDictData(dictData *model.SysDictData) (int64, error)
}
