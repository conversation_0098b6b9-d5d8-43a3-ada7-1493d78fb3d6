package service

import "backend/internal/model"

// SysDictTypeService 字典类型服务接口
type SysDictTypeService interface {
	// SelectDictTypeList 查询字典类型列表
	SelectDictTypeList(dictType *model.SysDictType) ([]*model.SysDictType, error)

	// SelectDictTypeAll 查询所有字典类型
	SelectDictTypeAll() ([]*model.SysDictType, error)

	// SelectDictTypeById 根据字典类型ID查询信息
	SelectDictTypeById(dictId int64) (*model.SysDictType, error)

	// SelectDictTypeByType 根据字典类型查询信息
	SelectDictTypeByType(dictType string) (*model.SysDictType, error)

	// SelectDictDataByType 根据字典类型查询字典数据
	SelectDictDataByType(dictType string) ([]*model.SysDictData, error)

	// DeleteDictTypeById 删除字典类型
	DeleteDictTypeById(dictId int64) error

	// DeleteDictTypeByIds 批量删除字典类型
	DeleteDictTypeByIds(dictIds []int64) error

	// InsertDictType 新增字典类型
	InsertDictType(dictType *model.SysDictType) (int64, error)

	// UpdateDictType 修改字典类型
	UpdateDictType(dictType *model.SysDictType) (int64, error)

	// CheckDictTypeUnique 校验字典类型是否唯一
	CheckDictTypeUnique(dictType *model.SysDictType) bool

	// ResetDictCache 重置字典缓存数据
	ResetDictCache() error

	// LoadingDictCache 加载字典缓存数据
	LoadingDictCache() error

	// ClearDictCache 清空字典缓存数据
	ClearDictCache() error
}
