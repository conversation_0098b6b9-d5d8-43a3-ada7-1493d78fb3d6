package service

import (
	"backend/internal/model"
)

// SysJobLogService 定时任务调度日志信息服务层接口
type SysJobLogService interface {
	// SelectJobLogList 获取定时任务调度日志列表
	SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error)

	// SelectJobLogById 通过调度任务日志ID查询调度信息
	SelectJobLogById(jobLogId int64) (*model.SysJobLog, error)

	// InsertJobLog 新增任务日志
	InsertJobLog(jobLog *model.SysJobLog) (int64, error)

	// DeleteJobLogByIds 批量删除调度日志信息
	DeleteJobLogByIds(jobLogIds []int64) (int, error)

	// DeleteJobLogById 删除任务日志
	DeleteJobLogById(jobLogId int64) (int, error)

	// CleanJobLog 清空任务日志
	CleanJobLog() (int, error)
}
