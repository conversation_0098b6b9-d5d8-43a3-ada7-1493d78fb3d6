package service

import (
	"backend/internal/dto"
	"backend/internal/model"
)

// SysMenuService 菜单服务接口
type SysMenuService interface {
	// SelectMenuList 根据用户查询系统菜单列表
	SelectMenuList(userId int64) ([]*model.SysMenu, error)

	// SelectMenuListWithCondition 根据用户和条件查询系统菜单列表
	SelectMenuListWithCondition(menu *model.SysMenu, userId int64) ([]*model.SysMenu, error)

	// SelectMenuPermsByUserId 根据用户ID查询权限
	SelectMenuPermsByUserId(userId int64) ([]string, error)

	// SelectMenuPermsByRoleId 根据角色ID查询权限
	SelectMenuPermsByRoleId(roleId int64) ([]string, error)

	// SelectMenuTreeByUserId 根据用户ID查询菜单树信息
	SelectMenuTreeByUserId(userId int64) ([]*model.SysMenu, error)

	// SelectMenuListByRoleId 根据角色ID查询菜单树信息
	SelectMenuListByRoleId(roleId int64) ([]int64, error)

	// BuildMenus 构建前端路由所需要的菜单
	BuildMenus(menus []*model.SysMenu) ([]*dto.RouterVo, error)

	// BuildMenuTree 构建前端所需要树结构
	BuildMenuTree(menus []*model.SysMenu) ([]*model.SysMenu, error)

	// BuildMenuTreeSelect 构建前端所需要下拉树结构
	BuildMenuTreeSelect(menus []*model.SysMenu) ([]*dto.TreeSelect, error)

	// SelectMenuById 根据菜单ID查询信息
	SelectMenuById(menuId int64) (*model.SysMenu, error)

	// HasChildByMenuId 是否存在菜单子节点
	HasChildByMenuId(menuId int64) (bool, error)

	// CheckMenuExistRole 查询菜单是否存在角色
	CheckMenuExistRole(menuId int64) (bool, error)

	// InsertMenu 新增保存菜单信息
	InsertMenu(menu *model.SysMenu) (int64, error)

	// UpdateMenu 修改保存菜单信息
	UpdateMenu(menu *model.SysMenu) (int64, error)

	// DeleteMenuById 删除菜单管理信息
	DeleteMenuById(menuId int64) (int64, error)

	// CheckMenuNameUnique 校验菜单名称是否唯一
	CheckMenuNameUnique(menu *model.SysMenu) (bool, error)
}
