package service

import "backend/internal/model"

// SysNoticeService 通知公告服务接口
type SysNoticeService interface {
	// SelectNoticeById 查询公告信息
	SelectNoticeById(noticeId int64) (*model.SysNotice, error)

	// SelectNoticeList 查询公告列表
	SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error)

	// InsertNotice 新增公告
	InsertNotice(notice *model.SysNotice) (int64, error)

	// UpdateNotice 修改公告
	UpdateNotice(notice *model.SysNotice) (int64, error)

	// DeleteNoticeById 删除公告信息
	DeleteNoticeById(noticeId int64) error

	// DeleteNoticeByIds 批量删除公告信息
	DeleteNoticeByIds(noticeIds []int64) error
}
