package service

import "backend/internal/model"

// SysOperLogService 操作日志服务接口
type SysOperLogService interface {
	// InsertOperLog 新增操作日志
	InsertOperLog(operLog *model.SysOperLog) error

	// SelectOperLogList 查询系统操作日志集合
	SelectOperLogList(operLog *model.SysOperLog) ([]*model.SysOperLog, error)

	// DeleteOperLogByIds 批量删除系统操作日志
	DeleteOperLogByIds(operIds []int64) (int64, error)

	// SelectOperLogById 查询操作日志详细
	SelectOperLogById(operId int64) (*model.SysOperLog, error)

	// CleanOperLog 清空操作日志
	CleanOperLog() error
}
