package service

import "backend/internal/model"

// SysRoleService 角色服务接口
type SysRoleService interface {
	// SelectRoleList 查询角色列表
	SelectRoleList(role *model.SysRole) ([]*model.SysRole, error)

	// CountRoleList 统计角色总数
	CountRoleList(role *model.SysRole) (int64, error)

	// SelectRoleById 根据角色ID查询角色
	SelectRoleById(roleId int64) (*model.SysRole, error)

	// SelectRoleAll 查询所有角色
	SelectRoleAll() ([]*model.SysRole, error)

	// SelectRolesByUserId 根据用户ID查询角色
	SelectRolesByUserId(userId int64) ([]*model.SysRole, error)

	// InsertRole 新增角色
	InsertRole(role *model.SysRole) (int, error)

	// UpdateRole 修改角色
	UpdateRole(role *model.SysRole) (int, error)

	// AuthDataScope 修改数据权限
	AuthDataScope(role *model.SysRole) (int, error)

	// UpdateRoleStatus 修改角色状态
	UpdateRoleStatus(role *model.SysRole) (int, error)

	// DeleteRoleById 删除角色
	DeleteRoleById(roleId int64) (int, error)

	// DeleteRoleByIds 批量删除角色
	DeleteRoleByIds(roleIds []int64) (int, error)

	// CheckRoleNameUnique 校验角色名称是否唯一
	CheckRoleNameUnique(role *model.SysRole) bool

	// CheckRoleKeyUnique 校验角色权限是否唯一
	CheckRoleKeyUnique(role *model.SysRole) bool

	// CheckRoleAllowed 校验角色是否允许操作
	CheckRoleAllowed(role *model.SysRole) error

	// CheckRoleDataScope 校验角色是否有数据权限
	CheckRoleDataScope(roleId int64) error

	// CheckRoleDataScopeByIds 校验角色是否有数据权限（批量）
	CheckRoleDataScopeByIds(roleIds []int64) error

	// DeleteAuthUser 取消授权用户角色
	DeleteAuthUser(userRole *model.SysUserRole) (int, error)

	// DeleteAuthUsers 批量取消授权用户角色
	DeleteAuthUsers(roleId int64, userIds []int64) (int, error)

	// InsertAuthUsers 批量选择授权用户角色
	InsertAuthUsers(roleId int64, userIds []int64) (int, error)
}
