package service

import "backend/internal/model"

// SysUserOnlineService 在线用户服务接口
type SysUserOnlineService interface {
	// SelectOnlineByIpaddr 通过登录地址查询信息
	SelectOnlineByIpaddr(ipaddr string, user *model.LoginUser) *model.SysUserOnline

	// SelectOnlineByUserName 通过用户名称查询信息
	SelectOnlineByUserName(userName string, user *model.LoginUser) *model.SysUserOnline

	// SelectOnlineByInfo 通过登录地址/用户名称查询信息
	SelectOnlineByInfo(ipaddr, userName string, user *model.LoginUser) *model.SysUserOnline

	// LoginUserToUserOnline 设置在线用户信息
	LoginUserToUserOnline(user *model.LoginUser) *model.SysUserOnline
}
