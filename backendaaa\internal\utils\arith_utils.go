package utils

import (
	"fmt"
	"math"
	"strconv"
)

// ArithUtils 精确的浮点数运算工具类
type ArithUtils struct{}

// Add 提供精确的加法运算
func (a *ArithUtils) Add(v1, v2 float64) float64 {
	return v1 + v2
}

// Sub 提供精确的减法运算
func (a *ArithUtils) Sub(v1, v2 float64) float64 {
	return v1 - v2
}

// Mul 提供精确的乘法运算
func (a *ArithUtils) Mul(v1, v2 float64) float64 {
	return v1 * v2
}

// Div 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到小数点以后10位，以后的数字四舍五入
func (a *ArithUtils) Div(v1, v2 float64, scale int) float64 {
	if scale < 0 {
		panic("The scale must be a positive integer or zero")
	}
	if v1 == 0 {
		return 0
	}
	if v2 == 0 {
		panic("Division by zero")
	}

	result := v1 / v2
	// 四舍五入
	return a.Round(result, scale)
}

// Round 提供精确的小数位四舍五入处理
func (a *ArithUtils) Round(v float64, scale int) float64 {
	if scale < 0 {
		panic("The scale must be a positive integer or zero")
	}

	// 计算缩放因子
	scaleFactor := math.Pow10(scale)
	// 四舍五入
	return math.Round(v*scaleFactor) / scaleFactor
}

// ConvertFileSize 字节转换
func (a *ArithUtils) ConvertFileSize(size int64) string {
	kb := int64(1024)
	mb := kb * 1024
	gb := mb * 1024

	if size >= gb {
		return fmt.Sprintf("%.1f GB", float64(size)/float64(gb))
	} else if size >= mb {
		f := float64(size) / float64(mb)
		if f > 100 {
			return fmt.Sprintf("%.0f MB", f)
		}
		return fmt.Sprintf("%.1f MB", f)
	} else if size >= kb {
		f := float64(size) / float64(kb)
		if f > 100 {
			return fmt.Sprintf("%.0f KB", f)
		}
		return fmt.Sprintf("%.1f KB", f)
	} else {
		return fmt.Sprintf("%d B", size)
	}
}

// ParseFloat 将字符串转换为浮点数
func (a *ArithUtils) ParseFloat(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}

// Arith 全局数值计算工具实例
var Arith = &ArithUtils{}
