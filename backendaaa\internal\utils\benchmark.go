package utils

import (
	"fmt"
	"runtime"
	"sync"
	"time"
)

// BenchmarkResult 基准测试结果
type BenchmarkResult struct {
	Name           string
	Duration       time.Duration
	TotalRequests  int
	SuccessCount   int
	FailureCount   int
	RequestsPerSec float64
	AvgLatency     time.Duration
	MaxLatency     time.Duration
	MinLatency     time.Duration
	Concurrency    int
	MemoryUsage    uint64
}

// BenchmarkOptions 基准测试选项
type BenchmarkOptions struct {
	Concurrency   int           // 并发数
	TotalRequests int           // 总请求数
	Duration      time.Duration // 测试持续时间
	Timeout       time.Duration // 单个请求超时时间
}

// DefaultBenchmarkOptions 默认基准测试选项
func DefaultBenchmarkOptions() *BenchmarkOptions {
	return &BenchmarkOptions{
		Concurrency:   10,
		TotalRequests: 1000,
		Duration:      10 * time.Second,
		Timeout:       2 * time.Second,
	}
}

// Benchmark 执行基准测试
func Benchmark(name string, fn func() error, options *BenchmarkOptions) *BenchmarkResult {
	if options == nil {
		options = DefaultBenchmarkOptions()
	}

	// 创建结果通道
	resultChan := make(chan bool, options.TotalRequests)
	latencyChan := make(chan time.Duration, options.TotalRequests)

	// 记录开始时间
	startTime := time.Now()

	// 记录初始内存使用
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	initialMemory := memStats.Alloc

	// 创建工作池
	var wg sync.WaitGroup
	throttle := NewThrottle(options.Concurrency)

	// 启动工作协程
	for i := 0; i < options.TotalRequests; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 使用节流器控制并发
			_, err := throttle.Execute(func() (interface{}, error) {
				start := time.Now()
				err := fn()
				latency := time.Since(start)

				// 记录延迟
				latencyChan <- latency

				// 记录结果
				resultChan <- (err == nil)

				return nil, err
			})

			if err != nil {
				// 记录失败
				resultChan <- false
			}
		}()
	}

	// 等待所有请求完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 设置超时
	select {
	case <-done:
		// 所有请求完成
	case <-time.After(options.Duration):
		// 超时
		fmt.Printf("测试超时，已执行 %v\n", options.Duration)
	}

	// 记录结束时间
	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 统计结果
	successCount := 0
	failureCount := 0

	// 统计已完成的请求
	close(resultChan)
	for success := range resultChan {
		if success {
			successCount++
		} else {
			failureCount++
		}
	}

	// 统计延迟
	var totalLatency time.Duration
	var maxLatency time.Duration
	var minLatency = time.Hour // 初始设置为一个较大的值

	close(latencyChan)
	latencyCount := 0
	for latency := range latencyChan {
		totalLatency += latency
		latencyCount++

		if latency > maxLatency {
			maxLatency = latency
		}

		if latency < minLatency {
			minLatency = latency
		}
	}

	// 计算平均延迟
	var avgLatency time.Duration
	if latencyCount > 0 {
		avgLatency = totalLatency / time.Duration(latencyCount)
	}

	// 计算每秒请求数
	requestsPerSec := float64(successCount+failureCount) / duration.Seconds()

	// 获取内存使用
	runtime.ReadMemStats(&memStats)
	finalMemory := memStats.Alloc
	memoryUsage := finalMemory - initialMemory

	// 创建结果
	result := &BenchmarkResult{
		Name:           name,
		Duration:       duration,
		TotalRequests:  successCount + failureCount,
		SuccessCount:   successCount,
		FailureCount:   failureCount,
		RequestsPerSec: requestsPerSec,
		AvgLatency:     avgLatency,
		MaxLatency:     maxLatency,
		MinLatency:     minLatency,
		Concurrency:    options.Concurrency,
		MemoryUsage:    memoryUsage,
	}

	return result
}

// String 格式化输出基准测试结果
func (r *BenchmarkResult) String() string {
	return fmt.Sprintf(`
基准测试: %s
总请求数: %d
成功请求: %d
失败请求: %d
并发数: %d
总耗时: %v
每秒请求: %.2f
平均延迟: %v
最大延迟: %v
最小延迟: %v
内存使用: %.2f MB
`,
		r.Name,
		r.TotalRequests,
		r.SuccessCount,
		r.FailureCount,
		r.Concurrency,
		r.Duration,
		r.RequestsPerSec,
		r.AvgLatency,
		r.MaxLatency,
		r.MinLatency,
		float64(r.MemoryUsage)/1024/1024,
	)
}

// CompareBenchmarks 比较两个基准测试结果
func CompareBenchmarks(baseline, current *BenchmarkResult) string {
	reqPerSecDiff := (current.RequestsPerSec - baseline.RequestsPerSec) / baseline.RequestsPerSec * 100
	avgLatencyDiff := float64(baseline.AvgLatency-current.AvgLatency) / float64(baseline.AvgLatency) * 100

	return fmt.Sprintf(`
性能对比 [%s vs %s]:
每秒请求: %.2f -> %.2f (%.2f%%)
平均延迟: %v -> %v (%.2f%%)
内存使用: %.2f MB -> %.2f MB
`,
		baseline.Name,
		current.Name,
		baseline.RequestsPerSec,
		current.RequestsPerSec,
		reqPerSecDiff,
		baseline.AvgLatency,
		current.AvgLatency,
		avgLatencyDiff,
		float64(baseline.MemoryUsage)/1024/1024,
		float64(current.MemoryUsage)/1024/1024,
	)
}
