package utils

import (
	"context"
	"errors"
	"sync"
	"time"
)

// Result 执行结果
type Result struct {
	Value interface{}
	Error error
}

// WorkFunc 工作函数类型
type WorkFunc func() (interface{}, error)

// WorkFuncWithContext 带上下文的工作函数类型
type WorkFuncWithContext func(ctx context.Context) (interface{}, error)

// ErrTimeout 超时错误
var ErrTimeout = errors.New("operation timed out")

// Semaphore 信号量实现
type Semaphore struct {
	tokens chan struct{}
}

// NewSemaphore 创建一个新的信号量
func NewSemaphore(size int) *Semaphore {
	return &Semaphore{
		tokens: make(chan struct{}, size),
	}
}

// Acquire 获取信号量
func (s *Semaphore) Acquire() {
	s.tokens <- struct{}{}
}

// Release 释放信号量
func (s *Semaphore) Release() {
	<-s.tokens
}

// TryAcquire 尝试获取信号量
func (s *Semaphore) TryAcquire() bool {
	select {
	case s.tokens <- struct{}{}:
		return true
	default:
		return false
	}
}

// AcquireWithTimeout 带超时的获取信号量
func (s *Semaphore) AcquireWithTimeout(timeout time.Duration) bool {
	timer := time.NewTimer(timeout)
	defer timer.Stop()

	select {
	case s.tokens <- struct{}{}:
		return true
	case <-timer.C:
		return false
	}
}

// WithTimeout 使用超时执行函数
func WithTimeout(timeout time.Duration, f WorkFunc) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	resultCh := make(chan Result, 1)

	go func() {
		value, err := f()
		resultCh <- Result{Value: value, Error: err}
	}()

	select {
	case <-ctx.Done():
		return nil, ErrTimeout
	case result := <-resultCh:
		return result.Value, result.Error
	}
}

// WithContext 使用上下文执行函数
func WithContext(ctx context.Context, f WorkFuncWithContext) (interface{}, error) {
	resultCh := make(chan Result, 1)

	go func() {
		value, err := f(ctx)
		resultCh <- Result{Value: value, Error: err}
	}()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case result := <-resultCh:
		return result.Value, result.Error
	}
}

// Throttle 节流器
type Throttle struct {
	sem *Semaphore
}

// NewThrottle 创建一个新的节流器
func NewThrottle(concurrency int) *Throttle {
	return &Throttle{
		sem: NewSemaphore(concurrency),
	}
}

// Execute 执行一个受节流控制的函数
func (t *Throttle) Execute(f WorkFunc) (interface{}, error) {
	t.sem.Acquire()
	defer t.sem.Release()

	return f()
}

// ExecuteWithContext 执行一个受节流控制的带上下文的函数
func (t *Throttle) ExecuteWithContext(ctx context.Context, f WorkFuncWithContext) (interface{}, error) {
	t.sem.Acquire()
	defer t.sem.Release()

	return f(ctx)
}

// WorkerPool 工作池
type WorkerPool struct {
	workChan chan WorkFunc
	wg       sync.WaitGroup
	quit     chan struct{}
}

// NewWorkerPool 创建一个新的工作池
func NewWorkerPool(size int) *WorkerPool {
	pool := &WorkerPool{
		workChan: make(chan WorkFunc),
		quit:     make(chan struct{}),
	}

	pool.wg.Add(size)
	for i := 0; i < size; i++ {
		go pool.worker()
	}

	return pool
}

// worker 工作协程
func (wp *WorkerPool) worker() {
	defer wp.wg.Done()

	for {
		select {
		case work := <-wp.workChan:
			work()
		case <-wp.quit:
			return
		}
	}
}

// Submit 提交工作到工作池
func (wp *WorkerPool) Submit(work WorkFunc) {
	wp.workChan <- work
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	close(wp.quit)
	wp.wg.Wait()
}

// ParallelExecute 并行执行多个函数
func ParallelExecute(funcs ...WorkFunc) []Result {
	var wg sync.WaitGroup
	results := make([]Result, len(funcs))

	for i, f := range funcs {
		wg.Add(1)
		go func(idx int, fn WorkFunc) {
			defer wg.Done()
			value, err := fn()
			results[idx] = Result{Value: value, Error: err}
		}(i, f)
	}

	wg.Wait()
	return results
}

// ParallelExecuteWithContext 并行执行多个带上下文的函数
func ParallelExecuteWithContext(ctx context.Context, funcs ...WorkFuncWithContext) []Result {
	var wg sync.WaitGroup
	results := make([]Result, len(funcs))

	for i, f := range funcs {
		wg.Add(1)
		go func(idx int, fn WorkFuncWithContext) {
			defer wg.Done()
			value, err := fn(ctx)
			results[idx] = Result{Value: value, Error: err}
		}(i, f)
	}

	wg.Wait()
	return results
}

// Once 确保函数只执行一次
type Once struct {
	once sync.Once
}

// Do 执行函数，确保只执行一次
func (o *Once) Do(f WorkFunc) (interface{}, error) {
	var (
		result interface{}
		err    error
	)
	o.once.Do(func() {
		result, err = f()
	})
	return result, err
}
