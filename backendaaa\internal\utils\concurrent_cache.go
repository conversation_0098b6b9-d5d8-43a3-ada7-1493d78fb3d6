package utils

import (
	"sync"
	"time"
)

// CacheItem 缓存项
type CacheItem struct {
	Value      interface{}
	Expiration int64
}

// 判断缓存项是否过期
func (item CacheItem) Expired() bool {
	if item.Expiration == 0 {
		return false
	}
	return time.Now().UnixNano() > item.Expiration
}

// ConcurrentCache 并发安全的缓存
type ConcurrentCache struct {
	items             map[string]CacheItem
	mu                sync.RWMutex
	defaultExpiration time.Duration
	cleanupInterval   time.Duration
	stopCleanup       chan bool
}

// NewConcurrentCache 创建一个新的并发安全缓存
func NewConcurrentCache(defaultExpiration, cleanupInterval time.Duration) *ConcurrentCache {
	cache := &ConcurrentCache{
		items:             make(map[string]CacheItem),
		defaultExpiration: defaultExpiration,
		cleanupInterval:   cleanupInterval,
		stopCleanup:       make(chan bool),
	}

	// 如果清理间隔大于0，启动定期清理过期项目
	if cleanupInterval > 0 {
		go cache.startCleanupTimer()
	}

	return cache
}

// startCleanupTimer 启动清理定时器
func (c *ConcurrentCache) startCleanupTimer() {
	ticker := time.NewTicker(c.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.DeleteExpired()
		case <-c.stopCleanup:
			return
		}
	}
}

// Set 设置缓存项
func (c *ConcurrentCache) Set(key string, value interface{}, duration time.Duration) {
	var expiration int64

	if duration == 0 {
		duration = c.defaultExpiration
	}

	if duration > 0 {
		expiration = time.Now().Add(duration).UnixNano()
	}

	c.mu.Lock()
	c.items[key] = CacheItem{
		Value:      value,
		Expiration: expiration,
	}
	c.mu.Unlock()
}

// Get 获取缓存项
func (c *ConcurrentCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	item, found := c.items[key]
	c.mu.RUnlock()

	if !found {
		return nil, false
	}

	if item.Expired() {
		c.mu.Lock()
		delete(c.items, key)
		c.mu.Unlock()
		return nil, false
	}

	return item.Value, true
}

// Delete 删除缓存项
func (c *ConcurrentCache) Delete(key string) {
	c.mu.Lock()
	delete(c.items, key)
	c.mu.Unlock()
}

// DeleteExpired 删除所有过期的缓存项
func (c *ConcurrentCache) DeleteExpired() {
	now := time.Now().UnixNano()

	c.mu.Lock()
	for k, v := range c.items {
		if v.Expiration > 0 && now > v.Expiration {
			delete(c.items, k)
		}
	}
	c.mu.Unlock()
}

// GetWithLoader 获取缓存项，如果不存在则加载
func (c *ConcurrentCache) GetWithLoader(key string, loader func() (interface{}, error)) (interface{}, error) {
	// 先尝试从缓存获取
	if value, found := c.Get(key); found {
		return value, nil
	}

	// 缓存未命中，加锁并再次检查（防止竞态条件）
	c.mu.Lock()
	defer c.mu.Unlock()

	// 再次检查缓存（可能在获取锁期间被其他协程设置）
	if item, found := c.items[key]; found {
		if !item.Expired() {
			return item.Value, nil
		}
	}

	// 加载数据
	value, err := loader()
	if err != nil {
		return nil, err
	}

	// 设置到缓存
	var expiration int64
	if c.defaultExpiration > 0 {
		expiration = time.Now().Add(c.defaultExpiration).UnixNano()
	}

	c.items[key] = CacheItem{
		Value:      value,
		Expiration: expiration,
	}

	return value, nil
}

// Clear 清空缓存
func (c *ConcurrentCache) Clear() {
	c.mu.Lock()
	c.items = make(map[string]CacheItem)
	c.mu.Unlock()
}

// Count 获取缓存项数量
func (c *ConcurrentCache) Count() int {
	c.mu.RLock()
	count := len(c.items)
	c.mu.RUnlock()
	return count
}

// Stop 停止清理定时器
func (c *ConcurrentCache) Stop() {
	if c.cleanupInterval > 0 {
		c.stopCleanup <- true
		close(c.stopCleanup)
	}
}

// Keys 获取所有缓存键
func (c *ConcurrentCache) Keys() []string {
	c.mu.RLock()
	keys := make([]string, 0, len(c.items))
	for k := range c.items {
		keys = append(keys, k)
	}
	c.mu.RUnlock()
	return keys
}

// Items 获取所有未过期的缓存项
func (c *ConcurrentCache) Items() map[string]interface{} {
	c.mu.RLock()
	items := make(map[string]interface{}, len(c.items))
	now := time.Now().UnixNano()
	for k, v := range c.items {
		if v.Expiration == 0 || now < v.Expiration {
			items[k] = v.Value
		}
	}
	c.mu.RUnlock()
	return items
}
