package utils

import (
	"strings"
	"unicode"
)

// ConvertToCamelCase 将字符串转换为驼峰命名法（首字母大写）
func ConvertToCamelCase(str string) string {
	// 如果字符串为空，则返回空字符串
	if str == "" {
		return ""
	}

	// 按下划线分割字符串
	parts := strings.Split(str, "_")
	result := ""

	// 将每个部分的首字母大写
	for _, part := range parts {
		if part == "" {
			continue
		}
		// 将首字母大写
		result += strings.ToUpper(part[:1]) + part[1:]
	}

	return result
}

// ConvertToLowerCamelCase 将字符串转换为小驼峰命名法（首字母小写）
func ConvertToLowerCamelCase(str string) string {
	camelCase := ConvertToCamelCase(str)
	if camelCase == "" {
		return ""
	}

	// 将首字母小写
	return strings.ToLower(camelCase[:1]) + camelCase[1:]
}

// ConvertToUnderlineCase 将驼峰命名法转换为下划线命名法
func ConvertToUnderlineCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if unicode.IsUpper(r) {
			if i > 0 {
				result.WriteRune('_')
			}
			result.WriteRune(unicode.ToLower(r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}
