package utils

import (
	"crypto/md5"
	"encoding/hex"
	"strings"
)

// EncryptPassword 对密码进行加密，模拟Java中的SecurityUtils.encryptPassword
func EncryptPassword(password string) string {
	// 创建一个MD5哈希对象
	h := md5.New()
	// 将密码写入哈希对象
	h.Write([]byte(password))
	// 获取哈希结果并转换为十六进制字符串
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}

// MatchPassword 匹配密码，模拟Java中的SecurityUtils.matchesPassword
func MatchesPassword(rawPassword, encodedPassword string) bool {
	return EncryptPassword(rawPassword) == encodedPassword
}
