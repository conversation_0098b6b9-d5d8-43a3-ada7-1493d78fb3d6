package utils

import (
	"fmt"
	"log"
	"runtime"
	"time"
)

// DumpCacheKeys 打印当前缓存中的所有键
func DumpCacheKeys(cache interface{}, pattern string) {
	if keyser, ok := cache.(interface {
		Keys(pattern string) ([]string, error)
	}); ok {
		keys, err := keyser.Keys(pattern)
		if err != nil {
			log.Printf("获取缓存键失败: %v", err)
			return
		}

		log.Printf("当前缓存中符合 %s 的键数量: %d", pattern, len(keys))
		if len(keys) > 0 {
			log.Printf("键列表: %v", keys)
		}
	} else {
		log.Printf("传入的缓存对象不支持Keys方法")
	}
}

// TraceCacheOperation 记录缓存操作及调用栈信息
func TraceCacheOperation(operation, key string, value interface{}) {
	// 获取调用栈信息
	pc := make([]uintptr, 10)
	n := runtime.Callers(2, pc)
	frames := runtime.CallersFrames(pc[:n])

	// 构建调用栈信息
	var stackInfo string
	for {
		frame, more := frames.Next()
		// 过滤掉一些不必要的栈信息
		if frame.Function != "" && !isSystemFunction(frame.Function) {
			stackInfo += fmt.Sprintf("\n\t%s:%d - %s", frame.File, frame.Line, frame.Function)
		}
		if !more {
			break
		}
	}

	// 记录缓存操作的详细信息
	log.Printf("缓存操作: %s, 键: %s, 值: %v, 时间: %s, 调用栈:%s",
		operation, key, value, time.Now().Format("2006-01-02 15:04:05.000"), stackInfo)
}

// isSystemFunction 判断是否为系统函数
func isSystemFunction(funcName string) bool {
	// 过滤一些标准库和运行时函数
	systemPrefixes := []string{
		"runtime.",
		"reflect.",
		"sync.",
		"encoding.",
		"fmt.",
		"log.",
	}

	for _, prefix := range systemPrefixes {
		if len(funcName) >= len(prefix) && funcName[:len(prefix)] == prefix {
			return true
		}
	}
	return false
}

// DumpCacheKeyInfo 打印特定缓存键的信息
func DumpCacheKeyInfo(key string, value interface{}, expireTime *time.Time) {
	var expireTimeStr string
	if expireTime == nil {
		expireTimeStr = "永不过期"
	} else {
		expireTimeStr = expireTime.Format("2006-01-02 15:04:05.000")
		if time.Now().After(*expireTime) {
			expireTimeStr += " (已过期)"
		} else {
			remaining := expireTime.Sub(time.Now())
			expireTimeStr += fmt.Sprintf(" (剩余 %.2f 秒)", remaining.Seconds())
		}
	}

	var valueType string
	var valueStr string
	if value == nil {
		valueType = "nil"
		valueStr = "nil"
	} else {
		valueType = fmt.Sprintf("%T", value)
		valueStr = fmt.Sprintf("%v", value)
	}

	log.Printf("缓存键详情: key=%s, 值类型=%s, 值=%s, 过期时间=%s",
		key, valueType, valueStr, expireTimeStr)
}
