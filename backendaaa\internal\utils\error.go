package utils

import (
	"errors"
	"fmt"
	"runtime"
	"strings"
)

// 错误类型常量，对应Java中的各种异常类型
const (
	// ErrorTypeService 业务逻辑错误
	ErrorTypeService = "ServiceError"
	// ErrorTypeSystem 系统错误
	ErrorTypeSystem = "SystemError"
	// ErrorTypeData 数据错误
	ErrorTypeData = "DataError"
	// ErrorTypeAuth 认证错误
	ErrorTypeAuth = "AuthError"
	// ErrorTypePermission 权限错误
	ErrorTypePermission = "PermissionError"
	// ErrorTypeValidation 验证错误
	ErrorTypeValidation = "ValidationError"
	// ErrorTypeNotFound 资源未找到错误
	ErrorTypeNotFound = "NotFoundError"
	// ErrorTypeDuplicate 重复操作错误
	ErrorTypeDuplicate = "DuplicateError"
)

// AppError 应用错误，包含更多上下文信息
type AppError struct {
	// 错误类型
	Type string
	// 错误消息
	Message string
	// 原始错误
	Err error
	// 错误代码
	Code int
	// 调用栈
	Stack string
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("[%s] %s: %s", e.Type, e.Message, e.Err.Error())
	}
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// Unwrap 支持errors.Unwrap
func (e *AppError) Unwrap() error {
	return e.Err
}

// Is 支持errors.Is
func (e *AppError) Is(target error) bool {
	t, ok := target.(*AppError)
	if !ok {
		return false
	}
	return e.Type == t.Type
}

// NewError 创建一个基本错误
func NewError(message string) error {
	return errors.New(message)
}

// NewAppError 创建一个应用错误
func NewAppError(errorType, message string, err error, code int) *AppError {
	stack := captureStack(2)
	return &AppError{
		Type:    errorType,
		Message: message,
		Err:     err,
		Code:    code,
		Stack:   stack,
	}
}

// NewServiceError 创建一个业务逻辑错误
func NewServiceError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeService, message, originalErr, 500)
}

// NewSystemError 创建一个系统错误
func NewSystemError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeSystem, message, originalErr, 500)
}

// NewDataError 创建一个数据错误
func NewDataError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeData, message, originalErr, 400)
}

// NewAuthError 创建一个认证错误
func NewAuthError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeAuth, message, originalErr, 401)
}

// NewPermissionError 创建一个权限错误
func NewPermissionError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypePermission, message, originalErr, 403)
}

// NewValidationError 创建一个验证错误
func NewValidationError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeValidation, message, originalErr, 400)
}

// NewNotFoundError 创建一个资源未找到错误
func NewNotFoundError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeNotFound, message, originalErr, 404)
}

// NewDuplicateError 创建一个重复操作错误
func NewDuplicateError(message string, err ...error) *AppError {
	var originalErr error
	if len(err) > 0 {
		originalErr = err[0]
	}
	return NewAppError(ErrorTypeDuplicate, message, originalErr, 409)
}

// WithMessage 使用新的消息创建一个Error
func WithMessage(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// IsAppError 判断是否为AppError
func IsAppError(err error) bool {
	var appErr *AppError
	return errors.As(err, &appErr)
}

// IsErrorType 判断错误是否为指定类型
func IsErrorType(err error, errorType string) bool {
	var appErr *AppError
	if !errors.As(err, &appErr) {
		return false
	}
	return appErr.Type == errorType
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) int {
	var appErr *AppError
	if !errors.As(err, &appErr) {
		return 500
	}
	return appErr.Code
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(err error) string {
	if err == nil {
		return ""
	}

	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Message
	}

	return err.Error()
}

// captureStack 捕获调用栈
func captureStack(skip int) string {
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(skip, pcs[:])
	frames := runtime.CallersFrames(pcs[:n])

	var sb strings.Builder
	for {
		frame, more := frames.Next()
		if !more {
			break
		}

		// 忽略标准库和runtime
		if strings.Contains(frame.File, "runtime/") {
			continue
		}

		sb.WriteString(fmt.Sprintf("%s\n\t%s:%d\n", frame.Function, frame.File, frame.Line))

		// 只记录几个关键帧
		if sb.Len() > 3 {
			break
		}
	}

	return sb.String()
}
