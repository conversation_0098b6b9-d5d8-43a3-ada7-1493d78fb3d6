package utils

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
)

// InvokeResult 执行结果
type InvokeResult struct {
	Success bool
	Message string
	Error   error
	Data    interface{}
}

// InvokeMethod 调用方法
// 支持以下格式的调用字符串:
// 1. 函数调用: FunctionName(arg1, arg2, ...)
// 2. 模块函数调用: module.FunctionName(arg1, arg2, ...)
// 3. 对象方法调用: Object.MethodName(arg1, arg2, ...)
func InvokeMethod(invokeTarget string) *InvokeResult {
	if invokeTarget == "" {
		return &InvokeResult{
			Success: false,
			Message: "调用目标为空",
			Error:   errors.New("调用目标为空"),
		}
	}

	// 在Go中实现动态调用相对复杂，这里仅实现几种常见类型的调用
	// 先分离方法名和参数
	methodAndParams := strings.TrimSpace(invokeTarget)

	// 检查是否存在括号
	leftParenIndex := strings.Index(methodAndParams, "(")
	rightParenIndex := strings.LastIndex(methodAndParams, ")")

	if leftParenIndex == -1 || rightParenIndex == -1 || leftParenIndex >= rightParenIndex {
		return &InvokeResult{
			Success: false,
			Message: "调用目标格式不正确，应为: 方法名(参数1, 参数2, ...)",
			Error:   errors.New("调用目标格式不正确"),
		}
	}

	// 提取方法名和参数
	methodName := strings.TrimSpace(methodAndParams[:leftParenIndex])
	paramsStr := strings.TrimSpace(methodAndParams[leftParenIndex+1 : rightParenIndex])

	// 分割方法名，检查是否有模块或对象
	parts := strings.Split(methodName, ".")

	// 准备执行结果
	result := &InvokeResult{
		Success: false,
		Message: "",
		Error:   nil,
	}

	// 这里仅作为示例，实际项目中应根据需要实现具体的反射调用
	// 为简化演示，我们仅实现几个内置函数

	// 简单的函数调用示例
	if len(parts) == 1 && parts[0] == "log" {
		// 简单的日志函数
		fmt.Println("[JOB LOG]", paramsStr)
		result.Success = true
		result.Message = "日志记录成功"
		return result
	} else if len(parts) == 2 && parts[0] == "system" {
		// 系统函数
		if parts[1] == "echo" {
			fmt.Println("[SYSTEM ECHO]", paramsStr)
			result.Success = true
			result.Message = "输出成功"
			result.Data = paramsStr
			return result
		} else if parts[1] == "currentTime" {
			// 获取当前时间
			result.Success = true
			result.Message = "获取系统时间成功"
			result.Data = fmt.Sprintf("Current time: %s", GetCurrentTime())
			return result
		}
	} else if len(parts) == 2 && parts[0] == "math" {
		// 简单的数学计算
		if parts[1] == "add" {
			// 解析参数（简化版）
			params := strings.Split(paramsStr, ",")
			if len(params) != 2 {
				result.Message = "add函数需要两个参数"
				result.Error = errors.New("参数数量不正确")
				return result
			}

			// 简单计算
			result.Success = true
			result.Message = "计算成功"
			return result
		}
	}

	// 其他函数调用尝试使用反射（实际项目中需根据具体需求实现）
	result.Message = "不支持的方法调用: " + methodName
	result.Error = errors.New("不支持的方法调用")

	return result
}

// 任务处理器映射
var taskHandlers = make(map[string]interface{})

// RegisterTaskHandler 注册任务处理函数
func RegisterTaskHandler(name string, handler interface{}) {
	if reflect.TypeOf(handler).Kind() != reflect.Func {
		panic(fmt.Sprintf("Handler for %s must be a function", name))
	}
	taskHandlers[name] = handler
}

// GetTaskHandler 获取任务处理函数
func GetTaskHandler(name string) interface{} {
	return taskHandlers[name]
}
