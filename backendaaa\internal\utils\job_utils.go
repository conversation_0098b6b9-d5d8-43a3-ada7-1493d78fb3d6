package utils

import (
	"backend/internal/model"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

// 任务相关常量
const (
	LOOKUP_RMI    = "rmi:"
	LOOKUP_LDAP   = "ldap:"
	LOOKUP_LDAPS  = "ldaps:"
	HTTP          = "http:"
	HTTPS         = "https:"
	JOB_ERROR_STR = "java.net.URL|javax.naming.InitialContext|org.yaml.snakeyaml|org.springframework.expression.spel"
)

// JOB_WHITELIST_STR 任务调用目标白名单
var JOB_WHITELIST_STR = []string{
	"TASK_CLASS_NAME",
	"TASK_METHOD_NAME",
	"TASK_PARAMS",
}

// 检验是否为合法的cron表达式
func IsCronExpression(cronExpression string) (bool, error) {
	if strings.TrimSpace(cronExpression) == "" {
		return false, errors.New("cron表达式不能为空")
	}

	_, err := cron.ParseStandard(cronExpression)
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetCronNextExecTime 获取下次执行时间
func GetCronNextExecTime(cronExpression string) (string, error) {
	if strings.TrimSpace(cronExpression) == "" {
		return "", errors.New("cron表达式不能为空")
	}

	schedule, err := cron.ParseStandard(cronExpression)
	if err != nil {
		return "", err
	}

	nextTime := schedule.Next(time.Now())
	return nextTime.Format("2006-01-02 15:04:05"), nil
}

// ValidCronExpression 验证cron表达式，不返回错误
func ValidCronExpression(cronExpr string) bool {
	_, err := cron.ParseStandard(cronExpr)
	return err == nil
}

// ValidCronExpressionWithRegex 使用正则表达式验证cron表达式
func ValidCronExpressionWithRegex(cronExpr string) bool {
	// 简单的cron表达式验证，实际上cron表达式的规则可能更加复杂
	// 这里只是一个基本的实现，可能需要根据实际情况进行扩展
	expr := `^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$`
	r, _ := regexp.Compile(expr)
	return r.MatchString(cronExpr)
}

// GetNextRunTime 获取下次执行时间
func GetNextRunTime(cronExpr string) (time.Time, error) {
	schedule, err := cron.ParseStandard(cronExpr)
	if err != nil {
		return time.Time{}, err
	}
	return schedule.Next(time.Now()), nil
}

// GetInvokeTarget 获取调用目标方法
func GetInvokeTarget(sysJob *model.SysJob) string {
	return sysJob.InvokeTarget
}

// CheckJobInvokeTarget 校验任务调用目标是否合法
func CheckJobInvokeTarget(invokeTarget string) error {
	if invokeTarget == "" {
		return errors.New("目标字符串不能为空")
	}

	// 检查禁止的协议前缀
	if strings.Contains(strings.ToLower(invokeTarget), LOOKUP_RMI) {
		return errors.New("目标字符串不允许'rmi'调用")
	}
	if strings.Contains(strings.ToLower(invokeTarget), LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(invokeTarget), LOOKUP_LDAPS) {
		return errors.New("目标字符串不允许'ldap(s)'调用")
	}
	if strings.Contains(strings.ToLower(invokeTarget), HTTP) ||
		strings.Contains(strings.ToLower(invokeTarget), HTTPS) {
		return errors.New("目标字符串不允许'http(s)'调用")
	}

	// 检查错误关键字
	errorStrs := strings.Split(JOB_ERROR_STR, "|")
	for _, errorStr := range errorStrs {
		if strings.Contains(strings.ToLower(invokeTarget), strings.ToLower(errorStr)) {
			return fmt.Errorf("目标字符串存在违规: %s", errorStr)
		}
	}

	// 检查白名单
	isWhiteList := false
	for _, whitePrefix := range JOB_WHITELIST_STR {
		if strings.HasPrefix(strings.ToLower(invokeTarget), strings.ToLower(whitePrefix)) {
			isWhiteList = true
			break
		}
	}

	if !isWhiteList {
		return errors.New("目标字符串不在白名单内")
	}

	return nil
}

// JobInvokeResult 作业调用结果
type JobInvokeResult struct {
	Success bool
	Message string
	Error   error
}

// 定义一个锁集合，用于并发控制
var (
	jobLocks = sync.Map{}
)

// TryLock 尝试获取锁
func TryLock(key string) bool {
	// 使用sync.Map的LoadOrStore方法尝试获取锁
	// 如果key不存在，则存储true并返回false（表示获取成功）
	// 如果key存在，则返回true（表示获取失败）
	_, loaded := jobLocks.LoadOrStore(key, true)
	return !loaded
}

// ReleaseLock 释放锁
func ReleaseLock(key string) {
	jobLocks.Delete(key)
}

// InitJob 初始化任务
func InitJob(job *model.SysJob) error {
	// 验证Cron表达式
	if !ValidCronExpression(job.CronExpression) {
		return ErrorInvalidCronExpression
	}
	return nil
}

// IsMatch 判断数组中是否包含指定元素
func IsMatch(ids []int64, id int64) bool {
	for _, i := range ids {
		if i == id {
			return true
		}
	}
	return false
}

// ConcurrencyError 并发执行错误
var ConcurrencyError = fmt.Errorf("任务正在执行，请稍候再试")

// TaskNotExistError 任务不存在错误
var TaskNotExistError = fmt.Errorf("任务不存在")

// ErrorInvalidCronExpression Cron表达式错误
var ErrorInvalidCronExpression = fmt.Errorf("Cron表达式错误")
