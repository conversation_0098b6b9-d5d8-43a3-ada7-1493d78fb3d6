package utils

import (
	"backend/internal/model"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWT密钥
var jwtSecret = []byte("your-secret-key")

// Claims 自定义JWT声明
type Claims struct {
	UserId   int64  `json:"userId"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// GenerateToken 生成JWT
func GenerateToken(userId int64, username string) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(24 * time.Hour)

	claims := Claims{
		UserId:   userId,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(nowTime),
			Issuer:    "ruoyi-go",
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(jwtSecret)
	return token, err
}

// ParseToken 解析JWT
func ParseToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, errors.New("invalid token")
}

// SetJWTSecret 设置JWT密钥
func SetJWTSecret(secret string) {
	jwtSecret = []byte(secret)
}

// GetUsernameFromUser 从LoginUser中获取用户名
func GetUsernameFromUser(loginUser *model.LoginUser) string {
	if loginUser != nil && loginUser.User != nil {
		return loginUser.User.UserName
	}
	return ""
}

// Pointer 创建指针类型
func Pointer[T any](v T) *T {
	return &v
}

// GetLocationByIP 根据IP获取地理位置
func GetLocationByIP(ip string) string {
	// 这里可以接入第三方IP地址库，暂时返回默认值
	if ip == "127.0.0.1" || ip == "localhost" {
		return "内网IP"
	}
	return "未知位置"
}
