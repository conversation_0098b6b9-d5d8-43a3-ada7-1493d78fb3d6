package utils

import (
	"image/color"
	"strings"

	"github.com/mojocn/base64Captcha"
)

// CustomMathDriver 自定义数学验证码驱动
type CustomMathDriver struct {
	base64Captcha.DriverMath
	TextCreator *MathTextCreator
}

// NewCustomMathDriver 创建自定义数学验证码驱动
func NewCustomMathDriver() *CustomMathDriver {
	mathDriver := &CustomMathDriver{
		DriverMath: base64Captcha.DriverMath{
			Height:          60,  // 高度
			Width:           160, // 宽度
			NoiseCount:      0,
			ShowLineOptions: 2,
			BgColor: &color.RGBA{
				R: 255,
				G: 255,
				B: 255,
				A: 255,
			},
			// 使用更通用的字体，不指定具体字体，让系统使用默认字体
			Fonts: []string{},
		},
		TextCreator: NewMathTextCreator(),
	}
	return mathDriver
}

// GenerateIdQuestionAnswer 生成验证码ID、问题和答案
func (d *CustomMathDriver) GenerateIdQuestionAnswer() (id, q, a string) {
	id = base64Captcha.RandomId()
	captchaText := d.TextCreator.CreateText()

	// 分割表达式和答案
	parts := strings.Split(captchaText, "@")
	if len(parts) == 2 {
		q = parts[0]
		a = parts[1]
	}

	return id, q, a
}

// DrawCaptcha 绘制验证码
func (d *CustomMathDriver) DrawCaptcha(content string) (item base64Captcha.Item, err error) {
	// 直接使用DriverMath的DrawCaptcha方法
	return d.DriverMath.DrawCaptcha(content)
}
