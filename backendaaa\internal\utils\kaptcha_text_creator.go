package utils

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// MathTextCreator 数学验证码文本生成器
type MathTextCreator struct {
	Numbers []string
}

// NewMathTextCreator 创建数学验证码文本生成器
func NewMathTextCreator() *MathTextCreator {
	return &MathTextCreator{
		Numbers: strings.Split("0,1,2,3,4,5,6,7,8,9,10", ","),
	}
}

// CreateText 创建验证码文本
func (m *MathTextCreator) CreateText() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	x := r.Intn(10)
	y := r.Intn(10)
	result := 0
	var expression strings.Builder

	randomOperands := r.Intn(3)
	if randomOperands == 0 {
		// 乘法
		result = x * y
		expression.WriteString(m.Numbers[x])
		expression.WriteString("*")
		expression.WriteString(m.Numbers[y])
	} else if randomOperands == 1 {
		// 除法或加法
		if x != 0 && y%x == 0 {
			result = y / x
			expression.WriteString(m.Numbers[y])
			expression.WriteString("/")
			expression.WriteString(m.Numbers[x])
		} else {
			// 加法
			result = x + y
			expression.WriteString(m.Numbers[x])
			expression.WriteString("+")
			expression.WriteString(m.Numbers[y])
		}
	} else {
		// 减法
		if x >= y {
			result = x - y
			expression.WriteString(m.Numbers[x])
			expression.WriteString("-")
			expression.WriteString(m.Numbers[y])
		} else {
			result = y - x
			expression.WriteString(m.Numbers[y])
			expression.WriteString("-")
			expression.WriteString(m.Numbers[x])
		}
	}

	// 格式为 "表达式=?@结果"
	return fmt.Sprintf("%s=?@%d", expression.String(), result)
}
