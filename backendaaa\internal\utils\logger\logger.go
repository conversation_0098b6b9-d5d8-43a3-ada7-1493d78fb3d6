package logger

import (
	"backend/internal/config"
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 全局日志实例
var Logger *zap.Logger

// SugarLogger 简化版日志实例
var SugarLogger *zap.SugaredLogger

// Setup 初始化日志
func Setup() {
	cfg := config.Global.Log

	// 创建日志目录
	if err := os.MkdirAll(cfg.Path, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
		os.Exit(1)
	}

	// 设置日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "warn":
		level = zap.WarnLevel
	case "error":
		level = zap.ErrorLevel
	default:
		level = zap.InfoLevel
	}

	// 日志文件配置
	writer := &lumberjack.Logger{
		Filename:   filepath.Join(cfg.Path, cfg.Filename+".log"),
		MaxSize:    cfg.MaxSize,    // 单个文件最大尺寸，MB
		MaxBackups: cfg.MaxBackups, // 最多保留文件数
		MaxAge:     cfg.MaxAge,     // 最多保留天数
		Compress:   cfg.Compress,   // 是否压缩
	}

	// 定义日志编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 配置日志核心
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig), // JSON格式
		zapcore.NewMultiWriteSyncer( // 输出到文件和控制台
			zapcore.AddSync(writer),
			zapcore.AddSync(os.Stdout),
		),
		level,
	)

	// 创建日志实例
	Logger = zap.New(
		core,
		zap.AddCaller(),      // 添加调用者信息
		zap.AddCallerSkip(1), // 跳过封装函数
	)

	// 创建SugarLogger
	SugarLogger = Logger.Sugar()

	// 替换全局Logger
	zap.ReplaceGlobals(Logger)
}

// Debug 记录调试日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Info 记录信息日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Warn 记录警告日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 记录错误日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 记录致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// DebugF 使用格式化字符串记录调试日志
func DebugF(format string, args ...interface{}) {
	SugarLogger.Debugf(format, args...)
}

// InfoF 使用格式化字符串记录信息日志
func InfoF(format string, args ...interface{}) {
	SugarLogger.Infof(format, args...)
}

// WarnF 使用格式化字符串记录警告日志
func WarnF(format string, args ...interface{}) {
	SugarLogger.Warnf(format, args...)
}

// ErrorF 使用格式化字符串记录错误日志
func ErrorF(format string, args ...interface{}) {
	SugarLogger.Errorf(format, args...)
}

// FatalF 使用格式化字符串记录致命错误日志
func FatalF(format string, args ...interface{}) {
	SugarLogger.Fatalf(format, args...)
}

// Sync 同步日志
func Sync() {
	_ = Logger.Sync()
	_ = SugarLogger.Sync()
}
