package utils

import (
	"fmt"
	"log"
	"os"
	"runtime"
	"runtime/debug"
	"runtime/pprof"
	"sync"
	"time"
)

// PerformanceMetrics 性能指标结构
type PerformanceMetrics struct {
	mu                sync.RWMutex
	operationMetrics  map[string]*OperationMetric
	goroutineCount    int
	memoryAllocated   uint64
	memoryHeapObjects uint64
	gcPauseTime       time.Duration
	gcPauseCount      uint32
}

// OperationMetric 操作指标结构
type OperationMetric struct {
	TotalCount   int64
	TotalTime    time.Duration
	MaxTime      time.Duration
	MinTime      time.Duration
	ErrorCount   int64
	LastExecuted time.Time
}

// NewPerformanceMetrics 创建性能指标
func NewPerformanceMetrics() *PerformanceMetrics {
	return &PerformanceMetrics{
		operationMetrics: make(map[string]*OperationMetric),
	}
}

// globalMetrics 全局性能指标
var globalMetrics = NewPerformanceMetrics()

// GetGlobalMetrics 获取全局性能指标
func GetGlobalMetrics() *PerformanceMetrics {
	return globalMetrics
}

// StartMonitoring 开始性能监控
func (pm *PerformanceMetrics) StartMonitoring(interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for range ticker.C {
			pm.collectMetrics()
		}
	}()
}

// collectMetrics 收集性能指标
func (pm *PerformanceMetrics) collectMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 收集goroutine数量
	pm.goroutineCount = runtime.NumGoroutine()

	// 收集内存指标
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	pm.memoryAllocated = memStats.Alloc
	pm.memoryHeapObjects = memStats.HeapObjects
	pm.gcPauseTime = time.Duration(memStats.PauseTotalNs)
	pm.gcPauseCount = memStats.NumGC
}

// TrackOperation 跟踪操作性能
func (pm *PerformanceMetrics) TrackOperation(name string, fn func() error) error {
	startTime := time.Now()
	err := fn()
	duration := time.Since(startTime)

	pm.mu.Lock()
	defer pm.mu.Unlock()

	metric, exists := pm.operationMetrics[name]
	if !exists {
		metric = &OperationMetric{
			MinTime: duration,
			MaxTime: duration,
		}
		pm.operationMetrics[name] = metric
	}

	metric.TotalCount++
	metric.TotalTime += duration
	metric.LastExecuted = time.Now()

	if duration > metric.MaxTime {
		metric.MaxTime = duration
	}
	if duration < metric.MinTime {
		metric.MinTime = duration
	}

	if err != nil {
		metric.ErrorCount++
	}

	return err
}

// GetOperationMetrics 获取所有操作指标
func (pm *PerformanceMetrics) GetOperationMetrics() map[string]OperationMetric {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]OperationMetric, len(pm.operationMetrics))
	for k, v := range pm.operationMetrics {
		result[k] = *v
	}
	return result
}

// GetMemoryUsage 获取内存使用情况
func (pm *PerformanceMetrics) GetMemoryUsage() (allocated uint64, heapObjects uint64) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return pm.memoryAllocated, pm.memoryHeapObjects
}

// GetGoroutineCount 获取goroutine数量
func (pm *PerformanceMetrics) GetGoroutineCount() int {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return pm.goroutineCount
}

// GetGCStats 获取GC统计信息
func (pm *PerformanceMetrics) GetGCStats() (pauseTime time.Duration, pauseCount uint32) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return pm.gcPauseTime, pm.gcPauseCount
}

// ResetOperationMetrics 重置操作指标
func (pm *PerformanceMetrics) ResetOperationMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.operationMetrics = make(map[string]*OperationMetric)
}

// PrintMetrics 打印性能指标
func (pm *PerformanceMetrics) PrintMetrics() {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	log.Println("=== Performance Metrics ===")
	log.Printf("Goroutines: %d\n", pm.goroutineCount)
	log.Printf("Memory Allocated: %.2f MB\n", float64(pm.memoryAllocated)/1024/1024)
	log.Printf("Heap Objects: %d\n", pm.memoryHeapObjects)
	log.Printf("GC Pause Time: %v\n", pm.gcPauseTime)
	log.Printf("GC Pause Count: %d\n", pm.gcPauseCount)

	log.Println("\n=== Operation Metrics ===")
	for name, metric := range pm.operationMetrics {
		avgTime := time.Duration(0)
		if metric.TotalCount > 0 {
			avgTime = metric.TotalTime / time.Duration(metric.TotalCount)
		}

		errorRate := float64(0)
		if metric.TotalCount > 0 {
			errorRate = float64(metric.ErrorCount) / float64(metric.TotalCount) * 100
		}

		log.Printf("Operation: %s\n", name)
		log.Printf("  Total Count: %d\n", metric.TotalCount)
		log.Printf("  Avg Time: %v\n", avgTime)
		log.Printf("  Min Time: %v\n", metric.MinTime)
		log.Printf("  Max Time: %v\n", metric.MaxTime)
		log.Printf("  Error Rate: %.2f%%\n", errorRate)
		log.Printf("  Last Executed: %v\n", metric.LastExecuted)
	}
}

// CreateFile 创建文件
func CreateFile(filename string) (*os.File, error) {
	return os.Create(filename)
}

// StartCPUProfiling 开始CPU性能分析
func StartCPUProfiling(filename string) error {
	f, err := CreateFile(filename)
	if err != nil {
		return fmt.Errorf("could not create CPU profile: %v", err)
	}
	if err := pprof.StartCPUProfile(f); err != nil {
		f.Close()
		return fmt.Errorf("could not start CPU profile: %v", err)
	}
	return nil
}

// StopCPUProfiling 停止CPU性能分析
func StopCPUProfiling() {
	pprof.StopCPUProfile()
}

// WriteHeapProfile 写入堆分析
func WriteHeapProfile(filename string) error {
	f, err := CreateFile(filename)
	if err != nil {
		return fmt.Errorf("could not create heap profile: %v", err)
	}
	defer f.Close()

	runtime.GC() // 手动触发GC获取更准确的内存信息
	if err := pprof.WriteHeapProfile(f); err != nil {
		return fmt.Errorf("could not write heap profile: %v", err)
	}
	return nil
}

// DebugGC 开启/关闭垃圾回收器调试
func DebugGC(enabled bool) {
	if enabled {
		debug.SetGCPercent(50) // 更积极的GC
	} else {
		debug.SetGCPercent(100) // 恢复默认值
	}
}

// OptimizeMemory 优化内存使用，尝试释放未使用的内存
func OptimizeMemory() {
	runtime.GC()
	debug.FreeOSMemory()
}

// TimeOperation 计时器，用于测量操作耗时
func TimeOperation(name string) func() {
	start := time.Now()
	return func() {
		elapsed := time.Since(start)
		log.Printf("Operation %s took %v", name, elapsed)

		// 记录到全局指标中
		globalMetrics.mu.Lock()
		defer globalMetrics.mu.Unlock()

		metric, exists := globalMetrics.operationMetrics[name]
		if !exists {
			metric = &OperationMetric{
				MinTime: elapsed,
				MaxTime: elapsed,
			}
			globalMetrics.operationMetrics[name] = metric
		}

		metric.TotalCount++
		metric.TotalTime += elapsed
		metric.LastExecuted = time.Now()

		if elapsed > metric.MaxTime {
			metric.MaxTime = elapsed
		}
		if elapsed < metric.MinTime {
			metric.MinTime = elapsed
		}
	}
}
