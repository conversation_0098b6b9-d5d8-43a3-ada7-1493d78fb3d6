package redis

import (
	"backend/internal/config"
	"backend/internal/utils/logger"
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	client *redis.Client
	ctx    = context.Background()
)

// Setup 初始化Redis连接
func Setup() error {
	cfg := config.Global.Redis

	client = redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.Database,
		PoolSize:     cfg.Pool.MaxActive,
		MinIdleConns: cfg.Pool.MinIdle,
		MaxIdleConns: cfg.Pool.MaxIdle,
		PoolTimeout:  time.Duration(cfg.Timeout) * time.Second,
	})

	// 测试连接
	_, err := client.Ping(ctx).Result()
	if err != nil {
		logger.ErrorF("Redis连接失败: %v", err)
		return err
	}

	logger.Info("Redis连接成功")
	return nil
}

// Close 关闭Redis连接
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}

// Get 获取缓存
func Get(key string) (string, error) {
	return client.Get(ctx, key).Result()
}

// Set 设置缓存
func Set(key string, value interface{}, expiration time.Duration) error {
	return client.Set(ctx, key, value, expiration).Err()
}

// Delete 删除缓存
func Delete(key string) error {
	return client.Del(ctx, key).Err()
}

// HashGet 获取哈希表项
func HashGet(key, field string) (string, error) {
	return client.HGet(ctx, key, field).Result()
}

// HashSet 设置哈希表项
func HashSet(key, field string, value interface{}) error {
	return client.HSet(ctx, key, field, value).Err()
}

// HashDelete 删除哈希表项
func HashDelete(key, field string) error {
	return client.HDel(ctx, key, field).Err()
}

// Keys 获取所有键
func Keys(pattern string) ([]string, error) {
	return client.Keys(ctx, pattern).Result()
}

// Exists 检查键是否存在
func Exists(key string) (bool, error) {
	result, err := client.Exists(ctx, key).Result()
	return result > 0, err
}

// Expire 设置过期时间
func Expire(key string, expiration time.Duration) error {
	return client.Expire(ctx, key, expiration).Err()
}

// Incr 自增
func Incr(key string) (int64, error) {
	return client.Incr(ctx, key).Result()
}

// Decr 自减
func Decr(key string) (int64, error) {
	return client.Decr(ctx, key).Result()
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return client
}

// GetContext 获取上下文
func GetContext() context.Context {
	return ctx
}
