package main

import (
	_ "backend/docs" // 导入swagger文档
	"backend/internal/api/middleware"
	"backend/internal/api/router"
	"backend/internal/bootstrap"
	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/job"
	"backend/internal/repository"
	"backend/internal/service/impl"
	"backend/internal/utils"
	"backend/internal/utils/logger"

	// "backend/internal/utils/redis"
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title           WOSM API
// @version         1.0
// @description     This is WOSM server.
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api

// @securityDefinitions.basic  BasicAuth
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

func main() {
	var configFile string
	flag.StringVar(&configFile, "config", "config/config.yaml", "配置文件路径")
	flag.Parse()

	// 加载配置
	_, err := config.LoadConfig(configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置运行模式
	if config.Global.Server.RunMode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化系统
	if err := bootstrap.Setup(); err != nil {
		logger.FatalF("系统初始化失败: %v", err)
	}

	// 设置运行模式
	mode := config.Global.Server.RunMode
	if mode == "debug" {
		gin.SetMode(gin.DebugMode)
		logger.Info("运行模式：调试")
	} else {
		gin.SetMode(gin.ReleaseMode)
		logger.Info("运行模式：生产")
	}

	// 初始化Redis连接 - 暂时禁用
	/*
		if err := redis.Setup(); err != nil {
			logger.WarnF("Redis初始化失败，将使用内存缓存: %v", err)
		} else {
			defer func() {
				if err := redis.Close(); err != nil {
					logger.ErrorF("关闭Redis连接失败: %v", err)
				}
			}()
		}
	*/
	logger.WarnF("Redis已禁用，使用内存缓存")

	// 获取数据库连接
	db := database.GetDB()

	// 初始化作业相关仓储
	sysJobRepo := repository.NewSysJobRepository(db)
	sysJobLogRepo := repository.NewSysJobLogRepository(db)

	// 初始化作业相关服务
	sysJobService := impl.NewSysJobService(sysJobRepo, sysJobLogRepo)
	sysJobLogService := impl.NewSysJobLogService(sysJobLogRepo)

	// 初始化任务调度器
	job.Setup(sysJobService, sysJobLogService)
	defer job.Shutdown()

	// 创建Gin引擎
	app := gin.New()

	// 注册全局中间件
	app.Use(gin.Recovery())
	app.Use(middleware.WithErrorLogging())
	app.Use(middleware.ErrorHandler())

	// 设置404和405处理
	app.NoRoute(middleware.NotFound())
	app.NoMethod(middleware.MethodNotAllowed())

	// 跨域配置
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Authorization"}
	app.Use(cors.New(corsConfig))

	// 注册Swagger
	if config.Global.Swagger.Enabled {
		app.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		logger.Info("Swagger已启用，访问地址: /swagger/index.html")
	}

	// 注册API路由
	router.InitRouter(app)

	// 启动性能监控
	metrics := utils.GetGlobalMetrics()
	metrics.StartMonitoring(time.Minute)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf("localhost:%d", config.Global.Server.Port),
		Handler: app,
	}

	// 启动HTTP服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.FatalF("Failed to start server: %v", err)
		}
	}()

	log.Printf("Server is running on localhost:%d", config.Global.Server.Port)

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 创建一个5秒超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.FatalF("Server forced to shutdown: %v", err)
	}

	log.Println("Server exiting")

	// 输出最终性能指标
	metrics.PrintMetrics()
}
