// 当构建正式版本时不编译此文件
//go:build ignore
// +build ignore

package main

import (
	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/job"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service/impl"
	"backend/internal/utils/logger"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// 简化版程序入口，专注于定时任务功能
func main() {
	// 初始化配置
	config.Init("")

	// 初始化日志
	logger.Setup()
	defer logger.Sync()

	logger.InfoF("服务启动，系统版本: %s", config.Global.System.Version)

	// 初始化数据库连接
	if err := database.Setup(); err != nil {
		logger.FatalF("数据库初始化失败: %v", err)
	}
	defer func() {
		if err := database.Close(); err != nil {
			logger.ErrorF("关闭数据库连接失败: %v", err)
		}
	}()

	// 获取数据库连接
	db := database.GetDB()

	// 初始化作业相关仓储
	sysJobRepo := repository.NewSysJobRepository(db)
	sysJobLogRepo := repository.NewSysJobLogRepository(db)

	// 初始化作业相关服务
	sysJobService := impl.NewSysJobService(sysJobRepo)
	sysJobLogService := impl.NewSysJobLogService(sysJobLogRepo)

	// 初始化任务调度器
	job.Setup(sysJobService, sysJobLogService)
	defer job.Shutdown()

	// 测试添加一个定时任务
	testJob := &model.SysJob{
		JobName:        "测试任务",
		JobGroup:       "DEFAULT",
		InvokeTarget:   "log.Info(\"这是一个测试任务\")",
		CronExpression: "0/5 * * * * ?", // 每5秒执行一次
		Status:         "0",             // 正常状态
	}

	// 创建测试任务
	jobId, err := sysJobService.InsertJob(testJob)
	if err != nil {
		logger.ErrorF("创建测试任务失败: %v", err)
	} else {
		logger.InfoF("创建测试任务成功，ID: %d", jobId)
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	logger.Info("服务已启动，按 Ctrl+C 停止...")

	// 运行10分钟后自动退出
	go func() {
		time.Sleep(10 * time.Minute)
		logger.Info("服务运行时间到，准备关闭...")
		quit <- syscall.SIGTERM
	}()

	<-quit
	logger.Info("服务器关闭中...")
}
