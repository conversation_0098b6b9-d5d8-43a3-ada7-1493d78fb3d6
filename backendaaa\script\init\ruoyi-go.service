[Unit]
Description=RuoYi-Go Backend Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=ruoyi
Group=ruoyi
WorkingDirectory=/opt/ruoyi-go
ExecStart=/usr/bin/ruoyi-go
Restart=on-failure
RestartSec=5s
LimitNOFILE=65536

# 环境变量设置
Environment=GIN_MODE=release

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ProtectHome=true

[Install]
WantedBy=multi-user.target 