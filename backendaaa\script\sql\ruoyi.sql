-- 创建主要表结构

-- 用户信息表
CREATE TABLE sys_user (
    user_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    dept_id BIGINT NULL,
    username NVARCHAR(30) NOT NULL,
    nickname NVARCHAR(30) NOT NULL,
    user_type NVARCHAR(2) DEFAULT '00',
    email NVARCHAR(50) DEFAULT '',
    phone_number NVARCHAR(11) DEFAULT '',
    gender NVARCHAR(1) DEFAULT '0',
    avatar NVARCHAR(100) DEFAULT '',
    password NVARCHAR(100) DEFAULT '',
    status NVARCHAR(1) DEFAULT '0',
    del_flag NVARCHAR(1) DEFAULT '0',
    login_ip NVARCHAR(128) DEFAULT '',
    login_date DATETIME NULL,
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHA<PERSON>(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 添加唯一约束
CREATE UNIQUE NONCLUSTERED INDEX idx_sys_user_username ON sys_user (username) WHERE username IS NOT NULL;

-- 角色信息表
CREATE TABLE sys_role (
    role_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    role_name NVARCHAR(30) NOT NULL,
    role_key NVARCHAR(100) NOT NULL,
    role_sort INT DEFAULT 0,
    data_scope NVARCHAR(1) DEFAULT '1',
    status NVARCHAR(1) DEFAULT '0',
    del_flag NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 菜单权限表
CREATE TABLE sys_menu (
    menu_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    menu_name NVARCHAR(50) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    order_num INT DEFAULT 0,
    path NVARCHAR(200) DEFAULT '',
    component NVARCHAR(255) DEFAULT NULL,
    is_frame INT DEFAULT 1,
    is_cache INT DEFAULT 0,
    menu_type NVARCHAR(1) DEFAULT '',
    visible NVARCHAR(1) DEFAULT '0',
    status NVARCHAR(1) DEFAULT '0',
    perms NVARCHAR(100) DEFAULT NULL,
    icon NVARCHAR(100) DEFAULT '#',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT ''
);

-- 部门表
CREATE TABLE sys_dept (
    dept_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    parent_id BIGINT DEFAULT 0,
    ancestors NVARCHAR(500) DEFAULT '',
    dept_name NVARCHAR(30) DEFAULT '',
    order_num INT DEFAULT 0,
    leader NVARCHAR(20) DEFAULT NULL,
    phone NVARCHAR(11) DEFAULT NULL,
    email NVARCHAR(50) DEFAULT NULL,
    status NVARCHAR(1) DEFAULT '0',
    del_flag NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL
);

-- 岗位信息表
CREATE TABLE sys_post (
    post_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    post_code NVARCHAR(64) NOT NULL,
    post_name NVARCHAR(50) NOT NULL,
    post_sort INT NOT NULL,
    status NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 用户和角色关联表
CREATE TABLE sys_user_role (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    CONSTRAINT pk_sys_user_role PRIMARY KEY (user_id, role_id)
);

-- 角色和菜单关联表
CREATE TABLE sys_role_menu (
    role_id BIGINT NOT NULL,
    menu_id BIGINT NOT NULL,
    CONSTRAINT pk_sys_role_menu PRIMARY KEY (role_id, menu_id)
);

-- 角色和部门关联表
CREATE TABLE sys_role_dept (
    role_id BIGINT NOT NULL,
    dept_id BIGINT NOT NULL,
    CONSTRAINT pk_sys_role_dept PRIMARY KEY (role_id, dept_id)
);

-- 用户与岗位关联表
CREATE TABLE sys_user_post (
    user_id BIGINT NOT NULL,
    post_id BIGINT NOT NULL,
    CONSTRAINT pk_sys_user_post PRIMARY KEY (user_id, post_id)
);

-- 字典类型表
CREATE TABLE sys_dict_type (
    dict_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    dict_name NVARCHAR(100) DEFAULT '',
    dict_type NVARCHAR(100) DEFAULT '',
    status NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 字典数据表
CREATE TABLE sys_dict_data (
    dict_code BIGINT IDENTITY(1,1) PRIMARY KEY,
    dict_sort INT DEFAULT 0,
    dict_label NVARCHAR(100) DEFAULT '',
    dict_value NVARCHAR(100) DEFAULT '',
    dict_type NVARCHAR(100) DEFAULT '',
    css_class NVARCHAR(100) DEFAULT NULL,
    list_class NVARCHAR(100) DEFAULT NULL,
    is_default NVARCHAR(1) DEFAULT 'N',
    status NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 参数配置表
CREATE TABLE sys_config (
    config_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    config_name NVARCHAR(100) DEFAULT '',
    config_key NVARCHAR(100) DEFAULT '',
    config_value NVARCHAR(500) DEFAULT '',
    config_type NVARCHAR(1) DEFAULT 'N',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT NULL
);

-- 操作日志记录
CREATE TABLE sys_oper_log (
    oper_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    title NVARCHAR(50) DEFAULT '',
    business_type INT DEFAULT 0,
    method NVARCHAR(100) DEFAULT '',
    request_method NVARCHAR(10) DEFAULT '',
    operator_type INT DEFAULT 0,
    oper_name NVARCHAR(50) DEFAULT '',
    dept_name NVARCHAR(50) DEFAULT '',
    oper_url NVARCHAR(255) DEFAULT '',
    oper_ip NVARCHAR(128) DEFAULT '',
    oper_location NVARCHAR(255) DEFAULT '',
    oper_param NVARCHAR(MAX) DEFAULT '',
    json_result NVARCHAR(MAX) DEFAULT '',
    status INT DEFAULT 0,
    error_msg NVARCHAR(MAX) DEFAULT '',
    oper_time DATETIME DEFAULT GETDATE()
);

-- 系统访问记录
CREATE TABLE sys_login_log (
    info_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_name NVARCHAR(50) DEFAULT '',
    ipaddr NVARCHAR(128) DEFAULT '',
    login_location NVARCHAR(255) DEFAULT '',
    browser NVARCHAR(50) DEFAULT '',
    os NVARCHAR(50) DEFAULT '',
    status NVARCHAR(1) DEFAULT '0',
    msg NVARCHAR(255) DEFAULT '',
    login_time DATETIME DEFAULT GETDATE()
);

-- 定时任务调度表
CREATE TABLE sys_job (
    job_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    job_name NVARCHAR(64) NOT NULL,
    job_group NVARCHAR(64) NOT NULL,
    invoke_target NVARCHAR(500) NOT NULL,
    cron_expression NVARCHAR(255) DEFAULT '',
    misfire_policy NVARCHAR(1) DEFAULT '3',
    concurrent NVARCHAR(1) DEFAULT '1',
    status NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(500) DEFAULT ''
);

-- 定时任务调度日志表
CREATE TABLE sys_job_log (
    job_log_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    job_name NVARCHAR(64) NOT NULL,
    job_group NVARCHAR(64) NOT NULL,
    invoke_target NVARCHAR(500) NOT NULL,
    job_message NVARCHAR(500) DEFAULT NULL,
    status NVARCHAR(1) DEFAULT '0',
    exception_info NVARCHAR(MAX) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE()
);

-- 通知公告表
CREATE TABLE sys_notice (
    notice_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    notice_title NVARCHAR(50) NOT NULL,
    notice_type NVARCHAR(1) NOT NULL,
    notice_content NVARCHAR(MAX) DEFAULT NULL,
    status NVARCHAR(1) DEFAULT '0',
    create_by NVARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT GETDATE(),
    update_by NVARCHAR(64) DEFAULT '',
    update_time DATETIME NULL,
    remark NVARCHAR(255) DEFAULT NULL
);

-- 插入初始数据

-- 初始化用户信息
SET IDENTITY_INSERT sys_user ON;
INSERT INTO sys_user (user_id, dept_id, username, nickname, user_type, email, phone_number, gender, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark) VALUES
(1, 103, 'admin', '管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', GETDATE(), 'admin', GETDATE(), '', NULL, '管理员'),
(2, 105, 'test', '测试用户', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', GETDATE(), 'admin', GETDATE(), '', NULL, '测试用户');
SET IDENTITY_INSERT sys_user OFF;

-- 初始化角色信息
SET IDENTITY_INSERT sys_role ON;
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
(1, '管理员', 'admin', 1, '1', '0', '0', 'admin', GETDATE(), '', NULL, '管理员'),
(2, '普通角色', 'common', 2, '2', '0', '0', 'admin', GETDATE(), '', NULL, '普通角色');
SET IDENTITY_INSERT sys_role OFF;

-- 初始化菜单信息
SET IDENTITY_INSERT sys_menu ON;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES 
(1, '系统管理', 0, 1, 'system', NULL, 1, 0, 'M', '0', '0', '', 'system', 'admin', GETDATE(), '', NULL, '系统管理目录'),
(2, '系统监控', 0, 2, 'monitor', NULL, 1, 0, 'M', '0', '0', '', 'monitor', 'admin', GETDATE(), '', NULL, '系统监控目录'),
(3, '系统工具', 0, 3, 'tool', NULL, 1, 0, 'M', '0', '0', '', 'tool', 'admin', GETDATE(), '', NULL, '系统工具目录'),
(100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', GETDATE(), '', NULL, '用户管理菜单'),
(101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', GETDATE(), '', NULL, '角色管理菜单'),
(102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', GETDATE(), '', NULL, '菜单管理菜单'),
(103, '部门管理', 1, 4, 'dept', 'system/dept/index', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', GETDATE(), '', NULL, '部门管理菜单'),
(104, '岗位管理', 1, 5, 'post', 'system/post/index', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', GETDATE(), '', NULL, '岗位管理菜单'),
(105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', GETDATE(), '', NULL, '字典管理菜单'),
(106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', GETDATE(), '', NULL, '参数设置菜单'),
(107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', GETDATE(), '', NULL, '通知公告菜单'),
(108, '日志管理', 1, 9, 'log', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', GETDATE(), '', NULL, '日志管理菜单'),
(109, '在线用户', 2, 1, 'online', 'monitor/online/index', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', GETDATE(), '', NULL, '在线用户菜单'),
(110, '定时任务', 2, 2, 'job', 'monitor/job/index', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', GETDATE(), '', NULL, '定时任务菜单');
SET IDENTITY_INSERT sys_menu OFF;

-- 初始化部门
SET IDENTITY_INSERT sys_dept ON;
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time) VALUES 
(100, 0, '0', 'RuoYi科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(101, 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL),
(109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', GETDATE(), '', NULL);
SET IDENTITY_INSERT sys_dept OFF;

-- 初始化岗位
SET IDENTITY_INSERT sys_post ON;
INSERT INTO sys_post (post_id, post_code, post_name, post_sort, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, 'ceo', '董事长', 1, '0', 'admin', GETDATE(), '', NULL, ''),
(2, 'se', '项目经理', 2, '0', 'admin', GETDATE(), '', NULL, ''),
(3, 'hr', '人力资源', 3, '0', 'admin', GETDATE(), '', NULL, ''),
(4, 'user', '普通员工', 4, '0', 'admin', GETDATE(), '', NULL, '');
SET IDENTITY_INSERT sys_post OFF;

-- 初始化用户与角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1),
(2, 2);

-- 初始化角色与菜单关联
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 1),
(1, 2),
(1, 3),
(1, 100),
(1, 101),
(1, 102),
(1, 103),
(1, 104),
(1, 105),
(1, 106),
(1, 107),
(1, 108),
(1, 109),
(1, 110),
(2, 1),
(2, 100),
(2, 101),
(2, 102);

-- 初始化用户与岗位关联
INSERT INTO sys_user_post (user_id, post_id) VALUES
(1, 1),
(2, 4);

-- 初始化字典类型
SET IDENTITY_INSERT sys_dict_type ON;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '用户性别', 'sys_user_sex', '0', 'admin', GETDATE(), '', NULL, '用户性别列表'),
(2, '菜单状态', 'sys_show_hide', '0', 'admin', GETDATE(), '', NULL, '菜单状态列表'),
(3, '系统开关', 'sys_normal_disable', '0', 'admin', GETDATE(), '', NULL, '系统开关列表'),
(4, '任务状态', 'sys_job_status', '0', 'admin', GETDATE(), '', NULL, '任务状态列表'),
(5, '任务分组', 'sys_job_group', '0', 'admin', GETDATE(), '', NULL, '任务分组列表'),
(6, '系统是否', 'sys_yes_no', '0', 'admin', GETDATE(), '', NULL, '系统是否列表');
SET IDENTITY_INSERT sys_dict_type OFF;

-- 初始化字典数据
SET IDENTITY_INSERT sys_dict_data ON;
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', GETDATE(), '', NULL, '性别男'),
(2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', GETDATE(), '', NULL, '性别女'),
(3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', GETDATE(), '', NULL, '性别未知'),
(4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', GETDATE(), '', NULL, '显示菜单'),
(5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', GETDATE(), '', NULL, '隐藏菜单'),
(6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', GETDATE(), '', NULL, '正常状态'),
(7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', GETDATE(), '', NULL, '停用状态');
SET IDENTITY_INSERT sys_dict_data OFF;

-- 初始化参数配置
SET IDENTITY_INSERT sys_config ON;
INSERT INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES
(1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', GETDATE(), '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),
(2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', GETDATE(), '', NULL, '初始化密码 123456'),
(3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', GETDATE(), '', NULL, '深色主题theme-dark，浅色主题theme-light');
SET IDENTITY_INSERT sys_config OFF;

-- 初始化通知公告
SET IDENTITY_INSERT sys_notice ON;
INSERT INTO sys_notice (notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '温馨提醒：2023-07-01 RuoYi-Go新版本发布啦', '2', '<p>新版本内容</p>', '0', 'admin', GETDATE(), '', NULL, '管理员'),
(2, '维护通知：2023-07-01 系统凌晨维护', '1', '<p>维护内容</p>', '0', 'admin', GETDATE(), '', NULL, '管理员');
 