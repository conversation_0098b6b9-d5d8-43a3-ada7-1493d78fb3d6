package test

import (
	"backend/internal/bootstrap"
	"backend/internal/utils"
	"fmt"
	"net/http"
	"testing"
	"time"
)

// TestSystemPerformance 测试系统性能
func TestSystemPerformance(t *testing.T) {
	// 初始化系统
	err := bootstrap.Setup()
	if err != nil {
		t.Fatalf("系统初始化失败: %v", err)
	}
	defer bootstrap.Shutdown()

	// 定义测试函数
	testFunc := func() error {
		// 这里可以替换为实际的API调用
		resp, err := http.Get("http://localhost:8080/api/system/health")
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
		}
		return nil
	}

	// 设置测试选项
	options := &utils.BenchmarkOptions{
		Concurrency:   10,
		TotalRequests: 100,
		Duration:      5 * time.Second,
		Timeout:       1 * time.Second,
	}

	// 执行基准测试
	result := utils.Benchmark("系统健康检查", testFunc, options)

	// 输出测试结果
	t.Log(result.String())

	// 性能断言
	if result.FailureCount > 0 {
		t.Errorf("测试失败: %d 个请求失败", result.FailureCount)
	}

	if result.AvgLatency > 200*time.Millisecond {
		t.Logf("警告: 平均延迟 (%v) 超过 200ms", result.AvgLatency)
	}

	if result.RequestsPerSec < 10 {
		t.Logf("警告: 每秒请求数 (%.2f) 低于 10", result.RequestsPerSec)
	}
}

// TestDatabasePerformance 测试数据库性能
func TestDatabasePerformance(t *testing.T) {
	// 初始化系统
	err := bootstrap.Setup()
	if err != nil {
		t.Fatalf("系统初始化失败: %v", err)
	}
	defer bootstrap.Shutdown()

	// 定义测试函数
	testFunc := func() error {
		// 这里可以替换为实际的数据库操作
		resp, err := http.Get("http://localhost:8080/api/system/config/list")
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
		}
		return nil
	}

	// 设置测试选项
	options := &utils.BenchmarkOptions{
		Concurrency:   5,
		TotalRequests: 50,
		Duration:      5 * time.Second,
		Timeout:       2 * time.Second,
	}

	// 执行基准测试
	result := utils.Benchmark("数据库查询性能", testFunc, options)

	// 输出测试结果
	t.Log(result.String())

	// 性能断言
	if result.FailureCount > 0 {
		t.Errorf("测试失败: %d 个请求失败", result.FailureCount)
	}

	if result.AvgLatency > 500*time.Millisecond {
		t.Logf("警告: 平均延迟 (%v) 超过 500ms", result.AvgLatency)
	}
}

// TestConcurrencyPerformance 测试并发性能
func TestConcurrencyPerformance(t *testing.T) {
	// 初始化系统
	err := bootstrap.Setup()
	if err != nil {
		t.Fatalf("系统初始化失败: %v", err)
	}
	defer bootstrap.Shutdown()

	// 定义基准测试函数
	baselineFunc := func() error {
		time.Sleep(10 * time.Millisecond) // 模拟操作
		return nil
	}

	// 定义优化后的测试函数
	optimizedFunc := func() error {
		time.Sleep(5 * time.Millisecond) // 模拟优化后的操作
		return nil
	}

	// 设置测试选项
	options := &utils.BenchmarkOptions{
		Concurrency:   20,
		TotalRequests: 200,
		Duration:      3 * time.Second,
	}

	// 执行基准测试
	baselineResult := utils.Benchmark("基准测试", baselineFunc, options)
	optimizedResult := utils.Benchmark("优化测试", optimizedFunc, options)

	// 输出对比结果
	t.Log(utils.CompareBenchmarks(baselineResult, optimizedResult))

	// 性能改进断言
	if optimizedResult.RequestsPerSec <= baselineResult.RequestsPerSec {
		t.Errorf("性能未改进: %.2f RPS -> %.2f RPS",
			baselineResult.RequestsPerSec, optimizedResult.RequestsPerSec)
	}

	if optimizedResult.AvgLatency >= baselineResult.AvgLatency {
		t.Errorf("延迟未改进: %v -> %v",
			baselineResult.AvgLatency, optimizedResult.AvgLatency)
	}
}
