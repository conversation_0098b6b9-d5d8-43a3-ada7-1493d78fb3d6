package test

import (
	"backend/internal/config"
	"os"
	"path/filepath"
	"testing"
)

// TestLoadConfig 测试配置加载
func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	tmpDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// 配置文件内容
	configContent := `
system:
  name: TestApp
  version: 1.0.0
  copyrightYear: 2025
  profile: /test/upload
  addressEnabled: false
  captchaType: math

server:
  port: 8888
  contextPath: /
  runMode: debug

database:
  type: mysql
  driverName: mysql
  host: localhost
  port: 3306
  database: test_db
  username: test_user
  password: test_password
  params: charset=utf8mb4&parseTime=True&loc=Local
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 30
`

	// 写入配置文件
	configPath := filepath.Join(tmpDir, "config.yaml")
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 验证配置内容
	if cfg.System.Name != "TestApp" {
		t.Errorf("Expected system name 'TestApp', got '%s'", cfg.System.Name)
	}

	if cfg.Server.Port != 8888 {
		t.Errorf("Expected server port 8888, got %d", cfg.Server.Port)
	}

	if cfg.Database.Type != "mysql" {
		t.Errorf("Expected database type 'mysql', got '%s'", cfg.Database.Type)
	}

	// 测试 DSN 生成
	expectedDSN := "test_user:test_password@tcp(localhost:3306)/test_db?charset=utf8mb4&parseTime=True&loc=Local"
	if dsn := cfg.Database.GetDSN(); dsn != expectedDSN {
		t.Errorf("Expected DSN '%s', got '%s'", expectedDSN, dsn)
	}
}

// TestInit 测试配置初始化
func TestInit(t *testing.T) {
	// 创建临时配置文件
	tmpDir, err := os.MkdirTemp("", "config-init-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// 配置文件内容
	configContent := `
system:
  name: InitTest
  version: 1.0.0
  copyrightYear: 2025
  profile: /test/upload
  addressEnabled: false
  captchaType: math

server:
  port: 8889
  contextPath: /
  runMode: debug

database:
  type: mysql
  driverName: mysql
  host: localhost
  port: 3306
  database: init_test_db
  username: test_user
  password: test_password
  params: charset=utf8mb4&parseTime=True&loc=Local
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 30
`

	// 写入配置文件
	configPath := filepath.Join(tmpDir, "config.yaml")
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 初始化配置
	config.Init(configPath)

	// 验证全局配置
	if config.Global == nil {
		t.Fatal("Global config should not be nil")
	}

	if config.Global.System.Name != "InitTest" {
		t.Errorf("Expected system name 'InitTest', got '%s'", config.Global.System.Name)
	}

	if config.Global.Server.Port != 8889 {
		t.Errorf("Expected server port 8889, got %d", config.Global.Server.Port)
	}
}
