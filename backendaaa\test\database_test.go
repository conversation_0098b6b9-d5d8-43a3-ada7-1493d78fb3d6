package test

import (
	"backend/internal/config"
	"backend/internal/database"
	"errors"
	"os"
	"path/filepath"
	"testing"

	"gorm.io/gorm"
)

// TestDatabaseSetup 测试数据库连接初始化
func TestDatabaseSetup(t *testing.T) {
	// 跳过实际连接测试，因为CI环境可能没有数据库
	if os.Getenv("CI") == "true" {
		t.<PERSON><PERSON>("Skipping database connection test in CI environment")
	}

	// 创建临时配置文件
	tmpDir, err := os.MkdirTemp("", "db-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// 配置文件内容，使用SQLite以便测试
	configContent := `
system:
  name: DBTest
  version: 1.0.0

server:
  port: 8080
  contextPath: /
  runMode: debug

database:
  type: sqlite
  driverName: sqlite3
  database: :memory:
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 30
`

	// 写入配置文件
	configPath := filepath.Join(tmpDir, "config.yaml")
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 初始化配置
	config.Init(configPath)

	// 测试数据库连接
	err = database.Setup()
	if err != nil {
		// 如果是由于缺少驱动导致的错误，跳过测试
		if err.Error() == "unsupported database type: sqlite" {
			t.Skip("SQLite driver not available, skipping test")
		} else {
			t.Fatalf("Failed to connect to database: %v", err)
		}
	}

	// 确保数据库连接不为空
	db := database.GetDB()
	if db == nil {
		t.Error("Database connection is nil")
	}

	// 测试关闭连接
	err = database.Close()
	if err != nil {
		t.Errorf("Failed to close database connection: %v", err)
	}
}

// TestTransaction 测试事务功能
func TestTransaction(t *testing.T) {
	// 跳过实际连接测试，因为CI环境可能没有数据库
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping database transaction test in CI environment")
	}

	// 创建临时配置文件
	tmpDir, err := os.MkdirTemp("", "db-transaction-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// 配置文件内容，使用SQLite以便测试
	configContent := `
system:
  name: TransactionTest
  version: 1.0.0

server:
  port: 8080
  contextPath: /
  runMode: debug

database:
  type: sqlite
  driverName: sqlite3
  database: :memory:
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 30
`

	// 写入配置文件
	configPath := filepath.Join(tmpDir, "config.yaml")
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 初始化配置
	config.Init(configPath)

	// 测试数据库连接
	err = database.Setup()
	if err != nil {
		// 如果是由于缺少驱动导致的错误，跳过测试
		if err.Error() == "unsupported database type: sqlite" {
			t.Skip("SQLite driver not available, skipping test")
		} else {
			t.Fatalf("Failed to connect to database: %v", err)
		}
	}
	defer database.Close()

	// 测试事务成功
	err = database.Transaction(func(tx *gorm.DB) error {
		// 创建测试表
		if err := tx.Exec("CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT)").Error; err != nil {
			return err
		}

		// 插入测试数据
		if err := tx.Exec("INSERT INTO test_table (id, name) VALUES (1, 'test')").Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		t.Fatalf("Transaction failed: %v", err)
	}

	// 测试事务回滚
	var count int64
	err = database.Transaction(func(tx *gorm.DB) error {
		// 插入测试数据
		if err := tx.Exec("INSERT INTO test_table (id, name) VALUES (2, 'rollback')").Error; err != nil {
			return err
		}

		// 查询数据数量
		if err := tx.Raw("SELECT COUNT(*) FROM test_table").Scan(&count).Error; err != nil {
			return err
		}

		if count != 2 {
			t.Errorf("Expected 2 rows, got %d", count)
		}

		// 主动触发回滚
		return errors.New("trigger rollback")
	})
	if err == nil {
		t.Fatal("Transaction should have failed")
	}

	// 验证回滚后的数据
	err = database.GetDB().Raw("SELECT COUNT(*) FROM test_table").Scan(&count).Error
	if err != nil {
		t.Fatalf("Query failed: %v", err)
	}

	if count != 1 {
		t.Errorf("Expected 1 row after rollback, got %d", count)
	}
}
