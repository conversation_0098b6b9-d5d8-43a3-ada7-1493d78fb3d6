package benchmark

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"backend/internal/api/middleware"
	"backend/internal/api/response"
	"backend/internal/api/router"
	"backend/internal/config"
	"backend/internal/utils/logger"
)

var (
	db         *gorm.DB
	redisCache *redis.Client
	testEngine *gin.Engine
)

// 初始化测试环境
func init() {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 初始化配置
	config.InitConfig("../../config/config.yaml")

	// 初始化日志
	logger.InitLogger(&config.Conf.Logger)

	// 初始化数据库
	dbConfig := &config.Conf.Database
	// SQL Server连接字符串
	dsn := fmt.Sprintf("server=%s;port=%d;database=%s;user id=%s;password=%s;",
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Username,
		dbConfig.Password)

	var err error
	db, err = gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.NewGormLogger(logger.GetLogger()),
	})
	if err != nil {
		fmt.Printf("连接数据库失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化Redis缓存
	redisConfig := &config.Conf.Redis
	redisCache = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
		Password: redisConfig.Password,
		DB:       redisConfig.Database,
	})
	cache.SetRedisClient(redisCache)

	// 初始化测试引擎
	testEngine = gin.New()
	testEngine.Use(gin.Recovery())
	testEngine.Use(middleware.TraceIDMiddleware())
	testEngine.Use(middleware.LoggerMiddleware())
	testEngine.Use(middleware.TimeoutMiddleware(10 * time.Second))
	router.RegisterRoutes(testEngine)
}

// 帮助函数 - 执行GET请求
func performGetRequest(t *testing.B, r http.Handler, path string, token string) *httptest.ResponseRecorder {
	req, _ := http.NewRequest("GET", path, nil)
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	return w
}

// 帮助函数 - 执行POST请求
func performPostRequest(t *testing.B, r http.Handler, path string, body interface{}, token string) *httptest.ResponseRecorder {
	bodyBytes, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", path, bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	return w
}

// 获取测试用的token
func getToken(b *testing.B) string {
	b.Helper()
	loginBody := map[string]string{
		"username": "admin",
		"password": "admin123",
	}
	w := performPostRequest(b, testEngine, "/login", loginBody, "")
	if w.Code != http.StatusOK {
		b.Fatalf("登录失败: %s", w.Body.String())
	}

	var result map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &result); err != nil {
		b.Fatalf("解析登录响应失败: %v", err)
	}

	token, ok := result["token"].(string)
	if !ok {
		b.Fatalf("登录响应中没有token: %s", w.Body.String())
	}

	return token
}

// 基准测试 - 登录性能
func BenchmarkLogin(b *testing.B) {
	loginBody := map[string]string{
		"username": "admin",
		"password": "admin123",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := performPostRequest(b, testEngine, "/login", loginBody, "")
		if w.Code != http.StatusOK {
			b.Fatalf("登录失败: %s", w.Body.String())
		}
	}
}

// 基准测试 - 获取用户信息
func BenchmarkGetInfo(b *testing.B) {
	token := getToken(b)
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := performGetRequest(b, testEngine, "/getInfo", token)
		if w.Code != http.StatusOK {
			b.Fatalf("获取用户信息失败: %s", w.Body.String())
		}
	}
}

// 基准测试 - 获取路由信息
func BenchmarkGetRouters(b *testing.B) {
	token := getToken(b)
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := performGetRequest(b, testEngine, "/getRouters", token)
		if w.Code != http.StatusOK {
			b.Fatalf("获取路由信息失败: %s", w.Body.String())
		}
	}
}

// 基准测试 - 获取用户列表
func BenchmarkUserList(b *testing.B) {
	token := getToken(b)
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := performGetRequest(b, testEngine, "/system/user/list?pageNum=1&pageSize=10", token)
		if w.Code != http.StatusOK {
			b.Fatalf("获取用户列表失败: %s", w.Body.String())
		}
	}
}

// 基准测试 - Redis缓存性能
func BenchmarkRedisCache(b *testing.B) {
	ctx := context.Background()
	key := "benchmark:test:key"
	value := "benchmark-value"

	// 预先设置一些值
	if err := redisCache.Set(ctx, key, value, 10*time.Minute).Err(); err != nil {
		b.Fatalf("设置缓存失败: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if _, err := redisCache.Get(ctx, key).Result(); err != nil {
			b.Fatalf("获取缓存失败: %v", err)
		}
	}
}

// 基准测试 - 数据库查询性能
func BenchmarkDatabaseQuery(b *testing.B) {
	type User struct {
		ID       int64
		UserName string
		NickName string
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var user User
		if err := db.Table("sys_user").Where("user_id = ?", 1).First(&user).Error; err != nil {
			b.Fatalf("查询数据库失败: %v", err)
		}
	}
}

// 基准测试 - 响应包装器性能
func BenchmarkResponseWrapper(b *testing.B) {
	data := map[string]string{
		"message": "测试消息",
		"value":   "测试值",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = response.Success(data)
	}
}

// 基准测试 - 中间件性能
func BenchmarkMiddleware(b *testing.B) {
	// 创建一个带有所有中间件的引擎
	engine := gin.New()
	engine.Use(middleware.TraceIDMiddleware())
	engine.Use(middleware.LoggerMiddleware())
	engine.Use(middleware.TimeoutMiddleware(10 * time.Second))
	engine.Use(middleware.RateLimitMiddleware(100, 1))
	engine.Use(middleware.XSSMiddleware())

	// 添加一个简单的处理程序
	engine.GET("/benchmark", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok", "id": c.GetString("trace_id")})
	})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/benchmark", nil)
		req.Header.Set("X-Request-ID", strconv.Itoa(i))
		engine.ServeHTTP(w, req)
		if w.Code != http.StatusOK {
			b.Fatalf("中间件测试失败: %s", w.Body.String())
		}
	}
}

// 并发基准测试 - 模拟高并发请求
func BenchmarkConcurrentRequests(b *testing.B) {
	token := getToken(b)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			w := performGetRequest(b, testEngine, "/getInfo", token)
			if w.Code != http.StatusOK {
				b.Fatalf("并发请求失败: %s", w.Body.String())
			}
		}
	})
}

// 基准测试 - 健康检查接口性能
func BenchmarkHealthCheck(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := performGetRequest(b, testEngine, "/health", "")
		if w.Code != http.StatusOK {
			b.Fatalf("健康检查失败: %s", w.Body.String())
		}
	}
}
