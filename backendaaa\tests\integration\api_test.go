package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"

	"github.com/your-project/config"
	"github.com/your-project/logger"
)

const (
	baseURL = "http://localhost:8080"
	timeout = 10 * time.Second
)

// 测试结构体
type testCase struct {
	name           string
	method         string
	path           string
	requestBody    interface{}
	expectedStatus int
	skipAuth       bool
	validate       func(t *testing.T, body []byte) bool
}

// 全局变量，用于存储测试过程中的token
var token string

// 初始化函数
func init() {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 初始化配置
	config.InitConfig("../../config/config.yaml")

	// 初始化日志
	logger.InitLogger(&config.Conf.Logger)

	// 初始化数据库
	dbConfig := &config.Conf.Database
	// SQL Server连接字符串
	dsn := fmt.Sprintf("server=%s;port=%d;database=%s;user id=%s;password=%s;",
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Username,
		dbConfig.Password)

	var err error
	db, err = gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.NewGormLogger(logger.GetLogger()),
	})
	if err != nil {
		fmt.Printf("连接数据库失败: %v\n", err)
		os.Exit(1)
	}

	// 等待服务启动
	time.Sleep(2 * time.Second)
}

// 登录并获取token
func getToken(t *testing.T) string {
	if token != "" {
		return token
	}

	loginBody := map[string]string{
		"username": "admin",
		"password": "admin123",
	}

	bodyBytes, _ := json.Marshal(loginBody)
	req, err := http.NewRequest("POST", baseURL+"/login", bytes.NewBuffer(bodyBytes))
	if err != nil {
		t.Fatalf("创建登录请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{Timeout: timeout}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("执行登录请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("读取登录响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		t.Fatalf("登录失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		t.Fatalf("解析登录响应失败: %v", err)
	}

	tokenData, ok := result["token"]
	if !ok {
		t.Fatalf("登录响应中没有token: %s", string(body))
	}

	token = tokenData.(string)
	return token
}

// 执行测试用例
func runTestCase(t *testing.T, tc testCase) {
	var req *http.Request
	var err error

	// 准备请求体
	if tc.requestBody != nil {
		bodyBytes, _ := json.Marshal(tc.requestBody)
		req, err = http.NewRequest(tc.method, baseURL+tc.path, bytes.NewBuffer(bodyBytes))
	} else {
		req, err = http.NewRequest(tc.method, baseURL+tc.path, nil)
	}

	if err != nil {
		t.Fatalf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 如果需要认证，添加token
	if !tc.skipAuth {
		token := getToken(t)
		req.Header.Set("Authorization", "Bearer "+token)
	}

	// 发送请求
	client := &http.Client{Timeout: timeout}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("执行请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != tc.expectedStatus {
		t.Errorf("状态码不匹配，期望: %d, 实际: %d", tc.expectedStatus, resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("读取响应失败: %v", err)
	}

	// 如果有自定义验证函数，执行它
	if tc.validate != nil {
		if !tc.validate(t, body) {
			t.Errorf("响应验证失败: %s", string(body))
		}
	}
}

// 测试登录接口
func TestLogin(t *testing.T) {
	tc := testCase{
		name:   "登录接口",
		method: "POST",
		path:   "/login",
		requestBody: map[string]string{
			"username": "admin",
			"password": "admin123",
		},
		expectedStatus: http.StatusOK,
		skipAuth:       true,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应是否包含token
			_, hasToken := result["token"]
			if !hasToken {
				t.Logf("响应中没有token: %s", string(body))
				return false
			}

			return true
		},
	}

	runTestCase(t, tc)
}

// 测试获取用户信息
func TestGetUserInfo(t *testing.T) {
	tc := testCase{
		name:           "获取用户信息",
		method:         "GET",
		path:           "/getInfo",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的用户信息
			user, hasUser := result["user"]
			if !hasUser {
				t.Logf("响应中没有用户信息: %s", string(body))
				return false
			}

			// 检查是否有权限信息
			_, hasPerms := result["permissions"]
			if !hasPerms {
				t.Logf("响应中没有权限信息: %s", string(body))
				return false
			}

			// 打印用户名
			userMap, ok := user.(map[string]interface{})
			if ok {
				t.Logf("用户名: %v", userMap["userName"])
			}

			return true
		},
	}

	runTestCase(t, tc)
}

// 测试获取路由信息
func TestGetRouters(t *testing.T) {
	tc := testCase{
		name:           "获取路由信息",
		method:         "GET",
		path:           "/getRouters",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的路由信息
			data, hasData := result["data"]
			if !hasData {
				t.Logf("响应中没有路由信息: %s", string(body))
				return false
			}

			// 打印路由数量
			routes, ok := data.([]interface{})
			if ok {
				t.Logf("路由数量: %d", len(routes))
			}

			return true
		},
	}

	runTestCase(t, tc)
}

// 测试健康检查接口
func TestHealthCheck(t *testing.T) {
	tc := testCase{
		name:           "健康检查",
		method:         "GET",
		path:           "/health",
		expectedStatus: http.StatusOK,
		skipAuth:       true,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的状态信息
			status, hasStatus := result["status"]
			if !hasStatus {
				t.Logf("响应中没有状态信息: %s", string(body))
				return false
			}

			t.Logf("健康状态: %v", status)
			return true
		},
	}

	runTestCase(t, tc)
}

// 测试用户列表接口
func TestUserList(t *testing.T) {
	tc := testCase{
		name:           "用户列表",
		method:         "GET",
		path:           "/system/user/list?pageNum=1&pageSize=10",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的数据
			_, hasRows := result["rows"]
			total, hasTotal := result["total"]
			if !hasRows || !hasTotal {
				t.Logf("响应中没有数据或总数: %s", string(body))
				return false
			}

			// 打印用户数量
			t.Logf("用户总数: %v", total)
			return true
		},
	}

	runTestCase(t, tc)
}

// 测试角色列表接口
func TestRoleList(t *testing.T) {
	tc := testCase{
		name:           "角色列表",
		method:         "GET",
		path:           "/system/role/list?pageNum=1&pageSize=10",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的数据
			_, hasRows := result["rows"]
			total, hasTotal := result["total"]
			if !hasRows || !hasTotal {
				t.Logf("响应中没有数据或总数: %s", string(body))
				return false
			}

			// 打印角色数量
			t.Logf("角色总数: %v", total)
			return true
		},
	}

	runTestCase(t, tc)
}

// 测试菜单列表接口
func TestMenuList(t *testing.T) {
	tc := testCase{
		name:           "菜单列表",
		method:         "GET",
		path:           "/system/menu/list",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的数据
			data, hasData := result["data"]
			if !hasData {
				t.Logf("响应中没有数据: %s", string(body))
				return false
			}

			// 检查数据类型
			dataArr, ok := data.([]interface{})
			if !ok {
				t.Logf("数据类型不是数组: %s", string(body))
				return false
			}

			// 打印菜单数量
			t.Logf("菜单总数: %d", len(dataArr))
			return true
		},
	}

	runTestCase(t, tc)
}

// 测试服务器信息接口
func TestServerInfo(t *testing.T) {
	tc := testCase{
		name:           "服务器信息",
		method:         "GET",
		path:           "/health/server",
		expectedStatus: http.StatusOK,
		validate: func(t *testing.T, body []byte) bool {
			var result map[string]interface{}
			if err := json.Unmarshal(body, &result); err != nil {
				t.Logf("解析响应失败: %v", err)
				return false
			}

			// 检查响应中的数据
			data, hasData := result["data"]
			if !hasData {
				t.Logf("响应中没有数据: %s", string(body))
				return false
			}

			// 检查数据内容
			dataMap, ok := data.(map[string]interface{})
			if !ok {
				t.Logf("数据类型不是对象: %s", string(body))
				return false
			}

			// 检查进程信息
			process, hasProcess := dataMap["process"]
			if !hasProcess {
				t.Logf("数据中没有进程信息: %s", string(body))
				return false
			}

			// 打印进程信息
			processMap, ok := process.(map[string]interface{})
			if ok {
				t.Logf("操作系统: %v, Go版本: %v",
					processMap["goos"],
					processMap["goVersion"])
			}

			return true
		},
	}

	runTestCase(t, tc)
}

// 运行所有测试
func TestMain(t *testing.T) {
	fmt.Println("开始前后端集成测试...")

	// 测试健康检查
	t.Run("HealthCheck", TestHealthCheck)

	// 测试登录功能
	t.Run("Login", TestLogin)

	// 测试获取用户信息
	t.Run("GetUserInfo", TestGetUserInfo)

	// 测试获取路由信息
	t.Run("GetRouters", TestGetRouters)

	// 测试用户列表
	t.Run("UserList", TestUserList)

	// 测试角色列表
	t.Run("RoleList", TestRoleList)

	// 测试菜单列表
	t.Run("MenuList", TestMenuList)

	// 测试服务器信息
	t.Run("ServerInfo", TestServerInfo)

	fmt.Println("集成测试完成！")
}
