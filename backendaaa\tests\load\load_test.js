import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// 自定义指标
const authFailRate = new Rate('auth_failures');
const apiRequestsCounter = new Counter('api_requests');
const apiLatencyTrend = new Trend('api_latency');

// 测试配置
export const options = {
  // 阶段式负载测试
  stages: [
    { duration: '30s', target: 10 },   // 慢启动
    { duration: '1m', target: 50 },    // 逐渐增加到50个用户
    { duration: '2m', target: 50 },    // 保持50个用户2分钟
    { duration: '30s', target: 100 },  // 突增到100个用户
    { duration: '1m', target: 100 },   // 保持100个用户1分钟
    { duration: '1m', target: 0 },     // 逐渐减少到0
  ],
  thresholds: {
    // 设置SLO阈值
    'http_req_duration': ['p(95)<500'], // 95%的请求应该小于500ms
    'http_req_failed': ['rate<0.01'],   // 错误率应低于1%
    'auth_failures': ['rate<0.05'],     // 认证失败率应低于5%
  },
};

// 测试数据 - 登录凭证
const loginData = new SharedArray('login credentials', function() {
  return [
    { username: 'admin', password: 'admin123' },
    { username: 'user', password: 'user123' },
    // 可以添加更多用户凭证
  ];
});

// 会话管理
const sessions = new Map();

// 设置
const BASE_URL = 'http://localhost:8080';
const API_ENDPOINTS = [
  '/getInfo',
  '/getRouters',
  '/system/user/list?pageNum=1&pageSize=10',
  '/system/role/list?pageNum=1&pageSize=10',
  '/system/menu/list',
  '/health',
  '/health/server',
];

// 初始化函数 - 每个VU只执行一次
export function setup() {
  console.log('开始负载测试...');
}

// 登录并获取令牌
function authenticate(username, password) {
  const payload = JSON.stringify({
    username: username,
    password: password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const loginResponse = http.post(`${BASE_URL}/login`, payload, params);
  apiRequestsCounter.add(1);
  apiLatencyTrend.add(loginResponse.timings.duration);

  // 检查登录是否成功
  const success = check(loginResponse, {
    'login successful': (r) => r.status === 200,
    'has token': (r) => JSON.parse(r.body).token !== undefined,
  });

  if (!success) {
    authFailRate.add(1);
    console.error(`登录失败: ${loginResponse.status} ${loginResponse.body}`);
    return null;
  }

  // 提取令牌
  return JSON.parse(loginResponse.body).token;
}

// 主测试函数
export default function() {
  // 为VU选择随机凭证
  const credentials = loginData[randomIntBetween(0, loginData.length - 1)];
  
  // VU唯一ID
  const vuId = `${__VU}_${credentials.username}`;
  
  // 检查是否已经有会话令牌
  if (!sessions.has(vuId)) {
    const token = authenticate(credentials.username, credentials.password);
    if (token) {
      sessions.set(vuId, token);
    } else {
      // 如果登录失败，休息一会再试
      sleep(3);
      return;
    }
  }

  const token = sessions.get(vuId);
  if (!token) {
    // 如果没有令牌，重新登录
    return;
  }

  // 选择随机API端点
  const endpoint = API_ENDPOINTS[randomIntBetween(0, API_ENDPOINTS.length - 1)];
  
  // 准备请求参数
  const params = {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  // 发送请求
  const response = http.get(`${BASE_URL}${endpoint}`, params);
  apiRequestsCounter.add(1);
  apiLatencyTrend.add(response.timings.duration);

  // 检查响应
  check(response, {
    'status is 200': (r) => r.status === 200,
    'has valid response': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body !== undefined;
      } catch (e) {
        return false;
      }
    },
  });

  // 添加随机延迟，模拟真实用户行为
  sleep(randomIntBetween(1, 3));
}

// 结束函数 - 测试结束时执行一次
export function teardown() {
  console.log('负载测试完成');
} 