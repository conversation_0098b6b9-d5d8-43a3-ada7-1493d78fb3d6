#!/bin/bash

# 性能测试运行脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_result() {
    echo -e "${BLUE}[RESULT]${NC} $1"
}

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.." || exit

# 确保结果目录存在
mkdir -p tests/results

# 测试结果文件
RESULT_FILE="tests/results/benchmark_$(date +%Y%m%d_%H%M%S).txt"
PROFILE_FILE="tests/results/cpu_profile_$(date +%Y%m%d_%H%M%S).out"
MEM_PROFILE_FILE="tests/results/mem_profile_$(date +%Y%m%d_%H%M%S).out"

print_info "开始执行性能测试..."
print_info "结果将保存到: ${RESULT_FILE}"

# 检查命令行参数
BENCHMARK_FILTER="."
if [ -n "$1" ]; then
    BENCHMARK_FILTER=$1
    print_info "将只运行匹配 '$BENCHMARK_FILTER' 的基准测试"
fi

# 运行基准测试 - 标准模式
print_info "运行标准基准测试..."
go test -benchmem -bench="$BENCHMARK_FILTER" ./tests/benchmark/... | tee -a "$RESULT_FILE"
if [ ${PIPESTATUS[0]} -ne 0 ]; then
    print_error "基准测试失败"
    exit 1
fi

# 运行CPU分析
print_info "运行CPU分析测试..."
go test -benchmem -bench="$BENCHMARK_FILTER" -cpuprofile="$PROFILE_FILE" ./tests/benchmark/... | tee -a "$RESULT_FILE"
if [ ${PIPESTATUS[0]} -ne 0 ]; then
    print_error "CPU分析测试失败"
    exit 1
fi

# 运行内存分析
print_info "运行内存分析测试..."
go test -benchmem -bench="$BENCHMARK_FILTER" -memprofile="$MEM_PROFILE_FILE" ./tests/benchmark/... | tee -a "$RESULT_FILE"
if [ ${PIPESTATUS[0]} -ne 0 ]; then
    print_error "内存分析测试失败"
    exit 1
fi

# 并发性能测试
print_info "运行不同并发级别的测试..."
for PROCS in 1 2 4 8 16; do
    print_info "测试 GOMAXPROCS=$PROCS"
    GOMAXPROCS=$PROCS go test -benchmem -bench="Concurrent" ./tests/benchmark/... | tee -a "$RESULT_FILE"
done

print_success "所有性能测试完成！"
print_info "测试结果已保存到: ${RESULT_FILE}"
print_info "CPU分析文件: ${PROFILE_FILE}"
print_info "内存分析文件: ${MEM_PROFILE_FILE}"

# 输出一些分析提示
print_info "要查看CPU分析结果，可以运行:"
print_result "go tool pprof -http=:8081 ${PROFILE_FILE}"

print_info "要查看内存分析结果，可以运行:"
print_result "go tool pprof -http=:8082 ${MEM_PROFILE_FILE}" 