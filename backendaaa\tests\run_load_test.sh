#!/bin/bash

# 负载测试运行脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_command() {
    echo -e "${BLUE}[CMD]${NC} $1"
}

# 检查k6是否安装
check_k6() {
    if ! command -v k6 &> /dev/null; then
        print_error "k6 未安装，请按照以下说明安装k6："
        echo "Ubuntu/Debian: sudo apt-get install k6"
        echo "MacOS: brew install k6"
        echo "Windows: choco install k6"
        echo "或者访问: https://k6.io/docs/getting-started/installation/"
        exit 1
    fi
}

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.." || exit

# 确保结果目录存在
mkdir -p tests/results

# 检查k6是否安装
check_k6

# 默认测试脚本
TEST_SCRIPT="tests/load/load_test.js"
# 默认结果文件
RESULT_DIR="tests/results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULT_FILE="${RESULT_DIR}/load_test_${TIMESTAMP}.json"
SUMMARY_FILE="${RESULT_DIR}/load_test_summary_${TIMESTAMP}.txt"

# 检查测试脚本是否存在
if [ ! -f "$TEST_SCRIPT" ]; then
    print_error "测试脚本不存在: $TEST_SCRIPT"
    exit 1
fi

# 检查服务是否运行
print_info "检查服务是否运行..."
if ! curl -s http://localhost:8080/health > /dev/null; then
    print_error "服务未运行，请先启动服务"
    print_command "go run main.go"
    exit 1
fi

print_success "服务已运行"

# 显示测试参数
print_info "开始负载测试..."
print_info "测试脚本: $TEST_SCRIPT"
print_info "结果文件: $RESULT_FILE"

# 运行负载测试
print_info "执行负载测试，这可能需要几分钟..."
k6 run --out json="$RESULT_FILE" "$TEST_SCRIPT"

# 检查测试是否成功
if [ $? -ne 0 ]; then
    print_error "负载测试失败"
    exit 1
fi

# 生成摘要报告
print_info "生成摘要报告..."
{
    echo "====== 负载测试摘要 $(date) ======"
    echo ""
    echo "测试脚本: $TEST_SCRIPT"
    echo "结果文件: $RESULT_FILE"
    echo ""
    echo "===== 性能指标 ====="
    
    # 使用jq从JSON结果中提取关键指标（如果安装了jq）
    if command -v jq &> /dev/null; then
        echo "请求总数: $(jq '.metrics.http_reqs.count' "$RESULT_FILE")"
        echo "每秒请求数: $(jq '.metrics.http_reqs.rate' "$RESULT_FILE")"
        echo "请求失败率: $(jq '.metrics.http_req_failed.rate' "$RESULT_FILE")"
        echo "平均响应时间: $(jq '.metrics.http_req_duration.avg' "$RESULT_FILE") ms"
        echo "响应时间中位数: $(jq '.metrics.http_req_duration.med' "$RESULT_FILE") ms"
        echo "响应时间95百分位: $(jq '.metrics.http_req_duration["p(95)"]' "$RESULT_FILE") ms"
        echo "响应时间99百分位: $(jq '.metrics.http_req_duration["p(99)"]' "$RESULT_FILE") ms"
    else
        echo "未安装jq，无法提取详细指标。请安装jq以获得更详细的报告。"
        echo "Ubuntu/Debian: sudo apt-get install jq"
        echo "MacOS: brew install jq"
        echo "Windows: choco install jq"
    fi
} > "$SUMMARY_FILE"

print_success "负载测试完成！"
print_info "摘要报告已保存到: $SUMMARY_FILE"

# 显示摘要报告
cat "$SUMMARY_FILE" 