#!/bin/bash

# 测试运行脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.." || exit

print_info "开始执行测试..."

# 运行单元测试
print_info "运行单元测试..."
go test -v ./... -run "^Test[^Main]" -coverprofile=coverage.out
if [ $? -ne 0 ]; then
    print_error "单元测试失败"
    exit 1
fi
print_success "单元测试通过"

# 生成覆盖率报告
print_info "生成覆盖率报告..."
go tool cover -html=coverage.out -o coverage.html
print_success "覆盖率报告已生成: coverage.html"

# 确保服务处于运行状态
print_info "检查服务是否运行..."
if curl -s http://localhost:8080/health > /dev/null; then
    print_info "服务已运行"
else
    print_info "启动服务用于集成测试..."
    # 在后台启动服务
    go run main.go &
    SERVER_PID=$!
    
    # 等待服务启动
    print_info "等待服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/health > /dev/null; then
            print_success "服务已启动"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "服务启动超时"
            exit 1
        fi
        sleep 1
    done
fi

# 运行集成测试
print_info "运行集成测试..."
go test -v ./tests/integration/... -timeout 60s
INTEGRATION_TEST_RESULT=$?

# 如果我们启动了服务，现在关闭它
if [ -n "$SERVER_PID" ]; then
    print_info "关闭测试服务..."
    kill $SERVER_PID
    wait $SERVER_PID 2>/dev/null
    print_success "服务已关闭"
fi

# 检查集成测试结果
if [ $INTEGRATION_TEST_RESULT -ne 0 ]; then
    print_error "集成测试失败"
    exit 1
fi

print_success "集成测试通过"
print_success "所有测试完成" 