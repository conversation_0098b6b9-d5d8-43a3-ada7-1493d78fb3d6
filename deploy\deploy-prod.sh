#!/bin/bash

# 生产环境部署脚本

set -e

echo "开始生产环境部署..."

# 检查环境变量文件
if [ ! -f ".env.prod" ]; then
    echo "错误: .env.prod 文件不存在"
    echo "请复制 .env.example 为 .env.prod 并配置生产环境变量"
    exit 1
fi

# 加载环境变量
source .env.prod

# 验证必需的环境变量
required_vars=("DB_HOST" "DB_USERNAME" "DB_PASSWORD" "REDIS_PASSWORD" "JWT_SECRET")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误: 环境变量 $var 未设置"
        exit 1
    fi
done

# 验证JWT密钥长度
if [ ${#JWT_SECRET} -lt 32 ]; then
    echo "错误: JWT_SECRET 长度必须至少32个字符"
    exit 1
fi

# 构建应用
echo "构建应用..."
cd backend
go mod tidy
go build -o wosm-backend ./cmd/main.go

# 创建必要目录
mkdir -p logs data temp

# 设置文件权限
chmod +x wosm-backend

echo "部署完成!"
echo "请使用以下命令启动应用:"
echo "cd backend && ./wosm-backend" 