package com.wosm.tools;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;

import java.io.FileInputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * JavaParser示例类
 * 用于解析Java代码并提取类和方法信息
 */
public class JavaParserExample {

    public static void main(String[] args) {
        try {
            System.out.println("JavaParser示例开始运行...");
            
            // 解析自身代码作为示例
            String javaFilePath = "src/main/java/com/wosm/tools/SimpleTest.java";
            System.out.println("尝试解析文件: " + javaFilePath);
            
            // 解析Java文件
            JavaParser parser = new JavaParser();
            FileInputStream fileInputStream = new FileInputStream(javaFilePath);
            CompilationUnit compilationUnit = parser.parse(fileInputStream).getResult().orElseThrow(NoSuchElementException::new);
            
            // 提取类信息
            List<String> classNames = new ArrayList<>();
            new VoidVisitorAdapter<List<String>>() {
                @Override
                public void visit(ClassOrInterfaceDeclaration n, List<String> collector) {
                    super.visit(n, collector);
                    collector.add(n.getNameAsString());
                }
            }.visit(compilationUnit, classNames);
            
            System.out.println("找到的类:");
            classNames.forEach(className -> System.out.println("- " + className));
            
            // 关闭文件流
            fileInputStream.close();
            
            System.out.println("JavaParser示例运行完成!");
            
        } catch (Exception e) {
            System.err.println("解析Java文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 