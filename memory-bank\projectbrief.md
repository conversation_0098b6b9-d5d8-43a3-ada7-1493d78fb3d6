# 项目简介

## 项目概述
该项目是一个基于前后端分离架构的系统，前端使用现代前端技术栈，后端基于Java开发。从项目结构来看，这是一个RuoYi框架的项目，包含了前端和后端代码。

## 核心需求和目标
- 提供一个完整的企业级应用系统
- 实现前后端分离架构
- 提供标准的CRUD功能和权限管理
- 支持数据库操作（项目中包含SQL Server脚本）

## 项目范围
- 前端：基于现代前端框架（可能是Vue，根据vite.config.js判断）
- 后端：基于Java的RuoYi框架
- 数据库：支持SQL Server
- 缓存：使用Redis（根据项目中的redis目录和dump.rdb文件判断）

## 技术栈
- 前端：Vue + Vite
- 后端：Java（RuoYi框架）
- 数据库：SQL Server
- 缓存：Redis
- 构建工具：npm/yarn（根据package.json文件判断）

## 项目结构
- ruoyi-java/：后端Java代码
- src/：前端源代码
- public/：前端静态资源
- sql/：数据库脚本
- redis/：Redis相关配置
- bin/：可执行脚本
- vite/：Vite配置文件
- .github/：GitHub相关配置

## 启动脚本
项目包含多个批处理脚本：
- start-project.bat：启动项目
- setup-database.bat：设置数据库
- run-backend.bat：运行后端服务 