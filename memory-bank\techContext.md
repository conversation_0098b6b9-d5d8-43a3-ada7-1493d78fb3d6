# 技术上下文

## 使用的技术
1. **前端技术**
   - Vue3：前端框架
   - Element Plus：UI组件库
   - Vite：构建工具
   - Node.js：JavaScript运行环境
   - npm/yarn：包管理工具

2. **后端技术**
   - Java：编程语言
   - Spring Boot：Java应用开发框架
   - MyBatis：ORM框架
   - RuoYi框架：基于Spring Boot的企业级开发框架
   - Quartz：定时任务调度框架

3. **数据库**
   - SQL Server：主数据库（根据SqlServer_20200720.sql文件判断）
   - MySQL 5.7+：可选数据库（根据ruoyi-setup.md文件判断）
   - Redis：缓存数据库

## 开发环境设置
1. **前端开发环境**
   - Node.js环境
   - npm或yarn包管理器
   - 使用vite进行构建和开发
   - 默认运行在 http://localhost:80 或 http://localhost:81

2. **后端开发环境**
   - JDK 8或更高版本
   - Maven 3或更高版本（可使用D:\tools\apache-maven-3.9.10）
   - IDE（如IntelliJ IDEA或Eclipse）
   - 默认运行在 http://localhost:8080

3. **数据库环境**
   - SQL Server实例
   - 或MySQL 5.7+实例（数据库名：ry-vue）
   - Redis服务器

## 技术约束
1. **前端约束**
   - 兼容性要求（根据项目具体需求）
   - 性能要求
   - 前端代理配置（vite.config.js中的baseUrl）

2. **后端约束**
   - Java版本要求（JDK 8+）
   - 服务器资源限制
   - 安全性要求
   - 数据库连接配置

3. **数据库约束**
   - SQL Server或MySQL 5.7+版本要求
   - 数据库连接池配置（使用Druid）
   - Redis缓存策略

## 依赖关系
1. **前端依赖**
   - package.json中定义的npm包
   - Vue3核心库
   - Element Plus UI组件库
   - Vite构建工具

2. **后端依赖**
   - Spring Boot及其相关模块
   - MyBatis ORM框架
   - RuoYi框架组件
   - Quartz定时任务框架
   - Druid数据库连接池

3. **系统依赖**
   - 数据库连接
   - Redis缓存连接
   - 其他外部系统集成（如有）

## 部署架构
1. **开发环境**
   - 本地开发服务器
   - 本地数据库实例
   - 本地Redis实例
   - 前后端分离部署

2. **测试环境**（如有）
   - 测试服务器配置
   - 测试数据库实例

3. **生产环境**（如有）
   - 生产服务器配置
   - 生产数据库实例
   - 负载均衡策略（如有）

## 启动流程
1. **使用自动化脚本**
   - 设置数据库（执行setup-database.bat）
   - 启动后端服务（执行run-backend.bat）
   - 启动前端开发服务器（npm run dev）
   - 或使用start-project.bat一键启动项目

2. **手动配置步骤**
   - 创建数据库（ry-vue）
   - 导入数据库脚本（ruoyi-java/sql/ry_20250522.sql和quartz.sql）
   - 修改数据库连接信息（ruoyi-java/ruoyi-admin/src/main/resources/application-druid.yml）
   - 编译后端（cd ruoyi-java && mvn clean package -DskipTests）
   - 运行后端（java -jar ruoyi-admin/target/ruoyi-admin.jar）
   - 启动前端（npm run dev）

## 系统访问信息
- 访问地址：http://localhost:80 或 http://localhost:81
- 默认账号：admin
- 默认密码：admin123

## 内置功能
1. 用户管理：用户是系统操作者，该功能主要完成系统用户配置
2. 部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限
3. 岗位管理：配置系统用户所属担任职务
4. 菜单管理：配置系统菜单，操作权限，按钮权限标识等
5. 角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分
6. 字典管理：对系统中经常使用的一些较为固定的数据进行维护
7. 参数管理：对系统动态配置常用参数
8. 通知公告：系统通知公告信息发布维护
9. 操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询
10. 登录日志：系统登录日志记录查询包含登录异常
11. 在线用户：当前系统中活跃用户状态监控
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载
14. 系统接口：根据业务代码自动生成相关的api接口文档
15. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息
16. 缓存监控：对系统的缓存信息查询，命令统计等
17. 在线构建器：拖动表单元素生成相应的HTML代码
18. 连接池监视：监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈 