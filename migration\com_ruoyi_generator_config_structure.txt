# Package: com.ruoyi.generator.config

## Class: GenConfig

### Fields:
- String author
- String packageName
- boolean autoRemovePre
- String tablePrefix
- boolean allowOverwrite

### Methods:
- String getAuthor()
- void setAuthor(String author) (Value)
- String getPackageName()
- void setPackageName(String packageName) (Value)
- boolean getAutoRemovePre()
- void setAutoRemovePre(boolean autoRemovePre) (Value)
- String getTablePrefix()
- void setTablePrefix(String tablePrefix) (Value)
- boolean isAllowOverwrite()
- void setAllowOverwrite(boolean allowOverwrite) (Value)

### Go Implementation Suggestion:
```go
package config

type GenConfig struct {
	Author string
	PackageName string
	AutoRemovePre bool
	TablePrefix string
	AllowOverwrite bool
}

func (c *GenConfig) getAuthor() string {
	// TODO: Implement method
	return ""
}

func (c *GenConfig) setAuthor(author string) {
	// TODO: Implement method
}

func (c *GenConfig) getPackageName() string {
	// TODO: Implement method
	return ""
}

func (c *GenConfig) setPackageName(packageName string) {
	// TODO: Implement method
}

func (c *GenConfig) getAutoRemovePre() bool {
	// TODO: Implement method
	return false
}

func (c *GenConfig) setAutoRemovePre(autoRemovePre bool) {
	// TODO: Implement method
}

func (c *GenConfig) getTablePrefix() string {
	// TODO: Implement method
	return ""
}

func (c *GenConfig) setTablePrefix(tablePrefix string) {
	// TODO: Implement method
}

func (c *GenConfig) isAllowOverwrite() bool {
	// TODO: Implement method
	return false
}

func (c *GenConfig) setAllowOverwrite(allowOverwrite bool) {
	// TODO: Implement method
}

```

