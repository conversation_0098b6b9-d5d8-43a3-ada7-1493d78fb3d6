# Package: com.ruoyi.generator.controller

## Class: GenController

Extends: BaseController

### Fields:
- IGenTableService genTableService (Autowired)
- IGenTableColumnService genTableColumnService (Autowired)

### Methods:
- TableDataInfo genList(GenTable genTable) (PreAuthorize, GetMapping)
- AjaxResult getInfo(Long tableId) (PreAuthorize, GetMapping)
- TableDataInfo dataList(GenTable genTable) (PreAuthorize, GetMapping)
- TableDataInfo columnList(Long tableId) (PreAuthorize, GetMapping)
- AjaxResult importTableSave(String tables) (PreAuthorize, Log, PostMapping)
- AjaxResult createTableSave(String sql) (PreAuthorize, Log, PostMapping)
- AjaxResult editSave(GenTable genTable) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] tableIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult preview(Long tableId) (PreAuthorize, GetMapping)
- void download(HttpServletResponse response, String tableName) (PreAuthorize, Log, GetMapping)
- AjaxResult genCode(String tableName) (PreAuthorize, Log, GetMapping)
- AjaxResult synchDb(String tableName) (PreAuthorize, Log, GetMapping)
- void batchGenCode(HttpServletResponse response, String tables) (PreAuthorize, Log, GetMapping)
- void genCode(HttpServletResponse response, byte[] data)

### Go Implementation Suggestion:
```go
package controller

type GenController struct {
	GenTableService IGenTableService
	GenTableColumnService IGenTableColumnService
}

func (c *GenController) genList(genTable GenTable) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *GenController) getInfo(tableId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) dataList(genTable GenTable) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *GenController) columnList(tableId int64) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *GenController) importTableSave(tables string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) createTableSave(sql string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) editSave(genTable GenTable) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) remove(tableIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) preview(tableId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) download(response HttpServletResponse, tableName string) {
	// TODO: Implement method
}

func (c *GenController) genCode(tableName string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) synchDb(tableName string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GenController) batchGenCode(response HttpServletResponse, tables string) {
	// TODO: Implement method
}

func (c *GenController) genCode(response HttpServletResponse, data []byte) {
	// TODO: Implement method
}

```

