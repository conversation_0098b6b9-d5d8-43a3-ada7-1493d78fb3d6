# Package: com.ruoyi.generator.domain

## Class: GenTable

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long tableId
- String tableName (NotBlank)
- String tableComment (NotBlank)
- String subTableName
- String subTableFkName
- String className (NotBlank)
- String tplCategory
- String tplWebType
- String packageName (NotBlank)
- String moduleName (NotBlank)
- String businessName (NotBlank)
- String functionName (NotBlank)
- String functionAuthor (NotBlank)
- String genType
- String genPath
- GenTableColumn pkColumn
- GenTable subTable
- List<GenTableColumn> columns (Valid)
- String options
- String treeCode
- String treeParentCode
- String treeName
- Long parentMenuId
- String parentMenuName

### Methods:
- Long getTableId()
- void setTableId(Long tableId)
- String getTableName()
- void setTableName(String tableName)
- String getTableComment()
- void setTableComment(String tableComment)
- String getSubTableName()
- void setSubTableName(String subTableName)
- String getSubTableFkName()
- void setSubTableFkName(String subTableFkName)
- String getClassName()
- void setClassName(String className)
- String getTplCategory()
- void setTplCategory(String tplCategory)
- String getTplWebType()
- void setTplWebType(String tplWebType)
- String getPackageName()
- void setPackageName(String packageName)
- String getModuleName()
- void setModuleName(String moduleName)
- String getBusinessName()
- void setBusinessName(String businessName)
- String getFunctionName()
- void setFunctionName(String functionName)
- String getFunctionAuthor()
- void setFunctionAuthor(String functionAuthor)
- String getGenType()
- void setGenType(String genType)
- String getGenPath()
- void setGenPath(String genPath)
- GenTableColumn getPkColumn()
- void setPkColumn(GenTableColumn pkColumn)
- GenTable getSubTable()
- void setSubTable(GenTable subTable)
- List<GenTableColumn> getColumns()
- void setColumns(List<GenTableColumn> columns)
- String getOptions()
- void setOptions(String options)
- String getTreeCode()
- void setTreeCode(String treeCode)
- String getTreeParentCode()
- void setTreeParentCode(String treeParentCode)
- String getTreeName()
- void setTreeName(String treeName)
- Long getParentMenuId()
- void setParentMenuId(Long parentMenuId)
- String getParentMenuName()
- void setParentMenuName(String parentMenuName)
- boolean isSub()
- boolean isSub(String tplCategory)
- boolean isTree()
- boolean isTree(String tplCategory)
- boolean isCrud()
- boolean isCrud(String tplCategory)
- boolean isSuperColumn(String javaField)
- boolean isSuperColumn(String tplCategory, String javaField)

### Go Implementation Suggestion:
```go
package domain

type GenTable struct {
	SerialVersionUID int64
	TableId int64
	TableName string
	TableComment string
	SubTableName string
	SubTableFkName string
	ClassName string
	TplCategory string
	TplWebType string
	PackageName string
	ModuleName string
	BusinessName string
	FunctionName string
	FunctionAuthor string
	GenType string
	GenPath string
	PkColumn GenTableColumn
	SubTable GenTable
	Columns []GenTableColumn
	Options string
	TreeCode string
	TreeParentCode string
	TreeName string
	ParentMenuId int64
	ParentMenuName string
}

func (c *GenTable) getTableId() int64 {
	// TODO: Implement method
	return 0
}

func (c *GenTable) setTableId(tableId int64) {
	// TODO: Implement method
}

func (c *GenTable) getTableName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTableName(tableName string) {
	// TODO: Implement method
}

func (c *GenTable) getTableComment() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTableComment(tableComment string) {
	// TODO: Implement method
}

func (c *GenTable) getSubTableName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setSubTableName(subTableName string) {
	// TODO: Implement method
}

func (c *GenTable) getSubTableFkName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setSubTableFkName(subTableFkName string) {
	// TODO: Implement method
}

func (c *GenTable) getClassName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setClassName(className string) {
	// TODO: Implement method
}

func (c *GenTable) getTplCategory() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTplCategory(tplCategory string) {
	// TODO: Implement method
}

func (c *GenTable) getTplWebType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTplWebType(tplWebType string) {
	// TODO: Implement method
}

func (c *GenTable) getPackageName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setPackageName(packageName string) {
	// TODO: Implement method
}

func (c *GenTable) getModuleName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setModuleName(moduleName string) {
	// TODO: Implement method
}

func (c *GenTable) getBusinessName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setBusinessName(businessName string) {
	// TODO: Implement method
}

func (c *GenTable) getFunctionName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setFunctionName(functionName string) {
	// TODO: Implement method
}

func (c *GenTable) getFunctionAuthor() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setFunctionAuthor(functionAuthor string) {
	// TODO: Implement method
}

func (c *GenTable) getGenType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setGenType(genType string) {
	// TODO: Implement method
}

func (c *GenTable) getGenPath() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setGenPath(genPath string) {
	// TODO: Implement method
}

func (c *GenTable) getPkColumn() GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *GenTable) setPkColumn(pkColumn GenTableColumn) {
	// TODO: Implement method
}

func (c *GenTable) getSubTable() GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTable) setSubTable(subTable GenTable) {
	// TODO: Implement method
}

func (c *GenTable) getColumns() []GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *GenTable) setColumns(columns []GenTableColumn) {
	// TODO: Implement method
}

func (c *GenTable) getOptions() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setOptions(options string) {
	// TODO: Implement method
}

func (c *GenTable) getTreeCode() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTreeCode(treeCode string) {
	// TODO: Implement method
}

func (c *GenTable) getTreeParentCode() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTreeParentCode(treeParentCode string) {
	// TODO: Implement method
}

func (c *GenTable) getTreeName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setTreeName(treeName string) {
	// TODO: Implement method
}

func (c *GenTable) getParentMenuId() int64 {
	// TODO: Implement method
	return 0
}

func (c *GenTable) setParentMenuId(parentMenuId int64) {
	// TODO: Implement method
}

func (c *GenTable) getParentMenuName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTable) setParentMenuName(parentMenuName string) {
	// TODO: Implement method
}

func (c *GenTable) isSub() bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isSub(tplCategory string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isTree() bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isTree(tplCategory string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isCrud() bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isCrud(tplCategory string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isSuperColumn(javaField string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTable) isSuperColumn(tplCategory string, javaField string) bool {
	// TODO: Implement method
	return false
}

```

## Class: GenTableColumn

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long columnId
- Long tableId
- String columnName
- String columnComment
- String columnType
- String javaType
- String javaField (NotBlank)
- String isPk
- String isIncrement
- String isRequired
- String isInsert
- String isEdit
- String isList
- String isQuery
- String queryType
- String htmlType
- String dictType
- Integer sort

### Methods:
- void setColumnId(Long columnId)
- Long getColumnId()
- void setTableId(Long tableId)
- Long getTableId()
- void setColumnName(String columnName)
- String getColumnName()
- void setColumnComment(String columnComment)
- String getColumnComment()
- void setColumnType(String columnType)
- String getColumnType()
- void setJavaType(String javaType)
- String getJavaType()
- void setJavaField(String javaField)
- String getJavaField()
- String getCapJavaField()
- void setIsPk(String isPk)
- String getIsPk()
- boolean isPk()
- boolean isPk(String isPk)
- String getIsIncrement()
- void setIsIncrement(String isIncrement)
- boolean isIncrement()
- boolean isIncrement(String isIncrement)
- void setIsRequired(String isRequired)
- String getIsRequired()
- boolean isRequired()
- boolean isRequired(String isRequired)
- void setIsInsert(String isInsert)
- String getIsInsert()
- boolean isInsert()
- boolean isInsert(String isInsert)
- void setIsEdit(String isEdit)
- String getIsEdit()
- boolean isEdit()
- boolean isEdit(String isEdit)
- void setIsList(String isList)
- String getIsList()
- boolean isList()
- boolean isList(String isList)
- void setIsQuery(String isQuery)
- String getIsQuery()
- boolean isQuery()
- boolean isQuery(String isQuery)
- void setQueryType(String queryType)
- String getQueryType()
- String getHtmlType()
- void setHtmlType(String htmlType)
- void setDictType(String dictType)
- String getDictType()
- void setSort(Integer sort)
- Integer getSort()
- boolean isSuperColumn()
- boolean isSuperColumn(String javaField)
- boolean isUsableColumn()
- boolean isUsableColumn(String javaField)
- String readConverterExp()

### Go Implementation Suggestion:
```go
package domain

type GenTableColumn struct {
	SerialVersionUID int64
	ColumnId int64
	TableId int64
	ColumnName string
	ColumnComment string
	ColumnType string
	JavaType string
	JavaField string
	IsPk string
	IsIncrement string
	IsRequired string
	IsInsert string
	IsEdit string
	IsList string
	IsQuery string
	QueryType string
	HtmlType string
	DictType string
	Sort int
}

func (c *GenTableColumn) setColumnId(columnId int64) {
	// TODO: Implement method
}

func (c *GenTableColumn) getColumnId() int64 {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumn) setTableId(tableId int64) {
	// TODO: Implement method
}

func (c *GenTableColumn) getTableId() int64 {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumn) setColumnName(columnName string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getColumnName() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setColumnComment(columnComment string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getColumnComment() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setColumnType(columnType string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getColumnType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setJavaType(javaType string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getJavaType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setJavaField(javaField string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getJavaField() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) getCapJavaField() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setIsPk(isPk string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsPk() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isPk() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isPk(isPk string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) getIsIncrement() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setIsIncrement(isIncrement string) {
	// TODO: Implement method
}

func (c *GenTableColumn) isIncrement() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isIncrement(isIncrement string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setIsRequired(isRequired string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsRequired() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isRequired() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isRequired(isRequired string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setIsInsert(isInsert string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsInsert() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isInsert() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isInsert(isInsert string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setIsEdit(isEdit string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsEdit() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isEdit() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isEdit(isEdit string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setIsList(isList string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsList() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isList() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isList(isList string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setIsQuery(isQuery string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getIsQuery() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) isQuery() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isQuery(isQuery string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) setQueryType(queryType string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getQueryType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) getHtmlType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setHtmlType(htmlType string) {
	// TODO: Implement method
}

func (c *GenTableColumn) setDictType(dictType string) {
	// TODO: Implement method
}

func (c *GenTableColumn) getDictType() string {
	// TODO: Implement method
	return ""
}

func (c *GenTableColumn) setSort(sort int) {
	// TODO: Implement method
}

func (c *GenTableColumn) getSort() int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumn) isSuperColumn() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isSuperColumn(javaField string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isUsableColumn() bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) isUsableColumn(javaField string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableColumn) readConverterExp() string {
	// TODO: Implement method
	return ""
}

```

