# Package: com.ruoyi.generator.mapper

## Class: GenTableColumnMapper

### Fields:

### Methods:
- List<GenTableColumn> selectDbTableColumnsByName(String tableName)
- List<GenTableColumn> selectGenTableColumnListByTableId(Long tableId)
- int insertGenTableColumn(GenTableColumn genTableColumn)
- int updateGenTableColumn(GenTableColumn genTableColumn)
- int deleteGenTableColumns(List<GenTableColumn> genTableColumns)
- int deleteGenTableColumnByIds(Long[] ids)

### Go Implementation Suggestion:
```go
package mapper

type GenTableColumnMapper struct {
}

func (c *GenTableColumnMapper) selectDbTableColumnsByName(tableName string) []GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *GenTableColumnMapper) selectGenTableColumnListByTableId(tableId int64) []GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *GenTableColumnMapper) insertGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumnMapper) updateGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumnMapper) deleteGenTableColumns(genTableColumns []GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumnMapper) deleteGenTableColumnByIds(ids []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: GenTableMapper

### Fields:

### Methods:
- List<GenTable> selectGenTableList(GenTable genTable)
- List<GenTable> selectDbTableList(GenTable genTable)
- List<GenTable> selectDbTableListByNames(String[] tableNames)
- List<GenTable> selectGenTableAll()
- GenTable selectGenTableById(Long id)
- GenTable selectGenTableByName(String tableName)
- int insertGenTable(GenTable genTable)
- int updateGenTable(GenTable genTable)
- int deleteGenTableByIds(Long[] ids)
- int createTable(String sql)

### Go Implementation Suggestion:
```go
package mapper

type GenTableMapper struct {
}

func (c *GenTableMapper) selectGenTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) selectDbTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) selectDbTableListByNames(tableNames []string) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) selectGenTableAll() []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) selectGenTableById(id int64) GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) selectGenTableByName(tableName string) GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableMapper) insertGenTable(genTable GenTable) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableMapper) updateGenTable(genTable GenTable) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableMapper) deleteGenTableByIds(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableMapper) createTable(sql string) int {
	// TODO: Implement method
	return 0
}

```

