# Package: com.ruoyi.generator.service

## Class: GenTableColumnServiceImpl

Implements: IGenTableColumnService

### Fields:
- GenTableColumnMapper genTableColumnMapper (Autowired)

### Methods:
- List<GenTableColumn> selectGenTableColumnListByTableId(Long tableId) (Override)
- int insertGenTableColumn(GenTableColumn genTableColumn) (Override)
- int updateGenTableColumn(GenTableColumn genTableColumn) (Override)
- int deleteGenTableColumnByIds(String ids) (Override)

### Go Implementation Suggestion:
```go
package service

type GenTableColumnServiceImpl struct {
	GenTableColumnMapper GenTableColumnMapper
}

func (c *GenTableColumnServiceImpl) selectGenTableColumnListByTableId(tableId int64) []GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *GenTableColumnServiceImpl) insertGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumnServiceImpl) updateGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *GenTableColumnServiceImpl) deleteGenTableColumnByIds(ids string) int {
	// TODO: Implement method
	return 0
}

```

## Class: GenTableServiceImpl

Implements: IGenTableService

### Fields:
- Logger log
- GenTableMapper genTableMapper (Autowired)
- GenTableColumnMapper genTableColumnMapper (Autowired)

### Methods:
- GenTable selectGenTableById(Long id) (Override)
- List<GenTable> selectGenTableList(GenTable genTable) (Override)
- List<GenTable> selectDbTableList(GenTable genTable) (Override)
- List<GenTable> selectDbTableListByNames(String[] tableNames) (Override)
- List<GenTable> selectGenTableAll() (Override)
- void updateGenTable(GenTable genTable) (Override, Transactional)
- void deleteGenTableByIds(Long[] tableIds) (Override, Transactional)
- boolean createTable(String sql) (Override)
- void importGenTable(List<GenTable> tableList, String operName) (Override, Transactional)
- Map<String, String> previewCode(Long tableId) (Override)
- byte[] downloadCode(String tableName) (Override)
- void generatorCode(String tableName) (Override)
- void synchDb(String tableName) (Override, Transactional)
- byte[] downloadCode(String[] tableNames) (Override)
- void generatorCode(String tableName, ZipOutputStream zip)
- void validateEdit(GenTable genTable) (Override)
- void setPkColumn(GenTable table)
- void setSubTable(GenTable table)
- void setTableFromOptions(GenTable genTable)
- String getGenPath(GenTable table, String template)

### Go Implementation Suggestion:
```go
package service

type GenTableServiceImpl struct {
	Log Logger
	GenTableMapper GenTableMapper
	GenTableColumnMapper GenTableColumnMapper
}

func (c *GenTableServiceImpl) selectGenTableById(id int64) GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) selectGenTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) selectDbTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) selectDbTableListByNames(tableNames []string) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) selectGenTableAll() []GenTable {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) updateGenTable(genTable GenTable) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) deleteGenTableByIds(tableIds []int64) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) createTable(sql string) bool {
	// TODO: Implement method
	return false
}

func (c *GenTableServiceImpl) importGenTable(tableList []GenTable, operName string) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) previewCode(tableId int64) map[string]string {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) downloadCode(tableName string) []byte {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) generatorCode(tableName string) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) synchDb(tableName string) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) downloadCode(tableNames []string) []byte {
	// TODO: Implement method
	return nil
}

func (c *GenTableServiceImpl) generatorCode(tableName string, zip ZipOutputStream) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) validateEdit(genTable GenTable) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) setPkColumn(table GenTable) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) setSubTable(table GenTable) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) setTableFromOptions(genTable GenTable) {
	// TODO: Implement method
}

func (c *GenTableServiceImpl) getGenPath(table GenTable, template string) string {
	// TODO: Implement method
	return ""
}

```

## Class: IGenTableColumnService

### Fields:

### Methods:
- List<GenTableColumn> selectGenTableColumnListByTableId(Long tableId)
- int insertGenTableColumn(GenTableColumn genTableColumn)
- int updateGenTableColumn(GenTableColumn genTableColumn)
- int deleteGenTableColumnByIds(String ids)

### Go Implementation Suggestion:
```go
package service

type IGenTableColumnService struct {
}

func (c *IGenTableColumnService) selectGenTableColumnListByTableId(tableId int64) []GenTableColumn {
	// TODO: Implement method
	return nil
}

func (c *IGenTableColumnService) insertGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *IGenTableColumnService) updateGenTableColumn(genTableColumn GenTableColumn) int {
	// TODO: Implement method
	return 0
}

func (c *IGenTableColumnService) deleteGenTableColumnByIds(ids string) int {
	// TODO: Implement method
	return 0
}

```

## Class: IGenTableService

### Fields:

### Methods:
- List<GenTable> selectGenTableList(GenTable genTable)
- List<GenTable> selectDbTableList(GenTable genTable)
- List<GenTable> selectDbTableListByNames(String[] tableNames)
- List<GenTable> selectGenTableAll()
- GenTable selectGenTableById(Long id)
- void updateGenTable(GenTable genTable)
- void deleteGenTableByIds(Long[] tableIds)
- boolean createTable(String sql)
- void importGenTable(List<GenTable> tableList, String operName)
- Map<String, String> previewCode(Long tableId)
- byte[] downloadCode(String tableName)
- void generatorCode(String tableName)
- void synchDb(String tableName)
- byte[] downloadCode(String[] tableNames)
- void validateEdit(GenTable genTable)

### Go Implementation Suggestion:
```go
package service

type IGenTableService struct {
}

func (c *IGenTableService) selectGenTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) selectDbTableList(genTable GenTable) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) selectDbTableListByNames(tableNames []string) []GenTable {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) selectGenTableAll() []GenTable {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) selectGenTableById(id int64) GenTable {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) updateGenTable(genTable GenTable) {
	// TODO: Implement method
}

func (c *IGenTableService) deleteGenTableByIds(tableIds []int64) {
	// TODO: Implement method
}

func (c *IGenTableService) createTable(sql string) bool {
	// TODO: Implement method
	return false
}

func (c *IGenTableService) importGenTable(tableList []GenTable, operName string) {
	// TODO: Implement method
}

func (c *IGenTableService) previewCode(tableId int64) map[string]string {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) downloadCode(tableName string) []byte {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) generatorCode(tableName string) {
	// TODO: Implement method
}

func (c *IGenTableService) synchDb(tableName string) {
	// TODO: Implement method
}

func (c *IGenTableService) downloadCode(tableNames []string) []byte {
	// TODO: Implement method
	return nil
}

func (c *IGenTableService) validateEdit(genTable GenTable) {
	// TODO: Implement method
}

```

