# Package: com.ruoyi.generator.util

## Class: GenUtils

### Fields:

### Methods:
- void initTable(GenTable genTable, String operName)
- void initColumnField(GenTableColumn column, GenTable table)
- boolean arraysContains(String[] arr, String targetValue)
- String getModuleName(String packageName)
- String getBusinessName(String tableName)
- String convertClassName(String tableName)
- String replaceFirst(String replacementm, String[] searchList)
- String replaceText(String text)
- String getDbType(String columnType)
- Integer getColumnLength(String columnType)

### Go Implementation Suggestion:
```go
package util

type GenUtils struct {
}

func (c *GenUtils) initTable(genTable GenTable, operName string) {
	// TODO: Implement method
}

func (c *GenUtils) initColumnField(column GenTableColumn, table GenTable) {
	// TODO: Implement method
}

func (c *GenUtils) arraysContains(arr []string, targetValue string) bool {
	// TODO: Implement method
	return false
}

func (c *GenUtils) getModuleName(packageName string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) getBusinessName(tableName string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) convertClassName(tableName string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) replaceFirst(replacementm string, searchList []string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) replaceText(text string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) getDbType(columnType string) string {
	// TODO: Implement method
	return ""
}

func (c *GenUtils) getColumnLength(columnType string) int {
	// TODO: Implement method
	return 0
}

```

## Class: VelocityInitializer

### Fields:

### Methods:
- void initVelocity()

### Go Implementation Suggestion:
```go
package util

type VelocityInitializer struct {
}

func (c *VelocityInitializer) initVelocity() {
	// TODO: Implement method
}

```

## Class: VelocityUtils

### Fields:
- String PROJECT_PATH
- String MYBATIS_PATH
- String DEFAULT_PARENT_MENU_ID

### Methods:
- VelocityContext prepareContext(GenTable genTable)
- void setMenuVelocityContext(VelocityContext context, GenTable genTable)
- void setTreeVelocityContext(VelocityContext context, GenTable genTable)
- void setSubVelocityContext(VelocityContext context, GenTable genTable)
- List<String> getTemplateList(String tplCategory, String tplWebType)
- String getFileName(String template, GenTable genTable)
- String getPackagePrefix(String packageName)
- HashSet<String> getImportList(GenTable genTable)
- String getDicts(GenTable genTable)
- void addDicts(Set<String> dicts, List<GenTableColumn> columns)
- String getPermissionPrefix(String moduleName, String businessName)
- String getParentMenuId(JSONObject paramsObj)
- String getTreecode(JSONObject paramsObj)
- String getTreeParentCode(JSONObject paramsObj)
- String getTreeName(JSONObject paramsObj)
- int getExpandColumn(GenTable genTable)

### Go Implementation Suggestion:
```go
package util

type VelocityUtils struct {
	PROJECT_PATH string
	MYBATIS_PATH string
	DEFAULT_PARENT_MENU_ID string
}

func (c *VelocityUtils) prepareContext(genTable GenTable) VelocityContext {
	// TODO: Implement method
	return nil
}

func (c *VelocityUtils) setMenuVelocityContext(context VelocityContext, genTable GenTable) {
	// TODO: Implement method
}

func (c *VelocityUtils) setTreeVelocityContext(context VelocityContext, genTable GenTable) {
	// TODO: Implement method
}

func (c *VelocityUtils) setSubVelocityContext(context VelocityContext, genTable GenTable) {
	// TODO: Implement method
}

func (c *VelocityUtils) getTemplateList(tplCategory string, tplWebType string) []string {
	// TODO: Implement method
	return nil
}

func (c *VelocityUtils) getFileName(template string, genTable GenTable) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getPackagePrefix(packageName string) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getImportList(genTable GenTable) HashSet<String> {
	// TODO: Implement method
	return nil
}

func (c *VelocityUtils) getDicts(genTable GenTable) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) addDicts(dicts Set<String>, columns []GenTableColumn) {
	// TODO: Implement method
}

func (c *VelocityUtils) getPermissionPrefix(moduleName string, businessName string) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getParentMenuId(paramsObj JSONObject) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getTreecode(paramsObj JSONObject) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getTreeParentCode(paramsObj JSONObject) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getTreeName(paramsObj JSONObject) string {
	// TODO: Implement method
	return ""
}

func (c *VelocityUtils) getExpandColumn(genTable GenTable) int {
	// TODO: Implement method
	return 0
}

```

