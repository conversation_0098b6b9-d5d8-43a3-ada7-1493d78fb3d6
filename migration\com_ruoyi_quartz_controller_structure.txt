# Package: com.ruoyi.quartz.controller

## Class: SysJobController

Extends: BaseController

### Fields:
- ISysJobService jobService (Autowired)

### Methods:
- TableDataInfo list(SysJob sysJob) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysJob sysJob) (PreAuthorize, Log, PostMapping)
- AjaxResult getInfo(Long jobId) (PreAuthorize, GetMapping)
- AjaxResult add(SysJob job) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysJob job) (PreAuthorize, Log, PutMapping)
- AjaxResult changeStatus(SysJob job) (PreAuthorize, Log, PutMapping)
- AjaxResult run(SysJob job) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] jobIds) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package controller

type SysJobController struct {
	JobService ISysJobService
}

func (c *SysJobController) list(sysJob SysJob) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) export(response HttpServletResponse, sysJob SysJob) {
	// TODO: Implement method
}

func (c *SysJobController) getInfo(jobId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) add(job SysJob) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) edit(job SysJob) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) changeStatus(job SysJob) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) run(job SysJob) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobController) remove(jobIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysJobLogController

Extends: BaseController

### Fields:
- ISysJobLogService jobLogService (Autowired)

### Methods:
- TableDataInfo list(SysJobLog sysJobLog) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysJobLog sysJobLog) (PreAuthorize, Log, PostMapping)
- AjaxResult getInfo(Long jobLogId) (PreAuthorize, GetMapping)
- AjaxResult remove(Long[] jobLogIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult clean() (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package controller

type SysJobLogController struct {
	JobLogService ISysJobLogService
}

func (c *SysJobLogController) list(sysJobLog SysJobLog) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogController) export(response HttpServletResponse, sysJobLog SysJobLog) {
	// TODO: Implement method
}

func (c *SysJobLogController) getInfo(jobLogId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogController) remove(jobLogIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogController) clean() AjaxResult {
	// TODO: Implement method
	return nil
}

```

