# Package: com.ruoyi.quartz.domain

## Class: SysJob

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long jobId (Excel)
- String jobName (Excel)
- String jobGroup (Excel)
- String invokeTarget (Excel)
- String cronExpression (Excel)
- String misfirePolicy (Excel)
- String concurrent (Excel)
- String status (Excel)

### Methods:
- Long getJobId()
- void setJobId(Long jobId)
- String getJobName() (NotBlank, Size)
- void setJobName(String jobName)
- String getJobGroup()
- void setJobGroup(String jobGroup)
- String getInvokeTarget() (NotBlank, Size)
- void setInvokeTarget(String invokeTarget)
- String getCronExpression() (NotBlank, Size)
- void setCronExpression(String cronExpression)
- Date getNextValidTime() (JsonFormat)
- String getMisfirePolicy()
- void setMisfirePolicy(String misfirePolicy)
- String getConcurrent()
- void setConcurrent(String concurrent)
- String getStatus()
- void setStatus(String status)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysJob struct {
	SerialVersionUID int64
	JobId int64
	JobName string
	JobGroup string
	InvokeTarget string
	CronExpression string
	MisfirePolicy string
	Concurrent string
	Status string
}

func (c *SysJob) getJobId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysJob) setJobId(jobId int64) {
	// TODO: Implement method
}

func (c *SysJob) getJobName() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setJobName(jobName string) {
	// TODO: Implement method
}

func (c *SysJob) getJobGroup() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setJobGroup(jobGroup string) {
	// TODO: Implement method
}

func (c *SysJob) getInvokeTarget() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setInvokeTarget(invokeTarget string) {
	// TODO: Implement method
}

func (c *SysJob) getCronExpression() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setCronExpression(cronExpression string) {
	// TODO: Implement method
}

func (c *SysJob) getNextValidTime() Date {
	// TODO: Implement method
	return nil
}

func (c *SysJob) getMisfirePolicy() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setMisfirePolicy(misfirePolicy string) {
	// TODO: Implement method
}

func (c *SysJob) getConcurrent() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setConcurrent(concurrent string) {
	// TODO: Implement method
}

func (c *SysJob) getStatus() string {
	// TODO: Implement method
	return ""
}

func (c *SysJob) setStatus(status string) {
	// TODO: Implement method
}

func (c *SysJob) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysJobLog

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long jobLogId (Excel)
- String jobName (Excel)
- String jobGroup (Excel)
- String invokeTarget (Excel)
- String jobMessage (Excel)
- String status (Excel)
- String exceptionInfo (Excel)
- Date startTime
- Date stopTime

### Methods:
- Long getJobLogId()
- void setJobLogId(Long jobLogId)
- String getJobName()
- void setJobName(String jobName)
- String getJobGroup()
- void setJobGroup(String jobGroup)
- String getInvokeTarget()
- void setInvokeTarget(String invokeTarget)
- String getJobMessage()
- void setJobMessage(String jobMessage)
- String getStatus()
- void setStatus(String status)
- String getExceptionInfo()
- void setExceptionInfo(String exceptionInfo)
- Date getStartTime()
- void setStartTime(Date startTime)
- Date getStopTime()
- void setStopTime(Date stopTime)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysJobLog struct {
	SerialVersionUID int64
	JobLogId int64
	JobName string
	JobGroup string
	InvokeTarget string
	JobMessage string
	Status string
	ExceptionInfo string
	StartTime Date
	StopTime Date
}

func (c *SysJobLog) getJobLogId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysJobLog) setJobLogId(jobLogId int64) {
	// TODO: Implement method
}

func (c *SysJobLog) getJobName() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setJobName(jobName string) {
	// TODO: Implement method
}

func (c *SysJobLog) getJobGroup() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setJobGroup(jobGroup string) {
	// TODO: Implement method
}

func (c *SysJobLog) getInvokeTarget() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setInvokeTarget(invokeTarget string) {
	// TODO: Implement method
}

func (c *SysJobLog) getJobMessage() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setJobMessage(jobMessage string) {
	// TODO: Implement method
}

func (c *SysJobLog) getStatus() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setStatus(status string) {
	// TODO: Implement method
}

func (c *SysJobLog) getExceptionInfo() string {
	// TODO: Implement method
	return ""
}

func (c *SysJobLog) setExceptionInfo(exceptionInfo string) {
	// TODO: Implement method
}

func (c *SysJobLog) getStartTime() Date {
	// TODO: Implement method
	return nil
}

func (c *SysJobLog) setStartTime(startTime Date) {
	// TODO: Implement method
}

func (c *SysJobLog) getStopTime() Date {
	// TODO: Implement method
	return nil
}

func (c *SysJobLog) setStopTime(stopTime Date) {
	// TODO: Implement method
}

func (c *SysJobLog) toString() string {
	// TODO: Implement method
	return ""
}

```

