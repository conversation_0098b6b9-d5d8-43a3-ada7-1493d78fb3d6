# Package: com.ruoyi.quartz.mapper

## Class: SysJobLogMapper

### Fields:

### Methods:
- List<SysJobLog> selectJob<PERSON>ogList(SysJobLog jobLog)
- List<SysJobLog> selectJobLogAll()
- SysJobLog selectJobLogById(Long jobLogId)
- int insertJobLog(SysJobLog jobLog)
- int deleteJobLogByIds(Long[] logIds)
- int deleteJobLogById(Long jobId)
- void cleanJobLog()

### Go Implementation Suggestion:
```go
package mapper

type SysJobLogMapper struct {
}

func (c *SysJobLogMapper) selectJobLogList(jobLog SysJobLog) []SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogMapper) selectJobLogAll() []SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogMapper) selectJobLogById(jobLogId int64) SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogMapper) insertJobLog(jobLog SysJobLog) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobLogMapper) deleteJobLogByIds(logIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobLogMapper) deleteJobLogById(jobId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobLogMapper) cleanJobLog() {
	// TODO: Implement method
}

```

## Class: SysJobMapper

### Fields:

### Methods:
- List<SysJob> selectJobList(SysJob job)
- List<SysJob> selectJobAll()
- SysJob selectJobById(Long jobId)
- int deleteJobById(Long jobId)
- int deleteJobByIds(Long[] ids)
- int updateJob(SysJob job)
- int insertJob(SysJob job)

### Go Implementation Suggestion:
```go
package mapper

type SysJobMapper struct {
}

func (c *SysJobMapper) selectJobList(job SysJob) []SysJob {
	// TODO: Implement method
	return nil
}

func (c *SysJobMapper) selectJobAll() []SysJob {
	// TODO: Implement method
	return nil
}

func (c *SysJobMapper) selectJobById(jobId int64) SysJob {
	// TODO: Implement method
	return nil
}

func (c *SysJobMapper) deleteJobById(jobId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobMapper) deleteJobByIds(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobMapper) updateJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobMapper) insertJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

```

