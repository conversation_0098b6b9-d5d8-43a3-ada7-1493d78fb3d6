# Package: com.ruoyi.quartz.service.impl

## Class: SysJobLogServiceImpl

Implements: ISysJobLogService

### Fields:
- SysJobLogMapper jobLogMapper (Autowired)

### Methods:
- List<SysJobLog> selectJobLogList(SysJobLog jobLog) (Override)
- SysJobLog selectJobLogById(Long jobLogId) (Override)
- void addJobLog(SysJobLog jobLog) (Override)
- int deleteJobLogByIds(Long[] logIds) (Override)
- int deleteJobLogById(Long jobId) (Override)
- void cleanJobLog() (Override)

### Go Implementation Suggestion:
```go
package impl

type SysJobLogServiceImpl struct {
	JobLogMapper SysJobLogMapper
}

func (c *SysJobLogServiceImpl) selectJobLogList(jobLog SysJobLog) []SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogServiceImpl) selectJobLogById(jobLogId int64) SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *SysJobLogServiceImpl) addJobLog(jobLog SysJobLog) {
	// TODO: Implement method
}

func (c *SysJobLogServiceImpl) deleteJobLogByIds(logIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobLogServiceImpl) deleteJobLogById(jobId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobLogServiceImpl) cleanJobLog() {
	// TODO: Implement method
}

```

## Class: SysJobServiceImpl

Implements: ISysJobService

### Fields:
- Scheduler scheduler (Autowired)
- SysJobMapper jobMapper (Autowired)

### Methods:
- void init() (PostConstruct)
- List<SysJob> selectJobList(SysJob job) (Override)
- SysJob selectJobById(Long jobId) (Override)
- int pauseJob(SysJob job) (Override, Transactional)
- int resumeJob(SysJob job) (Override, Transactional)
- int deleteJob(SysJob job) (Override, Transactional)
- void deleteJobByIds(Long[] jobIds) (Override, Transactional)
- int changeStatus(SysJob job) (Override, Transactional)
- boolean run(SysJob job) (Override, Transactional)
- int insertJob(SysJob job) (Override, Transactional)
- int updateJob(SysJob job) (Override, Transactional)
- void updateSchedulerJob(SysJob job, String jobGroup)
- boolean checkCronExpressionIsValid(String cronExpression) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysJobServiceImpl struct {
	Scheduler Scheduler
	JobMapper SysJobMapper
}

func (c *SysJobServiceImpl) init() {
	// TODO: Implement method
}

func (c *SysJobServiceImpl) selectJobList(job SysJob) []SysJob {
	// TODO: Implement method
	return nil
}

func (c *SysJobServiceImpl) selectJobById(jobId int64) SysJob {
	// TODO: Implement method
	return nil
}

func (c *SysJobServiceImpl) pauseJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) resumeJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) deleteJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) deleteJobByIds(jobIds []int64) {
	// TODO: Implement method
}

func (c *SysJobServiceImpl) changeStatus(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) run(job SysJob) bool {
	// TODO: Implement method
	return false
}

func (c *SysJobServiceImpl) insertJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) updateJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *SysJobServiceImpl) updateSchedulerJob(job SysJob, jobGroup string) {
	// TODO: Implement method
}

func (c *SysJobServiceImpl) checkCronExpressionIsValid(cronExpression string) bool {
	// TODO: Implement method
	return false
}

```

