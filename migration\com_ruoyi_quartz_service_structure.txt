# Package: com.ruoyi.quartz.service

## Class: ISysJobLogService

### Fields:

### Methods:
- List<SysJobLog> selectJob<PERSON>ogList(SysJobLog jobLog)
- SysJobLog selectJobLogById(Long jobLogId)
- void addJobLog(SysJobLog jobLog)
- int deleteJobLogByIds(Long[] logIds)
- int deleteJobLogById(Long jobId)
- void cleanJobLog()

### Go Implementation Suggestion:
```go
package service

type ISysJobLogService struct {
}

func (c *ISysJobLogService) selectJobLogList(jobLog SysJobLog) []SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *ISysJobLogService) selectJobLogById(jobLogId int64) SysJobLog {
	// TODO: Implement method
	return nil
}

func (c *ISysJobLogService) addJobLog(jobLog SysJobLog) {
	// TODO: Implement method
}

func (c *ISysJobLogService) deleteJobLogByIds(logIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobLogService) deleteJobLogById(jobId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobLogService) cleanJobLog() {
	// TODO: Implement method
}

```

## Class: ISysJobService

### Fields:

### Methods:
- List<SysJob> selectJobList(SysJob job)
- SysJob selectJobById(Long jobId)
- int pauseJob(SysJob job)
- int resumeJob(SysJob job)
- int deleteJob(SysJob job)
- void deleteJobByIds(Long[] jobIds)
- int changeStatus(SysJob job)
- boolean run(SysJob job)
- int insertJob(SysJob job)
- int updateJob(SysJob job)
- boolean checkCronExpressionIsValid(String cronExpression)

### Go Implementation Suggestion:
```go
package service

type ISysJobService struct {
}

func (c *ISysJobService) selectJobList(job SysJob) []SysJob {
	// TODO: Implement method
	return nil
}

func (c *ISysJobService) selectJobById(jobId int64) SysJob {
	// TODO: Implement method
	return nil
}

func (c *ISysJobService) pauseJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) resumeJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) deleteJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) deleteJobByIds(jobIds []int64) {
	// TODO: Implement method
}

func (c *ISysJobService) changeStatus(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) run(job SysJob) bool {
	// TODO: Implement method
	return false
}

func (c *ISysJobService) insertJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) updateJob(job SysJob) int {
	// TODO: Implement method
	return 0
}

func (c *ISysJobService) checkCronExpressionIsValid(cronExpression string) bool {
	// TODO: Implement method
	return false
}

```

