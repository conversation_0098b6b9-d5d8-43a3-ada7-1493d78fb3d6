# Package: com.ruoyi.quartz.task

## Class: RyTask

### Fields:

### Methods:
- void ryMultipleParams(String s, <PERSON>ole<PERSON> b, <PERSON> l, Double d, Integer i)
- void ryParams(String params)
- void ryNoParams()

### Go Implementation Suggestion:
```go
package task

type RyTask struct {
}

func (c *RyTask) ryMultipleParams(s string, b bool, l int64, d float64, i int) {
	// TODO: Implement method
}

func (c *RyTask) ryParams(params string) {
	// TODO: Implement method
}

func (c *RyTask) ryNoParams() {
	// TODO: Implement method
}

```

