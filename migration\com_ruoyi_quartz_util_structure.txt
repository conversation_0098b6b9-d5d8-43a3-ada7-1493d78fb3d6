# Package: com.ruoyi.quartz.util

## Class: AbstractQuartzJob

Implements: Job

### Fields:
- Logger log
- ThreadLocal<Date> threadLocal

### Methods:
- void execute(JobExecutionContext context) (Override)
- void before(JobExecutionContext context, SysJob sysJob)
- void after(JobExecutionContext context, SysJob sysJob, Exception e)
- void doExecute(JobExecutionContext context, SysJob sysJob)

### Go Implementation Suggestion:
```go
package util

type AbstractQuartzJob struct {
	Log Logger
	ThreadLocal ThreadLocal<Date>
}

func (c *AbstractQuartzJob) execute(context JobExecutionContext) {
	// TODO: Implement method
}

func (c *AbstractQuartzJob) before(context JobExecutionContext, sysJob SysJob) {
	// TODO: Implement method
}

func (c *AbstractQuartzJob) after(context JobExecutionContext, sys<PERSON>ob <PERSON>ysJob, e Exception) {
	// TODO: Implement method
}

func (c *AbstractQuartzJob) doExecute(context JobExecutionContext, sysJob SysJob) {
	// TODO: Implement method
}

```

## Class: CronUtils

### Fields:

### Methods:
- boolean isValid(String cronExpression)
- String getInvalidMessage(String cronExpression)
- Date getNextExecution(String cronExpression)

### Go Implementation Suggestion:
```go
package util

type CronUtils struct {
}

func (c *CronUtils) isValid(cronExpression string) bool {
	// TODO: Implement method
	return false
}

func (c *CronUtils) getInvalidMessage(cronExpression string) string {
	// TODO: Implement method
	return ""
}

func (c *CronUtils) getNextExecution(cronExpression string) Date {
	// TODO: Implement method
	return nil
}

```

## Class: JobInvokeUtil

### Fields:

### Methods:
- void invokeMethod(SysJob sysJob)
- void invokeMethod(Object bean, String methodName, List<Object[]> methodParams)
- boolean isValidClassName(String invokeTarget)
- String getBeanName(String invokeTarget)
- String getMethodName(String invokeTarget)
- List<Object[]> getMethodParams(String invokeTarget)
- Class<?>[] getMethodParamsType(List<Object[]> methodParams)
- Object[] getMethodParamsValue(List<Object[]> methodParams)

### Go Implementation Suggestion:
```go
package util

type JobInvokeUtil struct {
}

func (c *JobInvokeUtil) invokeMethod(sysJob SysJob) {
	// TODO: Implement method
}

func (c *JobInvokeUtil) invokeMethod(bean interface{}, methodName string, methodParams [][]interface{}) {
	// TODO: Implement method
}

func (c *JobInvokeUtil) isValidClassName(invokeTarget string) bool {
	// TODO: Implement method
	return false
}

func (c *JobInvokeUtil) getBeanName(invokeTarget string) string {
	// TODO: Implement method
	return ""
}

func (c *JobInvokeUtil) getMethodName(invokeTarget string) string {
	// TODO: Implement method
	return ""
}

func (c *JobInvokeUtil) getMethodParams(invokeTarget string) [][]interface{} {
	// TODO: Implement method
	return nil
}

func (c *JobInvokeUtil) getMethodParamsType(methodParams [][]interface{}) []Class<?> {
	// TODO: Implement method
	return nil
}

func (c *JobInvokeUtil) getMethodParamsValue(methodParams [][]interface{}) []interface{} {
	// TODO: Implement method
	return nil
}

```

## Class: QuartzDisallowConcurrentExecution

Extends: AbstractQuartzJob

### Fields:

### Methods:
- void doExecute(JobExecutionContext context, SysJob sysJob) (Override)

### Go Implementation Suggestion:
```go
package util

type QuartzDisallowConcurrentExecution struct {
}

func (c *QuartzDisallowConcurrentExecution) doExecute(context JobExecutionContext, sysJob SysJob) {
	// TODO: Implement method
}

```

## Class: QuartzJobExecution

Extends: AbstractQuartzJob

### Fields:

### Methods:
- void doExecute(JobExecutionContext context, SysJob sysJob) (Override)

### Go Implementation Suggestion:
```go
package util

type QuartzJobExecution struct {
}

func (c *QuartzJobExecution) doExecute(context JobExecutionContext, sysJob SysJob) {
	// TODO: Implement method
}

```

## Class: ScheduleUtils

### Fields:

### Methods:
- Class<? extends Job> getQuartzJobClass(SysJob sysJob)
- TriggerKey getTriggerKey(Long jobId, String jobGroup)
- JobKey getJobKey(Long jobId, String jobGroup)
- void createScheduleJob(Scheduler scheduler, SysJob job)
- CronScheduleBuilder handleCronScheduleMisfirePolicy(SysJob job, CronScheduleBuilder cb)
- boolean whiteList(String invokeTarget)

### Go Implementation Suggestion:
```go
package util

type ScheduleUtils struct {
}

func (c *ScheduleUtils) getQuartzJobClass(sysJob SysJob) Class<? extends Job> {
	// TODO: Implement method
	return nil
}

func (c *ScheduleUtils) getTriggerKey(jobId int64, jobGroup string) TriggerKey {
	// TODO: Implement method
	return nil
}

func (c *ScheduleUtils) getJobKey(jobId int64, jobGroup string) JobKey {
	// TODO: Implement method
	return nil
}

func (c *ScheduleUtils) createScheduleJob(scheduler Scheduler, job SysJob) {
	// TODO: Implement method
}

func (c *ScheduleUtils) handleCronScheduleMisfirePolicy(job SysJob, cb CronScheduleBuilder) CronScheduleBuilder {
	// TODO: Implement method
	return nil
}

func (c *ScheduleUtils) whiteList(invokeTarget string) bool {
	// TODO: Implement method
	return false
}

```

