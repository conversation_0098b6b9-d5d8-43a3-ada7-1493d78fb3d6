# Package: com.ruoyi.system.domain

## Class: SysCache

### Fields:
- String cacheName
- String cacheKey
- String cacheValue
- String remark

### Methods:
- String getCacheName()
- void setCacheName(String cacheName)
- String getCacheKey()
- void setCacheKey(String cacheKey)
- String getCacheValue()
- void setCacheValue(String cacheValue)
- String getRemark()
- void setRemark(String remark)

### Go Implementation Suggestion:
```go
package domain

type SysCache struct {
	CacheName string
	CacheKey string
	CacheValue string
	Remark string
}

func (c *SysCache) getCacheName() string {
	// TODO: Implement method
	return ""
}

func (c *SysCache) setCacheName(cacheName string) {
	// TODO: Implement method
}

func (c *SysCache) getCacheKey() string {
	// TODO: Implement method
	return ""
}

func (c *SysCache) setCacheKey(cacheKey string) {
	// TODO: Implement method
}

func (c *SysCache) getCacheValue() string {
	// TODO: Implement method
	return ""
}

func (c *SysCache) setCacheValue(cacheValue string) {
	// TODO: Implement method
}

func (c *SysCache) getRemark() string {
	// TODO: Implement method
	return ""
}

func (c *SysCache) setRemark(remark string) {
	// TODO: Implement method
}

```

## Class: SysConfig

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long configId (Excel)
- String configName (Excel)
- String configKey (Excel)
- String configValue (Excel)
- String configType (Excel)

### Methods:
- Long getConfigId()
- void setConfigId(Long configId)
- String getConfigName() (NotBlank, Size)
- void setConfigName(String configName)
- String getConfigKey() (NotBlank, Size)
- void setConfigKey(String configKey)
- String getConfigValue() (NotBlank, Size)
- void setConfigValue(String configValue)
- String getConfigType()
- void setConfigType(String configType)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysConfig struct {
	SerialVersionUID int64
	ConfigId int64
	ConfigName string
	ConfigKey string
	ConfigValue string
	ConfigType string
}

func (c *SysConfig) getConfigId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysConfig) setConfigId(configId int64) {
	// TODO: Implement method
}

func (c *SysConfig) getConfigName() string {
	// TODO: Implement method
	return ""
}

func (c *SysConfig) setConfigName(configName string) {
	// TODO: Implement method
}

func (c *SysConfig) getConfigKey() string {
	// TODO: Implement method
	return ""
}

func (c *SysConfig) setConfigKey(configKey string) {
	// TODO: Implement method
}

func (c *SysConfig) getConfigValue() string {
	// TODO: Implement method
	return ""
}

func (c *SysConfig) setConfigValue(configValue string) {
	// TODO: Implement method
}

func (c *SysConfig) getConfigType() string {
	// TODO: Implement method
	return ""
}

func (c *SysConfig) setConfigType(configType string) {
	// TODO: Implement method
}

func (c *SysConfig) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysLogininfor

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long infoId (Excel)
- String userName (Excel)
- String status (Excel)
- String ipaddr (Excel)
- String loginLocation (Excel)
- String browser (Excel)
- String os (Excel)
- String msg (Excel)
- Date loginTime (JsonFormat, Excel)

### Methods:
- Long getInfoId()
- void setInfoId(Long infoId)
- String getUserName()
- void setUserName(String userName)
- String getStatus()
- void setStatus(String status)
- String getIpaddr()
- void setIpaddr(String ipaddr)
- String getLoginLocation()
- void setLoginLocation(String loginLocation)
- String getBrowser()
- void setBrowser(String browser)
- String getOs()
- void setOs(String os)
- String getMsg()
- void setMsg(String msg)
- Date getLoginTime()
- void setLoginTime(Date loginTime)

### Go Implementation Suggestion:
```go
package domain

type SysLogininfor struct {
	SerialVersionUID int64
	InfoId int64
	UserName string
	Status string
	Ipaddr string
	LoginLocation string
	Browser string
	Os string
	Msg string
	LoginTime Date
}

func (c *SysLogininfor) getInfoId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysLogininfor) setInfoId(infoId int64) {
	// TODO: Implement method
}

func (c *SysLogininfor) getUserName() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setUserName(userName string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getStatus() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setStatus(status string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getIpaddr() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setIpaddr(ipaddr string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getLoginLocation() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setLoginLocation(loginLocation string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getBrowser() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setBrowser(browser string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getOs() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setOs(os string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getMsg() string {
	// TODO: Implement method
	return ""
}

func (c *SysLogininfor) setMsg(msg string) {
	// TODO: Implement method
}

func (c *SysLogininfor) getLoginTime() Date {
	// TODO: Implement method
	return nil
}

func (c *SysLogininfor) setLoginTime(loginTime Date) {
	// TODO: Implement method
}

```

## Class: SysNotice

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long noticeId
- String noticeTitle
- String noticeType
- String noticeContent
- String status

### Methods:
- Long getNoticeId()
- void setNoticeId(Long noticeId)
- void setNoticeTitle(String noticeTitle)
- String getNoticeTitle() (Xss, NotBlank, Size)
- void setNoticeType(String noticeType)
- String getNoticeType()
- void setNoticeContent(String noticeContent)
- String getNoticeContent()
- void setStatus(String status)
- String getStatus()
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysNotice struct {
	SerialVersionUID int64
	NoticeId int64
	NoticeTitle string
	NoticeType string
	NoticeContent string
	Status string
}

func (c *SysNotice) getNoticeId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysNotice) setNoticeId(noticeId int64) {
	// TODO: Implement method
}

func (c *SysNotice) setNoticeTitle(noticeTitle string) {
	// TODO: Implement method
}

func (c *SysNotice) getNoticeTitle() string {
	// TODO: Implement method
	return ""
}

func (c *SysNotice) setNoticeType(noticeType string) {
	// TODO: Implement method
}

func (c *SysNotice) getNoticeType() string {
	// TODO: Implement method
	return ""
}

func (c *SysNotice) setNoticeContent(noticeContent string) {
	// TODO: Implement method
}

func (c *SysNotice) getNoticeContent() string {
	// TODO: Implement method
	return ""
}

func (c *SysNotice) setStatus(status string) {
	// TODO: Implement method
}

func (c *SysNotice) getStatus() string {
	// TODO: Implement method
	return ""
}

func (c *SysNotice) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysOperLog

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long operId (Excel)
- String title (Excel)
- Integer businessType (Excel)
- Integer businessTypes
- String method (Excel)
- String requestMethod (Excel)
- Integer operatorType (Excel)
- String operName (Excel)
- String deptName (Excel)
- String operUrl (Excel)
- String operIp (Excel)
- String operLocation (Excel)
- String operParam (Excel)
- String jsonResult (Excel)
- Integer status (Excel)
- String errorMsg (Excel)
- Date operTime (JsonFormat, Excel)
- Long costTime (Excel)

### Methods:
- Long getOperId()
- void setOperId(Long operId)
- String getTitle()
- void setTitle(String title)
- Integer getBusinessType()
- void setBusinessType(Integer businessType)
- Integer[] getBusinessTypes()
- void setBusinessTypes(Integer[] businessTypes)
- String getMethod()
- void setMethod(String method)
- String getRequestMethod()
- void setRequestMethod(String requestMethod)
- Integer getOperatorType()
- void setOperatorType(Integer operatorType)
- String getOperName()
- void setOperName(String operName)
- String getDeptName()
- void setDeptName(String deptName)
- String getOperUrl()
- void setOperUrl(String operUrl)
- String getOperIp()
- void setOperIp(String operIp)
- String getOperLocation()
- void setOperLocation(String operLocation)
- String getOperParam()
- void setOperParam(String operParam)
- String getJsonResult()
- void setJsonResult(String jsonResult)
- Integer getStatus()
- void setStatus(Integer status)
- String getErrorMsg()
- void setErrorMsg(String errorMsg)
- Date getOperTime()
- void setOperTime(Date operTime)
- Long getCostTime()
- void setCostTime(Long costTime)

### Go Implementation Suggestion:
```go
package domain

type SysOperLog struct {
	SerialVersionUID int64
	OperId int64
	Title string
	BusinessType int
	BusinessTypes int
	Method string
	RequestMethod string
	OperatorType int
	OperName string
	DeptName string
	OperUrl string
	OperIp string
	OperLocation string
	OperParam string
	JsonResult string
	Status int
	ErrorMsg string
	OperTime Date
	CostTime int64
}

func (c *SysOperLog) getOperId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysOperLog) setOperId(operId int64) {
	// TODO: Implement method
}

func (c *SysOperLog) getTitle() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setTitle(title string) {
	// TODO: Implement method
}

func (c *SysOperLog) getBusinessType() int {
	// TODO: Implement method
	return 0
}

func (c *SysOperLog) setBusinessType(businessType int) {
	// TODO: Implement method
}

func (c *SysOperLog) getBusinessTypes() []int {
	// TODO: Implement method
	return nil
}

func (c *SysOperLog) setBusinessTypes(businessTypes []int) {
	// TODO: Implement method
}

func (c *SysOperLog) getMethod() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setMethod(method string) {
	// TODO: Implement method
}

func (c *SysOperLog) getRequestMethod() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setRequestMethod(requestMethod string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperatorType() int {
	// TODO: Implement method
	return 0
}

func (c *SysOperLog) setOperatorType(operatorType int) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperName() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setOperName(operName string) {
	// TODO: Implement method
}

func (c *SysOperLog) getDeptName() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setDeptName(deptName string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperUrl() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setOperUrl(operUrl string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperIp() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setOperIp(operIp string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperLocation() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setOperLocation(operLocation string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperParam() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setOperParam(operParam string) {
	// TODO: Implement method
}

func (c *SysOperLog) getJsonResult() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setJsonResult(jsonResult string) {
	// TODO: Implement method
}

func (c *SysOperLog) getStatus() int {
	// TODO: Implement method
	return 0
}

func (c *SysOperLog) setStatus(status int) {
	// TODO: Implement method
}

func (c *SysOperLog) getErrorMsg() string {
	// TODO: Implement method
	return ""
}

func (c *SysOperLog) setErrorMsg(errorMsg string) {
	// TODO: Implement method
}

func (c *SysOperLog) getOperTime() Date {
	// TODO: Implement method
	return nil
}

func (c *SysOperLog) setOperTime(operTime Date) {
	// TODO: Implement method
}

func (c *SysOperLog) getCostTime() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysOperLog) setCostTime(costTime int64) {
	// TODO: Implement method
}

```

## Class: SysPost

Extends: BaseEntity

### Fields:
- long serialVersionUID
- Long postId (Excel)
- String postCode (Excel)
- String postName (Excel)
- Integer postSort (Excel)
- String status (Excel)
- boolean flag

### Methods:
- Long getPostId()
- void setPostId(Long postId)
- String getPostCode() (NotBlank, Size)
- void setPostCode(String postCode)
- String getPostName() (NotBlank, Size)
- void setPostName(String postName)
- Integer getPostSort() (NotNull)
- void setPostSort(Integer postSort)
- String getStatus()
- void setStatus(String status)
- boolean isFlag()
- void setFlag(boolean flag)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysPost struct {
	SerialVersionUID int64
	PostId int64
	PostCode string
	PostName string
	PostSort int
	Status string
	Flag bool
}

func (c *SysPost) getPostId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysPost) setPostId(postId int64) {
	// TODO: Implement method
}

func (c *SysPost) getPostCode() string {
	// TODO: Implement method
	return ""
}

func (c *SysPost) setPostCode(postCode string) {
	// TODO: Implement method
}

func (c *SysPost) getPostName() string {
	// TODO: Implement method
	return ""
}

func (c *SysPost) setPostName(postName string) {
	// TODO: Implement method
}

func (c *SysPost) getPostSort() int {
	// TODO: Implement method
	return 0
}

func (c *SysPost) setPostSort(postSort int) {
	// TODO: Implement method
}

func (c *SysPost) getStatus() string {
	// TODO: Implement method
	return ""
}

func (c *SysPost) setStatus(status string) {
	// TODO: Implement method
}

func (c *SysPost) isFlag() bool {
	// TODO: Implement method
	return false
}

func (c *SysPost) setFlag(flag bool) {
	// TODO: Implement method
}

func (c *SysPost) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysRoleDept

### Fields:
- Long roleId
- Long deptId

### Methods:
- Long getRoleId()
- void setRoleId(Long roleId)
- Long getDeptId()
- void setDeptId(Long deptId)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysRoleDept struct {
	RoleId int64
	DeptId int64
}

func (c *SysRoleDept) getRoleId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysRoleDept) setRoleId(roleId int64) {
	// TODO: Implement method
}

func (c *SysRoleDept) getDeptId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysRoleDept) setDeptId(deptId int64) {
	// TODO: Implement method
}

func (c *SysRoleDept) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysRoleMenu

### Fields:
- Long roleId
- Long menuId

### Methods:
- Long getRoleId()
- void setRoleId(Long roleId)
- Long getMenuId()
- void setMenuId(Long menuId)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysRoleMenu struct {
	RoleId int64
	MenuId int64
}

func (c *SysRoleMenu) getRoleId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMenu) setRoleId(roleId int64) {
	// TODO: Implement method
}

func (c *SysRoleMenu) getMenuId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMenu) setMenuId(menuId int64) {
	// TODO: Implement method
}

func (c *SysRoleMenu) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysUserOnline

### Fields:
- String tokenId
- String deptName
- String userName
- String ipaddr
- String loginLocation
- String browser
- String os
- Long loginTime

### Methods:
- String getTokenId()
- void setTokenId(String tokenId)
- String getDeptName()
- void setDeptName(String deptName)
- String getUserName()
- void setUserName(String userName)
- String getIpaddr()
- void setIpaddr(String ipaddr)
- String getLoginLocation()
- void setLoginLocation(String loginLocation)
- String getBrowser()
- void setBrowser(String browser)
- String getOs()
- void setOs(String os)
- Long getLoginTime()
- void setLoginTime(Long loginTime)

### Go Implementation Suggestion:
```go
package domain

type SysUserOnline struct {
	TokenId string
	DeptName string
	UserName string
	Ipaddr string
	LoginLocation string
	Browser string
	Os string
	LoginTime int64
}

func (c *SysUserOnline) getTokenId() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setTokenId(tokenId string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getDeptName() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setDeptName(deptName string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getUserName() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setUserName(userName string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getIpaddr() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setIpaddr(ipaddr string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getLoginLocation() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setLoginLocation(loginLocation string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getBrowser() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setBrowser(browser string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getOs() string {
	// TODO: Implement method
	return ""
}

func (c *SysUserOnline) setOs(os string) {
	// TODO: Implement method
}

func (c *SysUserOnline) getLoginTime() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysUserOnline) setLoginTime(loginTime int64) {
	// TODO: Implement method
}

```

## Class: SysUserPost

### Fields:
- Long userId
- Long postId

### Methods:
- Long getUserId()
- void setUserId(Long userId)
- Long getPostId()
- void setPostId(Long postId)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysUserPost struct {
	UserId int64
	PostId int64
}

func (c *SysUserPost) getUserId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysUserPost) setUserId(userId int64) {
	// TODO: Implement method
}

func (c *SysUserPost) getPostId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysUserPost) setPostId(postId int64) {
	// TODO: Implement method
}

func (c *SysUserPost) toString() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysUserRole

### Fields:
- Long userId
- Long roleId

### Methods:
- Long getUserId()
- void setUserId(Long userId)
- Long getRoleId()
- void setRoleId(Long roleId)
- String toString() (Override)

### Go Implementation Suggestion:
```go
package domain

type SysUserRole struct {
	UserId int64
	RoleId int64
}

func (c *SysUserRole) getUserId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysUserRole) setUserId(userId int64) {
	// TODO: Implement method
}

func (c *SysUserRole) getRoleId() int64 {
	// TODO: Implement method
	return 0
}

func (c *SysUserRole) setRoleId(roleId int64) {
	// TODO: Implement method
}

func (c *SysUserRole) toString() string {
	// TODO: Implement method
	return ""
}

```

