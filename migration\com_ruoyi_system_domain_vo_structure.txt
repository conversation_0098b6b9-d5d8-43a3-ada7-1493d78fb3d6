# Package: com.ruoyi.system.domain.vo

## Class: MetaVo

### Fields:
- String title
- String icon
- boolean noCache
- String link

### Methods:
- boolean isNoCache()
- void setNoCache(boolean noCache)
- String getTitle()
- void setTitle(String title)
- String getIcon()
- void setIcon(String icon)
- String getLink()
- void setLink(String link)

### Go Implementation Suggestion:
```go
package vo

type MetaVo struct {
	Title string
	Icon string
	NoCache bool
	Link string
}

func (c *MetaVo) isNoCache() bool {
	// TODO: Implement method
	return false
}

func (c *MetaVo) setNoCache(noCache bool) {
	// TODO: Implement method
}

func (c *MetaVo) getTitle() string {
	// TODO: Implement method
	return ""
}

func (c *MetaVo) setTitle(title string) {
	// TODO: Implement method
}

func (c *MetaVo) getIcon() string {
	// TODO: Implement method
	return ""
}

func (c *MetaVo) setIcon(icon string) {
	// TODO: Implement method
}

func (c *MetaVo) getLink() string {
	// TODO: Implement method
	return ""
}

func (c *MetaVo) setLink(link string) {
	// TODO: Implement method
}

```

## Class: RouterVo

### Fields:
- String name
- String path
- boolean hidden
- String redirect
- String component
- String query
- Boolean alwaysShow
- MetaVo meta
- List<RouterVo> children

### Methods:
- String getName()
- void setName(String name)
- String getPath()
- void setPath(String path)
- boolean getHidden()
- void setHidden(boolean hidden)
- String getRedirect()
- void setRedirect(String redirect)
- String getComponent()
- void setComponent(String component)
- String getQuery()
- void setQuery(String query)
- Boolean getAlwaysShow()
- void setAlwaysShow(Boolean alwaysShow)
- MetaVo getMeta()
- void setMeta(MetaVo meta)
- List<RouterVo> getChildren()
- void setChildren(List<RouterVo> children)

### Go Implementation Suggestion:
```go
package vo

type RouterVo struct {
	Name string
	Path string
	Hidden bool
	Redirect string
	Component string
	Query string
	AlwaysShow bool
	Meta MetaVo
	Children []RouterVo
}

func (c *RouterVo) getName() string {
	// TODO: Implement method
	return ""
}

func (c *RouterVo) setName(name string) {
	// TODO: Implement method
}

func (c *RouterVo) getPath() string {
	// TODO: Implement method
	return ""
}

func (c *RouterVo) setPath(path string) {
	// TODO: Implement method
}

func (c *RouterVo) getHidden() bool {
	// TODO: Implement method
	return false
}

func (c *RouterVo) setHidden(hidden bool) {
	// TODO: Implement method
}

func (c *RouterVo) getRedirect() string {
	// TODO: Implement method
	return ""
}

func (c *RouterVo) setRedirect(redirect string) {
	// TODO: Implement method
}

func (c *RouterVo) getComponent() string {
	// TODO: Implement method
	return ""
}

func (c *RouterVo) setComponent(component string) {
	// TODO: Implement method
}

func (c *RouterVo) getQuery() string {
	// TODO: Implement method
	return ""
}

func (c *RouterVo) setQuery(query string) {
	// TODO: Implement method
}

func (c *RouterVo) getAlwaysShow() bool {
	// TODO: Implement method
	return false
}

func (c *RouterVo) setAlwaysShow(alwaysShow bool) {
	// TODO: Implement method
}

func (c *RouterVo) getMeta() MetaVo {
	// TODO: Implement method
	return nil
}

func (c *RouterVo) setMeta(meta MetaVo) {
	// TODO: Implement method
}

func (c *RouterVo) getChildren() []RouterVo {
	// TODO: Implement method
	return nil
}

func (c *RouterVo) setChildren(children []RouterVo) {
	// TODO: Implement method
}

```

