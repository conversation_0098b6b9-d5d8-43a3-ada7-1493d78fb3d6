# Package: com.ruoyi.system.mapper

## Class: SysConfigMapper

### Fields:

### Methods:
- SysConfig selectConfig(SysConfig config)
- SysConfig selectConfigById(Long configId)
- List<SysConfig> selectConfigList(SysConfig config)
- SysConfig checkConfigKeyUnique(String configKey)
- int insertConfig(SysConfig config)
- int updateConfig(SysConfig config)
- int deleteConfigById(Long configId)
- int deleteConfigByIds(Long[] configIds)

### Go Implementation Suggestion:
```go
package mapper

type SysConfigMapper struct {
}

func (c *SysConfigMapper) selectConfig(config SysConfig) SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigMapper) selectConfigById(configId int64) SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigMapper) selectConfigList(config SysConfig) []SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigMapper) checkConfigKeyUnique(configKey string) SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigMapper) insertConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *SysConfigMapper) updateConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *SysConfigMapper) deleteConfigById(configId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysConfigMapper) deleteConfigByIds(configIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysDeptMapper

### Fields:

### Methods:
- List<SysDept> selectDeptList(SysDept dept)
- List<Long> selectDeptListByRoleId(Long roleId, boolean deptCheckStrictly)
- SysDept selectDeptById(Long deptId)
- List<SysDept> selectChildrenDeptById(Long deptId)
- int selectNormalChildrenDeptById(Long deptId)
- int hasChildByDeptId(Long deptId)
- int checkDeptExistUser(Long deptId)
- SysDept checkDeptNameUnique(String deptName, Long parentId)
- int insertDept(SysDept dept)
- int updateDept(SysDept dept)
- void updateDeptStatusNormal(Long[] deptIds)
- int updateDeptChildren(List<SysDept> depts)
- int deleteDeptById(Long deptId)

### Go Implementation Suggestion:
```go
package mapper

type SysDeptMapper struct {
}

func (c *SysDeptMapper) selectDeptList(dept SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptMapper) selectDeptListByRoleId(roleId int64, deptCheckStrictly bool) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysDeptMapper) selectDeptById(deptId int64) SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptMapper) selectChildrenDeptById(deptId int64) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptMapper) selectNormalChildrenDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) hasChildByDeptId(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) checkDeptExistUser(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) checkDeptNameUnique(deptName string, parentId int64) SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptMapper) insertDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) updateDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) updateDeptStatusNormal(deptIds []int64) {
	// TODO: Implement method
}

func (c *SysDeptMapper) updateDeptChildren(depts []SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptMapper) deleteDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysDictDataMapper

### Fields:

### Methods:
- List<SysDictData> selectDictDataList(SysDictData dictData)
- List<SysDictData> selectDictDataByType(String dictType)
- String selectDictLabel(String dictType, String dictValue)
- SysDictData selectDictDataById(Long dictCode)
- int countDictDataByType(String dictType)
- int deleteDictDataById(Long dictCode)
- int deleteDictDataByIds(Long[] dictCodes)
- int insertDictData(SysDictData dictData)
- int updateDictData(SysDictData dictData)
- int updateDictDataType(String oldDictType, String newDictType)

### Go Implementation Suggestion:
```go
package mapper

type SysDictDataMapper struct {
}

func (c *SysDictDataMapper) selectDictDataList(dictData SysDictData) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataMapper) selectDictDataByType(dictType string) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataMapper) selectDictLabel(dictType string, dictValue string) string {
	// TODO: Implement method
	return ""
}

func (c *SysDictDataMapper) selectDictDataById(dictCode int64) SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataMapper) countDictDataByType(dictType string) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataMapper) deleteDictDataById(dictCode int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataMapper) deleteDictDataByIds(dictCodes []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataMapper) insertDictData(dictData SysDictData) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataMapper) updateDictData(dictData SysDictData) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataMapper) updateDictDataType(oldDictType string, newDictType string) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysDictTypeMapper

### Fields:

### Methods:
- List<SysDictType> selectDictTypeList(SysDictType dictType)
- List<SysDictType> selectDictTypeAll()
- SysDictType selectDictTypeById(Long dictId)
- SysDictType selectDictTypeByType(String dictType)
- int deleteDictTypeById(Long dictId)
- int deleteDictTypeByIds(Long[] dictIds)
- int insertDictType(SysDictType dictType)
- int updateDictType(SysDictType dictType)
- SysDictType checkDictTypeUnique(String dictType)

### Go Implementation Suggestion:
```go
package mapper

type SysDictTypeMapper struct {
}

func (c *SysDictTypeMapper) selectDictTypeList(dictType SysDictType) []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeMapper) selectDictTypeAll() []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeMapper) selectDictTypeById(dictId int64) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeMapper) selectDictTypeByType(dictType string) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeMapper) deleteDictTypeById(dictId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeMapper) deleteDictTypeByIds(dictIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeMapper) insertDictType(dictType SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeMapper) updateDictType(dictType SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeMapper) checkDictTypeUnique(dictType string) SysDictType {
	// TODO: Implement method
	return nil
}

```

## Class: SysLogininforMapper

### Fields:

### Methods:
- void insertLogininfor(SysLogininfor logininfor)
- List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
- int deleteLogininforByIds(Long[] infoIds)
- int cleanLogininfor()

### Go Implementation Suggestion:
```go
package mapper

type SysLogininforMapper struct {
}

func (c *SysLogininforMapper) insertLogininfor(logininfor SysLogininfor) {
	// TODO: Implement method
}

func (c *SysLogininforMapper) selectLogininforList(logininfor SysLogininfor) []SysLogininfor {
	// TODO: Implement method
	return nil
}

func (c *SysLogininforMapper) deleteLogininforByIds(infoIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysLogininforMapper) cleanLogininfor() int {
	// TODO: Implement method
	return 0
}

```

## Class: SysMenuMapper

### Fields:

### Methods:
- List<SysMenu> selectMenuList(SysMenu menu)
- List<String> selectMenuPerms()
- List<SysMenu> selectMenuListByUserId(SysMenu menu)
- List<String> selectMenuPermsByRoleId(Long roleId)
- List<String> selectMenuPermsByUserId(Long userId)
- List<SysMenu> selectMenuTreeAll()
- List<SysMenu> selectMenuTreeByUserId(Long userId)
- List<Long> selectMenuListByRoleId(Long roleId, boolean menuCheckStrictly)
- SysMenu selectMenuById(Long menuId)
- int hasChildByMenuId(Long menuId)
- int insertMenu(SysMenu menu)
- int updateMenu(SysMenu menu)
- int deleteMenuById(Long menuId)
- SysMenu checkMenuNameUnique(String menuName, Long parentId)

### Go Implementation Suggestion:
```go
package mapper

type SysMenuMapper struct {
}

func (c *SysMenuMapper) selectMenuList(menu SysMenu) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuPerms() []string {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuListByUserId(menu SysMenu) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuPermsByRoleId(roleId int64) []string {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuPermsByUserId(userId int64) []string {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuTreeAll() []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuTreeByUserId(userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuListByRoleId(roleId int64, menuCheckStrictly bool) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) selectMenuById(menuId int64) SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuMapper) hasChildByMenuId(menuId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuMapper) insertMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuMapper) updateMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuMapper) deleteMenuById(menuId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuMapper) checkMenuNameUnique(menuName string, parentId int64) SysMenu {
	// TODO: Implement method
	return nil
}

```

## Class: SysNoticeMapper

### Fields:

### Methods:
- SysNotice selectNoticeById(Long noticeId)
- List<SysNotice> selectNoticeList(SysNotice notice)
- int insertNotice(SysNotice notice)
- int updateNotice(SysNotice notice)
- int deleteNoticeById(Long noticeId)
- int deleteNoticeByIds(Long[] noticeIds)

### Go Implementation Suggestion:
```go
package mapper

type SysNoticeMapper struct {
}

func (c *SysNoticeMapper) selectNoticeById(noticeId int64) SysNotice {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeMapper) selectNoticeList(notice SysNotice) []SysNotice {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeMapper) insertNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeMapper) updateNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeMapper) deleteNoticeById(noticeId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeMapper) deleteNoticeByIds(noticeIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysOperLogMapper

### Fields:

### Methods:
- void insertOperlog(SysOperLog operLog)
- List<SysOperLog> selectOperLogList(SysOperLog operLog)
- int deleteOperLogByIds(Long[] operIds)
- SysOperLog selectOperLogById(Long operId)
- void cleanOperLog()

### Go Implementation Suggestion:
```go
package mapper

type SysOperLogMapper struct {
}

func (c *SysOperLogMapper) insertOperlog(operLog SysOperLog) {
	// TODO: Implement method
}

func (c *SysOperLogMapper) selectOperLogList(operLog SysOperLog) []SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *SysOperLogMapper) deleteOperLogByIds(operIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysOperLogMapper) selectOperLogById(operId int64) SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *SysOperLogMapper) cleanOperLog() {
	// TODO: Implement method
}

```

## Class: SysPostMapper

### Fields:

### Methods:
- List<SysPost> selectPostList(SysPost post)
- List<SysPost> selectPostAll()
- SysPost selectPostById(Long postId)
- List<Long> selectPostListByUserId(Long userId)
- List<SysPost> selectPostsByUserName(String userName)
- int deletePostById(Long postId)
- int deletePostByIds(Long[] postIds)
- int updatePost(SysPost post)
- int insertPost(SysPost post)
- SysPost checkPostNameUnique(String postName)
- SysPost checkPostCodeUnique(String postCode)

### Go Implementation Suggestion:
```go
package mapper

type SysPostMapper struct {
}

func (c *SysPostMapper) selectPostList(post SysPost) []SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) selectPostAll() []SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) selectPostById(postId int64) SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) selectPostListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) selectPostsByUserName(userName string) []SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) deletePostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostMapper) deletePostByIds(postIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostMapper) updatePost(post SysPost) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostMapper) insertPost(post SysPost) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostMapper) checkPostNameUnique(postName string) SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostMapper) checkPostCodeUnique(postCode string) SysPost {
	// TODO: Implement method
	return nil
}

```

## Class: SysRoleDeptMapper

### Fields:

### Methods:
- int deleteRoleDeptByRoleId(Long roleId)
- int deleteRoleDept(Long[] ids)
- int selectCountRoleDeptByDeptId(Long deptId)
- int batchRoleDept(List<SysRoleDept> roleDeptList)

### Go Implementation Suggestion:
```go
package mapper

type SysRoleDeptMapper struct {
}

func (c *SysRoleDeptMapper) deleteRoleDeptByRoleId(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleDeptMapper) deleteRoleDept(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleDeptMapper) selectCountRoleDeptByDeptId(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleDeptMapper) batchRoleDept(roleDeptList []SysRoleDept) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysRoleMapper

### Fields:

### Methods:
- List<SysRole> selectRoleList(SysRole role)
- List<SysRole> selectRolePermissionByUserId(Long userId)
- List<SysRole> selectRoleAll()
- List<Long> selectRoleListByUserId(Long userId)
- SysRole selectRoleById(Long roleId)
- List<SysRole> selectRolesByUserName(String userName)
- SysRole checkRoleNameUnique(String roleName)
- SysRole checkRoleKeyUnique(String roleKey)
- int updateRole(SysRole role)
- int insertRole(SysRole role)
- int deleteRoleById(Long roleId)
- int deleteRoleByIds(Long[] roleIds)

### Go Implementation Suggestion:
```go
package mapper

type SysRoleMapper struct {
}

func (c *SysRoleMapper) selectRoleList(role SysRole) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) selectRolePermissionByUserId(userId int64) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) selectRoleAll() []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) selectRoleListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) selectRoleById(roleId int64) SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) selectRolesByUserName(userName string) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) checkRoleNameUnique(roleName string) SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) checkRoleKeyUnique(roleKey string) SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleMapper) updateRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMapper) insertRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMapper) deleteRoleById(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMapper) deleteRoleByIds(roleIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysRoleMenuMapper

### Fields:

### Methods:
- int checkMenuExistRole(Long menuId)
- int deleteRoleMenuByRoleId(Long roleId)
- int deleteRoleMenu(Long[] ids)
- int batchRoleMenu(List<SysRoleMenu> roleMenuList)

### Go Implementation Suggestion:
```go
package mapper

type SysRoleMenuMapper struct {
}

func (c *SysRoleMenuMapper) checkMenuExistRole(menuId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMenuMapper) deleteRoleMenuByRoleId(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMenuMapper) deleteRoleMenu(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleMenuMapper) batchRoleMenu(roleMenuList []SysRoleMenu) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysUserMapper

### Fields:

### Methods:
- List<SysUser> selectUserList(SysUser sysUser)
- List<SysUser> selectAllocatedList(SysUser user)
- List<SysUser> selectUnallocatedList(SysUser user)
- SysUser selectUserByUserName(String userName)
- SysUser selectUserById(Long userId)
- int insertUser(SysUser user)
- int updateUser(SysUser user)
- int updateUserAvatar(Long userId, String avatar)
- int resetUserPwd(Long userId, String password)
- int deleteUserById(Long userId)
- int deleteUserByIds(Long[] userIds)
- SysUser checkUserNameUnique(String userName)
- SysUser checkPhoneUnique(String phonenumber)
- SysUser checkEmailUnique(String email)

### Go Implementation Suggestion:
```go
package mapper

type SysUserMapper struct {
}

func (c *SysUserMapper) selectUserList(sysUser SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) selectAllocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) selectUnallocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) selectUserByUserName(userName string) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) selectUserById(userId int64) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) insertUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) updateUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) updateUserAvatar(userId int64, avatar string) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) resetUserPwd(userId int64, password string) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) deleteUserById(userId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) deleteUserByIds(userIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserMapper) checkUserNameUnique(userName string) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) checkPhoneUnique(phonenumber string) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserMapper) checkEmailUnique(email string) SysUser {
	// TODO: Implement method
	return nil
}

```

## Class: SysUserPostMapper

### Fields:

### Methods:
- int deleteUserPostByUserId(Long userId)
- int countUserPostById(Long postId)
- int deleteUserPost(Long[] ids)
- int batchUserPost(List<SysUserPost> userPostList)

### Go Implementation Suggestion:
```go
package mapper

type SysUserPostMapper struct {
}

func (c *SysUserPostMapper) deleteUserPostByUserId(userId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserPostMapper) countUserPostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserPostMapper) deleteUserPost(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserPostMapper) batchUserPost(userPostList []SysUserPost) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysUserRoleMapper

### Fields:

### Methods:
- int deleteUserRoleByUserId(Long userId)
- int deleteUserRole(Long[] ids)
- int countUserRoleByRoleId(Long roleId)
- int batchUserRole(List<SysUserRole> userRoleList)
- int deleteUserRoleInfo(SysUserRole userRole)
- int deleteUserRoleInfos(Long roleId, Long[] userIds)

### Go Implementation Suggestion:
```go
package mapper

type SysUserRoleMapper struct {
}

func (c *SysUserRoleMapper) deleteUserRoleByUserId(userId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserRoleMapper) deleteUserRole(ids []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserRoleMapper) countUserRoleByRoleId(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserRoleMapper) batchUserRole(userRoleList []SysUserRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserRoleMapper) deleteUserRoleInfo(userRole SysUserRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserRoleMapper) deleteUserRoleInfos(roleId int64, userIds []int64) int {
	// TODO: Implement method
	return 0
}

```

