# Package: com.ruoyi.system.service.impl

## Class: SysConfigServiceImpl

Implements: ISysConfigService

### Fields:
- SysConfigMapper configMapper (Autowired)
- RedisCache redisCache (Autowired)

### Methods:
- void init() (PostConstruct)
- SysConfig selectConfigById(Long configId) (Override, DataSource)
- String selectConfigByKey(String configKey) (Override)
- boolean selectCaptchaEnabled() (Override)
- List<SysConfig> selectConfigList(SysConfig config) (Override)
- int insertConfig(SysConfig config) (Override)
- int updateConfig(SysConfig config) (Override)
- void deleteConfigByIds(Long[] configIds) (Override)
- void loadingConfigCache() (Override)
- void clearConfigCache() (Override)
- void resetConfigCache() (Override)
- boolean checkConfigKeyUnique(SysConfig config) (Override)
- String getCacheKey(String configKey)

### Go Implementation Suggestion:
```go
package impl

type SysConfigServiceImpl struct {
	ConfigMapper SysConfigMapper
	RedisCache RedisCache
}

func (c *SysConfigServiceImpl) init() {
	// TODO: Implement method
}

func (c *SysConfigServiceImpl) selectConfigById(configId int64) SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigServiceImpl) selectConfigByKey(configKey string) string {
	// TODO: Implement method
	return ""
}

func (c *SysConfigServiceImpl) selectCaptchaEnabled() bool {
	// TODO: Implement method
	return false
}

func (c *SysConfigServiceImpl) selectConfigList(config SysConfig) []SysConfig {
	// TODO: Implement method
	return nil
}

func (c *SysConfigServiceImpl) insertConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *SysConfigServiceImpl) updateConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *SysConfigServiceImpl) deleteConfigByIds(configIds []int64) {
	// TODO: Implement method
}

func (c *SysConfigServiceImpl) loadingConfigCache() {
	// TODO: Implement method
}

func (c *SysConfigServiceImpl) clearConfigCache() {
	// TODO: Implement method
}

func (c *SysConfigServiceImpl) resetConfigCache() {
	// TODO: Implement method
}

func (c *SysConfigServiceImpl) checkConfigKeyUnique(config SysConfig) bool {
	// TODO: Implement method
	return false
}

func (c *SysConfigServiceImpl) getCacheKey(configKey string) string {
	// TODO: Implement method
	return ""
}

```

## Class: SysDeptServiceImpl

Implements: ISysDeptService

### Fields:
- SysDeptMapper deptMapper (Autowired)
- SysRoleMapper roleMapper (Autowired)

### Methods:
- List<SysDept> selectDeptList(SysDept dept) (Override, DataScope)
- List<TreeSelect> selectDeptTreeList(SysDept dept) (Override)
- List<SysDept> buildDeptTree(List<SysDept> depts) (Override)
- List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) (Override)
- List<Long> selectDeptListByRoleId(Long roleId) (Override)
- SysDept selectDeptById(Long deptId) (Override)
- int selectNormalChildrenDeptById(Long deptId) (Override)
- boolean hasChildByDeptId(Long deptId) (Override)
- boolean checkDeptExistUser(Long deptId) (Override)
- boolean checkDeptNameUnique(SysDept dept) (Override)
- void checkDeptDataScope(Long deptId) (Override)
- int insertDept(SysDept dept) (Override)
- int updateDept(SysDept dept) (Override)
- void updateParentDeptStatusNormal(SysDept dept)
- void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
- int deleteDeptById(Long deptId) (Override)
- void recursionFn(List<SysDept> list, SysDept t)
- List<SysDept> getChildList(List<SysDept> list, SysDept t)
- boolean hasChild(List<SysDept> list, SysDept t)

### Go Implementation Suggestion:
```go
package impl

type SysDeptServiceImpl struct {
	DeptMapper SysDeptMapper
	RoleMapper SysRoleMapper
}

func (c *SysDeptServiceImpl) selectDeptList(dept SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) selectDeptTreeList(dept SysDept) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) buildDeptTree(depts []SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) buildDeptTreeSelect(depts []SysDept) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) selectDeptListByRoleId(roleId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) selectDeptById(deptId int64) SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) selectNormalChildrenDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptServiceImpl) hasChildByDeptId(deptId int64) bool {
	// TODO: Implement method
	return false
}

func (c *SysDeptServiceImpl) checkDeptExistUser(deptId int64) bool {
	// TODO: Implement method
	return false
}

func (c *SysDeptServiceImpl) checkDeptNameUnique(dept SysDept) bool {
	// TODO: Implement method
	return false
}

func (c *SysDeptServiceImpl) checkDeptDataScope(deptId int64) {
	// TODO: Implement method
}

func (c *SysDeptServiceImpl) insertDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptServiceImpl) updateDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptServiceImpl) updateParentDeptStatusNormal(dept SysDept) {
	// TODO: Implement method
}

func (c *SysDeptServiceImpl) updateDeptChildren(deptId int64, newAncestors string, oldAncestors string) {
	// TODO: Implement method
}

func (c *SysDeptServiceImpl) deleteDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysDeptServiceImpl) recursionFn(list []SysDept, t SysDept) {
	// TODO: Implement method
}

func (c *SysDeptServiceImpl) getChildList(list []SysDept, t SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *SysDeptServiceImpl) hasChild(list []SysDept, t SysDept) bool {
	// TODO: Implement method
	return false
}

```

## Class: SysDictDataServiceImpl

Implements: ISysDictDataService

### Fields:
- SysDictDataMapper dictDataMapper (Autowired)

### Methods:
- List<SysDictData> selectDictDataList(SysDictData dictData) (Override)
- String selectDictLabel(String dictType, String dictValue) (Override)
- SysDictData selectDictDataById(Long dictCode) (Override)
- void deleteDictDataByIds(Long[] dictCodes) (Override)
- int insertDictData(SysDictData data) (Override)
- int updateDictData(SysDictData data) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysDictDataServiceImpl struct {
	DictDataMapper SysDictDataMapper
}

func (c *SysDictDataServiceImpl) selectDictDataList(dictData SysDictData) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataServiceImpl) selectDictLabel(dictType string, dictValue string) string {
	// TODO: Implement method
	return ""
}

func (c *SysDictDataServiceImpl) selectDictDataById(dictCode int64) SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataServiceImpl) deleteDictDataByIds(dictCodes []int64) {
	// TODO: Implement method
}

func (c *SysDictDataServiceImpl) insertDictData(data SysDictData) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictDataServiceImpl) updateDictData(data SysDictData) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysDictTypeServiceImpl

Implements: ISysDictTypeService

### Fields:
- SysDictTypeMapper dictTypeMapper (Autowired)
- SysDictDataMapper dictDataMapper (Autowired)

### Methods:
- void init() (PostConstruct)
- List<SysDictType> selectDictTypeList(SysDictType dictType) (Override)
- List<SysDictType> selectDictTypeAll() (Override)
- List<SysDictData> selectDictDataByType(String dictType) (Override)
- SysDictType selectDictTypeById(Long dictId) (Override)
- SysDictType selectDictTypeByType(String dictType) (Override)
- void deleteDictTypeByIds(Long[] dictIds) (Override)
- void loadingDictCache() (Override)
- void clearDictCache() (Override)
- void resetDictCache() (Override)
- int insertDictType(SysDictType dict) (Override)
- int updateDictType(SysDictType dict) (Override, Transactional)
- boolean checkDictTypeUnique(SysDictType dict) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysDictTypeServiceImpl struct {
	DictTypeMapper SysDictTypeMapper
	DictDataMapper SysDictDataMapper
}

func (c *SysDictTypeServiceImpl) init() {
	// TODO: Implement method
}

func (c *SysDictTypeServiceImpl) selectDictTypeList(dictType SysDictType) []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeServiceImpl) selectDictTypeAll() []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeServiceImpl) selectDictDataByType(dictType string) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeServiceImpl) selectDictTypeById(dictId int64) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeServiceImpl) selectDictTypeByType(dictType string) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeServiceImpl) deleteDictTypeByIds(dictIds []int64) {
	// TODO: Implement method
}

func (c *SysDictTypeServiceImpl) loadingDictCache() {
	// TODO: Implement method
}

func (c *SysDictTypeServiceImpl) clearDictCache() {
	// TODO: Implement method
}

func (c *SysDictTypeServiceImpl) resetDictCache() {
	// TODO: Implement method
}

func (c *SysDictTypeServiceImpl) insertDictType(dict SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeServiceImpl) updateDictType(dict SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *SysDictTypeServiceImpl) checkDictTypeUnique(dict SysDictType) bool {
	// TODO: Implement method
	return false
}

```

## Class: SysLogininforServiceImpl

Implements: ISysLogininforService

### Fields:
- SysLogininforMapper logininforMapper (Autowired)

### Methods:
- void insertLogininfor(SysLogininfor logininfor) (Override)
- List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) (Override)
- int deleteLogininforByIds(Long[] infoIds) (Override)
- void cleanLogininfor() (Override)

### Go Implementation Suggestion:
```go
package impl

type SysLogininforServiceImpl struct {
	LogininforMapper SysLogininforMapper
}

func (c *SysLogininforServiceImpl) insertLogininfor(logininfor SysLogininfor) {
	// TODO: Implement method
}

func (c *SysLogininforServiceImpl) selectLogininforList(logininfor SysLogininfor) []SysLogininfor {
	// TODO: Implement method
	return nil
}

func (c *SysLogininforServiceImpl) deleteLogininforByIds(infoIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysLogininforServiceImpl) cleanLogininfor() {
	// TODO: Implement method
}

```

## Class: SysMenuServiceImpl

Implements: ISysMenuService

### Fields:
- String PREMISSION_STRING
- SysMenuMapper menuMapper (Autowired)
- SysRoleMapper roleMapper (Autowired)
- SysRoleMenuMapper roleMenuMapper (Autowired)

### Methods:
- List<SysMenu> selectMenuList(Long userId) (Override)
- List<SysMenu> selectMenuList(SysMenu menu, Long userId) (Override)
- Set<String> selectMenuPermsByUserId(Long userId) (Override)
- Set<String> selectMenuPermsByRoleId(Long roleId) (Override)
- List<SysMenu> selectMenuTreeByUserId(Long userId) (Override)
- List<Long> selectMenuListByRoleId(Long roleId) (Override)
- List<RouterVo> buildMenus(List<SysMenu> menus) (Override)
- List<SysMenu> buildMenuTree(List<SysMenu> menus) (Override)
- List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) (Override)
- SysMenu selectMenuById(Long menuId) (Override)
- boolean hasChildByMenuId(Long menuId) (Override)
- boolean checkMenuExistRole(Long menuId) (Override)
- int insertMenu(SysMenu menu) (Override)
- int updateMenu(SysMenu menu) (Override)
- int deleteMenuById(Long menuId) (Override)
- boolean checkMenuNameUnique(SysMenu menu) (Override)
- String getRouteName(SysMenu menu)
- String getRouteName(String name, String path)
- String getRouterPath(SysMenu menu)
- String getComponent(SysMenu menu)
- boolean isMenuFrame(SysMenu menu)
- boolean isInnerLink(SysMenu menu)
- boolean isParentView(SysMenu menu)
- List<SysMenu> getChildPerms(List<SysMenu> list, int parentId)
- void recursionFn(List<SysMenu> list, SysMenu t)
- List<SysMenu> getChildList(List<SysMenu> list, SysMenu t)
- boolean hasChild(List<SysMenu> list, SysMenu t)
- String innerLinkReplaceEach(String path)

### Go Implementation Suggestion:
```go
package impl

type SysMenuServiceImpl struct {
	PREMISSION_STRING string
	MenuMapper SysMenuMapper
	RoleMapper SysRoleMapper
	RoleMenuMapper SysRoleMenuMapper
}

func (c *SysMenuServiceImpl) selectMenuList(userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuList(menu SysMenu, userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuPermsByUserId(userId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuPermsByRoleId(roleId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuTreeByUserId(userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuListByRoleId(roleId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) buildMenus(menus []SysMenu) []RouterVo {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) buildMenuTree(menus []SysMenu) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) buildMenuTreeSelect(menus []SysMenu) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) selectMenuById(menuId int64) SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) hasChildByMenuId(menuId int64) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) checkMenuExistRole(menuId int64) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) insertMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuServiceImpl) updateMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuServiceImpl) deleteMenuById(menuId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysMenuServiceImpl) checkMenuNameUnique(menu SysMenu) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) getRouteName(menu SysMenu) string {
	// TODO: Implement method
	return ""
}

func (c *SysMenuServiceImpl) getRouteName(name string, path string) string {
	// TODO: Implement method
	return ""
}

func (c *SysMenuServiceImpl) getRouterPath(menu SysMenu) string {
	// TODO: Implement method
	return ""
}

func (c *SysMenuServiceImpl) getComponent(menu SysMenu) string {
	// TODO: Implement method
	return ""
}

func (c *SysMenuServiceImpl) isMenuFrame(menu SysMenu) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) isInnerLink(menu SysMenu) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) isParentView(menu SysMenu) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) getChildPerms(list []SysMenu, parentId int) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) recursionFn(list []SysMenu, t SysMenu) {
	// TODO: Implement method
}

func (c *SysMenuServiceImpl) getChildList(list []SysMenu, t SysMenu) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *SysMenuServiceImpl) hasChild(list []SysMenu, t SysMenu) bool {
	// TODO: Implement method
	return false
}

func (c *SysMenuServiceImpl) innerLinkReplaceEach(path string) string {
	// TODO: Implement method
	return ""
}

```

## Class: SysNoticeServiceImpl

Implements: ISysNoticeService

### Fields:
- SysNoticeMapper noticeMapper (Autowired)

### Methods:
- SysNotice selectNoticeById(Long noticeId) (Override)
- List<SysNotice> selectNoticeList(SysNotice notice) (Override)
- int insertNotice(SysNotice notice) (Override)
- int updateNotice(SysNotice notice) (Override)
- int deleteNoticeById(Long noticeId) (Override)
- int deleteNoticeByIds(Long[] noticeIds) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysNoticeServiceImpl struct {
	NoticeMapper SysNoticeMapper
}

func (c *SysNoticeServiceImpl) selectNoticeById(noticeId int64) SysNotice {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeServiceImpl) selectNoticeList(notice SysNotice) []SysNotice {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeServiceImpl) insertNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeServiceImpl) updateNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeServiceImpl) deleteNoticeById(noticeId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysNoticeServiceImpl) deleteNoticeByIds(noticeIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysOperLogServiceImpl

Implements: ISysOperLogService

### Fields:
- SysOperLogMapper operLogMapper (Autowired)

### Methods:
- void insertOperlog(SysOperLog operLog) (Override)
- List<SysOperLog> selectOperLogList(SysOperLog operLog) (Override)
- int deleteOperLogByIds(Long[] operIds) (Override)
- SysOperLog selectOperLogById(Long operId) (Override)
- void cleanOperLog() (Override)

### Go Implementation Suggestion:
```go
package impl

type SysOperLogServiceImpl struct {
	OperLogMapper SysOperLogMapper
}

func (c *SysOperLogServiceImpl) insertOperlog(operLog SysOperLog) {
	// TODO: Implement method
}

func (c *SysOperLogServiceImpl) selectOperLogList(operLog SysOperLog) []SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *SysOperLogServiceImpl) deleteOperLogByIds(operIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysOperLogServiceImpl) selectOperLogById(operId int64) SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *SysOperLogServiceImpl) cleanOperLog() {
	// TODO: Implement method
}

```

## Class: SysPostServiceImpl

Implements: ISysPostService

### Fields:
- SysPostMapper postMapper (Autowired)
- SysUserPostMapper userPostMapper (Autowired)

### Methods:
- List<SysPost> selectPostList(SysPost post) (Override)
- List<SysPost> selectPostAll() (Override)
- SysPost selectPostById(Long postId) (Override)
- List<Long> selectPostListByUserId(Long userId) (Override)
- boolean checkPostNameUnique(SysPost post) (Override)
- boolean checkPostCodeUnique(SysPost post) (Override)
- int countUserPostById(Long postId) (Override)
- int deletePostById(Long postId) (Override)
- int deletePostByIds(Long[] postIds) (Override)
- int insertPost(SysPost post) (Override)
- int updatePost(SysPost post) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysPostServiceImpl struct {
	PostMapper SysPostMapper
	UserPostMapper SysUserPostMapper
}

func (c *SysPostServiceImpl) selectPostList(post SysPost) []SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostServiceImpl) selectPostAll() []SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostServiceImpl) selectPostById(postId int64) SysPost {
	// TODO: Implement method
	return nil
}

func (c *SysPostServiceImpl) selectPostListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysPostServiceImpl) checkPostNameUnique(post SysPost) bool {
	// TODO: Implement method
	return false
}

func (c *SysPostServiceImpl) checkPostCodeUnique(post SysPost) bool {
	// TODO: Implement method
	return false
}

func (c *SysPostServiceImpl) countUserPostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostServiceImpl) deletePostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostServiceImpl) deletePostByIds(postIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostServiceImpl) insertPost(post SysPost) int {
	// TODO: Implement method
	return 0
}

func (c *SysPostServiceImpl) updatePost(post SysPost) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysRoleServiceImpl

Implements: ISysRoleService

### Fields:
- SysRoleMapper roleMapper (Autowired)
- SysRoleMenuMapper roleMenuMapper (Autowired)
- SysUserRoleMapper userRoleMapper (Autowired)
- SysRoleDeptMapper roleDeptMapper (Autowired)

### Methods:
- List<SysRole> selectRoleList(SysRole role) (Override, DataScope)
- List<SysRole> selectRolesByUserId(Long userId) (Override)
- Set<String> selectRolePermissionByUserId(Long userId) (Override)
- List<SysRole> selectRoleAll() (Override)
- List<Long> selectRoleListByUserId(Long userId) (Override)
- SysRole selectRoleById(Long roleId) (Override)
- boolean checkRoleNameUnique(SysRole role) (Override)
- boolean checkRoleKeyUnique(SysRole role) (Override)
- void checkRoleAllowed(SysRole role) (Override)
- void checkRoleDataScope(Long roleIds) (Override)
- int countUserRoleByRoleId(Long roleId) (Override)
- int insertRole(SysRole role) (Override, Transactional)
- int updateRole(SysRole role) (Override, Transactional)
- int updateRoleStatus(SysRole role) (Override)
- int authDataScope(SysRole role) (Override, Transactional)
- int insertRoleMenu(SysRole role)
- int insertRoleDept(SysRole role)
- int deleteRoleById(Long roleId) (Override, Transactional)
- int deleteRoleByIds(Long[] roleIds) (Override, Transactional)
- int deleteAuthUser(SysUserRole userRole) (Override)
- int deleteAuthUsers(Long roleId, Long[] userIds) (Override)
- int insertAuthUsers(Long roleId, Long[] userIds) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysRoleServiceImpl struct {
	RoleMapper SysRoleMapper
	RoleMenuMapper SysRoleMenuMapper
	UserRoleMapper SysUserRoleMapper
	RoleDeptMapper SysRoleDeptMapper
}

func (c *SysRoleServiceImpl) selectRoleList(role SysRole) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) selectRolesByUserId(userId int64) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) selectRolePermissionByUserId(userId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) selectRoleAll() []SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) selectRoleListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) selectRoleById(roleId int64) SysRole {
	// TODO: Implement method
	return nil
}

func (c *SysRoleServiceImpl) checkRoleNameUnique(role SysRole) bool {
	// TODO: Implement method
	return false
}

func (c *SysRoleServiceImpl) checkRoleKeyUnique(role SysRole) bool {
	// TODO: Implement method
	return false
}

func (c *SysRoleServiceImpl) checkRoleAllowed(role SysRole) {
	// TODO: Implement method
}

func (c *SysRoleServiceImpl) checkRoleDataScope(roleIds int64) {
	// TODO: Implement method
}

func (c *SysRoleServiceImpl) countUserRoleByRoleId(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) insertRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) updateRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) updateRoleStatus(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) authDataScope(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) insertRoleMenu(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) insertRoleDept(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) deleteRoleById(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) deleteRoleByIds(roleIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) deleteAuthUser(userRole SysUserRole) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) deleteAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysRoleServiceImpl) insertAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: SysUserOnlineServiceImpl

Implements: ISysUserOnlineService

### Fields:

### Methods:
- SysUserOnline selectOnlineByIpaddr(String ipaddr, LoginUser user) (Override)
- SysUserOnline selectOnlineByUserName(String userName, LoginUser user) (Override)
- SysUserOnline selectOnlineByInfo(String ipaddr, String userName, LoginUser user) (Override)
- SysUserOnline loginUserToUserOnline(LoginUser user) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysUserOnlineServiceImpl struct {
}

func (c *SysUserOnlineServiceImpl) selectOnlineByIpaddr(ipaddr string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *SysUserOnlineServiceImpl) selectOnlineByUserName(userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *SysUserOnlineServiceImpl) selectOnlineByInfo(ipaddr string, userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *SysUserOnlineServiceImpl) loginUserToUserOnline(user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

```

## Class: SysUserServiceImpl

Implements: ISysUserService

### Fields:
- Logger log
- SysUserMapper userMapper (Autowired)
- SysRoleMapper roleMapper (Autowired)
- SysPostMapper postMapper (Autowired)
- SysUserRoleMapper userRoleMapper (Autowired)
- SysUserPostMapper userPostMapper (Autowired)
- ISysConfigService configService (Autowired)
- ISysDeptService deptService (Autowired)
- Validator validator (Autowired)

### Methods:
- List<SysUser> selectUserList(SysUser user) (Override, DataScope)
- List<SysUser> selectAllocatedList(SysUser user) (Override, DataScope)
- List<SysUser> selectUnallocatedList(SysUser user) (Override, DataScope)
- SysUser selectUserByUserName(String userName) (Override)
- SysUser selectUserById(Long userId) (Override)
- String selectUserRoleGroup(String userName) (Override)
- String selectUserPostGroup(String userName) (Override)
- boolean checkUserNameUnique(SysUser user) (Override)
- boolean checkPhoneUnique(SysUser user) (Override)
- boolean checkEmailUnique(SysUser user) (Override)
- void checkUserAllowed(SysUser user) (Override)
- void checkUserDataScope(Long userId) (Override)
- int insertUser(SysUser user) (Override, Transactional)
- boolean registerUser(SysUser user) (Override)
- int updateUser(SysUser user) (Override, Transactional)
- void insertUserAuth(Long userId, Long[] roleIds) (Override, Transactional)
- int updateUserStatus(SysUser user) (Override)
- int updateUserProfile(SysUser user) (Override)
- boolean updateUserAvatar(Long userId, String avatar) (Override)
- int resetPwd(SysUser user) (Override)
- int resetUserPwd(Long userId, String password) (Override)
- void insertUserRole(SysUser user)
- void insertUserPost(SysUser user)
- void insertUserRole(Long userId, Long[] roleIds)
- int deleteUserById(Long userId) (Override, Transactional)
- int deleteUserByIds(Long[] userIds) (Override, Transactional)
- String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) (Override)

### Go Implementation Suggestion:
```go
package impl

type SysUserServiceImpl struct {
	Log Logger
	UserMapper SysUserMapper
	RoleMapper SysRoleMapper
	PostMapper SysPostMapper
	UserRoleMapper SysUserRoleMapper
	UserPostMapper SysUserPostMapper
	ConfigService ISysConfigService
	DeptService ISysDeptService
	Validator Validator
}

func (c *SysUserServiceImpl) selectUserList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserServiceImpl) selectAllocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserServiceImpl) selectUnallocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserServiceImpl) selectUserByUserName(userName string) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserServiceImpl) selectUserById(userId int64) SysUser {
	// TODO: Implement method
	return nil
}

func (c *SysUserServiceImpl) selectUserRoleGroup(userName string) string {
	// TODO: Implement method
	return ""
}

func (c *SysUserServiceImpl) selectUserPostGroup(userName string) string {
	// TODO: Implement method
	return ""
}

func (c *SysUserServiceImpl) checkUserNameUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *SysUserServiceImpl) checkPhoneUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *SysUserServiceImpl) checkEmailUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *SysUserServiceImpl) checkUserAllowed(user SysUser) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) checkUserDataScope(userId int64) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) insertUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) registerUser(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *SysUserServiceImpl) updateUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) insertUserAuth(userId int64, roleIds []int64) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) updateUserStatus(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) updateUserProfile(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) updateUserAvatar(userId int64, avatar string) bool {
	// TODO: Implement method
	return false
}

func (c *SysUserServiceImpl) resetPwd(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) resetUserPwd(userId int64, password string) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) insertUserRole(user SysUser) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) insertUserPost(user SysUser) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) insertUserRole(userId int64, roleIds []int64) {
	// TODO: Implement method
}

func (c *SysUserServiceImpl) deleteUserById(userId int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) deleteUserByIds(userIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *SysUserServiceImpl) importUser(userList []SysUser, isUpdateSupport bool, operName string) string {
	// TODO: Implement method
	return ""
}

```

