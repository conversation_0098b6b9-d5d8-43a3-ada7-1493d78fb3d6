# Package: com.ruoyi.system.service

## Class: ISysConfigService

### Fields:

### Methods:
- SysConfig selectConfigById(Long configId)
- String selectConfigByKey(String configKey)
- boolean selectCaptchaEnabled()
- List<SysConfig> selectConfigList(SysConfig config)
- int insertConfig(SysConfig config)
- int updateConfig(SysConfig config)
- void deleteConfigByIds(Long[] configIds)
- void loadingConfigCache()
- void clearConfigCache()
- void resetConfigCache()
- boolean checkConfigKeyUnique(SysConfig config)

### Go Implementation Suggestion:
```go
package service

type ISysConfigService struct {
}

func (c *ISysConfigService) selectConfigById(configId int64) SysConfig {
	// TODO: Implement method
	return nil
}

func (c *ISysConfigService) selectConfigByKey(configKey string) string {
	// TODO: Implement method
	return ""
}

func (c *ISysConfigService) selectCaptchaEnabled() bool {
	// TODO: Implement method
	return false
}

func (c *ISysConfigService) selectConfigList(config SysConfig) []SysConfig {
	// TODO: Implement method
	return nil
}

func (c *ISysConfigService) insertConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *ISysConfigService) updateConfig(config SysConfig) int {
	// TODO: Implement method
	return 0
}

func (c *ISysConfigService) deleteConfigByIds(configIds []int64) {
	// TODO: Implement method
}

func (c *ISysConfigService) loadingConfigCache() {
	// TODO: Implement method
}

func (c *ISysConfigService) clearConfigCache() {
	// TODO: Implement method
}

func (c *ISysConfigService) resetConfigCache() {
	// TODO: Implement method
}

func (c *ISysConfigService) checkConfigKeyUnique(config SysConfig) bool {
	// TODO: Implement method
	return false
}

```

## Class: ISysDeptService

### Fields:

### Methods:
- List<SysDept> selectDeptList(SysDept dept)
- List<TreeSelect> selectDeptTreeList(SysDept dept)
- List<SysDept> buildDeptTree(List<SysDept> depts)
- List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
- List<Long> selectDeptListByRoleId(Long roleId)
- SysDept selectDeptById(Long deptId)
- int selectNormalChildrenDeptById(Long deptId)
- boolean hasChildByDeptId(Long deptId)
- boolean checkDeptExistUser(Long deptId)
- boolean checkDeptNameUnique(SysDept dept)
- void checkDeptDataScope(Long deptId)
- int insertDept(SysDept dept)
- int updateDept(SysDept dept)
- int deleteDeptById(Long deptId)

### Go Implementation Suggestion:
```go
package service

type ISysDeptService struct {
}

func (c *ISysDeptService) selectDeptList(dept SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) selectDeptTreeList(dept SysDept) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) buildDeptTree(depts []SysDept) []SysDept {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) buildDeptTreeSelect(depts []SysDept) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) selectDeptListByRoleId(roleId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) selectDeptById(deptId int64) SysDept {
	// TODO: Implement method
	return nil
}

func (c *ISysDeptService) selectNormalChildrenDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDeptService) hasChildByDeptId(deptId int64) bool {
	// TODO: Implement method
	return false
}

func (c *ISysDeptService) checkDeptExistUser(deptId int64) bool {
	// TODO: Implement method
	return false
}

func (c *ISysDeptService) checkDeptNameUnique(dept SysDept) bool {
	// TODO: Implement method
	return false
}

func (c *ISysDeptService) checkDeptDataScope(deptId int64) {
	// TODO: Implement method
}

func (c *ISysDeptService) insertDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDeptService) updateDept(dept SysDept) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDeptService) deleteDeptById(deptId int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: ISysDictDataService

### Fields:

### Methods:
- List<SysDictData> selectDictDataList(SysDictData dictData)
- String selectDictLabel(String dictType, String dictValue)
- SysDictData selectDictDataById(Long dictCode)
- void deleteDictDataByIds(Long[] dictCodes)
- int insertDictData(SysDictData dictData)
- int updateDictData(SysDictData dictData)

### Go Implementation Suggestion:
```go
package service

type ISysDictDataService struct {
}

func (c *ISysDictDataService) selectDictDataList(dictData SysDictData) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *ISysDictDataService) selectDictLabel(dictType string, dictValue string) string {
	// TODO: Implement method
	return ""
}

func (c *ISysDictDataService) selectDictDataById(dictCode int64) SysDictData {
	// TODO: Implement method
	return nil
}

func (c *ISysDictDataService) deleteDictDataByIds(dictCodes []int64) {
	// TODO: Implement method
}

func (c *ISysDictDataService) insertDictData(dictData SysDictData) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDictDataService) updateDictData(dictData SysDictData) int {
	// TODO: Implement method
	return 0
}

```

## Class: ISysDictTypeService

### Fields:

### Methods:
- List<SysDictType> selectDictTypeList(SysDictType dictType)
- List<SysDictType> selectDictTypeAll()
- List<SysDictData> selectDictDataByType(String dictType)
- SysDictType selectDictTypeById(Long dictId)
- SysDictType selectDictTypeByType(String dictType)
- void deleteDictTypeByIds(Long[] dictIds)
- void loadingDictCache()
- void clearDictCache()
- void resetDictCache()
- int insertDictType(SysDictType dictType)
- int updateDictType(SysDictType dictType)
- boolean checkDictTypeUnique(SysDictType dictType)

### Go Implementation Suggestion:
```go
package service

type ISysDictTypeService struct {
}

func (c *ISysDictTypeService) selectDictTypeList(dictType SysDictType) []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *ISysDictTypeService) selectDictTypeAll() []SysDictType {
	// TODO: Implement method
	return nil
}

func (c *ISysDictTypeService) selectDictDataByType(dictType string) []SysDictData {
	// TODO: Implement method
	return nil
}

func (c *ISysDictTypeService) selectDictTypeById(dictId int64) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *ISysDictTypeService) selectDictTypeByType(dictType string) SysDictType {
	// TODO: Implement method
	return nil
}

func (c *ISysDictTypeService) deleteDictTypeByIds(dictIds []int64) {
	// TODO: Implement method
}

func (c *ISysDictTypeService) loadingDictCache() {
	// TODO: Implement method
}

func (c *ISysDictTypeService) clearDictCache() {
	// TODO: Implement method
}

func (c *ISysDictTypeService) resetDictCache() {
	// TODO: Implement method
}

func (c *ISysDictTypeService) insertDictType(dictType SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDictTypeService) updateDictType(dictType SysDictType) int {
	// TODO: Implement method
	return 0
}

func (c *ISysDictTypeService) checkDictTypeUnique(dictType SysDictType) bool {
	// TODO: Implement method
	return false
}

```

## Class: ISysLogininforService

### Fields:

### Methods:
- void insertLogininfor(SysLogininfor logininfor)
- List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
- int deleteLogininforByIds(Long[] infoIds)
- void cleanLogininfor()

### Go Implementation Suggestion:
```go
package service

type ISysLogininforService struct {
}

func (c *ISysLogininforService) insertLogininfor(logininfor SysLogininfor) {
	// TODO: Implement method
}

func (c *ISysLogininforService) selectLogininforList(logininfor SysLogininfor) []SysLogininfor {
	// TODO: Implement method
	return nil
}

func (c *ISysLogininforService) deleteLogininforByIds(infoIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysLogininforService) cleanLogininfor() {
	// TODO: Implement method
}

```

## Class: ISysMenuService

### Fields:

### Methods:
- List<SysMenu> selectMenuList(Long userId)
- List<SysMenu> selectMenuList(SysMenu menu, Long userId)
- Set<String> selectMenuPermsByUserId(Long userId)
- Set<String> selectMenuPermsByRoleId(Long roleId)
- List<SysMenu> selectMenuTreeByUserId(Long userId)
- List<Long> selectMenuListByRoleId(Long roleId)
- List<RouterVo> buildMenus(List<SysMenu> menus)
- List<SysMenu> buildMenuTree(List<SysMenu> menus)
- List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus)
- SysMenu selectMenuById(Long menuId)
- boolean hasChildByMenuId(Long menuId)
- boolean checkMenuExistRole(Long menuId)
- int insertMenu(SysMenu menu)
- int updateMenu(SysMenu menu)
- int deleteMenuById(Long menuId)
- boolean checkMenuNameUnique(SysMenu menu)

### Go Implementation Suggestion:
```go
package service

type ISysMenuService struct {
}

func (c *ISysMenuService) selectMenuList(userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuList(menu SysMenu, userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuPermsByUserId(userId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuPermsByRoleId(roleId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuTreeByUserId(userId int64) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuListByRoleId(roleId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) buildMenus(menus []SysMenu) []RouterVo {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) buildMenuTree(menus []SysMenu) []SysMenu {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) buildMenuTreeSelect(menus []SysMenu) []TreeSelect {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) selectMenuById(menuId int64) SysMenu {
	// TODO: Implement method
	return nil
}

func (c *ISysMenuService) hasChildByMenuId(menuId int64) bool {
	// TODO: Implement method
	return false
}

func (c *ISysMenuService) checkMenuExistRole(menuId int64) bool {
	// TODO: Implement method
	return false
}

func (c *ISysMenuService) insertMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *ISysMenuService) updateMenu(menu SysMenu) int {
	// TODO: Implement method
	return 0
}

func (c *ISysMenuService) deleteMenuById(menuId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysMenuService) checkMenuNameUnique(menu SysMenu) bool {
	// TODO: Implement method
	return false
}

```

## Class: ISysNoticeService

### Fields:

### Methods:
- SysNotice selectNoticeById(Long noticeId)
- List<SysNotice> selectNoticeList(SysNotice notice)
- int insertNotice(SysNotice notice)
- int updateNotice(SysNotice notice)
- int deleteNoticeById(Long noticeId)
- int deleteNoticeByIds(Long[] noticeIds)

### Go Implementation Suggestion:
```go
package service

type ISysNoticeService struct {
}

func (c *ISysNoticeService) selectNoticeById(noticeId int64) SysNotice {
	// TODO: Implement method
	return nil
}

func (c *ISysNoticeService) selectNoticeList(notice SysNotice) []SysNotice {
	// TODO: Implement method
	return nil
}

func (c *ISysNoticeService) insertNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *ISysNoticeService) updateNotice(notice SysNotice) int {
	// TODO: Implement method
	return 0
}

func (c *ISysNoticeService) deleteNoticeById(noticeId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysNoticeService) deleteNoticeByIds(noticeIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: ISysOperLogService

### Fields:

### Methods:
- void insertOperlog(SysOperLog operLog)
- List<SysOperLog> selectOperLogList(SysOperLog operLog)
- int deleteOperLogByIds(Long[] operIds)
- SysOperLog selectOperLogById(Long operId)
- void cleanOperLog()

### Go Implementation Suggestion:
```go
package service

type ISysOperLogService struct {
}

func (c *ISysOperLogService) insertOperlog(operLog SysOperLog) {
	// TODO: Implement method
}

func (c *ISysOperLogService) selectOperLogList(operLog SysOperLog) []SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *ISysOperLogService) deleteOperLogByIds(operIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysOperLogService) selectOperLogById(operId int64) SysOperLog {
	// TODO: Implement method
	return nil
}

func (c *ISysOperLogService) cleanOperLog() {
	// TODO: Implement method
}

```

## Class: ISysPostService

### Fields:

### Methods:
- List<SysPost> selectPostList(SysPost post)
- List<SysPost> selectPostAll()
- SysPost selectPostById(Long postId)
- List<Long> selectPostListByUserId(Long userId)
- boolean checkPostNameUnique(SysPost post)
- boolean checkPostCodeUnique(SysPost post)
- int countUserPostById(Long postId)
- int deletePostById(Long postId)
- int deletePostByIds(Long[] postIds)
- int insertPost(SysPost post)
- int updatePost(SysPost post)

### Go Implementation Suggestion:
```go
package service

type ISysPostService struct {
}

func (c *ISysPostService) selectPostList(post SysPost) []SysPost {
	// TODO: Implement method
	return nil
}

func (c *ISysPostService) selectPostAll() []SysPost {
	// TODO: Implement method
	return nil
}

func (c *ISysPostService) selectPostById(postId int64) SysPost {
	// TODO: Implement method
	return nil
}

func (c *ISysPostService) selectPostListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *ISysPostService) checkPostNameUnique(post SysPost) bool {
	// TODO: Implement method
	return false
}

func (c *ISysPostService) checkPostCodeUnique(post SysPost) bool {
	// TODO: Implement method
	return false
}

func (c *ISysPostService) countUserPostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysPostService) deletePostById(postId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysPostService) deletePostByIds(postIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysPostService) insertPost(post SysPost) int {
	// TODO: Implement method
	return 0
}

func (c *ISysPostService) updatePost(post SysPost) int {
	// TODO: Implement method
	return 0
}

```

## Class: ISysRoleService

### Fields:

### Methods:
- List<SysRole> selectRoleList(SysRole role)
- List<SysRole> selectRolesByUserId(Long userId)
- Set<String> selectRolePermissionByUserId(Long userId)
- List<SysRole> selectRoleAll()
- List<Long> selectRoleListByUserId(Long userId)
- SysRole selectRoleById(Long roleId)
- boolean checkRoleNameUnique(SysRole role)
- boolean checkRoleKeyUnique(SysRole role)
- void checkRoleAllowed(SysRole role)
- void checkRoleDataScope(Long roleIds)
- int countUserRoleByRoleId(Long roleId)
- int insertRole(SysRole role)
- int updateRole(SysRole role)
- int updateRoleStatus(SysRole role)
- int authDataScope(SysRole role)
- int deleteRoleById(Long roleId)
- int deleteRoleByIds(Long[] roleIds)
- int deleteAuthUser(SysUserRole userRole)
- int deleteAuthUsers(Long roleId, Long[] userIds)
- int insertAuthUsers(Long roleId, Long[] userIds)

### Go Implementation Suggestion:
```go
package service

type ISysRoleService struct {
}

func (c *ISysRoleService) selectRoleList(role SysRole) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) selectRolesByUserId(userId int64) []SysRole {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) selectRolePermissionByUserId(userId int64) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) selectRoleAll() []SysRole {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) selectRoleListByUserId(userId int64) []int64 {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) selectRoleById(roleId int64) SysRole {
	// TODO: Implement method
	return nil
}

func (c *ISysRoleService) checkRoleNameUnique(role SysRole) bool {
	// TODO: Implement method
	return false
}

func (c *ISysRoleService) checkRoleKeyUnique(role SysRole) bool {
	// TODO: Implement method
	return false
}

func (c *ISysRoleService) checkRoleAllowed(role SysRole) {
	// TODO: Implement method
}

func (c *ISysRoleService) checkRoleDataScope(roleIds int64) {
	// TODO: Implement method
}

func (c *ISysRoleService) countUserRoleByRoleId(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) insertRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) updateRole(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) updateRoleStatus(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) authDataScope(role SysRole) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) deleteRoleById(roleId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) deleteRoleByIds(roleIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) deleteAuthUser(userRole SysUserRole) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) deleteAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysRoleService) insertAuthUsers(roleId int64, userIds []int64) int {
	// TODO: Implement method
	return 0
}

```

## Class: ISysUserOnlineService

### Fields:

### Methods:
- SysUserOnline selectOnlineByIpaddr(String ipaddr, LoginUser user)
- SysUserOnline selectOnlineByUserName(String userName, LoginUser user)
- SysUserOnline selectOnlineByInfo(String ipaddr, String userName, LoginUser user)
- SysUserOnline loginUserToUserOnline(LoginUser user)

### Go Implementation Suggestion:
```go
package service

type ISysUserOnlineService struct {
}

func (c *ISysUserOnlineService) selectOnlineByIpaddr(ipaddr string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *ISysUserOnlineService) selectOnlineByUserName(userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *ISysUserOnlineService) selectOnlineByInfo(ipaddr string, userName string, user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

func (c *ISysUserOnlineService) loginUserToUserOnline(user LoginUser) SysUserOnline {
	// TODO: Implement method
	return nil
}

```

## Class: ISysUserService

### Fields:

### Methods:
- List<SysUser> selectUserList(SysUser user)
- List<SysUser> selectAllocatedList(SysUser user)
- List<SysUser> selectUnallocatedList(SysUser user)
- SysUser selectUserByUserName(String userName)
- SysUser selectUserById(Long userId)
- String selectUserRoleGroup(String userName)
- String selectUserPostGroup(String userName)
- boolean checkUserNameUnique(SysUser user)
- boolean checkPhoneUnique(SysUser user)
- boolean checkEmailUnique(SysUser user)
- void checkUserAllowed(SysUser user)
- void checkUserDataScope(Long userId)
- int insertUser(SysUser user)
- boolean registerUser(SysUser user)
- int updateUser(SysUser user)
- void insertUserAuth(Long userId, Long[] roleIds)
- int updateUserStatus(SysUser user)
- int updateUserProfile(SysUser user)
- boolean updateUserAvatar(Long userId, String avatar)
- int resetPwd(SysUser user)
- int resetUserPwd(Long userId, String password)
- int deleteUserById(Long userId)
- int deleteUserByIds(Long[] userIds)
- String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)

### Go Implementation Suggestion:
```go
package service

type ISysUserService struct {
}

func (c *ISysUserService) selectUserList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *ISysUserService) selectAllocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *ISysUserService) selectUnallocatedList(user SysUser) []SysUser {
	// TODO: Implement method
	return nil
}

func (c *ISysUserService) selectUserByUserName(userName string) SysUser {
	// TODO: Implement method
	return nil
}

func (c *ISysUserService) selectUserById(userId int64) SysUser {
	// TODO: Implement method
	return nil
}

func (c *ISysUserService) selectUserRoleGroup(userName string) string {
	// TODO: Implement method
	return ""
}

func (c *ISysUserService) selectUserPostGroup(userName string) string {
	// TODO: Implement method
	return ""
}

func (c *ISysUserService) checkUserNameUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *ISysUserService) checkPhoneUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *ISysUserService) checkEmailUnique(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *ISysUserService) checkUserAllowed(user SysUser) {
	// TODO: Implement method
}

func (c *ISysUserService) checkUserDataScope(userId int64) {
	// TODO: Implement method
}

func (c *ISysUserService) insertUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) registerUser(user SysUser) bool {
	// TODO: Implement method
	return false
}

func (c *ISysUserService) updateUser(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) insertUserAuth(userId int64, roleIds []int64) {
	// TODO: Implement method
}

func (c *ISysUserService) updateUserStatus(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) updateUserProfile(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) updateUserAvatar(userId int64, avatar string) bool {
	// TODO: Implement method
	return false
}

func (c *ISysUserService) resetPwd(user SysUser) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) resetUserPwd(userId int64, password string) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) deleteUserById(userId int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) deleteUserByIds(userIds []int64) int {
	// TODO: Implement method
	return 0
}

func (c *ISysUserService) importUser(userList []SysUser, isUpdateSupport bool, operName string) string {
	// TODO: Implement method
	return ""
}

```

