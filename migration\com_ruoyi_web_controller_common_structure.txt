# Package: com.ruoyi.web.controller.common

## Class: CaptchaController

### Fields:
- Producer captchaProducer (Resource)
- Producer captchaProducerMath (Resource)
- RedisCache redisCache (Autowired)
- ISysConfigService configService (Autowired)

### Methods:
- AjaxResult getCode(HttpServletResponse response) (GetMapping)

### Go Implementation Suggestion:
```go
package common

type CaptchaController struct {
	CaptchaProducer Producer
	CaptchaProducerMath Producer
	RedisCache RedisCache
	ConfigService ISysConfigService
}

func (c *CaptchaController) getCode(response HttpServletResponse) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: CommonController

### Fields:
- Logger log
- ServerConfig serverConfig (Autowired)
- String FILE_DELIMETER

### Methods:
- void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) (GetMapping)
- AjaxResult uploadFile(MultipartFile file) (PostMapping)
- AjaxResult uploadFiles(List<MultipartFile> files) (PostMapping)
- void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response) (GetMapping)

### Go Implementation Suggestion:
```go
package common

type CommonController struct {
	Log Logger
	ServerConfig ServerConfig
	FILE_DELIMETER string
}

func (c *CommonController) fileDownload(fileName string, delete bool, response HttpServletResponse, request HttpServletRequest) {
	// TODO: Implement method
}

func (c *CommonController) uploadFile(file MultipartFile) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CommonController) uploadFiles(files []MultipartFile) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CommonController) resourceDownload(resource string, request HttpServletRequest, response HttpServletResponse) {
	// TODO: Implement method
}

```

