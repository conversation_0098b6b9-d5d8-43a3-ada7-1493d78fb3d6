# Package: com.ruoyi.web.controller.monitor

## Class: CacheController

### Fields:
- RedisTemplate<String, String> redisTemplate (Autowired)
- List<SysCache> caches

### Methods:
- AjaxResult getInfo() (PreAuthorize, GetMapping)
- AjaxResult cache() (PreAuthorize, GetMapping)
- AjaxResult getCacheKeys(String cacheName) (PreAuthorize, GetMapping)
- AjaxResult getCacheValue(String cacheName, String cacheKey) (PreAuthorize, GetMapping)
- AjaxResult clearCacheName(String cacheName) (PreAuthorize, DeleteMapping)
- AjaxResult clearCacheKey(String cacheKey) (PreAuthorize, DeleteMapping)
- AjaxResult clearCacheAll() (PreAuthorize, DeleteMapping)

### Go Implementation Suggestion:
```go
package monitor

type CacheController struct {
	RedisTemplate RedisTemplate<String, String>
	Caches []SysCache
}

func (c *CacheController) getInfo() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) cache() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) getCacheKeys(cacheName string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) getCacheValue(cacheName string, cacheKey string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) clearCacheName(cacheName string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) clearCacheKey(cacheKey string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *CacheController) clearCacheAll() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: ServerController

### Fields:

### Methods:
- AjaxResult getInfo() (PreAuthorize, GetMapping)

### Go Implementation Suggestion:
```go
package monitor

type ServerController struct {
}

func (c *ServerController) getInfo() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysLogininforController

Extends: BaseController

### Fields:
- ISysLogininforService logininforService (Autowired)
- SysPasswordService passwordService (Autowired)

### Methods:
- TableDataInfo list(SysLogininfor logininfor) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysLogininfor logininfor) (Log, PreAuthorize, PostMapping)
- AjaxResult remove(Long[] infoIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult clean() (PreAuthorize, Log, DeleteMapping)
- AjaxResult unlock(String userName) (PreAuthorize, Log, GetMapping)

### Go Implementation Suggestion:
```go
package monitor

type SysLogininforController struct {
	LogininforService ISysLogininforService
	PasswordService SysPasswordService
}

func (c *SysLogininforController) list(logininfor SysLogininfor) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysLogininforController) export(response HttpServletResponse, logininfor SysLogininfor) {
	// TODO: Implement method
}

func (c *SysLogininforController) remove(infoIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysLogininforController) clean() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysLogininforController) unlock(userName string) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysOperlogController

Extends: BaseController

### Fields:
- ISysOperLogService operLogService (Autowired)

### Methods:
- TableDataInfo list(SysOperLog operLog) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysOperLog operLog) (Log, PreAuthorize, PostMapping)
- AjaxResult remove(Long[] operIds) (Log, PreAuthorize, DeleteMapping)
- AjaxResult clean() (Log, PreAuthorize, DeleteMapping)

### Go Implementation Suggestion:
```go
package monitor

type SysOperlogController struct {
	OperLogService ISysOperLogService
}

func (c *SysOperlogController) list(operLog SysOperLog) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysOperlogController) export(response HttpServletResponse, operLog SysOperLog) {
	// TODO: Implement method
}

func (c *SysOperlogController) remove(operIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysOperlogController) clean() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysUserOnlineController

Extends: BaseController

### Fields:
- ISysUserOnlineService userOnlineService (Autowired)
- RedisCache redisCache (Autowired)

### Methods:
- TableDataInfo list(String ipaddr, String userName) (PreAuthorize, GetMapping)
- AjaxResult forceLogout(String tokenId) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package monitor

type SysUserOnlineController struct {
	UserOnlineService ISysUserOnlineService
	RedisCache RedisCache
}

func (c *SysUserOnlineController) list(ipaddr string, userName string) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysUserOnlineController) forceLogout(tokenId string) AjaxResult {
	// TODO: Implement method
	return nil
}

```

