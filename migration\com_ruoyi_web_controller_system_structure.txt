# Package: com.ruoyi.web.controller.system

## Class: SysConfigController

Extends: BaseController

### Fields:
- ISysConfigService configService (Autowired)

### Methods:
- TableDataInfo list(SysConfig config) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysConfig config) (Log, PreAuthorize, PostMapping)
- AjaxResult getInfo(Long configId) (PreAuthorize, GetMapping)
- AjaxResult getConfigKey(String configKey) (GetMapping)
- AjaxResult add(SysConfig config) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysConfig config) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] configIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult refreshCache() (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package system

type SysConfigController struct {
	ConfigService ISysConfigService
}

func (c *SysConfigController) list(config SysConfig) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) export(response HttpServletResponse, config SysConfig) {
	// TODO: Implement method
}

func (c *SysConfigController) getInfo(configId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) getConfigKey(configKey string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) add(config SysConfig) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) edit(config SysConfig) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) remove(configIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysConfigController) refreshCache() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysDeptController

Extends: BaseController

### Fields:
- ISysDeptService deptService (Autowired)

### Methods:
- AjaxResult list(SysDept dept) (PreAuthorize, GetMapping)
- AjaxResult excludeChild(Long deptId) (PreAuthorize, GetMapping)
- AjaxResult getInfo(Long deptId) (PreAuthorize, GetMapping)
- AjaxResult add(SysDept dept) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysDept dept) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long deptId) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package system

type SysDeptController struct {
	DeptService ISysDeptService
}

func (c *SysDeptController) list(dept SysDept) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDeptController) excludeChild(deptId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDeptController) getInfo(deptId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDeptController) add(dept SysDept) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDeptController) edit(dept SysDept) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDeptController) remove(deptId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysDictDataController

Extends: BaseController

### Fields:
- ISysDictDataService dictDataService (Autowired)
- ISysDictTypeService dictTypeService (Autowired)

### Methods:
- TableDataInfo list(SysDictData dictData) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysDictData dictData) (Log, PreAuthorize, PostMapping)
- AjaxResult getInfo(Long dictCode) (PreAuthorize, GetMapping)
- AjaxResult dictType(String dictType) (GetMapping)
- AjaxResult add(SysDictData dict) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysDictData dict) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] dictCodes) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package system

type SysDictDataController struct {
	DictDataService ISysDictDataService
	DictTypeService ISysDictTypeService
}

func (c *SysDictDataController) list(dictData SysDictData) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataController) export(response HttpServletResponse, dictData SysDictData) {
	// TODO: Implement method
}

func (c *SysDictDataController) getInfo(dictCode int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataController) dictType(dictType string) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataController) add(dict SysDictData) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataController) edit(dict SysDictData) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictDataController) remove(dictCodes []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysDictTypeController

Extends: BaseController

### Fields:
- ISysDictTypeService dictTypeService (Autowired)

### Methods:
- TableDataInfo list(SysDictType dictType) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysDictType dictType) (Log, PreAuthorize, PostMapping)
- AjaxResult getInfo(Long dictId) (PreAuthorize, GetMapping)
- AjaxResult add(SysDictType dict) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysDictType dict) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] dictIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult refreshCache() (PreAuthorize, Log, DeleteMapping)
- AjaxResult optionselect() (GetMapping)

### Go Implementation Suggestion:
```go
package system

type SysDictTypeController struct {
	DictTypeService ISysDictTypeService
}

func (c *SysDictTypeController) list(dictType SysDictType) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) export(response HttpServletResponse, dictType SysDictType) {
	// TODO: Implement method
}

func (c *SysDictTypeController) getInfo(dictId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) add(dict SysDictType) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) edit(dict SysDictType) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) remove(dictIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) refreshCache() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysDictTypeController) optionselect() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysIndexController

### Fields:
- RuoYiConfig ruoyiConfig (Autowired)

### Methods:
- String index() (RequestMapping)

### Go Implementation Suggestion:
```go
package system

type SysIndexController struct {
	RuoyiConfig RuoYiConfig
}

func (c *SysIndexController) index() string {
	// TODO: Implement method
	return ""
}

```

## Class: SysLoginController

### Fields:
- SysLoginService loginService (Autowired)
- ISysMenuService menuService (Autowired)
- SysPermissionService permissionService (Autowired)
- TokenService tokenService (Autowired)
- ISysConfigService configService (Autowired)

### Methods:
- AjaxResult login(LoginBody loginBody) (PostMapping)
- AjaxResult getInfo() (GetMapping)
- AjaxResult getRouters() (GetMapping)
- boolean initPasswordIsModify(Date pwdUpdateDate)
- boolean passwordIsExpiration(Date pwdUpdateDate)

### Go Implementation Suggestion:
```go
package system

type SysLoginController struct {
	LoginService SysLoginService
	MenuService ISysMenuService
	PermissionService SysPermissionService
	TokenService TokenService
	ConfigService ISysConfigService
}

func (c *SysLoginController) login(loginBody LoginBody) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysLoginController) getInfo() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysLoginController) getRouters() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysLoginController) initPasswordIsModify(pwdUpdateDate Date) bool {
	// TODO: Implement method
	return false
}

func (c *SysLoginController) passwordIsExpiration(pwdUpdateDate Date) bool {
	// TODO: Implement method
	return false
}

```

## Class: SysMenuController

Extends: BaseController

### Fields:
- ISysMenuService menuService (Autowired)

### Methods:
- AjaxResult list(SysMenu menu) (PreAuthorize, GetMapping)
- AjaxResult getInfo(Long menuId) (PreAuthorize, GetMapping)
- AjaxResult treeselect(SysMenu menu) (GetMapping)
- AjaxResult roleMenuTreeselect(Long roleId) (GetMapping)
- AjaxResult add(SysMenu menu) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysMenu menu) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long menuId) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package system

type SysMenuController struct {
	MenuService ISysMenuService
}

func (c *SysMenuController) list(menu SysMenu) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) getInfo(menuId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) treeselect(menu SysMenu) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) roleMenuTreeselect(roleId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) add(menu SysMenu) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) edit(menu SysMenu) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysMenuController) remove(menuId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysNoticeController

Extends: BaseController

### Fields:
- ISysNoticeService noticeService (Autowired)

### Methods:
- TableDataInfo list(SysNotice notice) (PreAuthorize, GetMapping)
- AjaxResult getInfo(Long noticeId) (PreAuthorize, GetMapping)
- AjaxResult add(SysNotice notice) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysNotice notice) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] noticeIds) (PreAuthorize, Log, DeleteMapping)

### Go Implementation Suggestion:
```go
package system

type SysNoticeController struct {
	NoticeService ISysNoticeService
}

func (c *SysNoticeController) list(notice SysNotice) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeController) getInfo(noticeId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeController) add(notice SysNotice) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeController) edit(notice SysNotice) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysNoticeController) remove(noticeIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysPostController

Extends: BaseController

### Fields:
- ISysPostService postService (Autowired)

### Methods:
- TableDataInfo list(SysPost post) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysPost post) (Log, PreAuthorize, PostMapping)
- AjaxResult getInfo(Long postId) (PreAuthorize, GetMapping)
- AjaxResult add(SysPost post) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysPost post) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] postIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult optionselect() (GetMapping)

### Go Implementation Suggestion:
```go
package system

type SysPostController struct {
	PostService ISysPostService
}

func (c *SysPostController) list(post SysPost) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysPostController) export(response HttpServletResponse, post SysPost) {
	// TODO: Implement method
}

func (c *SysPostController) getInfo(postId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysPostController) add(post SysPost) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysPostController) edit(post SysPost) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysPostController) remove(postIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysPostController) optionselect() AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysProfileController

Extends: BaseController

### Fields:
- ISysUserService userService (Autowired)
- TokenService tokenService (Autowired)

### Methods:
- AjaxResult profile() (GetMapping)
- AjaxResult updateProfile(SysUser user) (Log, PutMapping)
- AjaxResult updatePwd(Map<String, String> params) (Log, PutMapping)
- AjaxResult avatar(MultipartFile file) (Log, PostMapping)

### Go Implementation Suggestion:
```go
package system

type SysProfileController struct {
	UserService ISysUserService
	TokenService TokenService
}

func (c *SysProfileController) profile() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysProfileController) updateProfile(user SysUser) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysProfileController) updatePwd(String> map[string]interface{}) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysProfileController) avatar(file MultipartFile) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysRegisterController

Extends: BaseController

### Fields:
- SysRegisterService registerService (Autowired)
- ISysConfigService configService (Autowired)

### Methods:
- AjaxResult register(RegisterBody user) (PostMapping)

### Go Implementation Suggestion:
```go
package system

type SysRegisterController struct {
	RegisterService SysRegisterService
	ConfigService ISysConfigService
}

func (c *SysRegisterController) register(user RegisterBody) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysRoleController

Extends: BaseController

### Fields:
- ISysRoleService roleService (Autowired)
- TokenService tokenService (Autowired)
- SysPermissionService permissionService (Autowired)
- ISysUserService userService (Autowired)
- ISysDeptService deptService (Autowired)

### Methods:
- TableDataInfo list(SysRole role) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysRole role) (Log, PreAuthorize, PostMapping)
- AjaxResult getInfo(Long roleId) (PreAuthorize, GetMapping)
- AjaxResult add(SysRole role) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysRole role) (PreAuthorize, Log, PutMapping)
- AjaxResult dataScope(SysRole role) (PreAuthorize, Log, PutMapping)
- AjaxResult changeStatus(SysRole role) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] roleIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult optionselect() (PreAuthorize, GetMapping)
- TableDataInfo allocatedList(SysUser user) (PreAuthorize, GetMapping)
- TableDataInfo unallocatedList(SysUser user) (PreAuthorize, GetMapping)
- AjaxResult cancelAuthUser(SysUserRole userRole) (PreAuthorize, Log, PutMapping)
- AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds) (PreAuthorize, Log, PutMapping)
- AjaxResult selectAuthUserAll(Long roleId, Long[] userIds) (PreAuthorize, Log, PutMapping)
- AjaxResult deptTree(Long roleId) (PreAuthorize, GetMapping)

### Go Implementation Suggestion:
```go
package system

type SysRoleController struct {
	RoleService ISysRoleService
	TokenService TokenService
	PermissionService SysPermissionService
	UserService ISysUserService
	DeptService ISysDeptService
}

func (c *SysRoleController) list(role SysRole) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) export(response HttpServletResponse, role SysRole) {
	// TODO: Implement method
}

func (c *SysRoleController) getInfo(roleId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) add(role SysRole) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) edit(role SysRole) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) dataScope(role SysRole) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) changeStatus(role SysRole) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) remove(roleIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) optionselect() AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) allocatedList(user SysUser) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) unallocatedList(user SysUser) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) cancelAuthUser(userRole SysUserRole) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) cancelAuthUserAll(roleId int64, userIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) selectAuthUserAll(roleId int64, userIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysRoleController) deptTree(roleId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

```

## Class: SysUserController

Extends: BaseController

### Fields:
- ISysUserService userService (Autowired)
- ISysRoleService roleService (Autowired)
- ISysDeptService deptService (Autowired)
- ISysPostService postService (Autowired)

### Methods:
- TableDataInfo list(SysUser user) (PreAuthorize, GetMapping)
- void export(HttpServletResponse response, SysUser user) (Log, PreAuthorize, PostMapping)
- AjaxResult importData(MultipartFile file, boolean updateSupport) (Log, PreAuthorize, PostMapping)
- void importTemplate(HttpServletResponse response) (PostMapping)
- AjaxResult getInfo(Long userId) (PreAuthorize, GetMapping)
- AjaxResult add(SysUser user) (PreAuthorize, Log, PostMapping)
- AjaxResult edit(SysUser user) (PreAuthorize, Log, PutMapping)
- AjaxResult remove(Long[] userIds) (PreAuthorize, Log, DeleteMapping)
- AjaxResult resetPwd(SysUser user) (PreAuthorize, Log, PutMapping)
- AjaxResult changeStatus(SysUser user) (PreAuthorize, Log, PutMapping)
- AjaxResult authRole(Long userId) (PreAuthorize, GetMapping)
- AjaxResult insertAuthRole(Long userId, Long[] roleIds) (PreAuthorize, Log, PutMapping)
- AjaxResult deptTree(SysDept dept) (PreAuthorize, GetMapping)

### Go Implementation Suggestion:
```go
package system

type SysUserController struct {
	UserService ISysUserService
	RoleService ISysRoleService
	DeptService ISysDeptService
	PostService ISysPostService
}

func (c *SysUserController) list(user SysUser) TableDataInfo {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) export(response HttpServletResponse, user SysUser) {
	// TODO: Implement method
}

func (c *SysUserController) importData(file MultipartFile, updateSupport bool) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) importTemplate(response HttpServletResponse) {
	// TODO: Implement method
}

func (c *SysUserController) getInfo(userId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) add(user SysUser) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) edit(user SysUser) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) remove(userIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) resetPwd(user SysUser) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) changeStatus(user SysUser) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) authRole(userId int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) insertAuthRole(userId int64, roleIds []int64) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *SysUserController) deptTree(dept SysDept) AjaxResult {
	// TODO: Implement method
	return nil
}

```

