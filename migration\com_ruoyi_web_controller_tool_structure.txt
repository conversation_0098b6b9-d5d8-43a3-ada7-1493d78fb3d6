# Package: com.ruoyi.web.controller.tool

## Class: TestController

Extends: BaseController

### Fields:
- Map<Integer, UserEntity> users

### Methods:
- R<List<UserEntity>> userList() (ApiOperation, GetMapping)
- R<UserEntity> getUser(Integer userId) (ApiOperation, ApiImplicitParam, GetMapping)
- R<String> save(UserEntity user) (ApiOperation, ApiImplicitParams, PostMapping)
- R<String> update(UserEntity user) (ApiOperation, PutMapping)
- R<String> delete(Integer userId) (ApiOperation, ApiImplicitParam, DeleteMapping)

### Go Implementation Suggestion:
```go
package tool

type TestController struct {
	Users map[int]UserEntity
}

func (c *TestController) userList() R<List<UserEntity>> {
	// TODO: Implement method
	return nil
}

func (c *TestController) getUser(userId int) R<UserEntity> {
	// TODO: Implement method
	return nil
}

func (c *TestController) save(user UserEntity) R<String> {
	// TODO: Implement method
	return nil
}

func (c *TestController) update(user UserEntity) R<String> {
	// TODO: Implement method
	return nil
}

func (c *TestController) delete(userId int) R<String> {
	// TODO: Implement method
	return nil
}

```

## Class: UserEntity

### Fields:
- Integer userId (ApiModelProperty)
- String username (ApiModelProperty)
- String password (ApiModelProperty)
- String mobile (ApiModelProperty)

### Methods:
- Integer getUserId()
- void setUserId(Integer userId)
- String getUsername()
- void setUsername(String username)
- String getPassword()
- void setPassword(String password)
- String getMobile()
- void setMobile(String mobile)

### Go Implementation Suggestion:
```go
package tool

type UserEntity struct {
	UserId int
	Username string
	Password string
	Mobile string
}

func (c *UserEntity) getUserId() int {
	// TODO: Implement method
	return 0
}

func (c *UserEntity) setUserId(userId int) {
	// TODO: Implement method
}

func (c *UserEntity) getUsername() string {
	// TODO: Implement method
	return ""
}

func (c *UserEntity) setUsername(username string) {
	// TODO: Implement method
}

func (c *UserEntity) getPassword() string {
	// TODO: Implement method
	return ""
}

func (c *UserEntity) setPassword(password string) {
	// TODO: Implement method
}

func (c *UserEntity) getMobile() string {
	// TODO: Implement method
	return ""
}

func (c *UserEntity) setMobile(mobile string) {
	// TODO: Implement method
}

```

