# Package: com.ruoyi.web.core.config

## Class: SwaggerConfig

### Fields:
- RuoYiConfig ruoyiConfig (Autowired)
- boolean enabled (Value)
- String pathMapping (Value)

### Methods:
- Docket createRestApi() (Bean)
- List<SecurityScheme> securitySchemes()
- List<SecurityContext> securityContexts()
- List<SecurityReference> defaultAuth()
- ApiInfo apiInfo()

### Go Implementation Suggestion:
```go
package config

type SwaggerConfig struct {
	RuoyiConfig RuoYiConfig
	Enabled bool
	PathMapping string
}

func (c *SwaggerConfig) createRestApi() Docket {
	// TODO: Implement method
	return nil
}

func (c *SwaggerConfig) securitySchemes() []SecurityScheme {
	// TODO: Implement method
	return nil
}

func (c *SwaggerConfig) securityContexts() []SecurityContext {
	// TODO: Implement method
	return nil
}

func (c *SwaggerConfig) defaultAuth() []SecurityReference {
	// TODO: Implement method
	return nil
}

func (c *SwaggerConfig) apiInfo() ApiInfo {
	// TODO: Implement method
	return nil
}

```

