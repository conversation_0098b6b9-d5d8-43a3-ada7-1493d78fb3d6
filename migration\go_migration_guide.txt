# Java to Go Migration Guide

## Package Structure Mapping

Java package: com.ruoyi.quartz.task -> Go package: task
Java package: com.ruoyi.framework.interceptor.impl -> Go package: impl
Java package: com.ruoyi.quartz.util -> Go package: util
Java package: com.ruoyi.common.exception -> Go package: exception
Java package: com.ruoyi.framework.interceptor -> Go package: interceptor
Java package: com.ruoyi.quartz.service -> Go package: service
Java package: com.ruoyi.common.exception.job -> Go package: job
Java package: com.ruoyi.common.core.controller -> Go package: controller
Java package: com.ruoyi.common.filter -> Go package: filter
Java package: com.ruoyi.framework.security.handle -> Go package: handle
Java package: com.ruoyi.common.utils -> Go package: utils
Java package: com.ruoyi.common.exception.file -> Go package: file
Java package: com.ruoyi.generator.util -> Go package: util
Java package: com.ruoyi.system.domain.vo -> Go package: vo
Java package: com.ruoyi.system.service.impl -> Go package: impl
Java package: com.ruoyi.common.core.domain.entity -> Go package: entity
Java package: com.ruoyi.common.constant -> Go package: constant
Java package: com.ruoyi.common.utils.poi -> Go package: poi
Java package: com.ruoyi.framework.aspectj -> Go package: aspectj
Java package: com.ruoyi.common.utils.file -> Go package: file
Java package: com.ruoyi.quartz.controller -> Go package: controller
Java package: com.ruoyi.common.config -> Go package: config
Java package: com.ruoyi.common.core.redis -> Go package: redis
Java package: com.ruoyi.common.utils.sql -> Go package: sql
Java package: com.ruoyi.generator.config -> Go package: config
Java package: com.ruoyi.framework.web.exception -> Go package: exception
Java package: com.ruoyi.framework.manager.factory -> Go package: factory
Java package: com.ruoyi.web.controller.tool -> Go package: tool
Java package: com.ruoyi.system.domain -> Go package: domain
Java package: com.ruoyi.common.utils.reflect -> Go package: reflect
Java package: com.ruoyi.framework.manager -> Go package: manager
Java package: com.ruoyi.web.controller.system -> Go package: system
Java package: com.ruoyi.framework.datasource -> Go package: datasource
Java package: com.ruoyi.system.service -> Go package: service
Java package: com.ruoyi.web.core.config -> Go package: config
Java package: com.ruoyi.quartz.mapper -> Go package: mapper
Java package: com.ruoyi.framework.security.filter -> Go package: filter
Java package: com.ruoyi.common.utils.http -> Go package: http
Java package: com.ruoyi.web.controller.monitor -> Go package: monitor
Java package: com.ruoyi.framework.web.service -> Go package: service
Java package: com.ruoyi.common.utils.spring -> Go package: spring
Java package: com.ruoyi.common.core.page -> Go package: page
Java package: com.ruoyi.generator.service -> Go package: service
Java package: com.ruoyi.common.xss -> Go package: xss
Java package: com.ruoyi.quartz.service.impl -> Go package: impl
Java package: com.ruoyi.generator.controller -> Go package: controller
Java package: com.ruoyi.common.core.domain.model -> Go package: model
Java package: com.ruoyi.quartz.domain -> Go package: domain
Java package: com.ruoyi.system.mapper -> Go package: mapper
Java package: com.ruoyi.common.exception.user -> Go package: user
Java package: com.ruoyi.generator.mapper -> Go package: mapper
Java package: com.ruoyi.framework.security.context -> Go package: context
Java package: com.ruoyi.web.controller.common -> Go package: common
Java package: com.ruoyi.common.utils.sign -> Go package: sign
Java package: com.ruoyi.common.utils.uuid -> Go package: uuid
Java package: com.ruoyi.framework.web.domain -> Go package: domain
Java package: com.ruoyi.common.utils.bean -> Go package: bean
Java package: com.ruoyi.framework.config.properties -> Go package: properties
Java package: com.ruoyi.common.exception.base -> Go package: base
Java package: com.ruoyi.common.core.domain -> Go package: domain
Java package: com.ruoyi -> Go package: ruoyi
Java package: com.ruoyi.common.utils.html -> Go package: html
Java package: com.ruoyi.common.core.text -> Go package: text
Java package: com.ruoyi.common.utils.ip -> Go package: ip
Java package: com.ruoyi.framework.web.domain.server -> Go package: server
Java package: com.ruoyi.generator.domain -> Go package: domain
Java package: com.ruoyi.common.config.serializer -> Go package: serializer
Java package: com.ruoyi.framework.config -> Go package: config

## Type Mapping

Java Type -> Go Type
String -> string
Integer -> int
Long -> int64
Boolean -> bool
List<T> -> []T
Map<K,V> -> map[K]V
Exception -> error

## Annotation Mapping

@RestController -> gin.RouterGroup
@RequestMapping -> gin.Group()
@GetMapping -> router.GET()
@PostMapping -> router.POST()
@Autowired -> dependency injection or constructor
@Service -> service struct
@Repository -> data access struct
@Transactional -> transaction function wrapper
