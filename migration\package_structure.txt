Package: com.ruoyi.quartz.task
  Class: RyTask
    Fields: 0
    Methods: 3


Package: com.ruoyi.framework.interceptor.impl
  Class: SameUrlDataInterceptor
    Extends: RepeatSubmitInterceptor
    Fields: 4
    Methods: 3


Package: com.ruoyi.quartz.util
  Class: AbstractQuartzJob
    Implements: Job
    Fields: 2
    Methods: 4

  Class: CronUtils
    Fields: 0
    Methods: 3

  Class: JobInvokeUtil
    Fields: 0
    Methods: 8

  Class: QuartzDisallowConcurrentExecution
    Extends: AbstractQuartzJob
    Fields: 0
    Methods: 1

  Class: QuartzJobExecution
    Extends: AbstractQuartzJob
    Fields: 0
    Methods: 1

  Class: ScheduleUtils
    Fields: 0
    Methods: 6


Package: com.ruoyi.common.exception
  Class: DemoModeException
    Extends: RuntimeException
    Fields: 1
    Methods: 0

  Class: GlobalException
    Extends: RuntimeException
    Fields: 3
    Methods: 4

  Class: ServiceException
    Extends: RuntimeException
    Fields: 4
    Methods: 5

  Class: UtilException
    Extends: RuntimeException
    Fields: 1
    Methods: 0


Package: com.ruoyi.framework.interceptor
  Class: RepeatSubmitInterceptor
    Implements: HandlerInterceptor
    Fields: 0
    Methods: 2


Package: com.ruoyi.quartz.service
  Class: ISysJobLogService
    Fields: 0
    Methods: 6

  Class: ISysJobService
    Fields: 0
    Methods: 11


Package: com.ruoyi.common.exception.job
  Class: TaskException
    Extends: Exception
    Fields: 2
    Methods: 1


Package: com.ruoyi.common.core.controller
  Class: BaseController
    Fields: 1
    Methods: 18


Package: com.ruoyi.common.filter
  Class: PropertyPreExcludeFilter
    Extends: SimplePropertyPreFilter
    Fields: 0
    Methods: 1

  Class: RepeatableFilter
    Implements: Filter
    Fields: 0
    Methods: 3

  Class: RepeatedlyRequestWrapper
    Extends: HttpServletRequestWrapper
    Fields: 1
    Methods: 2

  Class: XssFilter
    Implements: Filter
    Fields: 1
    Methods: 4

  Class: XssHttpServletRequestWrapper
    Extends: HttpServletRequestWrapper
    Fields: 0
    Methods: 3


Package: com.ruoyi.framework.security.handle
  Class: AuthenticationEntryPointImpl
    Implements: AuthenticationEntryPoint, Serializable
    Fields: 1
    Methods: 1

  Class: LogoutSuccessHandlerImpl
    Implements: LogoutSuccessHandler
    Fields: 1
    Methods: 1


Package: com.ruoyi.common.utils
  Class: Arith
    Fields: 1
    Methods: 6

  Class: DateUtils
    Extends: DateUtils
    Fields: 6
    Methods: 16

  Class: DesensitizedUtil
    Fields: 0
    Methods: 2

  Class: DictUtils
    Fields: 1
    Methods: 11

  Class: ExceptionUtil
    Fields: 0
    Methods: 2

  Class: LogUtils
    Fields: 0
    Methods: 1

  Class: MessageUtils
    Fields: 0
    Methods: 1

  Class: PageUtils
    Extends: PageHelper
    Fields: 0
    Methods: 2

  Class: SecurityUtils
    Fields: 0
    Methods: 12

  Class: ServletUtils
    Fields: 0
    Methods: 16

  Class: SqlServerConnectionTest
    Fields: 0
    Methods: 1

  Class: StringUtils
    Extends: StringUtils
    Fields: 3
    Methods: 35

  Class: Threads
    Fields: 1
    Methods: 3


Package: com.ruoyi.common.exception.file
  Class: FileException
    Extends: BaseException
    Fields: 1
    Methods: 0

  Class: FileNameLengthLimitExceededException
    Extends: FileException
    Fields: 1
    Methods: 0

  Class: FileSizeLimitExceededException
    Extends: FileException
    Fields: 1
    Methods: 0

  Class: FileUploadException
    Extends: Exception
    Fields: 2
    Methods: 3

  Class: InvalidExtensionException
    Extends: FileUploadException
    Fields: 4
    Methods: 3

  Class: InvalidImageExtensionException
    Extends: InvalidExtensionException
    Fields: 1
    Methods: 0

  Class: InvalidFlashExtensionException
    Extends: InvalidExtensionException
    Fields: 1
    Methods: 0

  Class: InvalidMediaExtensionException
    Extends: InvalidExtensionException
    Fields: 1
    Methods: 0

  Class: InvalidVideoExtensionException
    Extends: InvalidExtensionException
    Fields: 1
    Methods: 0


Package: com.ruoyi.generator.util
  Class: GenUtils
    Fields: 0
    Methods: 10

  Class: VelocityInitializer
    Fields: 0
    Methods: 1

  Class: VelocityUtils
    Fields: 3
    Methods: 16


Package: com.ruoyi.system.domain.vo
  Class: MetaVo
    Fields: 4
    Methods: 8

  Class: RouterVo
    Fields: 9
    Methods: 18


Package: com.ruoyi.system.service.impl
  Class: SysConfigServiceImpl
    Implements: ISysConfigService
    Fields: 2
    Methods: 13

  Class: SysDeptServiceImpl
    Implements: ISysDeptService
    Fields: 2
    Methods: 19

  Class: SysDictDataServiceImpl
    Implements: ISysDictDataService
    Fields: 1
    Methods: 6

  Class: SysDictTypeServiceImpl
    Implements: ISysDictTypeService
    Fields: 2
    Methods: 13

  Class: SysLogininforServiceImpl
    Implements: ISysLogininforService
    Fields: 1
    Methods: 4

  Class: SysMenuServiceImpl
    Implements: ISysMenuService
    Fields: 4
    Methods: 28

  Class: SysNoticeServiceImpl
    Implements: ISysNoticeService
    Fields: 1
    Methods: 6

  Class: SysOperLogServiceImpl
    Implements: ISysOperLogService
    Fields: 1
    Methods: 5

  Class: SysPostServiceImpl
    Implements: ISysPostService
    Fields: 2
    Methods: 11

  Class: SysRoleServiceImpl
    Implements: ISysRoleService
    Fields: 4
    Methods: 22

  Class: SysUserOnlineServiceImpl
    Implements: ISysUserOnlineService
    Fields: 0
    Methods: 4

  Class: SysUserServiceImpl
    Implements: ISysUserService
    Fields: 9
    Methods: 27


Package: com.ruoyi.common.core.domain.entity
  Class: SysDept
    Extends: BaseEntity
    Fields: 13
    Methods: 25

  Class: SysDictData
    Extends: BaseEntity
    Fields: 10
    Methods: 20

  Class: SysDictType
    Extends: BaseEntity
    Fields: 5
    Methods: 9

  Class: SysMenu
    Extends: BaseEntity
    Fields: 18
    Methods: 35

  Class: SysRole
    Extends: BaseEntity
    Fields: 14
    Methods: 29

  Class: SysUser
    Extends: BaseEntity
    Fields: 20
    Methods: 41


Package: com.ruoyi.common.constant
  Class: CacheConstants
    Fields: 7
    Methods: 0

  Class: Constants
    Fields: 32
    Methods: 0

  Class: GenConstants
    Fields: 35
    Methods: 0

  Class: HttpStatus
    Fields: 17
    Methods: 0

  Class: ScheduleConstants
    Fields: 6
    Methods: 0

  Class: UserConstants
    Fields: 24
    Methods: 0


Package: com.ruoyi.common.utils.poi
  Class: ExcelHandlerAdapter
    Fields: 0
    Methods: 1

  Class: ExcelUtil
    Fields: 25
    Methods: 59


Package: com.ruoyi.framework.aspectj
  Class: DataScopeAspect
    Fields: 6
    Methods: 4

  Class: DataSourceAspect
    Fields: 1
    Methods: 3

  Class: LogAspect
    Fields: 3
    Methods: 9

  Class: RateLimiterAspect
    Fields: 3
    Methods: 4


Package: com.ruoyi.common.utils.file
  Class: FileTypeUtils
    Fields: 0
    Methods: 3

  Class: FileUploadUtils
    Fields: 3
    Methods: 13

  Class: FileUtils
    Fields: 1
    Methods: 13

  Class: ImageUtils
    Fields: 1
    Methods: 3

  Class: MimeTypeUtils
    Fields: 10
    Methods: 1


Package: com.ruoyi.quartz.controller
  Class: SysJobController
    Extends: BaseController
    Fields: 1
    Methods: 8

  Class: SysJobLogController
    Extends: BaseController
    Fields: 1
    Methods: 5


Package: com.ruoyi.common.config
  Class: RuoYiConfig
    Fields: 6
    Methods: 16


Package: com.ruoyi.common.core.redis
  Class: RedisCache
    Fields: 1
    Methods: 20


Package: com.ruoyi.common.utils.sql
  Class: SqlUtil
    Fields: 3
    Methods: 3


Package: com.ruoyi.generator.config
  Class: GenConfig
    Fields: 5
    Methods: 10


Package: com.ruoyi.framework.web.exception
  Class: GlobalExceptionHandler
    Fields: 1
    Methods: 10


Package: com.ruoyi.framework.manager.factory
  Class: AsyncFactory
    Fields: 1
    Methods: 2


Package: com.ruoyi.web.controller.tool
  Class: TestController
    Extends: BaseController
    Fields: 1
    Methods: 5

  Class: UserEntity
    Fields: 4
    Methods: 8


Package: com.ruoyi.system.domain
  Class: SysCache
    Fields: 4
    Methods: 8

  Class: SysConfig
    Extends: BaseEntity
    Fields: 6
    Methods: 11

  Class: SysLogininfor
    Extends: BaseEntity
    Fields: 10
    Methods: 18

  Class: SysNotice
    Extends: BaseEntity
    Fields: 6
    Methods: 11

  Class: SysOperLog
    Extends: BaseEntity
    Fields: 19
    Methods: 36

  Class: SysPost
    Extends: BaseEntity
    Fields: 7
    Methods: 13

  Class: SysRoleDept
    Fields: 2
    Methods: 5

  Class: SysRoleMenu
    Fields: 2
    Methods: 5

  Class: SysUserOnline
    Fields: 8
    Methods: 16

  Class: SysUserPost
    Fields: 2
    Methods: 5

  Class: SysUserRole
    Fields: 2
    Methods: 5


Package: com.ruoyi.common.utils.reflect
  Class: ReflectUtils
    Fields: 4
    Methods: 15


Package: com.ruoyi.framework.manager
  Class: AsyncManager
    Fields: 3
    Methods: 3

  Class: ShutdownManager
    Fields: 1
    Methods: 2


Package: com.ruoyi.web.controller.system
  Class: SysConfigController
    Extends: BaseController
    Fields: 1
    Methods: 8

  Class: SysDeptController
    Extends: BaseController
    Fields: 1
    Methods: 6

  Class: SysDictDataController
    Extends: BaseController
    Fields: 2
    Methods: 7

  Class: SysDictTypeController
    Extends: BaseController
    Fields: 1
    Methods: 8

  Class: SysIndexController
    Fields: 1
    Methods: 1

  Class: SysLoginController
    Fields: 5
    Methods: 5

  Class: SysMenuController
    Extends: BaseController
    Fields: 1
    Methods: 7

  Class: SysNoticeController
    Extends: BaseController
    Fields: 1
    Methods: 5

  Class: SysPostController
    Extends: BaseController
    Fields: 1
    Methods: 7

  Class: SysProfileController
    Extends: BaseController
    Fields: 2
    Methods: 4

  Class: SysRegisterController
    Extends: BaseController
    Fields: 2
    Methods: 1

  Class: SysRoleController
    Extends: BaseController
    Fields: 5
    Methods: 15

  Class: SysUserController
    Extends: BaseController
    Fields: 4
    Methods: 13


Package: com.ruoyi.framework.datasource
  Class: DynamicDataSource
    Extends: AbstractRoutingDataSource
    Fields: 0
    Methods: 1

  Class: DynamicDataSourceContextHolder
    Fields: 2
    Methods: 3


Package: com.ruoyi.system.service
  Class: ISysConfigService
    Fields: 0
    Methods: 11

  Class: ISysDeptService
    Fields: 0
    Methods: 14

  Class: ISysDictDataService
    Fields: 0
    Methods: 6

  Class: ISysDictTypeService
    Fields: 0
    Methods: 12

  Class: ISysLogininforService
    Fields: 0
    Methods: 4

  Class: ISysMenuService
    Fields: 0
    Methods: 16

  Class: ISysNoticeService
    Fields: 0
    Methods: 6

  Class: ISysOperLogService
    Fields: 0
    Methods: 5

  Class: ISysPostService
    Fields: 0
    Methods: 11

  Class: ISysRoleService
    Fields: 0
    Methods: 20

  Class: ISysUserOnlineService
    Fields: 0
    Methods: 4

  Class: ISysUserService
    Fields: 0
    Methods: 24


Package: com.ruoyi.web.core.config
  Class: SwaggerConfig
    Fields: 3
    Methods: 5


Package: com.ruoyi.quartz.mapper
  Class: SysJobLogMapper
    Fields: 0
    Methods: 7

  Class: SysJobMapper
    Fields: 0
    Methods: 7


Package: com.ruoyi.framework.security.filter
  Class: JwtAuthenticationTokenFilter
    Extends: OncePerRequestFilter
    Fields: 1
    Methods: 1


Package: com.ruoyi.common.utils.http
  Class: HttpHelper
    Fields: 1
    Methods: 1

  Class: HttpUtils
    Fields: 1
    Methods: 7

  Class: TrustAnyTrustManager
    Implements: X509TrustManager
    Fields: 0
    Methods: 3

  Class: TrustAnyHostnameVerifier
    Implements: HostnameVerifier
    Fields: 0
    Methods: 1


Package: com.ruoyi.web.controller.monitor
  Class: CacheController
    Fields: 2
    Methods: 7

  Class: ServerController
    Fields: 0
    Methods: 1

  Class: SysLogininforController
    Extends: BaseController
    Fields: 2
    Methods: 5

  Class: SysOperlogController
    Extends: BaseController
    Fields: 1
    Methods: 4

  Class: SysUserOnlineController
    Extends: BaseController
    Fields: 2
    Methods: 2


Package: com.ruoyi.framework.web.service
  Class: PermissionService
    Fields: 0
    Methods: 7

  Class: SysLoginService
    Fields: 5
    Methods: 4

  Class: SysPasswordService
    Fields: 3
    Methods: 4

  Class: SysPermissionService
    Fields: 2
    Methods: 2

  Class: SysRegisterService
    Fields: 3
    Methods: 2

  Class: TokenService
    Fields: 8
    Methods: 12

  Class: UserDetailsServiceImpl
    Implements: UserDetailsService
    Fields: 4
    Methods: 2


Package: com.ruoyi.common.utils.spring
  Class: SpringUtils
    Implements: BeanFactoryPostProcessor, ApplicationContextAware
    Fields: 2
    Methods: 12


Package: com.ruoyi.common.core.page
  Class: PageDomain
    Fields: 5
    Methods: 11

  Class: TableDataInfo
    Implements: Serializable
    Fields: 5
    Methods: 8

  Class: TableSupport
    Fields: 5
    Methods: 2


Package: com.ruoyi.generator.service
  Class: GenTableColumnServiceImpl
    Implements: IGenTableColumnService
    Fields: 1
    Methods: 4

  Class: GenTableServiceImpl
    Implements: IGenTableService
    Fields: 3
    Methods: 20

  Class: IGenTableColumnService
    Fields: 0
    Methods: 4

  Class: IGenTableService
    Fields: 0
    Methods: 15


Package: com.ruoyi.common.xss
  Class: XssValidator
    Implements: ConstraintValidator
    Fields: 1
    Methods: 2


Package: com.ruoyi.quartz.service.impl
  Class: SysJobLogServiceImpl
    Implements: ISysJobLogService
    Fields: 1
    Methods: 6

  Class: SysJobServiceImpl
    Implements: ISysJobService
    Fields: 2
    Methods: 13


Package: com.ruoyi.generator.controller
  Class: GenController
    Extends: BaseController
    Fields: 2
    Methods: 14


Package: com.ruoyi.common.core.domain.model
  Class: LoginBody
    Fields: 4
    Methods: 8

  Class: LoginUser
    Implements: UserDetails
    Fields: 12
    Methods: 29

  Class: RegisterBody
    Extends: LoginBody
    Fields: 0
    Methods: 0


Package: com.ruoyi.quartz.domain
  Class: SysJob
    Extends: BaseEntity
    Fields: 9
    Methods: 18

  Class: SysJobLog
    Extends: BaseEntity
    Fields: 10
    Methods: 19


Package: com.ruoyi.system.mapper
  Class: SysConfigMapper
    Fields: 0
    Methods: 8

  Class: SysDeptMapper
    Fields: 0
    Methods: 13

  Class: SysDictDataMapper
    Fields: 0
    Methods: 10

  Class: SysDictTypeMapper
    Fields: 0
    Methods: 9

  Class: SysLogininforMapper
    Fields: 0
    Methods: 4

  Class: SysMenuMapper
    Fields: 0
    Methods: 14

  Class: SysNoticeMapper
    Fields: 0
    Methods: 6

  Class: SysOperLogMapper
    Fields: 0
    Methods: 5

  Class: SysPostMapper
    Fields: 0
    Methods: 11

  Class: SysRoleDeptMapper
    Fields: 0
    Methods: 4

  Class: SysRoleMapper
    Fields: 0
    Methods: 12

  Class: SysRoleMenuMapper
    Fields: 0
    Methods: 4

  Class: SysUserMapper
    Fields: 0
    Methods: 14

  Class: SysUserPostMapper
    Fields: 0
    Methods: 4

  Class: SysUserRoleMapper
    Fields: 0
    Methods: 6


Package: com.ruoyi.common.exception.user
  Class: BlackListException
    Extends: UserException
    Fields: 1
    Methods: 0

  Class: CaptchaException
    Extends: UserException
    Fields: 1
    Methods: 0

  Class: CaptchaExpireException
    Extends: UserException
    Fields: 1
    Methods: 0

  Class: UserException
    Extends: BaseException
    Fields: 1
    Methods: 0

  Class: UserNotExistsException
    Extends: UserException
    Fields: 1
    Methods: 0

  Class: UserPasswordNotMatchException
    Extends: UserException
    Fields: 1
    Methods: 0

  Class: UserPasswordRetryLimitExceedException
    Extends: UserException
    Fields: 1
    Methods: 0


Package: com.ruoyi.generator.mapper
  Class: GenTableColumnMapper
    Fields: 0
    Methods: 6

  Class: GenTableMapper
    Fields: 0
    Methods: 10


Package: com.ruoyi.framework.security.context
  Class: AuthenticationContextHolder
    Fields: 1
    Methods: 3

  Class: PermissionContextHolder
    Fields: 1
    Methods: 2


Package: com.ruoyi.web.controller.common
  Class: CaptchaController
    Fields: 4
    Methods: 1

  Class: CommonController
    Fields: 3
    Methods: 4


Package: com.ruoyi.common.utils.sign
  Class: Base64
    Fields: 10
    Methods: 6

  Class: Md5Utils
    Fields: 1
    Methods: 3


Package: com.ruoyi.common.utils.uuid
  Class: IdUtils
    Fields: 0
    Methods: 4

  Class: Seq
    Fields: 5
    Methods: 4

  Class: UUID
    Implements: Serializable, Comparable
    Fields: 3
    Methods: 21

  Class: Holder
    Fields: 1
    Methods: 0


Package: com.ruoyi.framework.web.domain
  Class: Server
    Fields: 6
    Methods: 17


Package: com.ruoyi.common.utils.bean
  Class: BeanUtils
    Extends: BeanUtils
    Fields: 3
    Methods: 4

  Class: BeanValidators
    Fields: 0
    Methods: 1


Package: com.ruoyi.framework.config.properties
  Class: DruidProperties
    Fields: 13
    Methods: 1

  Class: PermitAllUrlProperties
    Implements: InitializingBean, ApplicationContextAware
    Fields: 4
    Methods: 4


Package: com.ruoyi.common.exception.base
  Class: BaseException
    Extends: RuntimeException
    Fields: 5
    Methods: 5


Package: com.ruoyi.common.core.domain
  Class: AjaxResult
    Extends: HashMap
    Fields: 4
    Methods: 14

  Class: BaseEntity
    Implements: Serializable
    Fields: 8
    Methods: 14

  Class: R
    Implements: Serializable
    Fields: 6
    Methods: 17

  Class: TreeEntity
    Extends: BaseEntity
    Fields: 6
    Methods: 10

  Class: TreeSelect
    Implements: Serializable
    Fields: 5
    Methods: 8


Package: com.ruoyi
  Class: RuoYiApplication
    Fields: 0
    Methods: 1

  Class: RuoYiServletInitializer
    Extends: SpringBootServletInitializer
    Fields: 0
    Methods: 1


Package: com.ruoyi.common.utils.html
  Class: EscapeUtil
    Fields: 2
    Methods: 6

  Class: HTMLFilter
    Fields: 38
    Methods: 21


Package: com.ruoyi.common.core.text
  Class: CharsetKit
    Fields: 6
    Methods: 4

  Class: Convert
    Fields: 0
    Methods: 44

  Class: StrFormatter
    Fields: 4
    Methods: 1


Package: com.ruoyi.common.utils.ip
  Class: AddressUtils
    Fields: 3
    Methods: 1

  Class: IpUtils
    Fields: 4
    Methods: 15


Package: com.ruoyi.framework.web.domain.server
  Class: Cpu
    Fields: 6
    Methods: 12

  Class: Jvm
    Fields: 5
    Methods: 16

  Class: Mem
    Fields: 3
    Methods: 7

  Class: Sys
    Fields: 5
    Methods: 10

  Class: SysFile
    Fields: 7
    Methods: 14


Package: com.ruoyi.generator.domain
  Class: GenTable
    Extends: BaseEntity
    Fields: 25
    Methods: 56

  Class: GenTableColumn
    Extends: BaseEntity
    Fields: 19
    Methods: 56


Package: com.ruoyi.common.config.serializer
  Class: SensitiveJsonSerializer
    Implements: ContextualSerializer
    Extends: JsonSerializer
    Fields: 1
    Methods: 3


Package: com.ruoyi.framework.config
  Class: ApplicationConfig
    Fields: 0
    Methods: 1

  Class: CaptchaConfig
    Fields: 0
    Methods: 2

  Class: DruidConfig
    Fields: 0
    Methods: 5

  Class: FastJson2JsonRedisSerializer
    Implements: RedisSerializer
    Fields: 3
    Methods: 2

  Class: FilterConfig
    Fields: 2
    Methods: 2

  Class: I18nConfig
    Implements: WebMvcConfigurer
    Fields: 0
    Methods: 3

  Class: KaptchaTextCreator
    Extends: DefaultTextCreator
    Fields: 1
    Methods: 1

  Class: MyBatisConfig
    Fields: 2
    Methods: 3

  Class: RedisConfig
    Extends: CachingConfigurerSupport
    Fields: 0
    Methods: 3

  Class: ResourcesConfig
    Implements: WebMvcConfigurer
    Fields: 1
    Methods: 3

  Class: SecurityConfig
    Fields: 6
    Methods: 3

  Class: ServerConfig
    Fields: 0
    Methods: 2

  Class: ThreadPoolConfig
    Fields: 4
    Methods: 2

  Class: TlsConfig
    Fields: 2
    Methods: 1


