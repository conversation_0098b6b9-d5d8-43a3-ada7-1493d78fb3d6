#!/bin/bash

# 环境变量检查脚本

echo "检查生产环境配置..."

# 检查 .env.prod 文件
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod 文件不存在"
    exit 1
fi

source .env.prod

# 必需的环境变量
required_vars=(
    "APP_ENV:prod"
    "DB_HOST"
    "DB_USERNAME"
    "DB_PASSWORD"
    "DB_NAME"
    "REDIS_HOST"
    "REDIS_PASSWORD"
    "JWT_SECRET"
)

all_ok=true

for var_check in "${required_vars[@]}"; do
    IFS=':' read -r var_name expected_value <<< "$var_check"
    var_value="${!var_name}"

    if [ -z "$var_value" ]; then
        echo "❌ $var_name 未设置"
        all_ok=false
    elif [ -n "$expected_value" ] && [ "$var_value" != "$expected_value" ]; then
        echo "❌ $var_name 值不正确，期望: $expected_value，实际: $var_value"
        all_ok=false
    else
        echo "✅ $var_name 已设置"
    fi
done

# 特殊检查
if [ ${#JWT_SECRET} -lt 32 ]; then
    echo "❌ JWT_SECRET 长度不足（当前: ${#JWT_SECRET}，最少: 32）"
    all_ok=false
else
    echo "✅ JWT_SECRET 长度符合要求"
fi

if [ "$all_ok" = true ]; then
    echo "🎉 所有配置检查通过！"
    exit 0
else
    echo "❌ 配置检查失败，请修复上述问题"
    exit 1
fi 
 