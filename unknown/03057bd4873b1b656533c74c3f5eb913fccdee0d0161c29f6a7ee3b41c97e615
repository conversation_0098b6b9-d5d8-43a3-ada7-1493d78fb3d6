package utils

import (
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tealeg/xlsx"
)

// ExcelUtil Excel工具类
type ExcelUtil struct {
	SheetName string
	Title     string
	Headers   []string
	Fields    []string
}

// NewExcelUtil 创建Excel工具类
func NewExcelUtil(sheetName, title string, headers, fields []string) *ExcelUtil {
	return &ExcelUtil{
		SheetName: sheetName,
		Title:     title,
		Headers:   headers,
		Fields:    fields,
	}
}

// ExportToExcel 导出Excel
func (e *ExcelUtil) ExportToExcel(ctx *gin.Context, data interface{}, filename string) error {
	// 创建Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet(e.SheetName)
	if err != nil {
		return err
	}

	// 添加标题
	if e.Title != "" {
		titleRow := sheet.AddRow()
		titleCell := titleRow.AddCell()
		titleCell.Value = e.Title
		// 合并单元格 - 使用正确的方法
		sheet.Row(0).Cells[0].HMerge = len(e.Headers) - 1
	}

	// 添加表头
	headerRow := sheet.AddRow()
	for _, header := range e.Headers {
		cell := headerRow.AddCell()
		cell.Value = header
	}

	// 添加数据
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
	}

	if dataValue.Kind() != reflect.Slice {
		return errors.New("data must be a slice")
	}

	for i := 0; i < dataValue.Len(); i++ {
		item := dataValue.Index(i)
		if item.Kind() == reflect.Ptr {
			item = item.Elem()
		}

		row := sheet.AddRow()
		for _, field := range e.Fields {
			cell := row.AddCell()
			value := getFieldValue(item, field)
			cell.Value = value
		}
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	return file.Write(ctx.Writer)
}

// ExportToCSV 导出CSV
func (e *ExcelUtil) ExportToCSV(ctx *gin.Context, data interface{}, filename string) error {
	// 创建CSV缓冲区
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)

	// 添加表头
	err := writer.Write(e.Headers)
	if err != nil {
		return err
	}

	// 添加数据
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
	}

	if dataValue.Kind() != reflect.Slice {
		return errors.New("data must be a slice")
	}

	for i := 0; i < dataValue.Len(); i++ {
		item := dataValue.Index(i)
		if item.Kind() == reflect.Ptr {
			item = item.Elem()
		}

		row := make([]string, len(e.Fields))
		for j, field := range e.Fields {
			row[j] = getFieldValue(item, field)
		}

		err := writer.Write(row)
		if err != nil {
			return err
		}
	}

	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	_, err = ctx.Writer.Write(buf.Bytes())
	return err
}

// ImportFromExcel 从Excel导入
func ImportFromExcel(file io.Reader, skipRows int) ([][]string, error) {
	// 先将io.Reader的内容读取到字节数组
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, err
	}

	// 使用字节数组创建xlsx文件
	xlFile, err := xlsx.OpenBinary(buf.Bytes())
	if err != nil {
		return nil, err
	}

	if len(xlFile.Sheets) == 0 {
		return nil, errors.New("no sheets in the file")
	}

	sheet := xlFile.Sheets[0]
	result := make([][]string, 0, len(sheet.Rows)-skipRows)

	for i, row := range sheet.Rows {
		if i < skipRows {
			continue
		}

		rowData := make([]string, len(row.Cells))
		for j, cell := range row.Cells {
			rowData[j] = cell.String()
		}
		result = append(result, rowData)
	}

	return result, nil
}

// ImportFromCSV 从CSV导入
func ImportFromCSV(file io.Reader, skipRows int) ([][]string, error) {
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	if len(records) <= skipRows {
		return nil, errors.New("no data in the file")
	}

	return records[skipRows:], nil
}

// SaveExcelFile 保存Excel文件
func SaveExcelFile(data [][]string, filePath string) error {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("Sheet1")
	if err != nil {
		return err
	}

	for _, rowData := range data {
		row := sheet.AddRow()
		for _, cellData := range rowData {
			cell := row.AddCell()
			cell.Value = cellData
		}
	}

	// 创建目录
	dir := filepath.Dir(filePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			return err
		}
	}

	return file.Save(filePath)
}

// SaveCSVFile 保存CSV文件
func SaveCSVFile(data [][]string, filePath string) error {
	// 创建目录
	dir := filepath.Dir(filePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			return err
		}
	}

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	err = writer.WriteAll(data)
	if err != nil {
		return err
	}

	writer.Flush()
	return writer.Error()
}

// DownloadTemplate 下载模板
func (e *ExcelUtil) DownloadTemplate(ctx *gin.Context, filename string) error {
	// 创建Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet(e.SheetName)
	if err != nil {
		return err
	}

	// 添加表头
	headerRow := sheet.AddRow()
	for _, header := range e.Headers {
		cell := headerRow.AddCell()
		cell.Value = header
	}

	// 添加示例数据行
	exampleRow := sheet.AddRow()
	for range e.Headers {
		cell := exampleRow.AddCell()
		cell.Value = ""
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	return file.Write(ctx.Writer)
}

// DownloadCSVTemplate 下载CSV模板
func (e *ExcelUtil) DownloadCSVTemplate(ctx *gin.Context, filename string) error {
	// 创建CSV缓冲区
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)

	// 添加表头
	err := writer.Write(e.Headers)
	if err != nil {
		return err
	}

	// 添加示例数据行
	exampleRow := make([]string, len(e.Headers))
	err = writer.Write(exampleRow)
	if err != nil {
		return err
	}

	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	_, err = ctx.Writer.Write(buf.Bytes())
	return err
}

// getFieldValue 获取字段值
func getFieldValue(v reflect.Value, field string) string {
	if v.Kind() != reflect.Struct {
		return ""
	}

	// 处理嵌套字段，如 "Dept.DeptName"
	fields := strings.Split(field, ".")
	current := v

	for _, f := range fields {
		if current.Kind() == reflect.Struct {
			current = current.FieldByName(f)
		} else if current.Kind() == reflect.Ptr && !current.IsNil() {
			current = current.Elem().FieldByName(f)
		} else {
			return ""
		}

		if !current.IsValid() {
			return ""
		}
	}

	// 根据字段类型转换为字符串
	switch current.Kind() {
	case reflect.String:
		return current.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(current.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(current.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		return strconv.FormatFloat(current.Float(), 'f', 2, 64)
	case reflect.Bool:
		return strconv.FormatBool(current.Bool())
	case reflect.Struct:
		// 处理时间类型
		if t, ok := current.Interface().(time.Time); ok {
			return t.Format("2006-01-02 15:04:05")
		}
		return ""
	case reflect.Ptr:
		if current.IsNil() {
			return ""
		}
		// 处理时间指针
		if t, ok := current.Interface().(*time.Time); ok && t != nil {
			return t.Format("2006-01-02 15:04:05")
		}
		return getFieldValue(current.Elem(), "")
	default:
		return ""
	}
}

// WriteExcelResponse 写入Excel响应
func WriteExcelResponse(ctx *gin.Context, data [][]string, filename string) error {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("Sheet1")
	if err != nil {
		return err
	}

	for _, rowData := range data {
		row := sheet.AddRow()
		for _, cellData := range rowData {
			cell := row.AddCell()
			cell.Value = cellData
		}
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	return file.Write(ctx.Writer)
}

// WriteCSVResponse 写入CSV响应
func WriteCSVResponse(ctx *gin.Context, data [][]string, filename string) error {
	// 创建CSV缓冲区
	buf := new(bytes.Buffer)
	writer := csv.NewWriter(buf)

	// 写入数据
	err := writer.WriteAll(data)
	if err != nil {
		return err
	}

	writer.Flush()

	// 设置响应头
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入响应
	_, err = ctx.Writer.Write(buf.Bytes())
	return err
}

// ServeExcelFile 提供Excel文件下载
func ServeExcelFile(ctx *gin.Context, filePath, filename string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return err
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	ctx.Header("Content-Length", strconv.FormatInt(fileInfo.Size(), 10))

	// 发送文件
	http.ServeContent(ctx.Writer, ctx.Request, filename, fileInfo.ModTime(), file)
	return nil
}

// ServeCSVFile 提供CSV文件下载
func ServeCSVFile(ctx *gin.Context, filePath, filename string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return err
	}

	// 设置响应头
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	ctx.Header("Content-Length", strconv.FormatInt(fileInfo.Size(), 10))

	// 发送文件
	http.ServeContent(ctx.Writer, ctx.Request, filename, fileInfo.ModTime(), file)
	return nil
}
