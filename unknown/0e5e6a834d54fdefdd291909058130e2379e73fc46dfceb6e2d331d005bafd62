package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysPostController 岗位信息控制器
type SysPostController struct {
	controller.BaseController
	postService service.ISysPostService
}

// NewSysPostController 创建岗位控制器
func NewSysPostController(
	logger *zap.Logger,
	postService service.ISysPostService,
) *SysPostController {
	return &SysPostController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		postService: postService,
	}
}

// RegisterRoutes 注册路由
func (c *SysPostController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:postId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:postIds", c.Remove)
	r.GET("/optionselect", c.Optionselect)
}

// List 获取岗位列表
func (c *SysPostController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	post := &domain.SysPost{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取岗位列表
	list := c.postService.SelectPostList(*post)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// Export 导出岗位
func (c *SysPostController) Export(ctx *gin.Context) {
	// 构建查询条件
	post := &domain.SysPost{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取岗位列表
	list := c.postService.SelectPostList(*post)

	// TODO: 实现Excel导出
	c.SuccessWithMessage(ctx, "导出成功")
}

// GetInfo 根据岗位编号获取详细信息
func (c *SysPostController) GetInfo(ctx *gin.Context) {
	postIdStr := ctx.Param("postId")
	postId, err := strconv.ParseInt(postIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "岗位ID格式错误")
		return
	}

	// 获取岗位信息
	post := c.postService.SelectPostById(postId)
	c.SuccessWithData(ctx, post)
}

// Add 新增岗位
func (c *SysPostController) Add(ctx *gin.Context) {
	var post domain.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验岗位名称唯一性
	if !c.postService.CheckPostNameUnique(post) {
		c.ErrorWithMessage(ctx, "新增岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	} else if !c.postService.CheckPostCodeUnique(post) {
		c.ErrorWithMessage(ctx, "新增岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置创建者
	post.CreateBy = c.GetUsername(ctx)

	// 新增岗位
	rows := c.postService.InsertPost(post)
	c.ToAjax(ctx, int64(rows))
}

// Edit 修改岗位
func (c *SysPostController) Edit(ctx *gin.Context) {
	var post domain.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验岗位名称唯一性
	if !c.postService.CheckPostNameUnique(post) {
		c.ErrorWithMessage(ctx, "修改岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	} else if !c.postService.CheckPostCodeUnique(post) {
		c.ErrorWithMessage(ctx, "修改岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置更新者
	post.UpdateBy = c.GetUsername(ctx)

	// 更新岗位
	rows := c.postService.UpdatePost(post)
	c.ToAjax(ctx, int64(rows))
}

// Remove 删除岗位
func (c *SysPostController) Remove(ctx *gin.Context) {
	postIdsStr := ctx.Param("postIds")
	postIds := strings.Split(postIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(postIds))
	for _, idStr := range postIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 删除岗位
	rows := c.postService.DeletePostByIds(ids)
	c.ToAjax(ctx, int64(rows))
}

// Optionselect 获取岗位选择框列表
func (c *SysPostController) Optionselect(ctx *gin.Context) {
	posts := c.postService.SelectPostAll()
	c.SuccessWithData(ctx, posts)
}
