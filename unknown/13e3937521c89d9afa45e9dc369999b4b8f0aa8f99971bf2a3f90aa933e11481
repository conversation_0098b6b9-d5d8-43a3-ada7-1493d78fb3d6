package impl

import (
	"context"
	"strings"

	"github.com/redis/go-redis/v9"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// RedisCacheServiceImpl Redis缓存服务实现
type RedisCacheServiceImpl struct {
	logger       *zap.Logger
	redisClient  *redis.Client
	redisService redis.RedisService
}

// NewRedisCacheService 创建Redis缓存服务
func NewRedisCacheService(logger *zap.Logger, redisClient *redis.Client, redisService redis.RedisService) service.IRedisCacheService {
	return &RedisCacheServiceImpl{
		logger:       logger,
		redisClient:  redisClient,
		redisService: redisService,
	}
}

// GetCacheInfo 获取缓存信息
func (s *RedisCacheServiceImpl) GetCacheInfo() map[string]interface{} {
	ctx := context.Background()

	// 获取Redis信息
	info, err := s.redisClient.Info(ctx).Result()
	if err != nil {
		s.logger.Error("获取Redis信息失败", zap.Error(err))
		return s.getDefaultCacheInfo()
	}

	// 解析Redis信息
	infoMap := make(map[string]string)
	infoLines := strings.Split(info, "\r\n")
	for _, line := range infoLines {
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}
		infoMap[parts[0]] = parts[1]
	}

	// 获取命令统计信息
	cmdStats, err := s.redisClient.Info(ctx, "commandstats").Result()
	if err != nil {
		s.logger.Error("获取Redis命令统计信息失败", zap.Error(err))
		return s.getDefaultCacheInfo()
	}

	// 解析命令统计信息
	var pieList []map[string]string
	cmdStatsLines := strings.Split(cmdStats, "\r\n")
	for _, line := range cmdStatsLines {
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		if strings.HasPrefix(line, "cmdstat_") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) != 2 {
				continue
			}
			cmdName := strings.TrimPrefix(parts[0], "cmdstat_")
			cmdInfo := parts[1]
			callsIndex := strings.Index(cmdInfo, "calls=")
			usecIndex := strings.Index(cmdInfo, ",usec")
			if callsIndex >= 0 && usecIndex > callsIndex {
				callsValue := cmdInfo[callsIndex+6 : usecIndex]
				pieList = append(pieList, map[string]string{
					"name":  cmdName,
					"value": callsValue,
				})
			}
		}
	}

	// 获取数据库大小
	dbSize, err := s.redisClient.DBSize(ctx).Result()
	if err != nil {
		s.logger.Error("获取Redis数据库大小失败", zap.Error(err))
		return s.getDefaultCacheInfo()
	}

	return map[string]interface{}{
		"info":         infoMap,
		"commandStats": pieList,
		"dbSize":       dbSize,
	}
}

// GetCacheNames 获取缓存名称列表
func (s *RedisCacheServiceImpl) GetCacheNames() []map[string]string {
	return []map[string]string{
		{"cacheName": domain.LOGIN_TOKEN_KEY, "remark": "用户信息"},
		{"cacheName": domain.SYS_CONFIG_KEY, "remark": "配置信息"},
		{"cacheName": domain.SYS_DICT_KEY, "remark": "数据字典"},
		{"cacheName": domain.CAPTCHA_CODE_KEY, "remark": "验证码"},
		{"cacheName": domain.REPEAT_SUBMIT_KEY, "remark": "防重提交"},
		{"cacheName": domain.RATE_LIMIT_KEY, "remark": "限流处理"},
		{"cacheName": domain.PWD_ERR_CNT_KEY, "remark": "密码错误次数"},
	}
}

// GetCacheKeys 获取缓存键列表
func (s *RedisCacheServiceImpl) GetCacheKeys(cacheName string) []string {
	ctx := context.Background()
	pattern := cacheName + "*"

	// 获取所有匹配的键
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		s.logger.Error("获取Redis键列表失败", zap.String("pattern", pattern), zap.Error(err))
		return []string{}
	}

	// 排序
	sortedKeys := make([]string, 0, len(keys))
	sortedKeys = append(sortedKeys, keys...)

	return sortedKeys
}

// GetCacheValue 获取缓存值
func (s *RedisCacheServiceImpl) GetCacheValue(cacheName string, cacheKey string) map[string]string {
	ctx := context.Background()

	// 获取缓存值
	cacheValue, err := s.redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 键不存在
			return map[string]string{
				"cacheName":  cacheName,
				"cacheKey":   strings.Replace(cacheKey, cacheName, "", 1),
				"cacheValue": "",
			}
		}
		s.logger.Error("获取Redis缓存值失败", zap.String("cacheKey", cacheKey), zap.Error(err))
		return map[string]string{
			"cacheName":  cacheName,
			"cacheKey":   strings.Replace(cacheKey, cacheName, "", 1),
			"cacheValue": "",
		}
	}

	return map[string]string{
		"cacheName":  cacheName,
		"cacheKey":   strings.Replace(cacheKey, cacheName, "", 1),
		"cacheValue": cacheValue,
	}
}

// ClearCacheName 清空缓存名称下的所有键
func (s *RedisCacheServiceImpl) ClearCacheName(cacheName string) error {
	ctx := context.Background()
	pattern := cacheName + "*"

	// 获取所有匹配的键
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		s.logger.Error("获取Redis键列表失败", zap.String("pattern", pattern), zap.Error(err))
		return err
	}

	// 没有匹配的键
	if len(keys) == 0 {
		return nil
	}

	// 删除所有匹配的键
	_, err = s.redisClient.Del(ctx, keys...).Result()
	if err != nil {
		s.logger.Error("删除Redis缓存失败", zap.Strings("keys", keys), zap.Error(err))
		return err
	}

	return nil
}

// ClearCacheKey 清除指定缓存键
func (s *RedisCacheServiceImpl) ClearCacheKey(cacheKey string) error {
	ctx := context.Background()

	// 删除缓存键
	_, err := s.redisClient.Del(ctx, cacheKey).Result()
	if err != nil {
		s.logger.Error("删除Redis缓存键失败", zap.String("cacheKey", cacheKey), zap.Error(err))
		return err
	}

	return nil
}

// ClearCacheAll 清空所有缓存
func (s *RedisCacheServiceImpl) ClearCacheAll() error {
	ctx := context.Background()

	// 获取所有键
	keys, err := s.redisClient.Keys(ctx, "*").Result()
	if err != nil {
		s.logger.Error("获取Redis所有键失败", zap.Error(err))
		return err
	}

	// 没有键
	if len(keys) == 0 {
		return nil
	}

	// 删除所有键
	_, err = s.redisClient.Del(ctx, keys...).Result()
	if err != nil {
		s.logger.Error("删除Redis所有缓存失败", zap.Error(err))
		return err
	}

	return nil
}

// getDefaultCacheInfo 获取默认缓存信息
func (s *RedisCacheServiceImpl) getDefaultCacheInfo() map[string]interface{} {
	// 默认Redis信息
	infoMap := map[string]string{
		"redis_version":             "6.0.16",
		"redis_mode":                "standalone",
		"os":                        "Windows",
		"arch_bits":                 "64",
		"multiplexing_api":          "winsock_IOCP",
		"process_id":                "12345",
		"uptime_in_seconds":         "86400",
		"uptime_in_days":            "1",
		"connected_clients":         "1",
		"used_memory":               "1048576",
		"used_memory_human":         "1M",
		"used_memory_rss":           "2097152",
		"used_memory_rss_human":     "2M",
		"used_memory_peak":          "1048576",
		"used_memory_peak_human":    "1M",
		"total_system_memory":       "8589934592",
		"total_system_memory_human": "8G",
		"used_cpu_sys":              "0.12",
		"used_cpu_user":             "0.08",
		"used_cpu_sys_children":     "0.00",
		"used_cpu_user_children":    "0.00",
	}

	// 默认命令统计
	pieList := []map[string]string{
		{"name": "get", "value": "100"},
		{"name": "set", "value": "80"},
		{"name": "del", "value": "30"},
		{"name": "keys", "value": "20"},
		{"name": "hget", "value": "15"},
		{"name": "hset", "value": "10"},
		{"name": "incr", "value": "5"},
	}

	// 默认数据库大小
	dbSize := 100

	return map[string]interface{}{
		"info":         infoMap,
		"commandStats": pieList,
		"dbSize":       dbSize,
	}
}
