package config

import (
	"time"
)

// ServerConfig 服务器配置
type ServerConfig struct {
	// Host 主机地址
	Host string `mapstructure:"host" json:"host" yaml:"host"`

	// Port 端口号
	Port int `mapstructure:"port" json:"port" yaml:"port"`

	// ContextPath 上下文路径
	ContextPath string `mapstructure:"context_path" json:"context_path" yaml:"context_path"`

	// ReadTimeout 读取超时时间（秒）
	ReadTimeout int `mapstructure:"read_timeout" json:"read_timeout" yaml:"read_timeout"`

	// WriteTimeout 写入超时时间（秒）
	WriteTimeout int `mapstructure:"write_timeout" json:"write_timeout" yaml:"write_timeout"`

	// IdleTimeout 空闲超时时间（秒）
	IdleTimeout int `mapstructure:"idle_timeout" json:"idle_timeout" yaml:"idle_timeout"`

	// MaxHeaderBytes 最大请求头大小
	MaxHeaderBytes int `mapstructure:"max_header_bytes" json:"max_header_bytes" yaml:"max_header_bytes"`

	// EnableHTTP2 是否启用HTTP/2
	EnableHTTP2 bool `mapstructure:"enable_http2" json:"enable_http2" yaml:"enable_http2"`
}

// NewServerConfig 创建服务器配置
func NewServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:           "0.0.0.0",
		Port:           8080,
		ContextPath:    "",
		ReadTimeout:    60,
		WriteTimeout:   60,
		IdleTimeout:    60,
		MaxHeaderBytes: 1 << 20, // 1MB
		EnableHTTP2:    true,
	}
}

// GetAddr 获取地址
func (c *ServerConfig) GetAddr() string {
	return c.Host + ":" + string(c.Port)
}

// GetReadTimeout 获取读取超时时间
func (c *ServerConfig) GetReadTimeout() time.Duration {
	return time.Duration(c.ReadTimeout) * time.Second
}

// GetWriteTimeout 获取写入超时时间
func (c *ServerConfig) GetWriteTimeout() time.Duration {
	return time.Duration(c.WriteTimeout) * time.Second
}

// GetIdleTimeout 获取空闲超时时间
func (c *ServerConfig) GetIdleTimeout() time.Duration {
	return time.Duration(c.IdleTimeout) * time.Second
}

// GetContextPath 获取上下文路径
func (c *ServerConfig) GetContextPath() string {
	return c.ContextPath
}
