apiVersion: apps/v1
kind: Deployment
metadata:
  name: ruoyi-go
  namespace: ruoyi
  labels:
    app: ruoyi-go
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ruoyi-go
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: ruoyi-go
    spec:
      containers:
      - name: ruoyi-go
        image: ruoyi-go:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "sqlserver"
        - name: DB_PORT
          value: "1433"
        - name: DB_TYPE
          value: "sqlserver"
        - name: DB_NAME
          value: "ruoyi"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: db.user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: db.password
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: redis.password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ruoyi-secrets
              key: jwt.secret
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 2
      volumes:
      - name: config-volume
        configMap:
          name: ruoyi-config
      - name: logs-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: ruoyi-go
  namespace: ruoyi
spec:
  selector:
    app: ruoyi-go
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ruoyi-go-ingress
  namespace: ruoyi
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
  - host: ruoyi.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ruoyi-go
            port:
              number: 80
  tls:
  - hosts:
    - ruoyi.example.com
    secretName: ruoyi-tls-secret
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ruoyi-config
  namespace: ruoyi
data:
  db.host: "sqlserver"
  db.port: "1433"
  db.name: "ruoyi"
  redis.host: "redis"
  redis.port: "6379"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ruoyi-config-file
  namespace: ruoyi
data:
  config.yaml: |
    server:
      port: 8080
      mode: release

    database:
      type: sqlserver
      host: ${DB_HOST}
      port: ${DB_PORT}
      database: ${DB_NAME}
      username: ${DB_USER}
      password: ${DB_PASSWORD}
      max_open_conns: 100
      max_idle_conns: 20
      conn_max_lifetime: 30m

    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      database: 0
      pool_size: 100
      min_idle_conns: 10

    logger:
      level: info
      format: json
      path: ./logs
      max_size: 100
      max_backups: 10
      max_age: 30
      compress: true

    jwt:
      secret: ${JWT_SECRET}
      expire: 24h
      issuer: ruoyi-go
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ruoyi-go
  namespace: ruoyi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ruoyi-go
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80 