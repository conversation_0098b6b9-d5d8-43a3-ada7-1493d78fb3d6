package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"

	"backend/internal/api/router"
	"backend/internal/config"
	"backend/internal/utils/logger"
)

var frontendIntegrationServer *gin.Engine
var frontendIntegrationDb *gorm.DB

// 初始化测试环境
func init() {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 初始化配置
	config.InitConfig("../../config/config.yaml")

	// 初始化日志
	logger.InitLogger(&config.Conf.Logger)

	// 初始化数据库
	dbConfig := &config.Conf.Database
	// SQL Server连接字符串
	dsn := fmt.Sprintf("server=%s;port=%d;database=%s;user id=%s;password=%s;",
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Username,
		dbConfig.Password)

	var err error
	frontendIntegrationDb, err = gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.NewGormLogger(logger.GetLogger()),
	})
	if err != nil {
		fmt.Printf("连接数据库失败: %v\n", err)
		return
	}

	// 初始化路由
	frontendIntegrationServer = router.InitRouter()

	// 等待服务启动
	time.Sleep(2 * time.Second)
}

// 登录请求结构体
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// 登录响应结构体
type LoginResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Token   string `json:"token"`
		Expires int    `json:"expires"`
	} `json:"data"`
}

// 测试登录API - 前端与后端集成测试
func TestLogin(t *testing.T) {
	// 准备登录请求
	loginRequest := LoginRequest{
		Username: "admin",
		Password: "admin123",
	}
	requestBody, _ := json.Marshal(loginRequest)

	// 创建请求
	req := httptest.NewRequest(http.MethodPost, "/api/v1/login", bytes.NewReader(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// 记录响应
	w := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(w, req)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	responseBody, _ := io.ReadAll(w.Body)
	var loginResponse LoginResponse
	err := json.Unmarshal(responseBody, &loginResponse)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, 200, loginResponse.Code)
	assert.NotEmpty(t, loginResponse.Data.Token)
	assert.Greater(t, loginResponse.Data.Expires, 0)

	// 验证token可用性
	testGetUserInfoWithToken(t, loginResponse.Data.Token)
}

// 用户信息响应结构体
type UserInfoResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		User struct {
			UserId   int    `json:"userId"`
			Username string `json:"username"`
			Nickname string `json:"nickname"`
		} `json:"user"`
		Roles       []string `json:"roles"`
		Permissions []string `json:"permissions"`
	} `json:"data"`
}

// 测试获取用户信息API - 使用登录获取的token
func testGetUserInfoWithToken(t *testing.T, token string) {
	// 创建请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/getInfo", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// 记录响应
	w := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(w, req)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	responseBody, _ := io.ReadAll(w.Body)
	var userInfoResponse UserInfoResponse
	err := json.Unmarshal(responseBody, &userInfoResponse)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, 200, userInfoResponse.Code)
	assert.Equal(t, "admin", userInfoResponse.Data.User.Username)
	assert.NotEmpty(t, userInfoResponse.Data.Roles)
	assert.NotEmpty(t, userInfoResponse.Data.Permissions)
}

// 菜单路由响应结构体
type RouterResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    []struct {
		Name      string `json:"name"`
		Path      string `json:"path"`
		Hidden    bool   `json:"hidden"`
		Component string `json:"component"`
		Meta      struct {
			Title   string `json:"title"`
			Icon    string `json:"icon"`
			NoCache bool   `json:"noCache"`
		} `json:"meta"`
		Children []struct {
			Name      string `json:"name"`
			Path      string `json:"path"`
			Component string `json:"component"`
			Meta      struct {
				Title   string `json:"title"`
				Icon    string `json:"icon"`
				NoCache bool   `json:"noCache"`
			} `json:"meta"`
		} `json:"children,omitempty"`
	} `json:"data"`
}

// 测试获取路由信息API - 前端与后端集成测试
func TestGetRouters(t *testing.T) {
	// 先登录获取token
	loginRequest := LoginRequest{
		Username: "admin",
		Password: "admin123",
	}
	requestBody, _ := json.Marshal(loginRequest)

	// 创建登录请求
	loginReq := httptest.NewRequest(http.MethodPost, "/api/v1/login", bytes.NewReader(requestBody))
	loginReq.Header.Set("Content-Type", "application/json")

	// 记录响应
	loginW := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(loginW, loginReq)

	// 解析登录响应体
	loginResponseBody, _ := io.ReadAll(loginW.Body)
	var loginResponse LoginResponse
	json.Unmarshal(loginResponseBody, &loginResponse)

	// 获取token
	token := loginResponse.Data.Token

	// 创建获取路由请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/getRouters", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// 记录响应
	w := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(w, req)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	responseBody, _ := io.ReadAll(w.Body)
	var routerResponse RouterResponse
	err := json.Unmarshal(responseBody, &routerResponse)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, 200, routerResponse.Code)
	assert.NotEmpty(t, routerResponse.Data)

	// 检查系统管理菜单是否存在
	foundSystem := false
	for _, route := range routerResponse.Data {
		if route.Meta.Title == "系统管理" {
			foundSystem = true
			break
		}
	}
	assert.True(t, foundSystem, "未找到系统管理菜单")
}

// 用户列表请求参数
type UserListRequest struct {
	PageNum  int    `json:"pageNum"`
	PageSize int    `json:"pageSize"`
	Username string `json:"username,omitempty"`
	Status   string `json:"status,omitempty"`
}

// 用户列表响应结构体
type UserListResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Total int `json:"total"`
		Rows  []struct {
			UserId      int    `json:"userId"`
			Username    string `json:"username"`
			Nickname    string `json:"nickname"`
			Email       string `json:"email"`
			PhoneNumber string `json:"phoneNumber"`
			Status      string `json:"status"`
		} `json:"rows"`
	} `json:"data"`
}

// 测试获取用户列表API - 前端与后端集成测试
func TestGetUserList(t *testing.T) {
	// 先登录获取token
	loginRequest := LoginRequest{
		Username: "admin",
		Password: "admin123",
	}
	requestBody, _ := json.Marshal(loginRequest)

	// 创建登录请求
	loginReq := httptest.NewRequest(http.MethodPost, "/api/v1/login", bytes.NewReader(requestBody))
	loginReq.Header.Set("Content-Type", "application/json")

	// 记录响应
	loginW := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(loginW, loginReq)

	// 解析登录响应体
	loginResponseBody, _ := io.ReadAll(loginW.Body)
	var loginResponse LoginResponse
	json.Unmarshal(loginResponseBody, &loginResponse)

	// 获取token
	token := loginResponse.Data.Token

	// 创建用户列表请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/system/user/list?pageNum=1&pageSize=10", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// 记录响应
	w := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(w, req)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	responseBody, _ := io.ReadAll(w.Body)
	var userListResponse UserListResponse
	err := json.Unmarshal(responseBody, &userListResponse)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, 200, userListResponse.Code)
	assert.NotEmpty(t, userListResponse.Data.Rows)
	assert.Greater(t, userListResponse.Data.Total, 0)

	// 检查admin用户是否存在
	foundAdmin := false
	for _, user := range userListResponse.Data.Rows {
		if user.Username == "admin" {
			foundAdmin = true
			break
		}
	}
	assert.True(t, foundAdmin, "未找到admin用户")
}

// 部门树响应结构体
type DeptTreeResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    []struct {
		DeptId   int    `json:"deptId"`
		ParentId int    `json:"parentId"`
		DeptName string `json:"deptName"`
		OrderNum int    `json:"orderNum"`
		Leader   string `json:"leader"`
		Status   string `json:"status"`
		Children []struct {
			DeptId   int    `json:"deptId"`
			ParentId int    `json:"parentId"`
			DeptName string `json:"deptName"`
			OrderNum int    `json:"orderNum"`
			Leader   string `json:"leader"`
			Status   string `json:"status"`
		} `json:"children,omitempty"`
	} `json:"data"`
}

// 测试获取部门树API - 前端与后端集成测试
func TestGetDeptTree(t *testing.T) {
	// 先登录获取token
	loginRequest := LoginRequest{
		Username: "admin",
		Password: "admin123",
	}
	requestBody, _ := json.Marshal(loginRequest)

	// 创建登录请求
	loginReq := httptest.NewRequest(http.MethodPost, "/api/v1/login", bytes.NewReader(requestBody))
	loginReq.Header.Set("Content-Type", "application/json")

	// 记录响应
	loginW := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(loginW, loginReq)

	// 解析登录响应体
	loginResponseBody, _ := io.ReadAll(loginW.Body)
	var loginResponse LoginResponse
	json.Unmarshal(loginResponseBody, &loginResponse)

	// 获取token
	token := loginResponse.Data.Token

	// 创建部门树请求
	req := httptest.NewRequest(http.MethodGet, "/api/v1/system/dept/tree", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// 记录响应
	w := httptest.NewRecorder()
	frontendIntegrationServer.ServeHTTP(w, req)

	// 检查响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应体
	responseBody, _ := io.ReadAll(w.Body)
	var deptTreeResponse DeptTreeResponse
	err := json.Unmarshal(responseBody, &deptTreeResponse)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, 200, deptTreeResponse.Code)
	assert.NotEmpty(t, deptTreeResponse.Data)

	// 检查根部门是否存在
	assert.GreaterOrEqual(t, len(deptTreeResponse.Data), 1, "未找到根部门")
}
