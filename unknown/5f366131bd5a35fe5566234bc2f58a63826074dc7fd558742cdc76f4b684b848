package service

// IGenTableService Service interface
type IGenTableService interface {
	SelectGenTableList(genTable GenTable) []GenTable
	SelectDbTableList(genTable GenTable) []GenTable
	SelectDbTableListByNames(tableNames []string) []GenTable
	SelectGenTableAll() []GenTable
	SelectGenTableById(id int64) GenTable
	UpdateGenTable(genTable GenTable)
	DeleteGenTableByIds(tableIds []int64)
	CreateTable(sql string) boolean
	ImportGenTable(tableList []GenTable, operName string)
	String> previewCode(tableId int64) map[string]interface{}
	DownloadCode(tableName string) []byte
	GeneratorCode(tableName string)
	SynchDb(tableName string)
	DownloadCode(tableNames []string) []byte
	ValidateEdit(genTable GenTable)
}
