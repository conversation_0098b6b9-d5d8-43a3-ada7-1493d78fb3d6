package middleware

import (
	"bytes"
	"encoding/json"
	"html"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// XSSProtection XSS防护中间件
func XSSProtection() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过GET请求
		if c.Request.Method == http.MethodGet {
			c.Next()
			return
		}

		// 读取请求体
		var bodyBytes []byte
		if c.Request.Body != nil {
			bodyBytes, _ = io.ReadAll(c.Request.Body)
		}

		// 恢复请求体
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		// 如果是JSON请求
		contentType := c.GetHeader("Content-Type")
		if strings.Contains(contentType, "application/json") {
			// 解析JSON
			var data interface{}
			if err := json.Unmarshal(bodyBytes, &data); err != nil {
				c.Next()
				return
			}

			// 递归处理JSON数据
			sanitizedData := sanitizeJSON(data)

			// 重新编码JSON
			sanitizedJSON, err := json.Marshal(sanitizedData)
			if err != nil {
				c.Next()
				return
			}

			// 替换请求体
			c.Request.Body = io.NopCloser(bytes.NewBuffer(sanitizedJSON))
			c.Request.ContentLength = int64(len(sanitizedJSON))
		} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			// 处理表单数据
			c.Request.ParseForm()
			for key, values := range c.Request.PostForm {
				sanitizedValues := make([]string, len(values))
				for i, value := range values {
					sanitizedValues[i] = sanitizeString(value)
				}
				c.Request.PostForm[key] = sanitizedValues
			}
		}

		// 继续处理请求
		c.Next()
	}
}

// sanitizeJSON 递归处理JSON数据
func sanitizeJSON(data interface{}) interface{} {
	switch v := data.(type) {
	case string:
		return sanitizeString(v)
	case map[string]interface{}:
		sanitizedMap := make(map[string]interface{})
		for key, value := range v {
			sanitizedMap[key] = sanitizeJSON(value)
		}
		return sanitizedMap
	case []interface{}:
		sanitizedArray := make([]interface{}, len(v))
		for i, value := range v {
			sanitizedArray[i] = sanitizeJSON(value)
		}
		return sanitizedArray
	default:
		return v
	}
}

// sanitizeString 处理字符串
func sanitizeString(value string) string {
	// 简单的HTML实体编码
	return html.EscapeString(value)
}
