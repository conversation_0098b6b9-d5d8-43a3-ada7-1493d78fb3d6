package monitor

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// HealthStatus 健康状态
type HealthStatus string

const (
	// StatusUp 状态正常
	StatusUp HealthStatus = "UP"
	// StatusDown 状态异常
	StatusDown HealthStatus = "DOWN"
	// StatusUnknown 状态未知
	StatusUnknown HealthStatus = "UNKNOWN"
)

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Status      HealthStatus                `json:"status"`
	Details     map[string]string           `json:"details,omitempty"`
	Error       string                      `json:"error,omitempty"`
	LastChecked time.Time                   `json:"lastChecked"`
	Components  map[string]*ComponentHealth `json:"components,omitempty"`
}

// HealthCheck 健康检查接口
type HealthCheck interface {
	// Name 获取健康检查名称
	Name() string

	// Check 执行健康检查
	Check() *ComponentHealth
}

// SystemHealthChecker 系统健康检查器
type SystemHealthChecker struct {
	components map[string]HealthCheck
	results    map[string]*ComponentHealth
	mutex      sync.RWMutex
	ticker     *time.Ticker
}

// NewSystemHealthChecker 创建系统健康检查器
func NewSystemHealthChecker() *SystemHealthChecker {
	checker := &SystemHealthChecker{
		components: make(map[string]HealthCheck),
		results:    make(map[string]*ComponentHealth),
		ticker:     time.NewTicker(30 * time.Second),
	}

	// 启动定时检查
	go checker.startChecking()

	return checker
}

// RegisterCheck 注册健康检查
func (s *SystemHealthChecker) RegisterCheck(check HealthCheck) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.components[check.Name()] = check
}

// startChecking 开始定时检查
func (s *SystemHealthChecker) startChecking() {
	// 先执行一次检查
	s.checkAll()

	// 定时执行检查
	for range s.ticker.C {
		s.checkAll()
	}
}

// checkAll 检查所有组件
func (s *SystemHealthChecker) checkAll() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for name, check := range s.components {
		s.results[name] = check.Check()
	}
}

// GetResults 获取检查结果
func (s *SystemHealthChecker) GetResults() map[string]*ComponentHealth {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 深拷贝结果
	results := make(map[string]*ComponentHealth)
	for name, health := range s.results {
		results[name] = health
	}

	return results
}

// GetOverallStatus 获取整体状态
func (s *SystemHealthChecker) GetOverallStatus() HealthStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if len(s.results) == 0 {
		return StatusUnknown
	}

	// 如果任何组件状态为DOWN，则整体状态为DOWN
	for _, health := range s.results {
		if health.Status == StatusDown {
			return StatusDown
		}
	}

	return StatusUp
}

// Stop 停止健康检查
func (s *SystemHealthChecker) Stop() {
	if s.ticker != nil {
		s.ticker.Stop()
	}
}

// DatabaseHealthCheck 数据库健康检查
type DatabaseHealthCheck struct {
	db *sql.DB
}

// NewDatabaseHealthCheck 创建数据库健康检查
func NewDatabaseHealthCheck(db *sql.DB) *DatabaseHealthCheck {
	return &DatabaseHealthCheck{
		db: db,
	}
}

// Name 获取健康检查名称
func (d *DatabaseHealthCheck) Name() string {
	return "database"
}

// Check 执行健康检查
func (d *DatabaseHealthCheck) Check() *ComponentHealth {
	health := &ComponentHealth{
		Status:      StatusUnknown,
		Details:     make(map[string]string),
		LastChecked: time.Now(),
	}

	if d.db == nil {
		health.Status = StatusDown
		health.Error = "数据库连接未初始化"
		return health
	}

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 执行Ping
	err := d.db.PingContext(ctx)
	if err != nil {
		health.Status = StatusDown
		health.Error = err.Error()
		return health
	}

	// 获取数据库统计信息
	stats := d.db.Stats()
	health.Details["maxOpenConnections"] = toString(stats.MaxOpenConnections)
	health.Details["openConnections"] = toString(stats.OpenConnections)
	health.Details["inUse"] = toString(stats.InUse)
	health.Details["idle"] = toString(stats.Idle)
	health.Details["waitCount"] = toString(stats.WaitCount)

	health.Status = StatusUp
	return health
}

// HTTPHealthCheck HTTP健康检查
type HTTPHealthCheck struct {
	url     string
	timeout time.Duration
	client  *http.Client
}

// NewHTTPHealthCheck 创建HTTP健康检查
func NewHTTPHealthCheck(url string, timeout time.Duration) *HTTPHealthCheck {
	return &HTTPHealthCheck{
		url:     url,
		timeout: timeout,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// Name 获取健康检查名称
func (h *HTTPHealthCheck) Name() string {
	return "http"
}

// Check 执行健康检查
func (h *HTTPHealthCheck) Check() *ComponentHealth {
	health := &ComponentHealth{
		Status:      StatusUnknown,
		Details:     make(map[string]string),
		LastChecked: time.Now(),
	}

	// 创建请求
	req, err := http.NewRequest("GET", h.url, nil)
	if err != nil {
		health.Status = StatusDown
		health.Error = err.Error()
		return health
	}

	// 发送请求
	start := time.Now()
	resp, err := h.client.Do(req)
	duration := time.Since(start)

	if err != nil {
		health.Status = StatusDown
		health.Error = err.Error()
		return health
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		health.Status = StatusDown
		health.Error = "HTTP响应状态码: " + toString(resp.StatusCode)
		return health
	}

	health.Status = StatusUp
	health.Details["responseTime"] = duration.String()
	health.Details["statusCode"] = toString(resp.StatusCode)

	return health
}

// 转换为字符串
func toString(value interface{}) string {
	switch v := value.(type) {
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case string:
		return v
	default:
		return fmt.Sprintf("%v", v)
	}
}
