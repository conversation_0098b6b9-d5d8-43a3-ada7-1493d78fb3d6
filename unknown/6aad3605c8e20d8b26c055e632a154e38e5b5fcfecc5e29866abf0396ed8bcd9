package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysDictDataController 字典数据控制器
type SysDictDataController struct {
	controller.BaseController
	dictDataService service.ISysDictDataService
	dictTypeService service.ISysDictTypeService
}

// NewSysDictDataController 创建字典数据控制器
func NewSysDictDataController(
	logger *zap.Logger,
	dictDataService service.ISysDictDataService,
	dictTypeService service.ISysDictTypeService,
) *SysDictDataController {
	return &SysDictDataController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		dictDataService: dictDataService,
		dictTypeService: dictTypeService,
	}
}

// RegisterRoutes 注册路由
func (c *SysDictDataController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:dictCode", c.GetInfo)
	r.GET("/type/:dictType", c.DictType)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:dictCodes", c.Remove)
}

// List 获取字典数据列表
func (c *SysDictDataController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	dictData := &domain.SysDictData{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取字典数据列表
	list := c.dictDataService.SelectDictDataList(*dictData)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// Export 导出字典数据
func (c *SysDictDataController) Export(ctx *gin.Context) {
	// 构建查询条件
	dictData := &domain.SysDictData{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取字典数据列表
	list := c.dictDataService.SelectDictDataList(*dictData)

	// TODO: 实现Excel导出
	c.SuccessWithMessage(ctx, "导出成功")
}

// GetInfo 根据字典编码获取字典数据详细信息
func (c *SysDictDataController) GetInfo(ctx *gin.Context) {
	dictCodeStr := ctx.Param("dictCode")
	dictCode, err := strconv.ParseInt(dictCodeStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "字典编码格式错误")
		return
	}

	// 获取字典数据信息
	dictData := c.dictDataService.SelectDictDataById(dictCode)
	c.SuccessWithData(ctx, dictData)
}

// DictType 根据字典类型查询字典数据信息
func (c *SysDictDataController) DictType(ctx *gin.Context) {
	dictType := ctx.Param("dictType")

	// 获取字典数据列表
	data := c.dictTypeService.SelectDictDataByType(dictType)
	if data == nil {
		data = make([]domain.SysDictData, 0)
	}

	c.SuccessWithData(ctx, data)
}

// Add 新增字典数据
func (c *SysDictDataController) Add(ctx *gin.Context) {
	var dictData domain.SysDictData
	if err := ctx.ShouldBindJSON(&dictData); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 设置创建者
	dictData.CreateBy = c.GetUsername(ctx)

	// 新增字典数据
	rows := c.dictDataService.InsertDictData(dictData)
	c.ToAjax(ctx, rows)
}

// Edit 修改保存字典数据
func (c *SysDictDataController) Edit(ctx *gin.Context) {
	var dictData domain.SysDictData
	if err := ctx.ShouldBindJSON(&dictData); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 设置更新者
	dictData.UpdateBy = c.GetUsername(ctx)

	// 更新字典数据
	rows := c.dictDataService.UpdateDictData(dictData)
	c.ToAjax(ctx, rows)
}

// Remove 删除字典数据
func (c *SysDictDataController) Remove(ctx *gin.Context) {
	dictCodesStr := ctx.Param("dictCodes")
	dictCodes := strings.Split(dictCodesStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(dictCodes))
	for _, codeStr := range dictCodes {
		code, err := strconv.ParseInt(codeStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, code)
	}

	// 删除字典数据
	c.dictDataService.DeleteDictDataByIds(ids)
	c.Success(ctx)
}
