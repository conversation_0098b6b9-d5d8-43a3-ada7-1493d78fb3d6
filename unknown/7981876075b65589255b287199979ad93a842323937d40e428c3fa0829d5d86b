package monitor

import (
	"backend/internal/common/response"
	"backend/internal/monitor"
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthController 健康检查控制器
type HealthController struct {
	healthChecker  *monitor.SystemHealthChecker
	processMonitor *monitor.ProcessMonitor
}

// NewHealthController 创建健康检查控制器
func NewHealthController(healthChecker *monitor.SystemHealthChecker, processMonitor *monitor.ProcessMonitor) *HealthController {
	return &HealthController{
		healthChecker:  healthChecker,
		processMonitor: processMonitor,
	}
}

// Check 健康检查
func (h *HealthController) Check(c *gin.Context) {
	// 获取健康检查结果
	results := h.healthChecker.GetResults()

	// 获取整体状态
	status := h.healthChecker.GetOverallStatus()

	// 构建响应
	data := map[string]interface{}{
		"status":     status,
		"components": results,
	}

	// 根据状态设置响应状态码
	httpStatus := http.StatusOK
	if status == monitor.StatusDown {
		httpStatus = http.StatusServiceUnavailable
	}

	c.<PERSON>(httpStatus, data)
}

// ServerInfo 服务器信息
func (h *HealthController) ServerInfo(c *gin.Context) {
	// 获取进程信息
	processInfo := h.processMonitor.GetProcessInfo()

	// 获取内存信息
	memoryInfo := h.processMonitor.GetMemoryInfo()

	// 构建响应
	data := map[string]interface{}{
		"process": processInfo,
		"memory":  memoryInfo,
	}

	response.Success(c, data)
}

// Health 健康状态
func (h *HealthController) Health(c *gin.Context) {
	// 获取整体状态
	status := h.healthChecker.GetOverallStatus()

	// 构建响应
	data := map[string]interface{}{
		"status": status,
	}

	// 根据状态设置响应状态码
	httpStatus := http.StatusOK
	if status == monitor.StatusDown {
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, data)
}
