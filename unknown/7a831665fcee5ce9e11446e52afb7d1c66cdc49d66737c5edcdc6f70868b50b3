package routes

import (
	"backend/internal/job/controller"
	"backend/internal/job/service/impl"
	"backend/internal/repository"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterJobRoutes 注册定时任务相关路由
func RegisterJobRoutes(r *gin.Engine, db *gorm.DB) {
	// 创建存储库
	jobRepo := repository.NewSysJobRepository(db)
	jobLogRepo := repository.NewSysJobLogRepository(db)

	// 创建服务
	jobLogService := impl.NewSysJobLogService(jobLogRepo)
	jobService := impl.NewSysJobService(jobRepo, jobLogService)

	// 创建控制器
	jobController := controller.NewSysJobController(jobService)
	jobLogController := controller.NewSysJobLogController(jobLogService)

	// 定时任务路由
	jobRoutes := r.Group("/monitor/job")
	{
		jobRoutes.GET("/list", jobController.List)
		jobRoutes.GET("/:jobId", jobController.GetInfo)
		jobRoutes.POST("", jobController.Add)
		jobRoutes.PUT("", jobController.Edit)
		jobRoutes.DELETE("/:jobIds", jobController.Remove)
		jobRoutes.PUT("/changeStatus", jobController.ChangeStatus)
		jobRoutes.PUT("/run", jobController.Run)
		jobRoutes.GET("/checkCronExpressionIsValid", jobController.CheckCronExpressionIsValid)
		jobRoutes.POST("/export", jobController.Export)
	}

	// 定时任务日志路由
	jobLogRoutes := r.Group("/monitor/jobLog")
	{
		jobLogRoutes.GET("/list", jobLogController.List)
		jobLogRoutes.GET("/:jobLogId", jobLogController.GetInfo)
		jobLogRoutes.DELETE("/:jobLogIds", jobLogController.Remove)
		jobLogRoutes.DELETE("/clean", jobLogController.Clean)
		jobLogRoutes.POST("/export", jobLogController.Export)
	}
}
