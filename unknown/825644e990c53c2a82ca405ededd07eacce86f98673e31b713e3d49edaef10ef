package system

import (
	"backend/internal/api/controller"
	"fmt"

	"github.com/gin-gonic/gin"
)

// SysIndexController 首页控制器
type SysIndexController struct {
	controller.BaseController
	name    string // 系统名称
	version string // 系统版本
}

// NewSysIndexController 创建首页控制器
func NewSysIndexController(name, version string) *SysIndexController {
	return &SysIndexController{
		name:    name,
		version: version,
	}
}

// Index 访问首页，提示语
// @Summary 首页
// @Description 访问首页，提示语
// @Tags 首页
// @Accept json
// @Produce json
// @Success 200 {string} string "成功"
// @Router / [get]
func (c *SysIndexController) Index(ctx *gin.Context) {
	message := fmt.Sprintf("欢迎使用%s后台管理框架，当前版本：v%s，请通过前端地址访问。", c.name, c.version)
	ctx.String(200, message)
}
