package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"errors"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysProfileController 个人信息业务处理
type SysProfileController struct {
	controller.BaseController
	userService  service.SysUserService
	tokenService service.TokenService
	avatarPath   string // 头像上传路径
}

// NewSysProfileController 创建个人信息控制器
func NewSysProfileController(userService service.SysUserService, tokenService service.TokenService, avatarPath string) *SysProfileController {
	return &SysProfileController{
		userService:  userService,
		tokenService: tokenService,
		avatarPath:   avatarPath,
	}
}

// GetProfile 获取个人信息
// @Summary 获取个人信息
// @Description 获取当前登录用户的个人信息
// @Tags 个人信息
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /system/user/profile [get]
func (c *SysProfileController) GetProfile(ctx *gin.Context) {
	// 获取登录用户信息
	loginUser, exists := ctx.Get("loginUser")
	if !exists {
		c.Unauthorized(ctx, "用户未登录")
		return
	}

	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		c.Error(ctx, errors.New("获取用户信息失败"))
		return
	}

	// 获取角色组
	roleGroup, err := c.userService.SelectUserRoleGroup(user.User.UserName)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取岗位组
	postGroup, err := c.userService.SelectUserPostGroup(user.User.UserName)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建返回结果
	result := map[string]interface{}{
		"user":      user.User,
		"roleGroup": roleGroup,
		"postGroup": postGroup,
	}

	c.Success(ctx, result)
}

// UpdateProfile 修改个人信息
// @Summary 修改个人信息
// @Description 修改当前登录用户的个人信息
// @Tags 个人信息
// @Accept json
// @Produce json
// @Param user body model.SysUser true "用户信息"
// @Success 200 {object} controller.Response "成功"
// @Router /system/user/profile [put]
func (c *SysProfileController) UpdateProfile(ctx *gin.Context) {
	// 获取登录用户信息
	loginUser, exists := ctx.Get("loginUser")
	if !exists {
		c.Unauthorized(ctx, "用户未登录")
		return
	}

	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		c.Error(ctx, errors.New("获取用户信息失败"))
		return
	}

	// 获取请求参数
	var reqUser model.SysUser
	if err := ctx.ShouldBindJSON(&reqUser); err != nil {
		c.Error(ctx, err)
		return
	}

	// 更新用户信息
	currentUser := user.User
	currentUser.NickName = reqUser.NickName
	currentUser.Email = reqUser.Email
	currentUser.PhoneNumber = reqUser.PhoneNumber
	currentUser.Sex = reqUser.Sex

	// 校验手机号唯一性
	if currentUser.PhoneNumber != "" && !c.userService.CheckPhoneUnique(currentUser) {
		c.Warn(ctx, fmt.Sprintf("修改用户'%s'失败，手机号码已存在", user.User.UserName))
		return
	}

	// 校验邮箱唯一性
	if currentUser.Email != "" && !c.userService.CheckEmailUnique(currentUser) {
		c.Warn(ctx, fmt.Sprintf("修改用户'%s'失败，邮箱账号已存在", user.User.UserName))
		return
	}

	// 更新用户信息
	rows := c.userService.UpdateUserProfile(currentUser)
	if rows > 0 {
		// 更新缓存用户信息
		err := c.tokenService.RefreshToken(user)
		if err != nil {
			c.Error(ctx, err)
			return
		}
		c.Success(ctx, nil)
	} else {
		c.Warn(ctx, "修改个人信息异常，请联系管理员")
	}
}

// UpdatePwd 修改密码
// @Summary 修改密码
// @Description 修改当前登录用户的密码
// @Tags 个人信息
// @Accept json
// @Produce json
// @Param params body map[string]string true "密码信息"
// @Success 200 {object} controller.Response "成功"
// @Router /system/user/profile/updatePwd [put]
func (c *SysProfileController) UpdatePwd(ctx *gin.Context) {
	// 获取登录用户信息
	loginUser, exists := ctx.Get("loginUser")
	if !exists {
		c.Unauthorized(ctx, "用户未登录")
		return
	}

	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		c.Error(ctx, errors.New("获取用户信息失败"))
		return
	}

	// 获取请求参数
	var params map[string]string
	if err := ctx.ShouldBindJSON(&params); err != nil {
		c.Error(ctx, err)
		return
	}

	oldPassword := params["oldPassword"]
	newPassword := params["newPassword"]

	// 校验旧密码
	if !utils.MatchesPassword(oldPassword, user.User.Password) {
		c.Warn(ctx, "修改密码失败，旧密码错误")
		return
	}

	// 校验新密码是否与旧密码相同
	if utils.MatchesPassword(newPassword, user.User.Password) {
		c.Warn(ctx, "新密码不能与旧密码相同")
		return
	}

	// 加密新密码
	encryptedPassword := utils.EncryptPassword(newPassword)

	// 更新密码
	rows := c.userService.ResetUserPwd(user.UserID, encryptedPassword)
	if rows > 0 {
		// 更新缓存用户密码
		user.User.Password = encryptedPassword
		user.User.PwdUpdateDate = utils.GetNowDate()
		err := c.tokenService.RefreshToken(user)
		if err != nil {
			c.Error(ctx, err)
			return
		}
		c.Success(ctx, nil)
	} else {
		c.Warn(ctx, "修改密码异常，请联系管理员")
	}
}

// UploadAvatar 上传头像
// @Summary 上传头像
// @Description 上传用户头像
// @Tags 个人信息
// @Accept multipart/form-data
// @Produce json
// @Param avatarfile formData file true "头像文件"
// @Success 200 {object} controller.Response "成功"
// @Router /system/user/profile/avatar [post]
func (c *SysProfileController) UploadAvatar(ctx *gin.Context) {
	// 获取登录用户信息
	loginUser, exists := ctx.Get("loginUser")
	if !exists {
		c.Unauthorized(ctx, "用户未登录")
		return
	}

	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		c.Error(ctx, errors.New("获取用户信息失败"))
		return
	}

	// 获取上传文件
	file, err := ctx.FormFile("avatarfile")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if file == nil {
		c.Warn(ctx, "请选择文件")
		return
	}

	// 校验文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowExts := []string{".jpg", ".jpeg", ".png", ".gif"}
	allowed := false
	for _, allowExt := range allowExts {
		if ext == allowExt {
			allowed = true
			break
		}
	}

	if !allowed {
		c.Warn(ctx, "文件格式不支持，请上传"+strings.Join(allowExts, ",")+"格式")
		return
	}

	// 上传文件
	fileName := utils.GenerateUniqueFileName() + ext
	filePath := filepath.Join(c.avatarPath, fileName)
	if err := ctx.SaveUploadedFile(file, filePath); err != nil {
		c.Error(ctx, err)
		return
	}

	// 更新用户头像
	avatar := filepath.Join("/profile/avatar", fileName)
	if c.userService.UpdateUserAvatar(user.UserID, avatar) {
		// 删除旧头像
		oldAvatar := user.User.Avatar
		if oldAvatar != "" && !strings.HasPrefix(oldAvatar, "/static/") {
			utils.DeleteFile(filepath.Join(c.avatarPath, filepath.Base(oldAvatar)))
		}

		// 更新缓存用户头像
		user.User.Avatar = avatar
		err := c.tokenService.RefreshToken(user)
		if err != nil {
			c.Error(ctx, err)
			return
		}

		c.Success(ctx, map[string]string{
			"imgUrl": avatar,
		})
	} else {
		c.Warn(ctx, "上传图片异常，请联系管理员")
	}
}
