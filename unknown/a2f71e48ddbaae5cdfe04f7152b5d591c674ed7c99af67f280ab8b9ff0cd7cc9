package model

import (
	"time"
)

// GenTable 代码生成业务表
type GenTable struct {
	TableID        int64             `json:"tableId" gorm:"column:table_id;primaryKey"`      // 编号
	TableName      string            `json:"tableName" gorm:"column:table_name"`             // 表名称
	TableComment   string            `json:"tableComment" gorm:"column:table_comment"`       // 表描述
	SubTableName   string            `json:"subTableName" gorm:"column:sub_table_name"`      // 关联子表的表名
	SubTableFkName string            `json:"subTableFkName" gorm:"column:sub_table_fk_name"` // 子表关联的外键名
	ClassName      string            `json:"className" gorm:"column:class_name"`             // 实体类名称
	TplCategory    string            `json:"tplCategory" gorm:"column:tpl_category"`         // 使用的模板（crud单表操作 tree树表操作）
	PackageName    string            `json:"packageName" gorm:"column:package_name"`         // 生成包路径
	ModuleName     string            `json:"moduleName" gorm:"column:module_name"`           // 生成模块名
	BusinessName   string            `json:"businessName" gorm:"column:business_name"`       // 生成业务名
	FunctionName   string            `json:"functionName" gorm:"column:function_name"`       // 生成功能名
	FunctionAuthor string            `json:"functionAuthor" gorm:"column:function_author"`   // 生成功能作者
	GenType        string            `json:"genType" gorm:"column:gen_type"`                 // 生成代码方式（0zip压缩包 1自定义路径）
	GenPath        string            `json:"genPath" gorm:"column:gen_path"`                 // 生成路径（不填默认项目路径）
	Options        string            `json:"options" gorm:"column:options"`                  // 其它生成选项
	CreateBy       string            `json:"createBy" gorm:"column:create_by"`               // 创建者
	CreateTime     *time.Time        `json:"createTime" gorm:"column:create_time"`           // 创建时间
	UpdateBy       string            `json:"updateBy" gorm:"column:update_by"`               // 更新者
	UpdateTime     *time.Time        `json:"updateTime" gorm:"column:update_time"`           // 更新时间
	Remark         string            `json:"remark" gorm:"column:remark"`                    // 备注
	Columns        []*GenTableColumn `json:"columns" gorm:"-"`                               // 表列信息

	// 主键信息
	PkColumn *GenTableColumn `json:"pkColumn" gorm:"-"`
	// 子表信息
	SubTable *GenTable `json:"subTable" gorm:"-"`
	// 树编码字段
	TreeCode string `json:"treeCode" gorm:"-"`
	// 树父编码字段
	TreeParentCode string `json:"treeParentCode" gorm:"-"`
	// 树名称字段
	TreeName string `json:"treeName" gorm:"-"`
	// 上级菜单ID字段
	ParentMenuID int64 `json:"parentMenuId" gorm:"-"`
	// 上级菜单名称字段
	ParentMenuName string `json:"parentMenuName" gorm:"-"`
}

// TableNameStr 返回表名字符串
func (g GenTable) TableNameStr() string {
	return "gen_table"
}

// GetTableName 获取表名
func (g GenTable) GetTableName() string {
	return "gen_table"
}

// IsCrud 是否是单表操作
func (g GenTable) IsCrud() bool {
	return g.TplCategory == "crud"
}

// IsTree 是否是树表操作
func (g GenTable) IsTree() bool {
	return g.TplCategory == "tree"
}

// IsSub 是否是子表
func (g *GenTable) IsSub() bool {
	return g.TplCategory == "sub"
}
