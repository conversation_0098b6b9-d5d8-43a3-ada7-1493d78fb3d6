package tool

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SwaggerController swagger控制器
type SwaggerController struct{}

// NewSwaggerController 创建swagger控制器
func NewSwaggerController() *SwaggerController {
	return &SwaggerController{}
}

// RegisterRoutes 注册路由
func (c *SwaggerController) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}

// Redirect 重定向到swagger文档
func (c *SwaggerController) Redirect(ctx *gin.Context) {
	ctx.Redirect(302, "/swagger/index.html")
}
