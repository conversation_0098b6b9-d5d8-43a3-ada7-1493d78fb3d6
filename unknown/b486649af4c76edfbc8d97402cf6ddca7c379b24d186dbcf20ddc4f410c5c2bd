#!/bin/bash
#
# RuoYi-Go 启动脚本
#

# 程序名
APP_NAME="RuoYi-Go"
# 程序路径
APP_DIR=$(cd "$(dirname "$0")"; pwd)
# 程序可执行文件名
APP_BIN="main"
# 配置文件路径
CONFIG_FILE="${APP_DIR}/config/config.yaml"
# PID 文件路径
PID_FILE="${APP_DIR}/run.pid"
# 日志文件路径
LOG_PATH="${APP_DIR}/logs"
LOG_FILE="${LOG_PATH}/${APP_NAME}.log"

# 创建日志目录
mkdir -p "${LOG_PATH}"

# 检查 PID 文件是否存在
if [ -f "${PID_FILE}" ]; then
    PID=$(cat "${PID_FILE}")
    if kill -0 "${PID}" > /dev/null 2>&1; then
        echo "${APP_NAME} 已经在运行, PID: ${PID}"
        exit 1
    else
        rm -f "${PID_FILE}"
        echo "清理过期的 PID 文件"
    fi
fi

# 检查配置文件是否存在
if [ ! -f "${CONFIG_FILE}" ]; then
    echo "配置文件不存在: ${CONFIG_FILE}"
    exit 1
fi

# 编译程序
echo "编译 ${APP_NAME}..."
cd "${APP_DIR}" && go build -o "${APP_BIN}" main.go
if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

# 启动程序
echo "启动 ${APP_NAME}..."
nohup "${APP_DIR}/${APP_BIN}" > "${LOG_FILE}" 2>&1 &
PID=$!
echo "${PID}" > "${PID_FILE}"

echo "${APP_NAME} 已启动, PID: ${PID}"
echo "日志文件: ${LOG_FILE}"

# 检查程序是否启动成功
sleep 2
if kill -0 "${PID}" > /dev/null 2>&1; then
    echo "${APP_NAME} 启动成功"
else
    echo "${APP_NAME} 启动失败, 请查看日志文件: ${LOG_FILE}"
    exit 1
fi 