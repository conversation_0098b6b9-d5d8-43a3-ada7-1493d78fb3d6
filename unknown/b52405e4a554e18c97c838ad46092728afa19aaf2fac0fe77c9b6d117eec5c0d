package service

import (
	"github.com/ruoyi/backend/internal/domain"
)

// ISysUserOnlineService 在线用户服务接口
type ISysUserOnlineService interface {
	// SelectOnlineById 通过会话编号获取在线用户
	SelectOnlineById(string) *domain.SysUserOnline

	// SelectOnlineByUserName 通过用户名获取在线用户
	SelectOnlineByUserName(string) []*domain.SysUserOnline

	// SelectOnlineByIpaddr 通过IP地址获取在线用户
	SelectOnlineByIpaddr(string) []*domain.SysUserOnline

	// SelectOnlineList 获取在线用户列表
	SelectOnlineList(*domain.SysUserOnline) []*domain.SysUserOnline

	// DeleteOnlineById 强退在线用户
	DeleteOnlineById(string) int

	// DeleteOnlineByIds 批量强退在线用户
	DeleteOnlineByIds([]string) int

	// SaveOnline 保存在线用户
	SaveOnline(*domain.SysUserOnline)

	// ForceLogout 强制用户下线
	ForceLogout(string)
}
