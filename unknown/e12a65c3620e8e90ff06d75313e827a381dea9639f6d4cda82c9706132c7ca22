package middleware

import (
	"backend/internal/annotation"
	"backend/internal/model"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
)

// 数据权限常量
const (
	// 全部数据权限
	DATA_SCOPE_ALL = "1"
	// 自定数据权限
	DATA_SCOPE_CUSTOM = "2"
	// 部门数据权限
	DATA_SCOPE_DEPT = "3"
	// 部门及以下数据权限
	DATA_SCOPE_DEPT_AND_CHILD = "4"
	// 仅本人数据权限
	DATA_SCOPE_SELF = "5"
	// 数据权限过滤关键字
	DATA_SCOPE = "dataScope"
)

// DataScopeMiddleware 数据权限中间件
func DataScopeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前登录用户
		loginUser, exists := c.Get("loginUser")
		if !exists {
			c.Next()
			return
		}

		user, ok := loginUser.(*model.LoginUser)
		if !ok || user == nil || user.User == nil {
			c.Next()
			return
		}

		// 如果是超级管理员，则不过滤数据
		if user.User.IsAdmin() {
			c.Next()
			return
		}

		// 获取数据权限
		dataScope, exists := c.Get("dataScope")
		if !exists {
			c.Next()
			return
		}

		scope, ok := dataScope.(*annotation.DataScope)
		if !ok || scope == nil {
			c.Next()
			return
		}

		// 数据权限过滤
		sqlString := DataScopeFilter(user.User, scope.DeptAlias, scope.UserAlias, scope.Permission)
		if sqlString != "" {
			// 将数据权限SQL存储到上下文中
			c.Set(DATA_SCOPE, sqlString)
		}

		c.Next()
	}
}

// DataScopeFilter 数据范围过滤
func DataScopeFilter(user *model.SysUser, deptAlias, userAlias, permission string) string {
	if user == nil {
		return ""
	}

	// 如果是管理员，则不过滤数据
	if user.IsAdmin() {
		return ""
	}

	sqlString := strings.Builder{}
	conditions := make([]string, 0)
	scopeCustomIds := make([]string, 0)

	// 处理自定义权限
	for _, role := range user.Roles {
		if role.DataScope == DATA_SCOPE_CUSTOM && role.Status == "0" {
			// 检查权限
			if permission == "" || hasPermissionStr(role.Permissions, permission) {
				scopeCustomIds = append(scopeCustomIds, fmt.Sprintf("%d", role.RoleID))
			}
		}
	}

	// 处理不同的数据权限
	for _, role := range user.Roles {
		dataScope := role.DataScope

		// 跳过已处理的数据权限类型
		if containsStr(conditions, dataScope) || role.Status != "0" {
			continue
		}

		// 检查权限
		if permission != "" && !hasPermissionStr(role.Permissions, permission) {
			continue
		}

		// 全部数据权限
		if DATA_SCOPE_ALL == dataScope {
			sqlString.Reset()
			conditions = append(conditions, dataScope)
			break
		} else if DATA_SCOPE_CUSTOM == dataScope {
			// 自定义数据权限
			if len(scopeCustomIds) > 1 {
				// 多个自定数据权限使用in查询
				sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id IN (%s))", deptAlias, strings.Join(scopeCustomIds, ",")))
			} else if len(scopeCustomIds) == 1 {
				sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = %s)", deptAlias, scopeCustomIds[0]))
			}
		} else if DATA_SCOPE_DEPT == dataScope {
			// 部门数据权限
			sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id = %d", deptAlias, user.DeptID))
		} else if DATA_SCOPE_DEPT_AND_CHILD == dataScope {
			// 部门及以下数据权限
			sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id IN (SELECT dept_id FROM sys_dept WHERE dept_id = %d OR FIND_IN_SET(%d, ancestors))", deptAlias, user.DeptID, user.DeptID))
		} else if DATA_SCOPE_SELF == dataScope {
			// 仅本人数据权限
			if userAlias != "" {
				sqlString.WriteString(fmt.Sprintf(" OR %s.user_id = %d", userAlias, user.UserID))
			} else {
				// 数据权限为仅本人且没有userAlias别名不查询任何数据
				sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id = 0", deptAlias))
			}
		}

		conditions = append(conditions, dataScope)
	}

	// 如果没有匹配的权限，限制不查询任何数据
	if len(conditions) == 0 {
		sqlString.WriteString(fmt.Sprintf(" OR %s.dept_id = 0", deptAlias))
	}

	if sqlString.Len() > 0 {
		return " AND (" + sqlString.String()[4:] + ")"
	}

	return ""
}

// containsStr 判断字符串是否在切片中
func containsStr(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// hasPermissionStr 判断是否包含指定权限
func hasPermissionStr(permissions []string, permission string) bool {
	for _, p := range permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// SetDataScope 设置数据权限
func SetDataScope(c *gin.Context, deptAlias, userAlias, permission string) {
	scope := annotation.NewDataScope(deptAlias, userAlias, permission)
	c.Set("dataScope", scope)
}
