# RuoYi-Go 部署指南

本指南提供了多种部署 RuoYi-Go 应用的方法，包括Docker部署、Kubernetes部署和传统部署。

## 目录

- [环境要求](#环境要求)
- [Docker 部署](#docker-部署)
- [Kubernetes 部署](#kubernetes-部署)
- [传统部署](#传统部署)
- [数据库初始化](#数据库初始化)
- [常见问题](#常见问题)

## 环境要求

- Go 1.20+
- SQL Server 2012+
- Redis 6.0+

## Docker 部署

### 使用 Docker Compose

1. 确保已安装 Docker 和 Docker Compose
2. 克隆项目代码
3. 进入项目根目录
4. 构建并启动容器

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 使用 Dockerfile

如果不使用 Docker Compose，可以手动构建镜像并运行容器：

```bash
# 构建镜像
docker build -t ruoyi-go .

# 运行容器
docker run -d \
  -p 8080:8080 \
  -e DB_HOST=your-sqlserver-host \
  -e DB_PORT=1433 \
  -e DB_NAME=ruoyi \
  -e DB_USER=sa \
  -e DB_PASSWORD=YourStrongPassword123 \
  -e REDIS_HOST=your-redis-host \
  -e REDIS_PORT=6379 \
  --name ruoyi-go \
  ruoyi-go
```

## Kubernetes 部署

### 前提条件

- 一个可用的 Kubernetes 集群
- 已安装 kubectl 并配置了正确的 kubeconfig

### 部署步骤

1. 进入 `deploy/kubernetes` 目录
2. 按需修改配置文件
3. 创建命名空间和存储资源

```bash
kubectl apply -f namespace.yaml
kubectl apply -f storage.yaml
```

4. 部署数据库和缓存

```bash
kubectl apply -f secrets.yaml
kubectl apply -f database.yaml
```

5. 部署应用

```bash
kubectl apply -f deployment.yaml
```

6. 检查部署状态

```bash
kubectl get all -n ruoyi
```

## 传统部署

### 编译

```bash
# 编译
go build -o ruoyi-go .
```

### 运行

```bash
# 运行
./ruoyi-go
```

### 配置文件

默认使用 `config/config.yaml` 作为配置文件，可以通过环境变量修改配置路径：

```bash
export CONFIG_FILE=config/prod.yaml
```

## 数据库初始化

### SQL Server 2012 初始化

1. 创建数据库

```sql
CREATE DATABASE ruoyi;
GO
```

2. 执行初始化脚本

可以使用SQL Server Management Studio (SSMS) 或 sqlcmd 工具执行 `script/sql/ruoyi.sql` 初始化脚本：

```bash
sqlcmd -S localhost -U sa -P YourStrongPassword123 -d ruoyi -i script/sql/ruoyi.sql
```

## 常见问题

### 数据库连接失败

检查 SQL Server 配置：

1. 确保 SQL Server 允许远程连接
2. 检查防火墙是否允许 1433 端口通信
3. 验证连接字符串格式是否正确

### Redis 连接失败

1. 检查 Redis 服务是否正常运行
2. 验证 Redis 密码是否正确
3. 确保防火墙允许 6379 端口通信

### 应用启动失败

1. 检查日志文件查找详细错误信息
2. 验证配置文件中的设置是否正确
3. 确保应用有足够的权限访问相关资源 