package impl

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/ruoyi/backend/internal/domain"
	myredis "github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysUserOnlineServiceImpl 在线用户服务实现
type SysUserOnlineServiceImpl struct {
	logger       *zap.Logger
	redisClient  *redis.Client
	redisService myredis.RedisService
}

// NewSysUserOnlineService 创建在线用户服务
func NewSysUserOnlineService(logger *zap.Logger, redisClient *redis.Client, redisService myredis.RedisService) service.ISysUserOnlineService {
	return &SysUserOnlineServiceImpl{
		logger:       logger,
		redisClient:  redisClient,
		redisService: redisService,
	}
}

// SelectOnlineById 通过会话编号获取在线用户
func (s *SysUserOnlineServiceImpl) SelectOnlineById(tokenId string) *domain.SysUserOnline {
	ctx := context.Background()
	key := domain.LOGIN_TOKEN_KEY + tokenId

	// 获取Redis中的用户信息
	val, err := s.redisClient.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			// 键不存在
			return nil
		}
		s.logger.Error("获取在线用户失败", zap.String("tokenId", tokenId), zap.Error(err))
		return nil
	}

	// 解析用户信息
	var userOnline domain.SysUserOnline
	err = json.Unmarshal([]byte(val), &userOnline)
	if err != nil {
		s.logger.Error("解析在线用户失败", zap.String("tokenId", tokenId), zap.Error(err))
		return nil
	}

	return &userOnline
}

// SelectOnlineByUserName 通过用户名获取在线用户
func (s *SysUserOnlineServiceImpl) SelectOnlineByUserName(userName string) []*domain.SysUserOnline {
	ctx := context.Background()
	pattern := domain.LOGIN_TOKEN_KEY + "*"

	// 获取所有在线用户的键
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		s.logger.Error("获取在线用户键列表失败", zap.Error(err))
		return []*domain.SysUserOnline{}
	}

	result := make([]*domain.SysUserOnline, 0)

	// 遍历所有键，找出匹配用户名的在线用户
	for _, key := range keys {
		val, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			s.logger.Error("获取在线用户信息失败", zap.String("key", key), zap.Error(err))
			continue
		}

		var userOnline domain.SysUserOnline
		err = json.Unmarshal([]byte(val), &userOnline)
		if err != nil {
			s.logger.Error("解析在线用户失败", zap.String("key", key), zap.Error(err))
			continue
		}

		if userOnline.UserName == userName {
			userOnline.TokenId = strings.Replace(key, domain.LOGIN_TOKEN_KEY, "", 1)
			result = append(result, &userOnline)
		}
	}

	return result
}

// SelectOnlineByIpaddr 通过IP地址获取在线用户
func (s *SysUserOnlineServiceImpl) SelectOnlineByIpaddr(ipaddr string) []*domain.SysUserOnline {
	ctx := context.Background()
	pattern := domain.LOGIN_TOKEN_KEY + "*"

	// 获取所有在线用户的键
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		s.logger.Error("获取在线用户键列表失败", zap.Error(err))
		return []*domain.SysUserOnline{}
	}

	result := make([]*domain.SysUserOnline, 0)

	// 遍历所有键，找出匹配IP地址的在线用户
	for _, key := range keys {
		val, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			s.logger.Error("获取在线用户信息失败", zap.String("key", key), zap.Error(err))
			continue
		}

		var userOnline domain.SysUserOnline
		err = json.Unmarshal([]byte(val), &userOnline)
		if err != nil {
			s.logger.Error("解析在线用户失败", zap.String("key", key), zap.Error(err))
			continue
		}

		if userOnline.Ipaddr == ipaddr {
			userOnline.TokenId = strings.Replace(key, domain.LOGIN_TOKEN_KEY, "", 1)
			result = append(result, &userOnline)
		}
	}

	return result
}

// SelectOnlineList 获取在线用户列表
func (s *SysUserOnlineServiceImpl) SelectOnlineList(userOnline *domain.SysUserOnline) []*domain.SysUserOnline {
	ctx := context.Background()
	pattern := domain.LOGIN_TOKEN_KEY + "*"

	// 获取所有在线用户的键
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		s.logger.Error("获取在线用户键列表失败", zap.Error(err))
		return []*domain.SysUserOnline{}
	}

	result := make([]*domain.SysUserOnline, 0)

	// 遍历所有键，获取在线用户信息
	for _, key := range keys {
		val, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			s.logger.Error("获取在线用户信息失败", zap.String("key", key), zap.Error(err))
			continue
		}

		var online domain.SysUserOnline
		err = json.Unmarshal([]byte(val), &online)
		if err != nil {
			s.logger.Error("解析在线用户失败", zap.String("key", key), zap.Error(err))
			continue
		}

		online.TokenId = strings.Replace(key, domain.LOGIN_TOKEN_KEY, "", 1)

		// 根据条件过滤
		if userOnline != nil {
			if userOnline.Ipaddr != "" && userOnline.UserName != "" {
				// 同时按IP和用户名过滤
				if online.Ipaddr == userOnline.Ipaddr && online.UserName == userOnline.UserName {
					result = append(result, &online)
				}
			} else if userOnline.Ipaddr != "" {
				// 按IP过滤
				if online.Ipaddr == userOnline.Ipaddr {
					result = append(result, &online)
				}
			} else if userOnline.UserName != "" {
				// 按用户名过滤
				if online.UserName == userOnline.UserName {
					result = append(result, &online)
				}
			} else {
				// 无过滤条件
				result = append(result, &online)
			}
		} else {
			// 无过滤条件
			result = append(result, &online)
		}
	}

	return result
}

// DeleteOnlineById 强退在线用户
func (s *SysUserOnlineServiceImpl) DeleteOnlineById(tokenId string) int {
	ctx := context.Background()
	key := domain.LOGIN_TOKEN_KEY + tokenId

	// 删除在线用户
	result, err := s.redisClient.Del(ctx, key).Result()
	if err != nil {
		s.logger.Error("删除在线用户失败", zap.String("tokenId", tokenId), zap.Error(err))
		return 0
	}

	return int(result)
}

// DeleteOnlineByIds 批量强退在线用户
func (s *SysUserOnlineServiceImpl) DeleteOnlineByIds(tokenIds []string) int {
	count := 0
	for _, tokenId := range tokenIds {
		count += s.DeleteOnlineById(tokenId)
	}
	return count
}

// SaveOnline 保存在线用户
func (s *SysUserOnlineServiceImpl) SaveOnline(userOnline *domain.SysUserOnline) {
	ctx := context.Background()
	key := domain.LOGIN_TOKEN_KEY + userOnline.TokenId

	// 将用户信息序列化为JSON
	userOnlineJson, err := json.Marshal(userOnline)
	if err != nil {
		s.logger.Error("序列化在线用户失败", zap.Error(err))
		return
	}

	// 保存在线用户信息，有效期为12小时
	_, err = s.redisClient.Set(ctx, key, string(userOnlineJson), 12*time.Hour).Result()
	if err != nil {
		s.logger.Error("保存在线用户失败", zap.Error(err))
	}
}

// ForceLogout 强制用户下线
func (s *SysUserOnlineServiceImpl) ForceLogout(tokenId string) {
	s.DeleteOnlineById(tokenId)
}
