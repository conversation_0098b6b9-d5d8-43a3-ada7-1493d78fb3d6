# Java到Go迁移项目快速启动指南

## 🚀 快速开始

### 第一步：项目检查

在PowerShell中执行以下命令：

```powershell
# 进入项目目录
cd D:\wosm\backend

# 运行项目检查脚本
.\项目检查和修复脚本.ps1

# 或者手动检查
go version
go mod tidy
```

### 第二步：环境准备

#### 2.1 确认服务运行状态

**检查SQL Server 2012：**
```powershell
# 检查SQL Server服务
Get-Service -Name "MSSQLSERVER"

# 如果服务未运行，启动服务
Start-Service -Name "MSSQLSERVER"
```

**检查Redis（可选）：**
```powershell
# 如果安装了Redis，检查进程
Get-Process -Name "redis-server" -ErrorAction SilentlyContinue

# 如果没有Redis，项目会使用内存缓存
```

#### 2.2 修复常见问题

```powershell
# 运行修复脚本
.\项目检查和修复脚本.ps1 -Action fix
```

### 第三步：构建和运行

#### 3.1 构建项目

```powershell
# 方法1：使用脚本
.\项目检查和修复脚本.ps1 -Action build

# 方法2：手动构建
go build -o wosm-backend.exe cmd\main.go

# 方法3：使用生成的构建脚本
.\build.bat
```

#### 3.2 运行应用

```powershell
# 方法1：直接运行
.\wosm-backend.exe

# 方法2：使用启动脚本
.\start.bat

# 方法3：使用测试脚本
.\项目检查和修复脚本.ps1 -Action test
```

### 第四步：验证功能

#### 4.1 检查应用启动

应用启动后，您应该看到类似以下的日志输出：

```
{"level":"info","ts":**********.789,"caller":"main.go:24","msg":"服务启动中..."}
{"level":"info","ts":**********.790,"caller":"database.go:82","msg":"数据库连接成功"}
{"level":"info","ts":**********.791,"caller":"main.go:95","msg":"服务启动成功","port":8080}
```

#### 4.2 测试API接口

**健康检查：**
```powershell
# 使用curl（如果安装了）
curl http://localhost:8080/health

# 使用PowerShell
Invoke-RestMethod -Uri "http://localhost:8080/health" -Method Get
```

**预期响应：**
```json
{
  "status": "up",
  "service": "RuoYi-Go",
  "version": "1.0.0"
}
```

**登录接口测试：**
```powershell
# 使用PowerShell测试登录
$body = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body $body -ContentType "application/json"
```

## 🔧 常见问题解决

### 问题1：编译错误

**错误信息：**
```
go: module requires Go 1.18 or later
```

**解决方案：**
```powershell
# 检查Go版本
go version

# 如果版本过低，请升级Go到1.18+
```

### 问题2：数据库连接失败

**错误信息：**
```
连接数据库失败: cannot connect to SQL Server
```

**解决方案：**
1. 确认SQL Server 2012服务运行
2. 检查数据库配置
3. 验证数据库用户权限

```powershell
# 检查SQL Server服务
Get-Service -Name "MSSQLSERVER"

# 启动服务（如果未运行）
Start-Service -Name "MSSQLSERVER"
```

### 问题3：端口占用

**错误信息：**
```
listen tcp :8080: bind: address already in use
```

**解决方案：**
```powershell
# 查找占用8080端口的进程
netstat -ano | findstr :8080

# 终止占用进程（替换PID）
taskkill /PID <PID> /F

# 或者修改配置文件中的端口
```

### 问题4：Redis连接失败

**错误信息：**
```
Redis连接失败: dial tcp 127.0.0.1:6379: connect: connection refused
```

**解决方案：**
项目已配置为Redis连接失败时使用内存缓存，这不会影响基本功能。

如需启用Redis：
1. 安装并启动Redis服务
2. 修改main.go中的Redis初始化代码

## 📋 功能验证清单

### 基础功能验证

- [ ] 应用成功启动
- [ ] 数据库连接正常
- [ ] 健康检查接口响应正常
- [ ] 日志输出正常

### 核心功能验证

- [ ] 用户登录功能
- [ ] JWT令牌生成
- [ ] 权限验证
- [ ] 用户信息获取
- [ ] 菜单路由获取

### API接口验证

**公开接口（无需认证）：**
- [ ] GET /health - 健康检查
- [ ] POST /api/public/login - 用户登录
- [ ] GET /api/public/captcha - 验证码获取

**认证接口（需要JWT令牌）：**
- [ ] GET /api/getInfo - 获取用户信息
- [ ] GET /api/getRouters - 获取用户菜单
- [ ] POST /api/logout - 用户登出

## 🎯 下一步计划

### 立即执行（今天）

1. **运行项目检查脚本**
2. **修复发现的问题**
3. **成功启动应用**
4. **验证基础功能**

### 短期计划（1-3天）

1. **完善TODO方法**
2. **添加错误处理**
3. **完善API文档**
4. **添加基础测试**

### 中期计划（1周内）

1. **生产环境配置**
2. **性能优化**
3. **安全加固**
4. **部署文档**

## 📞 获取帮助

如果在执行过程中遇到问题：

1. **查看详细分析：** `迁移状况分析与落地方案.md`
2. **检查日志输出：** 应用启动时的控制台输出
3. **运行诊断脚本：** `.\项目检查和修复脚本.ps1`
4. **查看配置文件：** `configs\config.yaml`

## 🎉 成功标志

当您看到以下情况时，说明迁移项目已成功运行：

1. ✅ 应用启动无错误
2. ✅ 数据库连接成功
3. ✅ 健康检查接口返回正常
4. ✅ 登录接口可以正常使用
5. ✅ 可以获取用户信息和菜单

恭喜！您的Java到Go迁移项目已经成功运行！

## 🛠️ 高级操作指南

### 一键完整流程

如果您想要一次性完成所有检查、修复和构建：

```powershell
# 执行完整流程（推荐）
.\项目检查和修复脚本.ps1 -Action all

# 然后启动应用
.\quick-start.bat
```

### 开发调试模式

**启用详细日志：**
```powershell
# 设置环境变量启用调试模式
$env:LOG_LEVEL = "debug"
$env:APP_ENV = "dev"

# 启动应用
.\wosm-backend.exe
```

**实时代码修改：**
```powershell
# 使用go run进行开发（自动重新编译）
go run cmd/main.go

# 或者使用air工具实现热重载（需要先安装air）
# go install github.com/cosmtrek/air@latest
# air
```

### 数据库管理

**检查数据库连接：**
```powershell
# 使用sqlcmd连接数据库（如果安装了SQL Server工具）
sqlcmd -S localhost -d wosm -U sa -P F@2233 -Q "SELECT COUNT(*) FROM sys_user"
```

**初始化数据库表：**
```powershell
# 如果需要创建表结构，应用启动时会自动创建
# 或者手动运行迁移脚本（如果有的话）
```

### 性能监控

**监控应用性能：**
```powershell
# 查看应用进程信息
Get-Process -Name "wosm-backend" | Select-Object Name, CPU, WorkingSet

# 监控端口使用
netstat -ano | findstr :8080

# 查看日志文件
Get-Content logs\ruoyi.log -Tail 50 -Wait
```

### API接口测试

**使用专用测试脚本：**
```powershell
# 运行API测试脚本
.\test-api.ps1

# 或者使用检查脚本的API测试功能
.\项目检查和修复脚本.ps1 -Action api
```

**手动测试关键接口：**
```powershell
# 1. 健康检查
$health = Invoke-RestMethod -Uri "http://localhost:8080/health"
Write-Host "服务状态: $($health.status)"

# 2. 登录获取token
$loginBody = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body $loginBody -ContentType "application/json"
$token = $loginResponse.data.token

# 3. 获取用户信息
$headers = @{ Authorization = "Bearer $token" }
$userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers $headers
Write-Host "当前用户: $($userInfo.data.user.userName)"

# 4. 获取菜单路由
$routers = Invoke-RestMethod -Uri "http://localhost:8080/api/getRouters" -Headers $headers
Write-Host "菜单数量: $($routers.data.Count)"
```

## 🔍 故障排除指南

### 常见启动问题

**问题：应用启动后立即退出**
```powershell
# 检查配置文件
Get-Content configs\config.yaml | Select-String -Pattern "host|port|database"

# 检查环境变量
Get-ChildItem Env: | Where-Object Name -like "*DB_*"

# 查看详细错误信息
.\wosm-backend.exe 2>&1 | Tee-Object -FilePath startup-error.log
```

**问题：数据库连接超时**
```powershell
# 测试数据库连接
Test-NetConnection -ComputerName localhost -Port 1433

# 检查SQL Server服务
Get-Service -Name "*SQL*" | Where-Object Status -eq "Running"

# 验证数据库存在
sqlcmd -S localhost -U sa -P F@2233 -Q "SELECT name FROM sys.databases WHERE name='wosm'"
```

**问题：端口被占用**
```powershell
# 查找占用8080端口的进程
$process = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($process) {
    $processInfo = Get-Process -Id $process.OwningProcess
    Write-Host "端口8080被进程占用: $($processInfo.ProcessName) (PID: $($processInfo.Id))"

    # 询问是否终止进程
    $response = Read-Host "是否终止该进程? (y/N)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Stop-Process -Id $processInfo.Id -Force
        Write-Host "进程已终止"
    }
}
```

### 日志分析

**查看应用日志：**
```powershell
# 实时查看日志
Get-Content logs\ruoyi.log -Wait -Tail 20

# 搜索错误信息
Select-String -Path logs\ruoyi.log -Pattern "error|ERROR|fatal|FATAL" | Select-Object -Last 10

# 分析启动日志
Select-String -Path logs\ruoyi.log -Pattern "启动|连接|初始化" | Select-Object -Last 20
```

### 配置验证

**验证配置文件语法：**
```powershell
# 检查YAML语法（需要安装powershell-yaml模块）
# Install-Module powershell-yaml -Force
# $config = Get-Content configs\config.yaml -Raw | ConvertFrom-Yaml

# 简单的配置检查
$configContent = Get-Content configs\config.yaml -Raw
if ($configContent -match "host.*localhost" -and $configContent -match "port.*8080") {
    Write-Host "✅ 基础配置正常" -ForegroundColor Green
} else {
    Write-Host "⚠️  配置可能有问题" -ForegroundColor Yellow
}
```

## 📚 参考资源

### 项目文档
- `迁移状况分析与落地方案.md` - 详细的技术分析和修复方案
- `Java2Go.md` - 完整的迁移计划和进度跟踪
- `configs\config.yaml` - 应用配置文件

### 有用的命令
```powershell
# 项目信息
go mod graph                    # 查看依赖关系
go list -m all                  # 列出所有模块
go version -m wosm-backend.exe  # 查看编译信息

# 性能分析
go tool pprof http://localhost:8080/debug/pprof/profile  # CPU分析
go tool pprof http://localhost:8080/debug/pprof/heap     # 内存分析

# 代码质量
go vet ./...                    # 静态分析
go fmt ./...                    # 代码格式化
golangci-lint run              # 代码检查（需要安装golangci-lint）
```

### 在线资源
- [Go官方文档](https://golang.org/doc/)
- [Gin框架文档](https://gin-gonic.com/docs/)
- [GORM文档](https://gorm.io/docs/)
- [Zap日志库](https://github.com/uber-go/zap)

## 🎉 项目成功运行的标志

当您看到以下所有指标都正常时，说明迁移项目已经成功运行：

### 启动指标
- ✅ 应用启动无错误日志
- ✅ 数据库连接成功日志
- ✅ Redis连接成功（或内存缓存启用）
- ✅ 服务监听在8080端口

### 功能指标
- ✅ 健康检查接口返回 `{"status": "up"}`
- ✅ 登录接口返回JWT令牌
- ✅ 用户信息接口返回用户数据
- ✅ 菜单路由接口返回菜单数据

### 性能指标
- ✅ 接口响应时间 < 1秒
- ✅ 内存使用 < 100MB
- ✅ CPU使用率正常
- ✅ 数据库连接池正常

**🎊 恭喜！您已经成功完成了Java到Go的项目迁移！**
