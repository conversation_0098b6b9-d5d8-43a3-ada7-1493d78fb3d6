# Java到Go迁移项目检查和修复脚本
# 使用方法：在PowerShell中运行 .\项目检查和修复脚本.ps1

param(
    [string]$Action = "check"  # check, fix, build, test
)

# 设置项目路径
$ProjectPath = "D:\wosm\backend"
$JavaPath = "D:\wosm\ruoyi-java"

Write-Host "=== Java到Go迁移项目检查和修复工具 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

# 检查项目目录是否存在
if (-not (Test-Path $ProjectPath)) {
    Write-Host "错误: 项目目录不存在 $ProjectPath" -ForegroundColor Red
    exit 1
}

# 切换到项目目录
Set-Location $ProjectPath

function Check-Environment {
    Write-Host "`n=== 环境检查 ===" -ForegroundColor Cyan
    
    # 检查Go环境
    try {
        $goVersion = go version
        Write-Host "✅ Go环境: $goVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Go环境未安装或未配置" -ForegroundColor Red
        return $false
    }
    
    # 检查项目文件
    $requiredFiles = @(
        "go.mod",
        "configs\config.yaml",
        "cmd\main.go",
        "internal\database\database.go"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Host "✅ 文件存在: $file" -ForegroundColor Green
        } else {
            Write-Host "❌ 文件缺失: $file" -ForegroundColor Red
        }
    }
    
    return $true
}

function Check-Dependencies {
    Write-Host "`n=== 依赖检查 ===" -ForegroundColor Cyan
    
    try {
        Write-Host "正在检查Go模块依赖..." -ForegroundColor Yellow
        go mod tidy
        Write-Host "✅ 依赖检查完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 依赖检查失败: $_" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Check-Database {
    Write-Host "`n=== 数据库连接检查 ===" -ForegroundColor Cyan
    
    # 检查SQL Server服务
    try {
        $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        if ($sqlService -and $sqlService.Status -eq "Running") {
            Write-Host "✅ SQL Server服务运行中" -ForegroundColor Green
        } else {
            Write-Host "❌ SQL Server服务未运行" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️  无法检查SQL Server服务状态" -ForegroundColor Yellow
    }
    
    # 检查数据库配置文件
    $configFile = "configs\config.yaml"
    if (Test-Path $configFile) {
        $config = Get-Content $configFile -Raw
        if ($config -match "localhost.*1433.*wosm") {
            Write-Host "✅ 数据库配置存在" -ForegroundColor Green
        } else {
            Write-Host "⚠️  数据库配置可能需要调整" -ForegroundColor Yellow
        }
    }
}

function Check-Redis {
    Write-Host "`n=== Redis连接检查 ===" -ForegroundColor Cyan
    
    # 检查Redis服务
    try {
        $redisProcess = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
        if ($redisProcess) {
            Write-Host "✅ Redis服务运行中" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis服务未运行" -ForegroundColor Red
            Write-Host "提示: 请启动Redis服务或在配置中禁用Redis" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  无法检查Redis服务状态" -ForegroundColor Yellow
    }
}

function Check-Code-Issues {
    Write-Host "`n=== 代码问题检查 ===" -ForegroundColor Cyan
    
    # 检查TODO标记
    $todoFiles = @()
    Get-ChildItem -Path "internal" -Recurse -Filter "*.go" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match "TODO") {
            $todoFiles += $_.FullName
        }
    }
    
    if ($todoFiles.Count -gt 0) {
        Write-Host "⚠️  发现 $($todoFiles.Count) 个文件包含TODO标记:" -ForegroundColor Yellow
        $todoFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }
    } else {
        Write-Host "✅ 未发现TODO标记" -ForegroundColor Green
    }
    
    # 检查硬编码问题
    $hardcodedFiles = @()
    Get-ChildItem -Path "internal" -Recurse -Filter "*.go" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match "F@2233|localhost:6379|localhost:1433") {
            $hardcodedFiles += $_.FullName
        }
    }
    
    if ($hardcodedFiles.Count -gt 0) {
        Write-Host "⚠️  发现 $($hardcodedFiles.Count) 个文件包含硬编码配置:" -ForegroundColor Yellow
        $hardcodedFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }
    } else {
        Write-Host "✅ 未发现硬编码问题" -ForegroundColor Green
    }
}

function Build-Project {
    Write-Host "`n=== 项目构建 ===" -ForegroundColor Cyan
    
    try {
        Write-Host "正在构建项目..." -ForegroundColor Yellow
        go build -o wosm-backend.exe cmd\main.go
        
        if (Test-Path "wosm-backend.exe") {
            Write-Host "✅ 项目构建成功" -ForegroundColor Green
            $fileInfo = Get-Item "wosm-backend.exe"
            Write-Host "   可执行文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "❌ 项目构建失败" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 构建过程出错: $_" -ForegroundColor Red
        return $false
    }
}

function Test-Basic-Functionality {
    Write-Host "`n=== 基础功能测试 ===" -ForegroundColor Cyan
    
    if (-not (Test-Path "wosm-backend.exe")) {
        Write-Host "❌ 可执行文件不存在，请先构建项目" -ForegroundColor Red
        return $false
    }
    
    Write-Host "启动应用进行基础测试..." -ForegroundColor Yellow
    Write-Host "注意: 这将启动应用，请在另一个终端中测试API接口" -ForegroundColor Yellow
    Write-Host "测试完成后请按 Ctrl+C 停止应用" -ForegroundColor Yellow
    
    try {
        .\wosm-backend.exe
    } catch {
        Write-Host "应用已停止" -ForegroundColor Yellow
    }
}

function Fix-Common-Issues {
    Write-Host "`n=== 修复常见问题 ===" -ForegroundColor Cyan
    
    # 创建环境变量文件
    $envFile = ".env"
    if (-not (Test-Path $envFile)) {
        Write-Host "创建环境变量文件..." -ForegroundColor Yellow
        @"
# 数据库配置
DB_HOST=localhost
DB_PORT=1433
DB_NAME=wosm
DB_USERNAME=sa
DB_PASSWORD=F@2233

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-very-strong-jwt-secret-key-at-least-32-characters
JWT_EXPIRE=86400

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 应用配置
APP_ENV=dev
LOG_LEVEL=debug
"@ | Out-File -FilePath $envFile -Encoding UTF8
        Write-Host "✅ 环境变量文件已创建: $envFile" -ForegroundColor Green
    }
    
    # 创建构建脚本
    $buildScript = "build.bat"
    if (-not (Test-Path $buildScript)) {
        Write-Host "创建构建脚本..." -ForegroundColor Yellow
        @"
@echo off
echo 构建Go应用...
go mod tidy
go build -o wosm-backend.exe cmd\main.go
if exist wosm-backend.exe (
    echo 构建完成！
    echo 可执行文件: wosm-backend.exe
) else (
    echo 构建失败！
)
pause
"@ | Out-File -FilePath $buildScript -Encoding ASCII
        Write-Host "✅ 构建脚本已创建: $buildScript" -ForegroundColor Green
    }
    
    # 创建启动脚本
    $startScript = "start.bat"
    if (-not (Test-Path $startScript)) {
        Write-Host "创建启动脚本..." -ForegroundColor Yellow
        @"
@echo off
echo 启动WOSM后端服务...
if not exist wosm-backend.exe (
    echo 可执行文件不存在，正在构建...
    call build.bat
)
echo 启动服务...
wosm-backend.exe
pause
"@ | Out-File -FilePath $startScript -Encoding ASCII
        Write-Host "✅ 启动脚本已创建: $startScript" -ForegroundColor Green
    }
}

function Show-Summary {
    Write-Host "`n=== 检查总结 ===" -ForegroundColor Cyan
    Write-Host "项目状态分析完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步建议:" -ForegroundColor Yellow
    Write-Host "1. 如果发现问题，运行: .\项目检查和修复脚本.ps1 -Action fix" -ForegroundColor White
    Write-Host "2. 构建项目: .\项目检查和修复脚本.ps1 -Action build" -ForegroundColor White
    Write-Host "3. 测试应用: .\项目检查和修复脚本.ps1 -Action test" -ForegroundColor White
    Write-Host "4. 或者直接运行: .\build.bat 然后 .\start.bat" -ForegroundColor White
    Write-Host ""
    Write-Host "如需帮助，请查看: 迁移状况分析与落地方案.md" -ForegroundColor Cyan
}

function Test-API-Endpoints {
    Write-Host "`n=== API接口测试 ===" -ForegroundColor Cyan

    $baseUrl = "http://localhost:8080"

    # 测试健康检查接口
    try {
        Write-Host "测试健康检查接口..." -ForegroundColor Yellow
        $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 10
        Write-Host "✅ 健康检查接口正常: $($healthResponse.status)" -ForegroundColor Green
    } catch {
        Write-Host "❌ 健康检查接口失败: $_" -ForegroundColor Red
        return $false
    }

    # 测试登录接口
    try {
        Write-Host "测试登录接口..." -ForegroundColor Yellow
        $loginBody = @{
            username = "admin"
            password = "admin123"
        } | ConvertTo-Json

        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/public/login" -Method Post -Body $loginBody -ContentType "application/json" -TimeoutSec 10

        if ($loginResponse.code -eq 200) {
            Write-Host "✅ 登录接口正常" -ForegroundColor Green

            # 测试需要认证的接口
            $token = $loginResponse.data.token
            $headers = @{ Authorization = "Bearer $token" }

            try {
                $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/api/getInfo" -Method Get -Headers $headers -TimeoutSec 10
                Write-Host "✅ 用户信息接口正常" -ForegroundColor Green
            } catch {
                Write-Host "⚠️  用户信息接口异常: $_" -ForegroundColor Yellow
            }

        } else {
            Write-Host "⚠️  登录接口返回异常: $($loginResponse.msg)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 登录接口失败: $_" -ForegroundColor Red
        return $false
    }

    return $true
}

function Create-Quick-Start-Files {
    Write-Host "`n=== 创建快速启动文件 ===" -ForegroundColor Cyan

    # 创建一键启动脚本
    $quickStartScript = "quick-start.bat"
    if (-not (Test-Path $quickStartScript)) {
        Write-Host "创建一键启动脚本..." -ForegroundColor Yellow
        @"
@echo off
title WOSM后端服务
echo ================================
echo    WOSM后端服务一键启动
echo ================================
echo.

echo [1/4] 检查Go环境...
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ Go环境未安装或未配置
    pause
    exit /b 1
)
echo ✅ Go环境正常

echo.
echo [2/4] 整理依赖...
go mod tidy
if errorlevel 1 (
    echo ❌ 依赖整理失败
    pause
    exit /b 1
)
echo ✅ 依赖整理完成

echo.
echo [3/4] 构建应用...
go build -o wosm-backend.exe cmd\main.go
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo ✅ 构建完成

echo.
echo [4/4] 启动服务...
echo 服务将在 http://localhost:8080 启动
echo 按 Ctrl+C 停止服务
echo.
wosm-backend.exe
pause
"@ | Out-File -FilePath $quickStartScript -Encoding ASCII
        Write-Host "✅ 一键启动脚本已创建: $quickStartScript" -ForegroundColor Green
    }

    # 创建API测试脚本
    $apiTestScript = "test-api.ps1"
    if (-not (Test-Path $apiTestScript)) {
        Write-Host "创建API测试脚本..." -ForegroundColor Yellow
        @"
# API测试脚本
param([string]`$BaseUrl = "http://localhost:8080")

Write-Host "=== WOSM API测试 ===" -ForegroundColor Green
Write-Host "基础URL: `$BaseUrl" -ForegroundColor Yellow

# 测试健康检查
try {
    `$health = Invoke-RestMethod -Uri "`$BaseUrl/health" -Method Get
    Write-Host "✅ 健康检查: `$(`$health.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: `$_" -ForegroundColor Red
    exit 1
}

# 测试登录
try {
    `$loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json

    `$loginResponse = Invoke-RestMethod -Uri "`$BaseUrl/api/public/login" -Method Post -Body `$loginBody -ContentType "application/json"

    if (`$loginResponse.code -eq 200) {
        Write-Host "✅ 登录成功" -ForegroundColor Green
        `$token = `$loginResponse.data.token
        Write-Host "Token: `$token" -ForegroundColor Gray

        # 测试用户信息
        `$headers = @{ Authorization = "Bearer `$token" }
        `$userInfo = Invoke-RestMethod -Uri "`$BaseUrl/api/getInfo" -Method Get -Headers `$headers
        Write-Host "✅ 用户信息: `$(`$userInfo.data.user.userName)" -ForegroundColor Green

    } else {
        Write-Host "❌ 登录失败: `$(`$loginResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 登录测试失败: `$_" -ForegroundColor Red
}

Write-Host "`n测试完成！" -ForegroundColor Green
"@ | Out-File -FilePath $apiTestScript -Encoding UTF8
        Write-Host "✅ API测试脚本已创建: $apiTestScript" -ForegroundColor Green
    }
}

# 主执行逻辑
switch ($Action.ToLower()) {
    "check" {
        Check-Environment
        Check-Dependencies
        Check-Database
        Check-Redis
        Check-Code-Issues
        Show-Summary
    }
    "fix" {
        Check-Environment
        Fix-Common-Issues
        Create-Quick-Start-Files
        Write-Host "`n✅ 常见问题修复完成！" -ForegroundColor Green
        Write-Host "已创建快速启动文件，可以运行 .\quick-start.bat 一键启动" -ForegroundColor Cyan
    }
    "build" {
        Check-Environment
        Check-Dependencies
        Build-Project
    }
    "test" {
        Test-Basic-Functionality
    }
    "api" {
        Write-Host "正在测试API接口..." -ForegroundColor Yellow
        Write-Host "请确保应用已启动在 http://localhost:8080" -ForegroundColor Yellow
        Test-API-Endpoints
    }
    "all" {
        Write-Host "=== 完整检查和修复流程 ===" -ForegroundColor Green
        Check-Environment
        Check-Dependencies
        Check-Database
        Check-Redis
        Check-Code-Issues
        Fix-Common-Issues
        Create-Quick-Start-Files
        Build-Project
        Write-Host "`n🎉 完整流程执行完成！" -ForegroundColor Green
        Write-Host "下一步: 运行 .\quick-start.bat 启动应用" -ForegroundColor Cyan
    }
    default {
        Write-Host "用法: .\项目检查和修复脚本.ps1 -Action [check|fix|build|test|api|all]" -ForegroundColor Yellow
        Write-Host "  check: 检查项目状态（默认）" -ForegroundColor White
        Write-Host "  fix:   修复常见问题并创建启动文件" -ForegroundColor White
        Write-Host "  build: 构建项目" -ForegroundColor White
        Write-Host "  test:  测试应用启动" -ForegroundColor White
        Write-Host "  api:   测试API接口" -ForegroundColor White
        Write-Host "  all:   执行完整的检查、修复、构建流程" -ForegroundColor White
        Write-Host ""
        Write-Host "推荐使用: .\项目检查和修复脚本.ps1 -Action all" -ForegroundColor Cyan
    }
}
